<nav sidebar #sidebarComponent id="sidebar" role="navigation" class="sidebar"></nav>
<nav navbar (toggleSidebarEvent)="toggleSidebarListener($event)" (toggleChatEvent)="toggleChatListener()"
  class="page-controls navbar navbar-dashboard"></nav>
<aside chat-sidebar class="chat-sidebar"></aside>

<div class="content-wrap" id="content-wrap">
  <main id="content" class="content" role="main">
    <div class="row" style="margin-bottom: 7px;margin-top: -28px;">
      <div class="col-sm-7">
        <div class="row">
          <div class="col-sm-4" style="padding-right: 0px;">
            <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
              Search Report
            </label>
          </div>
          <div class="col-sm-6" style="padding-left: 0px;">
            <ng-select
              [items]="pageArray"
              [bindLabel]="'text'"
              [bindValue]="'id'"
              [(ngModel)]="pageUrl"
              (change)="pageTypeChanged($event)"
              [clearable]="false"
              placeholder="Select report type">
            </ng-select>
          </div>
        </div>
      </div>
      <div class="col-sm-5 text-right">
        <button *ngIf="router.url == '/admin/reservation'" class="btn btn-primary mr-2" (click)="openSendBookingModal()" type="button">Send Online Reservation Link</button>
        <button *ngIf="router.url == '/admin/reservation'" class="btn btn-info mr-2" (click)="getVerifyCard()" type="button">Verify Card</button>
        <button class="btn btn-sm btn-default" (click)="changeLanguage()">{{translateText}}</button>
      </div>
    </div>

    <router-outlet></router-outlet>
  </main>
</div>

<!-- Online Booking Popup (rendered on post-login pages) -->
<app-online-booking-popup></app-online-booking-popup>

<div class="modal fade" bsModal #checkInDynamicVerifyModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Verify Card Process
      </div>
      <!-- <div>{{StartDatetime}}</div> -->
      <div class="modal-body">
        <div class="row extra-padding">
          <div class="col-sm-12">
            <div class="reservations-wrapper" *ngIf="!getCardInfoValid && errMsg == ''">
              <div id="countdown">
                <div id='tiles' class="color-full">{{displayTime}}</div>
                <div id="left" class="countdown-label">Time Remaining</div>
              </div>
              
              <p class="reservations-wrapper-text">
                {{getCardInfo?.message}}
              </p>
            </div>
            <table class="table table-bordered"  *ngIf="getCardInfoValid && errMsg == ''">
              <tbody>
                <tr>
                  <th scope="row" width="170px">Room Number</th>
                  <td>{{getCardInfo?.roomId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Room Name</th>
                  <td>{{cardRoomName?.title}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Door Number</th>
                  <td>{{getCardInfo?.doorId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-in</th>
                  <td>{{StartDatetime}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-out</th>
                  <td>{{EndDatetime}}</td>
                </tr>
              </tbody>
            </table>

            <div *ngIf="!getCardInfoValid">
              <p class="reservations-wrapper-text" style="color: red;font-size: 25px;">
                <b>{{errMsg}}</b>
              </p>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-sm btn-inverse" (click)="checkInDynamicVerifyModal.hide();">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal for phone number input -->
<div class="modal fade" bsModal #getPhoneNumberModal="bs-modal" role="dialog" aria-labelledby="mySmallModalLabel2" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Send Online Reservation Link</h5>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <!-- Phone number input field -->
            <div class="form-group">
              <label for="phoneNumber">Phone Number:</label>
              <input id="phoneNumber" type="text" [(ngModel)]="phone_number" class="form-control" placeholder="Enter phone number"  />
            </div>

            <!-- Error message (if any) -->
            <div *ngIf="PhoneErrorMsg" [class]="PhoneErrorMsgStyle">
              {{ PhoneErrorMsg }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <!-- Send Link button -->
            <button type="button" class="btn btn-success" (click)="sendOnlineReservationRequest()" [disabled]="phoneButtonLoader">
              <span class="loader-parent-style" *ngIf="phoneButtonLoader"><i *ngIf="phoneButtonLoader" class="fa fa-circle-o-notch fa-pulse fa-2x"></i></span>
              <span *ngIf="!phoneButtonLoader"> Send Link</span>
            </button>
            <!-- Close button -->
            <button type="button" class="btn btn-secondary" (click)="getPhoneNumberModal.hide()" [disabled]="phoneButtonLoader">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>