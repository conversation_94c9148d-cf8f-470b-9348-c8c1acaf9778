import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()
export class BuildingService {

    constructor(private chttp: CommonHttpService) {  }

    getAllBuildings(){
        return this.chttp.get(`building/list`);
    }
    saveBuilding(data){
        return this.chttp.post(`building/add`,data, true);
    }
    updateBuilding(id,val){
        return this.chttp.post(`building/edit/${id}`,val, true);
    }
}