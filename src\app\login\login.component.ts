import { Component, ViewEncapsulation, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';

import { AuthGuard } from './../shared/guards/auth-guard.service';
import { _secretKey } from './../shared/globals/config';
import { UserService } from './../shared/services/user.service';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

import * as CryptoJS from 'crypto-js';

@Component({
  selector: 'login',
  styleUrls: ['./login.style.scss'],
  templateUrl: './login.template.html',
  encapsulation: ViewEncapsulation.None,
  host: {
    class: 'login-page app'
  },
  providers: [UserService,TranslateEventService]
})
export class Login {
  config: any;// New Change ****
  userLogin: FormGroup;
  email: any;
  password: any;
  private userService: any;
  private _secretKey: String = _secretKey
  public $destroy = new Subject(); // New Change ****
  private langChangeSub: Subscription; // New Change ****
  constructor(
    public translate: TranslateService,// New Change ****
    private _fb: FormBuilder,
    private router: Router,
    private authGuard: AuthGuard,
    private uService: UserService,
    config: AppConfig,// New Change ****
    private TS: TranslateEventService, // New Change ****
    ) {

    // build form
    this.userLogin = this._fb.group({
      email: ['', Validators.required],
      password: ['', Validators.required],
      rememberMe: [false]
    });

    // assign controls to variables to use in front end
    this.email = this.userLogin.controls['email']
    this.password = this.userLogin.controls['password']

    // redirect if user is logged in - has data in local storage
    if (localStorage.getItem('0')) {
      this.router.navigate([this.authGuard.loginRedirect]);
    }

    this.config = config.getConfig();// New Change ****
    let currentLang = localStorage.getItem('currentLang'); // New Change ****
    translate.setDefaultLang(currentLang || 'en');// New Change ****
    // New Change ****
    this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
        this.changeLang(res);
    });
  }
  changeLang(lang: string) {
    // New Change ****
    this.translate.use(lang);
 }

  login() {
    if (this.userLogin.valid) {
      let encPass = CryptoJS.AES.encrypt(this.userLogin.value.password, this._secretKey).toString();

      if (this.userLogin.value.password == this.userLogin.controls['password'].value) {
        this.userLogin.value.password = encPass; // encrypt password if not
      }

      this.userService = this.uService.userLogin(this.userLogin.value).subscribe(res => {
        if (res.status === 'success') {


           this.userLogin.reset();
          localStorage.setItem('admin_role', res.data.session_data.is_admin_role);

          if(res.data.session_data.role_id === 3) {
            // For Discount approval role (role_id= 3)
            this.router.navigateByUrl('admin/booking/report/discount');
          }
          else {
            // Set a one-time flag to show the online booking popup after navigation
            localStorage.setItem('show_online_booking_popup', '1');
            this.router.navigate([this.authGuard.loginRedirect]);
          }
        } else {
          // this.userLogin.reset();
          // console.log(res)
        }
      },
        (err) => {
          // this.userLogin.reset();
          // console.log(err)
        })
    }//endif
  }
  ngOnDestroy() {
    this.$destroy.next(); // New Change ****
    this.$destroy.complete(); // New Change ****

    if (this.userService) {
      this.userService.unsubscribe();
    }

    // New Change ****
    if (this.langChangeSub)
    this.langChangeSub.unsubscribe();
  }
}
