import { CustomValidators } from 'ng2-validation';
import { Component, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { CustomerTypeService } from './../../../shared/services/customerType.service';
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;
@Component({
    selector: 'add-customer-type',
    templateUrl: '../customerType.actions.html'
})
export class AddCustomerTypeComponent {
    pageType: string = "Add";
    customerTypeAdd: FormGroup;
    isPercentageValid: boolean = true;
    discountTypeRadio: string[] = ['amount', 'percentage', 'none'];
    private sub: any;
    // input/outputs
    @Input() gethiddenAddCT;
    @Output() sendHiddenAddCT = new EventEmitter;
    constructor(
        private _fb: FormBuilder,
        private CTS: CustomerTypeService,
        public translate: TranslateService
    ) {
        this.customerTypeAdd = this._fb.group({
            name: ['',Validators.required],
            discount_type: ['none',Validators.required],
            discount_value: ['0',[CustomValidators.number]],
            extra_bed_charge: ['0',[Validators.required,CustomValidators.number]],
            status: [true,Validators.required]
        });
        translate.get('CUSTOMER_TYPE.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    saveCustomerType() {
        if (this.customerTypeAdd.valid) {
            this.sub = this.CTS.saveCustomerType(this.customerTypeAdd.value).subscribe((res) => {
                if (res.status === "success") {
                    this.customerTypeAdd.reset();// reset form for reuse
                    this.toggleChild(res.data);// send added result
                }
            }, (err) => {
                let errBody = JSON.parse(err._body);
                let errors = errBody.data;
                if (errors.length > 0) {
                    errors.forEach(element => {
                        let control = this.customerTypeAdd.controls[element.fieldname];
                        control.setErrors({
                            backend: element.error
                        });
                    });
                }
            })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    changeDiscountValue() {
        let discountValue = this.customerTypeAdd.controls['discount_value'];
        let discountType = this.customerTypeAdd.value.discount_type;
        if (discountType == 'percentage' && this.customerTypeAdd.value.discount_value > 100) {
            discountValue.setErrors({"valid":false});
            this.isPercentageValid = false;
        }
        else {
            discountValue.setErrors(null);
            this.isPercentageValid = true;
        }
    }
    toggleChild(data) {
        let result;
        this.gethiddenAddCT = !this.gethiddenAddCT; // show/hide component
        if (data) {
            // send data for to add to list
            result = { gethiddenAddCT: this.gethiddenAddCT, data: data }
        } else {
            result = { gethiddenAddCT: this.gethiddenAddCT }
        }
        this.sendHiddenAddCT.emit(result);
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
    resrValue() {
        if (this.customerTypeAdd.controls['discount_type'].value == 'none') {
            this.customerTypeAdd.controls['discount_value'].patchValue('');
        }
    }
}