import { CustomValidators } from 'ng2-validation';
import { BookingService } from './../../../shared/services/booking.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;
@Component({
    selector: 'edit-extra-charges',
    templateUrl: '../extraCharges.actions.component.html'
})

export class EditExtraChargesComponent implements OnInit {

    extraChargesForm: FormGroup;
    private sub: any;
    public pageType: string = "Edit";
    @Input() data;
    @Output() sendEdited = new EventEmitter();
    @Output() closeEdit = new EventEmitter();

    constructor(
        private _fb: FormBuilder,
        private BS: BookingService,
        public translate: TranslateService
    ) { 
        translate.get('MANAGE_EXTRA_CHARGES.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.buildForm(this.data);
    }
    ngOnChanges(changes: any) {
        if (changes) {
            this.buildForm(this.data);
        }
    }
    buildForm(data) {
        this.extraChargesForm = this._fb.group({
            name: ['', [Validators.required]],
            charge: ['0', [Validators.required, CustomValidators.number]],
            status: ['', Validators.required]
        })
        if (data) {
            this.extraChargesForm.patchValue(data);
        }
    }
    closeThisComp() {
        this.closeEdit.emit('hiddenEdit');
    }
    saveExtraCharges(){
        if(this.extraChargesForm.valid){
            this.sub = this.BS.UpdateExtraChages(this.data.id,this.extraChargesForm.value)
            .subscribe((res) => {
                if(res.status == "success")
                {   
                    this.extraChargesForm.reset();
                    this.sendEdited.emit(res.data);
                }
            },(err) => {
                 let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.extraChargesForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
            })
        }else{
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }    
}