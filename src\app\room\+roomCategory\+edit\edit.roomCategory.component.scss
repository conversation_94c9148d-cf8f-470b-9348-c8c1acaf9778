.amenities-area{
    height: 100px;
    background:#efefef;
    border: 2px dashed rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    padding: 5px;   
    box-shadow: 0 0 0px 5px #fff inset;
}
.amenities-selection{
    display: inline-block;
    border: 1px solid rgba(0,0,0,.15);
    background: #fff;
    border-radius: 100px;
    font-size: 11px;
     padding: 3px 3px 3px 8px;
    cursor: default;
    margin: 5px;
    font-weight: bold;
    transition: all ease-in-out 200px;
    box-shadow: 0 0 2px rgba(0,0,0,.0);
    & > *, input{
        display: inline-block;
        padding: 0px;
        border: none;
        vertical-align: baseline
    }
    .amenities-cost:not(:empty){
        position: relative;
        &::before{
            content: '-';
            margin-right: 10px;
        }
    }
    .amenities-action{
        background: #aaa;
        border-radius: 100%;
        height: 20px;
        width: 20px;
        color:#fff;
        font-weight: 100;
        font-size: 11px;
        text-align: center;
        line-height: 21px;
            margin-left: 5px;
        cursor: pointer;
    }
    &:hover{
        box-shadow: 0 0 2px rgba(0,0,0,.1)
    }
}
