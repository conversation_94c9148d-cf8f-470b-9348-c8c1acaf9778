import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'add-agent',
    templateUrl: '../agent.action.html'
})

export class AddAgentComponent implements OnInit {
    public pageType: string = "Add";
    public agentTypeForm: FormGroup;
    private sub: any;
    @Input() cityList;
    @Output() addToList = new EventEmitter();
    @Output() hideAddEvent = new EventEmitter();
    public select2Options: any = {
        width: '100%'
    };
    constructor(
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        public translate: TranslateService;
    ) { 
        translate.get('AGENT.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.initForm();
    }
    initForm() {
        this.agentTypeForm = this._fb.group({
            name: ['', Validators.required],
            location: ['', [Validators.required, CustomValidators.notEqual("000000")]],
            location_name: [''],
            status: [true, Validators.required]
        })
    }
    saveAgentType() {
        if (this.agentTypeForm.valid) {
            this.sub = this.DS.saveAgent(this.agentTypeForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.addToList.emit(res.data);
                        this.agentTypeForm.reset();
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.agentTypeForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            for(let field in this.agentTypeForm.controls) {
                this.agentTypeForm.controls[field].markAsDirty();
                this.agentTypeForm.controls[field].markAsTouched();
            }
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    cityChanged(event: any) {
        let cityName = this.cityList[event?.id]?.text;
        this.agentTypeForm.get('location').patchValue(event?.id);
        this.agentTypeForm.get('location_name').patchValue(cityName);
        console.log("Location : ",this.agentTypeForm.value);
    }
    findIndex(searchTerm: any, property: any, targetArray: any[]) {
        for(let i = 0; i < targetArray.length; i++) {
            if(targetArray[i][property] === searchTerm) { return i}
        }
        return -1;
    }
    hideComponent() {
        this.hideAddEvent.emit();
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}