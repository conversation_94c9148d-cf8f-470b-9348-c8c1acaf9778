<!-- <edit-room *ngIf="!viewBookingDetail"></edit-room> -->


<section class="widget">
  <header>
    <h4><span class="" style="color: red;"><i class="fa fa-address-book-o"></i>&nbsp;&nbsp;Online booking Requests</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right text-right col-sm-6">
    <!-- <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'ROOM.ROOM_MANAGE.SEARCH'| translate:param}}">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div> -->
  </div>
  <div class="clearfix"></div>
  <div class="widget-body table-scroll">
    <div *ngIf="isLoading" class="loader-parent-style" style="display: flex;justify-content: center;padding: 2rem 0;">
      <i class="fa fa-circle-o-notch fa-pulse fa-2x"></i>
    </div>
    <div class="mt" *ngIf="!isLoading">
      <table class="table table-condence no-m-b small-footprint" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
          <tr>
            <th>
              <mfDefaultSorter by="id">#</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="room_title">Room Title</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="name">Name</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="checkin">Check-In</mfDefaultSorter>
            </th>
            <th class="">
              <mfDefaultSorter by="checkout">Check-Out</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="totalperson">Total Person</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="totalperson">Referance User</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="reservationdate">Reservation Date</mfDefaultSorter>
            </th>
            <th *ngIf="auth.roleAccessPermission('room','edit')" class="no-sort">
              <mfDefaultSorter by="status">Action</mfDefaultSorter>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let bookingequest of mf.data">
            <td>{{findIndex(bookingequest.id,"id")}}</td>
            <td><span class="fw-semi-bold uppercase">{{bookingequest.room_title}}</span></td>
            <td><span class="fw-semi-bold uppercase">{{bookingequest.name}}</span></td>
            <td>{{ReturnDate(bookingequest.check_in)}}</td>
            <td>{{ReturnDate(bookingequest.check_out)}}</td>
            <td>{{bookingequest.child + bookingequest.adult}}</td>
            <td>{{bookingequest.reference_user_name}}</td>
            <td>{{ReturnDate(bookingequest.reservation_date)}}</td>
            <td *ngIf="auth.roleAccessPermission('room','edit')" class="width-100 text-center"
              style="display: flex;gap: 0.3rem;">
              <button class="btn btn-xs btn-primary" (click)="showER(bookingequest)" placement="top"><i
                  class="fa fa-eye"></i></button>
              <button class="btn btn-xs btn-success" (click)="acceptmodal(bookingequest)" placement="top"><i
                  class="fa fa-check"></i></button>
              <button class="btn btn-xs btn-danger" (click)="canclemodal(bookingequest.id)" placement="top"><i
                  class="fa fa-close"></i></button>
            </td>
          </tr>
          <tr *ngIf="canViewRecords && mf.data.length === 0">
            <td colspan="100">
              {{'ROOM.ROOM_MANAGE.NO MATCHES'| translate:param}}
            </td>
          </tr>
          <tr *ngIf="!canViewRecords">
            <td class="text-danger" colspan="100">
              {{'ROOM.ROOM_MANAGE.PERMISSION_DENIED'| translate:param}}
            </td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="12">
              <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
            </td>
          </tr>
        </tfoot>
      </table>
    </div>
  </div>
</section>



<!-- View Details Modal -->
<div class="modal" bsModal #viewDetailsModal="bs-modal" role="dialog" [config]="{backdrop:false}" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true">

  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <section class="booking-info m-2 p-2">
        <header>
          <div class="row">
            <div class="col-sm-8">
              <h4>
                <span class="capitalized">
                  <i class="fa fa-clipboard"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.VIEW_BOOK' | translate:param}}</span>
              </h4>
            </div>
            <div class="col-sm-4 view-details-buttons">
              <button type="button" class="btn btn-sm btn-danger float-sm-right" (click)="goback()">
                <i class="fa fa-angle-left"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.BACK' | translate:param}}</button>
            </div>
          </div>
        </header>
        <hr class="large-hr" />
        <div class="clearfix"></div>
        <div class="widget-body" *ngIf="selectedBooking">
          <div class="mt">
            <div class="row">
              <!-- Booking Information Section -->
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h4>{{'VIEW_BOOKI_DETAI.BOOK_DE' | translate:param}}</h4>
                  </div>
                  <div class="panel-body">
                    <table class="table table-no-mar">
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.RES_ID' | translate:param}}:</td>
                        <td><strong>{{selectedBooking.id ? selectedBooking.id : '-'}}</strong></td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.STAY_TYPE' | translate:param}}:</td>
                        <td>
                          <strong>{{selectedBooking?.stayTypeDetails?.name}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.BOOK_STAT' | translate:param}}:</td>
                        <td>
                          <strong class="capitalize">{{selectedBooking.current_status ? selectedBooking.current_status :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.BOOK_DATE' | translate:param}}:</td>
                        <td>
                          <strong>{{(selectedBooking.reservation_date ? (selectedBooking.reservation_date | date) :
                            '-')}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.RATE' | translate:param}}:</td>
                        <td>
                          <strong>
                            <span
                              *ngIf="selectedBooking.roomCategoryDetails.charges">+&nbsp;{{selectedBooking.total_amount}}
                            </span>
                          </strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_IN' | translate:param}}:</td>
                        <td>
                          <strong class="text-success">{{(selectedBooking.check_in ? (selectedBooking.check_in |
                            date:'dd-MM-yyyy') : '-' )}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_OUT' | translate:param}}:</td>
                        <td>
                          <strong class="text-success">{{(selectedBooking.check_out ? (selectedBooking.check_out |
                            date:'dd-MM-yyyy') : '-')}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Aadhar Card:</td>
                        <td>
                          <strong class="text-danger">
                            {{selectedBooking.aadharcard_number ? selectedBooking.aadharcard_number : ''}}
                          </strong>
                        </td>
                      </tr>
                      <tr>
                        <td>PAN Card:</td>
                        <td>
                          <strong class="text-danger">
                            {{selectedBooking.pancard_number ? selectedBooking.pancard_number : 'not added'}}
                          </strong>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h4>{{'VIEW_BOOKI_DETAI.BILL_GUE_DETAI' | translate:param}}</h4>
                  </div>
                  <div class="panel-body">
                    <table class="table table-no-mar">
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.GUEST_NAME' | translate:param}}:</td>
                        <td style="text-transform:uppercase;">
                          <strong>{{selectedBooking.name ? selectedBooking.name : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.GUE_TYPE' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.customerDetails.name ? selectedBooking.customerDetails.name :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.CONT' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.contact ? selectedBooking.contact : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.EMAIL' | translate:param}}:</td>
                        <td>
                          <strong>{{selectedBooking.email ? selectedBooking.email : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.ROOM_NO' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.roomDetails.title ? selectedBooking.roomDetails.title :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'Referance User' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.reference_user_name ? selectedBooking.reference_user_name :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.ROOM_TYP' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.roomCategoryDetails.name ? selectedBooking.roomCategoryDetails.name
                            : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.TOT_ADU' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.adult ? selectedBooking.adult : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.TOT_CHIL' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.child ? selectedBooking.child : '-'}}</strong>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6" style="margin-top: 20px;">
              <div class="row">
                <!-- Transfer Room Dropdown -->
                <div class="form-group col-md-3 col-sm-3">
                  <p class="m-0 p-0">Room No</p>
                  <select class="form-control" (change)="onRoomChange($event)">
                    <option [value]="selectedBooking.roomDetails.title" disabled selected>
                      {{selectedBooking.roomDetails.title}}</option>
                    <option *ngFor="let room of roomsList" [value]="room.id">{{room.title}}</option>
                  </select>
                </div>
                <!-- Chance Customer Type Dropdown -->
                <div class="form-group col-md-5 col-sm-5">
                  <p class="m-0 p-0">Customer Type</p>
                  <select class="form-control" (change)="changeCustomerType($event)">
                    <option [value]="selectedBooking.customer_type" disabled selected>{{selectedBooking.customer_name}}</option>
                    <ng-container *ngFor="let customer of customerList">
                      <option *ngIf="customer.id !== selectedBooking.customer_type" [value]="customer.id">
                        {{customer.customer_name}}
                      </option>
                    </ng-container>
                  </select>
                </div>
                <div class="form-group col-md-4 col-sm-4">
                  <button class="btn btn-primary" style="margin-left: 10px;" (click)="updateRoomDetails(selectedBooking)" *ngIf="transferRoomId || customerTypeId">Update</button>
                </div>
              </div>
              <div class="row">
                <div [hidden]="showReferenceUserField" class="h-[30px] ms-2col-sm-8 ms-2">
                  <input type="text" [(ngModel)]="reference_user_name" required placeholder="Enter Referance User Name" style="width: 293px;
                                                                                    margin-left: 15px;
                                                                                    border: 1px solid gainsboro;
                                                                                    height: 30px;
                                                                                    padding-left: 5px;
                                                                                    border-radius: 2px;">
                </div>
              </div>

            </div>

            <div class="col-md-6" style="margin-top: 20px;">
              <button class="btn btn-primary" (click)="viewDocumentProof(selectedBooking.guestFiles)">View Documents</button>
              <button class="btn btn-success" (click)="acceptmodal(selectedBooking)">
                <span *ngIf="isLoading"><i class="fa fa-circle-o-notch fa-pulse fa-2x"></i></span>
                <span *ngIf="!isLoading">Accept</span>
              </button>
              <button class="btn btn-danger" (click)="canclemodal(selectedBooking.id)">Reject</button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>

<!-- Model to Give reason to Cancle Booking -->
<div class="modal " bsModal #cancelBookingRequest="bs-modal" role="dialog" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true" tabindex="1">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Reject Booking Request</h5>
      </div>
      <div class="modal-body">
        <div class="row">

          <div class="col-sm-12">
            <!-- Cancellation Reason Input -->
            <div class="form-group">
              <label for="cancelReason">Reason:</label>
              <textarea id="cancelReason" type="text" [(ngModel)]="cancelReason" class="form-control"
                placeholder="Enter cancellation reason"></textarea>
            </div>

            <!-- Error Message -->
            <div *ngIf="cancelErrorMessage" [class]="cancleBookingStyle">
              {{ cancelErrorMessage }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <!-- Cancel Booking Button -->
            <button type="button" class="btn btn-danger" (click)="cancelOnlineBookingRequest()">
              <i *ngIf="cancelButtenLoader" class="fa fa-circle-o-notch fa-pulse fa-2x"></i>
              <span *ngIf="!cancelButtenLoader"> Reject</span>
            </button>
            <!-- Close Modal Button -->
            <button type="button" class="btn btn-secondary" (click)="cancleRejectBooking()">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Model to accept Booking -->
<div class="modal " bsModal #acceptBookingModal="bs-modal" role="dialog" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true" tabindex="1">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Accept Booking Request</h5>
      </div>
      <div class="modal-body">
        <div class="row">

          <div class="col-sm-12">
            <!-- Error Message -->
            <div *ngIf="acceptErrorMessage" [class]="acceptBookingStyle">
              {{ acceptErrorMessage }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <!-- Cancel Booking Button -->
            <button type="button" class="btn btn-success" (click)="acceptBookingRequest()"
              [disabled]="acceptButtonLoader">
              <span class="loader-parent-style" *ngIf="acceptButtonLoader"><i *ngIf="acceptButtonLoader"
                  class="fa fa-circle-o-notch fa-pulse fa-2x"></i></span>
              <span *ngIf="!acceptButtonLoader"> Accept</span>
            </button>
            <!-- Close Modal Button -->
            <button type="button" class="btn btn-secondary" (click)="closeAcceptBooking()"
              [disabled]="acceptButtonLoader">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Model For View Document -->
<div bsModal #viewDocumentModal="bs-modal" id="viewDocumentModal" class="modal fade" tabindex="-1" role="dialog"
[config]="{backdrop: false, keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div style="display: flex; justify-content: space-evenly; align-items: center;">
        <ng-container *ngFor="let imageUrl of imageUrls">
          <div>
            <img class="image-modal-view px-1 pb-1 pt-1" [src]="imageUrl.Url" alt="">
            <p class="text-center"><b>{{imageUrl.doc_name}}</b></p>
          </div>
        </ng-container>
      </div>
      <div class="modal-footer">
        <div class="btn-group">
          <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="closeDocumentModal()">
            {{'VIEW_BOOKI_DETAI.CLOSE' | translate:param}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Model to accept Booking -->
<div class="modal " bsModal #successModal="bs-modal" role="dialog" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true" tabindex="1">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Message</h5>
      </div>
      <div class="modal-body">
        <div class="row">

          <div class="col-sm-12">
            <!-- Error Message -->
            <div *ngIf="acceptErrorMessage" [class]="successModalStyle">
              {{ acceptErrorMessage }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <!-- Cancel Booking Button -->
            <!-- <button type="button" class="btn btn-success" (click)="acceptBookingRequest()"
              [disabled]="acceptButtonLoader">
              <span class="loader-parent-style" *ngIf="acceptButtonLoader"><i *ngIf="acceptButtonLoader"
                  class="fa fa-circle-o-notch fa-pulse fa-2x"></i></span>
              <span *ngIf="!acceptButtonLoader"> accept booking</span>
            </button> -->
            <!-- Close Modal Button -->
            <button type="button" class="btn btn-secondary" (click)="successModal.hide()"
              [disabled]="acceptButtonLoader">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>