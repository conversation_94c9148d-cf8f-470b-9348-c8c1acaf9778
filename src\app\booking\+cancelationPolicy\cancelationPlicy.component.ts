import { BookingService } from './../../shared/services/booking.service';
import { Component, OnInit } from '@angular/core';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';
import { AppConfig } from 'app/app.config';
import { Subject, Subscription } from 'rxjs';
import { TranslateEventService } from 'app/shared/services/translation.service';

@Component({
    selector: 'cancelation-policy',
    templateUrl: './cancelationPlicy.component.html'
})

export class CancelationPlicyComponent implements OnInit {

    data: any[] = [];
    selectedPolicy: any;
    searchQuery: string;
    originalData: any[] = [];
    hiddenAdd: boolean = true;
    hiddenEdit: boolean = true;
    // services
    private sub: any;
    public canViewRecords: boolean;
    private updateCancellationReferenceAPI: any;
    config: any;
    public $destroy = new Subject();
    private langChangeSub: Subscription;
    constructor(
        private auth: AuthGuard,
        private BS: BookingService,
        public translate: TranslateService,
        config: AppConfig,
        private TS: TranslateEventService
    ) { 
        this.config = config.getConfig();
        let currentLang = localStorage.getItem('currentLang');
        translate.setDefaultLang('hi');
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            // initiate language change
            this.changeLang(res);
        });
    }

    ngOnInit() {
        this.canViewRecords = true;
        this.sub = this.BS.getAllPolicies()
            .subscribe((res) => {
                if (res.status == "success") {
                    // console.log(res.data)
                    this.data = res.data;
                    this.originalData = res.data;
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
    }
    changeLang(lang: string) {
        this.translate.use(lang);
    }
    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }
    showAdd() {
        this.hiddenAdd = false;
        this.hiddenEdit = true;
    }
    addtoList(event) {
        if (this.canViewRecords && event) {
            this.originalData.push(event);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }
    closeAdd(event) {
        if (event) {
            this.hiddenAdd = true;
        }
    }
    showEdit(ele) {
        this.hiddenEdit = false;
        this.hiddenAdd = true;
        this.selectedPolicy = ele;
    }
    addupdatedtoList(event) {
        // this.hiddenEdit = true;
        if (this.canViewRecords && event) {
            this.data[this.findIndex(event.id, "id", this.data) - 1] = event;
            this.originalData[this.findIndex(event.id, "id", this.originalData) - 1] = event;
        }
    }
    search() {
        this.initializeData();
        if (this.searchQuery && (<string>this.searchQuery).trim() != '') {
            this.data = this.data.filter(data => {
                return ((<number>data.day_before).toString().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    updateCancellationReference(policy: any) {
        let condition = policy.is_reference_required;
        let index = this.findIndex(policy.id, "id", this.data) - 1;
        this.data[index].is_reference_required = !condition;
        index = this.findIndex(policy.id, "id", this.originalData) - 1;
        this.originalData[index].is_reference_required = !condition;
        // console.log("is_reference_required : ", policy.is_reference_required);
        this.updateCancellationReferenceAPI = this.BS.updateCancellationReference(policy.id, this.data[index])
            .subscribe((res) => {
                if (res.status != "success") {
                    policy.is_reference_required = condition;
                    this.originalData[index].is_reference_required = condition;
                }
            }, (err) => {
                policy.is_reference_required = condition;
                this.originalData[index].is_reference_required = condition;
            });
    }
    updateCancellationNote(policy: any) {
        let condition = policy.is_note_required;
        let index = this.findIndex(policy.id, "id", this.data) - 1;
        this.data[index].is_note_required = !condition;
        index = this.findIndex(policy.id, "id", this.originalData) - 1;
        this.originalData[index].is_note_required = !condition;
        // console.log("is_note_required : ", policy.is_note_required);
        this.updateCancellationReferenceAPI = this.BS.updateCancellationNote(policy.id, this.data[index])
            .subscribe((res) => {
                if (res.status != "success") {
                    policy.is_note_required = condition;
                    this.originalData[index].is_note_required = condition;
                }
            }, (err) => {
                policy.is_note_required = condition;
                this.originalData[index].is_note_required = condition;
            });
    }
    initializeData() {
        this.data = this.originalData;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    closeEdit(event) {
        if (event) {
            this.hiddenEdit = true;
            this.selectedPolicy = "";
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
        if (this.updateCancellationReferenceAPI) {
            this.updateCancellationReferenceAPI.unsubscribe();
        }
    }
}