import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { ExpensesService } from 'app/shared/services/expenses.service';
import { UserShiftOutComponent } from './shift-out/user-shift-out.component';
import { UserShiftInComponent } from './shift-in/user-shift-in.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { ModalModule } from 'ngx-bootstrap/modal';
import { NgModule }      from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule }  from '@angular/common';
import { UserService } from 'app/shared/services/user.service';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClient } from '@angular/common/http';

export const routes = [
  { path: '', component: UserShiftInComponent, pathMatch: 'full' },
  { path: 'out', component: UserShiftOutComponent, pathMatch: 'full' }
];


@NgModule({
  imports: [
    FormsModule,
    CommonModule,
    NgSelectModule,
    TooltipModule,
    ReactiveFormsModule,
    ModalModule.forRoot(),
    RouterModule.forChild(routes),
    TranslateModule.forRoot(
      { 
        loader: {
          provide: TranslateLoader, 
          useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n//', '.json'),
          deps: [HttpClient] 
        }
      }
    )
  ],
  declarations: [
    UserShiftInComponent,
    UserShiftOutComponent
  ],
  providers: [
    UserService,
    ExpensesService
  ]
})
export class UserShiftModule {
  static routes = routes;
}
