$height: 55px;
$border-color: #dadada;

body.modal-open {
  overflow: scroll;

  div.bootstrap-timepicker-widget {
    z-index: 1000000 !important;
  }
}

.my-pagination {
  .pagination {
    display: table;
    margin: 0 0 0 auto;

    &._2 {
      margin: 0 auto 0 0 !important;
    }
  }

  .pagination a {
    color: #333;
    background: #fff;

    input[type="checkbox"] {
      position: absolute;
      left: -999%;

      &~label {
        margin-bottom: 0px;
        min-width: 200px;
        margin-left: -14px;
        margin-right: -14px;
        cursor: pointer;
      }

      &:checked~label {
        background: #fff;
        color: #222
      }

      &:not(:checked)~label {
        background: #f5f5f5;
        color: #999999
      }
    }
  }
}

.view_top {
  padding: 19.5px 20px 29.5px 20px;
  margin: 0 10px;
  gap: 490px;
  border-radius: 15px;
  opacity: 0px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
}

.view_top_1 {
  display: flex;
  align-items: center;
  justify-content: space-between;

  h1 {
    font-family: Inter;
    font-size: 40px;
    font-weight: 400;
    line-height: 60px;
  }

  label {
    font-family: Inter;
    font-size: 18px;
    font-weight: 500;
    line-height: 27px;
    color: rgba(122, 122, 122, 1)
  }

  strong {
    font-family: Inter;
    font-size: 20px;
    font-weight: 700;
    line-height: 30px;
    color: rgba(80, 178, 174, 1);
  }
}

.view_top_2 {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .view_top_2_left {
    font-family: Inter;
    display: flex;
    justify-content: space-between;
    width: 45%;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: rgba(93, 102, 121, 1);

    .view_top_2_left_first {
      width: 28%;
    }

    .view_top_2_left_second {
      width: 70%;
    }
  }

  .view_top_2_right {
    font-family: Inter;
    font-size: 18px;
    font-weight: 500;
    line-height: 27px;
    color: rgba(122, 122, 122, 1);

    strong {
      font-family: Inter;
      font-size: 20px;
      font-weight: 700;
      line-height: 30px;
      color: rgba(80, 178, 174, 1);
    }
  }
}

.view_top_3 {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .view_top_3_left {
    display: flex;
    width: 45%;
    align-items: center;
  
    svg {
      width: 15px;
      color: rgba(93, 102, 121, 1);
    }
  
    p {
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      margin: 0;
      color: rgba(93, 102, 121, 1);
    }
  }

  .view_top_3_right {
    .enable_Download_Receipt {
      padding: 5px 16px 5px 16px;
      border-radius: 5px;
      border: 1px solid rgba(133, 141, 157, 1);
      background: transparent;
      color: rgba(122, 122, 122, 1);
      font-family: Inter;
      font-size: 14px;
      font-weight: 500;
      line-height: 21px;
      text-align: left;
      cursor: pointer;
    }
    .enable_Download_Receipt:hover {
      background-color: rgba(80, 178, 174, 1);
      color: white;
      border: none;
    }
    .disable_Download_Receipt {
      font-family: Inter;
      font-size: 14px;
      font-weight: 500;
      line-height: 21px;
      text-align: left;
      padding: 5px 16px 5px 16px;
      border-radius: 5px;
      border: 1px solid rgba(133, 141, 157, 1);
      background: transparent;
      cursor: disable;
    }
  }
}

.view_content {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding: 0 10px;
}

.view_content_left_bottum {
  display: flex;
  justify-content: space-between;
  margin-top: 30px
}

.view_content_left {
  width: 49%;
  border-radius: 15px;
  border: 2px solid rgba(222, 222, 222, 1);
  padding: 25px 25px 20px;
  position: relative;

  p{
    font-family: Inter;
    font-size: 18px;
    font-weight: 500;
    line-height: 27px;
    text-align: left;
    margin: 0;
    color: rgba(122, 122, 122, 1);
  }

  b {
    font-family: Inter;
    font-size: 25px;
    font-weight: 700;
    line-height: 37.5px;
    text-align: left;
    color: rgba(56, 62, 73, 1);
  }

  .view_content_left_first {
    p{
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      text-align: left;
      margin: 10px 0;
      color: rgba(122, 122, 122, 1);
    }
  }

  .view_content_left_second {
    p {
      font-family: Inter;
      font-size: 18px;
      font-weight: 600;
      line-height: 27px;
      margin: 10px 0;
      text-align: right;
      color: rgba(56, 62, 73, 1);
    }
  }

  .view_content_left_image {
    position: absolute;
    top: -13px;
    right: -17px;
    z-index: 1;
  }
}

.view_content_right_bottum {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.view_content_right {
  width: 49%;
  border-radius: 15px;
  border: 2px solid rgba(222, 222, 222, 1);
  padding: 25px 25px 20px;
  background-color: rgba(229, 233, 242, 1);
  position: relative;

  p{
    font-family: Inter;
    font-size: 18px;
    font-weight: 500;
    line-height: 27px;
    text-align: left;
    margin: 0;
    color: rgba(122, 122, 122, 1);
  }

  b {
    font-family: Inter;
    font-size: 25px;
    font-weight: 700;
    line-height: 37.5px;
    text-align: right;
    color: rgba(56, 62, 73, 1);
  }

  .view_content_right_first {
    p{
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      text-align: left;
      margin: 10px 0;
      color: rgba(122, 122, 122, 1);
    }
  }

  .view_content_right_second {
    p {
      font-family: Inter;
      font-size: 18px;
      font-weight: 600;
      line-height: 27px;
      margin: 10px 0;
      text-align: right;
      color: rgba(56, 62, 73, 1);
    }
  }

  .view_content_right_image {
    position: absolute;
    top: -13px;
    right: -17px;
    z-index: 1;
  }
}

.view_button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 25px 10px;

  .button_Active {
    padding: 10px 15px;
    border-radius: 20px;
    border: 0.2px;
    background: rgba(80, 178, 174, 1);
    color: rgba(232, 241, 253, 1);
    font-size: 14px;
    font-family: Inter;
    font-weight: 500;
    line-height: 21px;
    cursor: pointer;
  }
  .button_inactive {
    padding: 10px 15px;
    border-radius: 20px;
    border: 0.2px solid rgba(80, 178, 174, 1);
    background: rgba(243, 253, 253, 1);
    color: rgba(80, 178, 174, 1);
    font-size: 14px;
    font-family: Inter;
    font-weight: 500;
    line-height: 21px;
    cursor: pointer;
  }

  .view_button_right {
    position: relative;

    .drop {
      position: absolute;
      top: 14px;
      right: 17px;
    }

    .dropdown {
      padding: 10px 42px 10px 20px;
      text-align: center;
      cursor: pointer;
    }
  }

  select {
    -webkit-appearance: none; /* For Chrome, Safari, Edge, and Opera */
    -moz-appearance: none; /* For Firefox */
    appearance: none;
  }

  option {
    background-color: white;
    color: rgba(80, 178, 174, 1);
    text-align: left;
    font-family: Inter;
    font-size: 14px;
    font-weight: 500;
  }
}

.view_data_content {
  margin: 0 10px;
  padding: 27px 20px;
  border-radius: 15px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);

  h1 {
    margin-bottom: 25px;
    font-weight: 600;
  }

  th {
    font-weight: 500;
    font-size: 18px;
    font-family: Inter;
  }

  td {
    font-size: 14px;
    font-family: Inter;

    button {
      background: rgba(243, 253, 253, 1);
      border: 0.1px solid rgba(180, 180, 180, 1);
      border-radius: 50%;
      padding: 7px;
      font-size: 16px;
      font-family: Inter;
      line-height: 16px;
      margin-right: 10px;
      cursor: pointer;
    }
  }

}

.custome_table {
  width: 100%;
  margin: 20px auto 0;

  td {
    padding: 2px 0;
    text-align: center;
    color: #075ac7;
    background: #fff;
    border: 1px solid $border-color;

    &.disabled {
      color: #999;
    }

    .verticle_text {
      align-content: stretch;
      writing-mode: vertical-rl;
      transform: rotate(180deg);
      font-weight: bold;
      color: #666;
      text-transform: uppercase;
      font-size: 12px;
      letter-spacing: 1.12px;
      margin: 0 auto;
    }

    .month_name,
    .day_name {
      font-weight: bold;
      font-size: 10px;
      text-transform: uppercase;
    }

    &>.dates {
      display: flex;
      width: 50px;
      padding: 2px;
      font-weight: bold;
      color: #666;
      font-size: 12px;

      &>span {
        flex: 1
      }

      .centered {
        line-height: 2;
        writing-mode: vertical-lr;
        transform: rotate(180deg);

        &+div {
          border-left: 1px solid #dadada;
        }
      }
    }

    &.__settings {
      input {
        -webkit-appearance: none;
        -moz-appearance: none;
        border: none;
        width: 25px;
        text-align: center;
        outline: none;
        outline-color: transparent;

        &:focus,
        &:active {
          outline: none;
          outline-color: transparent
        }
      }

      // .__inputs {
      //   input[type="text"]:first-child:not(:focus) {
      //     background: rgba(238, 238, 238, 0) // background: rgba(48, 255, 116, 0.23)
      //   }
      //   input[type="text"]:last-child:not(:focus) {
      //     background: rgba(239, 239, 239, 0) // background: rgba(255, 0, 0, 0.23);
      //   }
      // }
    }
  }
}

.widget.reservation__page {
  padding: 0;
}

.reservation__page {
  .tab-content>.tab-pane {
    padding: 2rem 1rem 1rem !important;
  }
}

.bold-fonts {
  font-weight: bold;
  text-transform: uppercase;
  text-align: center;
  padding: 5px;
  line-height: 1;
  color: #333;
  font-size: 12px;
}

.child-flexed {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  &>span {
    flex: 1;
    line-height: 2.5;
    font-size: 11px;
    padding: 0 3px;
  }
}

.date-list {
  span {
    display: block
  }
}

input[type=number] {
  -moz-appearance: textfield;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

table,
div.table-div {
  width: 100%;

  &.reservation_table {
    text-align: center;

    tr,
    .table-div-row {

      td,
      .table-div-cell {
        &:not([data-isbooked]) {
          border-right: 1px solid #dedede;

          &:last-child {
            border-right: 1px solid transparent;
          }
        }

        // border-left: 1px solid transparent;
        border-top: 1px solid #dedede;

        .__week_name,
        &.__week_name {
          text-transform: uppercase;
          font-weight: bold;
          font-size: 12px;
          color: #555
        }

        .__disabled-booking,
        &.__disabled-booking {
          color: #cacaca !important;

          .__week_name {
            color: #cacaca !important;
          }
        }
      }
    }

    .__date_range {
      position: relative; // display: flex;
      height: 35px;
      width: 100%;

      &>div {

        // flex: 1;
        &:not([data-isbooked="true"]) {
          border-right: 1px solid #dedede;
        }

        &[data-isbooked="true"]:last-of-type() {
          border-right: 1px solid #dedede;
        }

        &:last-child {
          border-right: transparent !important;

          label {
            border-right: transparent !important;
          }
        }
      }

      span {
        display: block;
        text-align: center;
      }

      input[type=checkbox] {
        position: absolute;
        left: -999999%;
        display: none;

        &~label {
          display: block;
          height: 100%;
          width: 100%;
          margin-bottom: 0;

          &:not(.__cannotselect):not(.__already_booked) {
            cursor: pointer;
          }

          &.__cannotselect {
            background: rgba(238, 238, 238, 0.57);
          }
        }

        &:checked~label {
          background: #ffd010;
          border-right: transparent !important;
        }

        &~label {
          position: absolute;
          top: 0;
          bottom: 0;
          right: 0;
          left: 0;
        }
      }
    }
  }

  &:last-child {
    border-bottom: 1px solid #dedede;
  }
}

.selection_area {
  .ui-selecting {
    background: #dfdfdf;

    label {
      background: #dfdfdf;
    }
  }
}

.__next_month {
  color: #b9b868;

  .__week_name {
    color: #b9b868 !important;
  }
}

.__prev_month {
  color: #b9b868;

  .__week_name {
    color: #b9b868 !important
  }
}

.__context-menu__ {
  display: none;
  min-width: 150px;
  background-color: beige;
  z-index: 1;
  margin-left: -235px;
  position: absolute;
  box-shadow: 0 0 5px 0px rgba(0, 0, 0, 0.3)
}

.__context-menu-option {
  display: block;
  padding: 5px 10px;
  margin: 0;
  cursor: pointer;

  &:hover {
    background: white
  }
}

.__context-menu__ ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.__context-menu__ ul li span {
  margin-left: 0px;
}

button.close {
  font-size: 13px;
  border: 1px solid #808080;
  padding: 6px;
  font-weight: 600;
  border-radius: 5px;
  transition: all ease 350ms;
}

.nav-tabs>.nav-item .nav-link.active,
.nav-tabs>.nav-item .nav-link.active:hover,
.nav-tabs>.nav-item .nav-link.active:focus {
  box-shadow: none;
}

.nav-tabs>.nav-item .nav-link {
  transition: all ease 0.75s;
}

.__booking_wizard {
  .nav {
    .__validation_indicator {
      color: red;
      opacity: 0;
      display: inline-block;
      transform-origin: center bottom;
      line-height: 0;

      &.is-not-valid {
        opacity: 1;
        animation-name: bounceIn;
        animation-duration: .75s;
        animation-fill-mode: both;
      }
    }
  }
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 35px !important;
}

.select2-container .select2-selection--single {
  height: 37px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 35px !important;
  width: 35px !important;
}

.dropup,
.dropdown.angular2-contextmenu {
  position: inherit;
}

@media (min-width: 992px) {
  .modal-lg {
    max-width: 1024px;
  }
}

label.form-control.bg-checkin {
  background: #337ab7;
  color: #fff;
}

label.form-control.bg-checkout {
  background: #f0ad4e;
  color: #fff;
}

.relative {
  position: relative;

  label.absolute {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 12px;
    z-index: -1;
  }
}

.full-table {
  table {
    cursor: pointer;
    width: 100%;
  }
}

.customerTypeSelections {
  .select2-selection__rendered {
    padding-left: 125px !important;
    line-height: 33px !important;
  }
}

.proofTypeSelections {
  .select2-selection__rendered {
    padding-left: 95px !important;
    line-height: 33px !important;
  }
}

.referenceSelectionsmain {
  .select2-selection__rendered {
    padding-left: 65px !important;
    line-height: 33px !important;
    text-transform: capitalize;
  }
}

.referenceSelections {
  .select2-selection__rendered {
    padding-left: 90px !important;
    line-height: 33px !important;
    text-transform: capitalize;
  }

  .select2-container--disabled {
    opacity: 0.5
  }

  .select2-container:not(.select2-container--disabled) {
    .select2-selection--single {
      border-radius: 0 3px 3px 0 !important;
    }
  }
}

.agentSelections {
  .select2-selection__rendered {
    padding-left: 60px !important;
    line-height: 33px !important;
    text-transform: capitalize;
  }

  .select2-container--disabled {
    opacity: 0.5
  }

  .select2-container:not(.select2-container--disabled) {
    .select2-selection--single {
      border-radius: 0 3px 3px 0 !important;
    }
  }
}

.agentLocationSelections {
  .select2-selection__rendered {
    padding-left: 120px !important;
    line-height: 33px !important;
    text-transform: capitalize;
  }

  .select2-container--disabled {
    opacity: 0.5
  }

  .select2-container:not(.select2-container--disabled) {
    .select2-selection--single {
      border-radius: 0 3px 3px 0 !important;
    }
  }
}

.sourceTypeSelection {
  .select2-selection__rendered {
    padding-left: 70px !important;
    line-height: 38px !important;
    text-transform: capitalize;
  }
}

.advancePaymentSelection {
  width: 200px;
  .select2-selection__rendered {
    padding-left: 125px !important;
    line-height: 38px !important;
    text-transform: capitalize;
  }
}

.return_amount_select {
  width: 100px;
}

.paymentMode{
  display: flex;
  align-items: center;
  gap: 5px;
  label{
    margin: 0px;
  }
}

.room-details {

  div.col-sm-4,
  div.col-sm-6 {
    padding-right: 0px;

    .second-addon {
      border-bottom-left-radius: .25rem;
      border-top-left-radius: .25rem;
      border: 1px solid rgba(0, 0, 0, 0.15)
    }
  }
}

.view-tab.row {
  .col-sm-6 {
    padding-left: inherit;
  }

  .col-sm-6:nth-child(even) {
    padding-right: inherit;
  }
}

@keyframes bounceIn {

  from,
  20%,
  40%,
  60%,
  80%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }

  0% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }

  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }

  40% {
    transform: scale3d(.9, .9, .9);
  }

  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }

  80% {
    transform: scale3d(.97, .97, .97);
  }

  to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }

  from {
    transform: none;
  }

  15% {
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }

  30% {
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }

  45% {
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }

  60% {
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }

  75% {
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }

  to {
    transform: none;
  }
}

.select2-container--default .select2-selection--single {
  background-color: rgba(255, 255, 255, 0) !important;
}

.is_reference.abc-checkbox {
  padding-left: 0;

  label::before {
    margin-left: -14px;
    top: 0;
    margin-top: -5px;
  }

  label::after {
    margin-left: -14px;
    margin-top: -3px;
    padding: 0;
  }

  input[type="checkbox"]:focus+label::before {
    outline: none;
  }
}

.is_reference_label.is_disabled {
  opacity: 0.5
}

.input-group-addon.has-border-right {
  border-right: 1px solid rgba(0, 0, 0, .15);
  border-radius: 3px;
}

.payroll {
  padding-top: 15px;

  .text-right {
    text-align: right;
  }

  .table-no-border {

    td,
    th {
      padding: 0.15rem;
      border-color: transparent !important
    }
  }
}

.table-white {
  background: #fff !important;
}

.payments {
  display: table;
  margin: 0 0 0 auto;
  cursor: pointer;
  font-weight: bold;
  color: #b5b3b3;

  &.is_expanded_trigger {
    margin: -5px 0 0 auto;
    color: #000;
    font-size: 15px;
  }
}

.totalpayments {
  margin-bottom: 0px;
}

.__already_booked {
  width: 100% !important;

  &[data-bookingstatus="reserved"] {
    background: #00a7ff;
  }

  &[data-bookingstatus="checkin"] {
    background: #0dd433;
  }

  &[data-bookingstatus="checkout"] {
    background: #d80000;
  }

  &[data-bookingstatus="split"] {
    background: #ffd010;
  }

  &[data-bookingstatus="all_guest_details_added"] {
    background: #F44336;
  }

  &[data-bookingstatus="maintenance"] {
    background: #808080;
  }

  &[data-starting="true"]:not([data-bookingstatus="split"]) {
    border-radius: 15px 0 0 15px;
    position: relative;

    &::before {
      content: attr(data-username);
      position: absolute;
      left: 0;
      height: 100%;
      line-height: 3;
      padding-left: 15px;
      color: #fff;
      font-size: 12px;
      font-weight: 700;
      text-transform: capitalize; // text-overflow: ellipsis;
      white-space: nowrap;
      z-index: 5;
    }

   
  }

  &[data-ending="true"]:not([data-bookingstatus="split"]) {
    position: relative;
    overflow: hidden;
    border-radius: 0 15px 15px 0;
    z-index: 1;
  }

  &[data-starting="true"][data-ending="true"]:not([data-bookingstatus="split"]) {
    border-radius: 15px;
    overflow: hidden;

    &::before {
      padding-left: 5px;
    }
  }

  &:not([data-ending="true"]) {
    border-right: transparent !important;
    overflow: hidden;
  }

  &.has-extra-booking {
    &[data-starting="true"]:not([data-bookingstatus="split"]) {
      &::before {
        background-color: #000;
        border-bottom-right-radius: 15px;
      }

      &[data-extrabookingstatus="checkin"] {
        &::before {
          background-color: #0dd433 !important;
        }
      }

      &[data-extrabookingstatus="all_guest_details_added"] {
        &::before {
          background-color: #F44336 !important;
        }
      }
    }
  }
}

.angular2-contextmenu {
  ul.dropdown-menu {
    position: absolute !important;
    margin-left: 0px !important;
  }
}

context-menu.wide-list {
  .angular2-contextmenu {
    ul.dropdown-menu {
      max-width: 30rem;

      li {
        width: 50%;
        display: inline-block;
      }

      li:nth-child(odd) {
        border-right: 1px solid #bfbfbf;
      }
    }
  }
}

.__custom_tab {
  .nav-link {
    span {
      line-height: 1;
      vertical-align: middle;
      font-weight: 600;
      font-size: 12px;
    }

    span.glyphicon {
      vertical-align: top;
      margin-left: 5px;
      margin-top: 3px;
      font-weight: 100;
      font-size: 16px;
    }
  }
}

.payment-pending {
  background: #eee
}

.payment-success {
  background: #adffbd
}

@media screen and (min-width: 998px) {
  .border-sm-left {
    border-left: 1px solid #ddd;
  }
}

.text-light {
  color: #8d9498;
}

div {
  .table-div {
    display: table;
    width: 100%;
    border-left: 1px solid #dedede;
    border-right: 1px solid #dedede;

    .table-div-row {
      display: table-row;
      width: 100%;
      clear: both;

      & div.table-div-cell {
        vertical-align: middle;
        position: relative;
        min-height: 35px;
        text-align: center;
        display: table-cell;
        width: 50px;
        min-width: 50px;
        max-width: 50px;

        .__week_name {
          display: block;
        }
      }
    }

    .__overbooking_cell {
      background: #d8fff1
    }
  }

  &.isMonth {
    overflow: auto;
  }
}

.fundCheck.abc-checkbox {
  border-radius: 3px !important;

  label::before,
  label::after {
    margin-top: -5px;
    margin-left: -8px;
  }
}

.splitBookingRoomSelect_div {
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;

  table {
    margin-bottom: 0px;
  }
}

.splitBookingRoomSelect {
  cursor: pointer;
  text-transform: capitalize;
  display: inline-block;

  i.fa-check {
    color: #b7b7b7;
    margin-right: 10px;
  }

  i:not(.fa-check) {
    margin: 0 15px 0 15px
  }

  &:hover i.fa-check {
    color: #00a7ff;
  }
}

fieldset.__fieldset {
  padding: 10px 20px;
  border: 1px solid rgb(247, 223, 157);

  legend {
    padding: 5px 10px;
    display: table;
    width: auto;
    border-bottom: none;
    margin-bottom: 0;
    color: #f2b92f;
    font-style: italic;
  }

  .Docs {
    display: table;
    position: relative;

    img {
      width: 100%;
    }

    .delete_image {
      position: absolute;
      top: 5px;
      right: -30px;
      cursor: pointer;
      height: 25px;
      width: 25px;
      text-align: center;
      color: black;
      border-radius: 100%;
      background: #e4e4e4;
      border: 0.5px solid white;

      i {
        line-height: 25px;
      }
    }

    .view_image {
      cursor: pointer;
    }
  }
}

.child-has-no-border {
  & * {
    border: none
  }
}

.room-details-with-margin {
  margin-bottom: 0.5rem;
}

.__flxed__rooms {
  display: flex;
  padding: 0 0 0 15px;

  .__room-name {
    flex-basis: 40%;
    line-height: 35px;
    font-weight: 800;
    height: 35px;
    text-align: left;
  }
}

div timepicker {
  table {
    tbody {
      tr {
        td {
          input.form-control {
            padding: 0px 12px;
          }

          button.btn {
            padding: 0px 12px;
          }

          a.btn.btn-link {
            padding: 1px 1rem;
            font-size: .9rem;
          }
        }
      }
    }
  }

  table:last-child {
    border-bottom: none;
  }
}

.customer-list-dropdown {
  border-top: none;
  max-height: 100px;
  overflow-y: scroll;
  border-radius: 0px 0px 4px 4px;
  border: 1px solid rgba(0, 0, 0, 0.15);

  div {
    padding: 6px 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15)
  }

  div:last-child {
    border-bottom: none;
  }
}

.table-div-cell.current-date-highlight {
  background: #F0F4C3 !important;

  &.same-day {
    background: #DCE775 !important;
  }
}

.table-div-cell {
  .current-date-highlight {
    background: #F0F4C3 !important;

    &.same-day {
      background: #DCE775 !important;
    }
  }

  .badge.note {
    top: 5px;
    z-index: 2;
    right: 0px;
    bottom: 0px;
    float: right;
    padding: 0px;
    font-size: 1.2rem;
    border-radius: 50%;
    position: absolute;
    margin: 0.2rem 0.5rem;
    background-color: transparent;

    &.satsangi {
      top: 0px !important;

      img {
        background: white;
        border-radius: 50%;
        padding: 2px;
      }

      div.free-customer {
        width: 20px;
        height: 20px;
        margin-top: 3px;
        margin-right: -2px;
        background-color: red;
        border-radius: 50%;
      }
    }

    &.stay-type {
      top: 5px !important;
    }

    &.with-satsangi {
      right: 32px !important;
    }

    &.with-note {
      right: 50px !important;
    }
  }

  .badge.note.overbooking-count-right {
    background-color: transparent;
    position: absolute;
    right: 5px;
    top: 11px;
    border-radius: 50%;
    font-size: 0.7rem;
    color: #000;
    float: right;
    bottom: 0px;
    padding: 0px;
    z-index: 2;
    margin: 0.2rem 0.5rem;
  }

  .badge.note.overbooking-count-left {
    background-color: transparent;
    position: absolute;
    left: 12px;
    right: auto;
    top: 11px;
    border-radius: 50%;
    font-size: 0.7rem;
    color: #000;
    float: left;
    bottom: 0px;
    padding: 0px;
    z-index: 2;
    margin: 0.2rem 0.5rem;

    &.text-label {
      font-size: .9rem;
      padding-left: 20px;
      color: white;
    }
  }
}

.relative-class {
  position: relative;
}

.badge.special {
  padding: 0px;
  position: absolute;
  right: 10px;
  bottom: 0;
  top: -100px;
  margin: 0px;
  height: 260px;
  width: 30px;
  transition: opacity 0.3s ease;
  border-radius: 0 !important;
  transform: rotate(70deg);
  background: #333;
}

@keyframes barberpole {
  from {
    background-position: 0 0;
  }

  to {
    background-position: 157px 0px;
  }
}

@keyframes stretch {
  0% {
    transform: scale(.3);
    background-color: #F44336;
    border-radius: 100%;
  }

  50% {
    background-color: #FF9800;
  }

  100% {
    transform: scale(1.5);
    background-color: #FFEB3B;
  }
}

.bill-total {
  border-top: 1px solid #ddd;
  font-weight: 600;
  text-align: right;

  .row {
    .key.padding-right {
      width: 80%;
      padding-left: 15px;
      padding-right: 15px;
    }

    .value.padding-right {
      width: 20%;
      padding-left: 15px;
      padding-right: 23px;
    }
  }
}

.bill-grand-total {
  font-weight: 600;
  font-size: 1rem;
  text-align: right;

  .row {
    .key.padding-right {
      width: 80%;
      padding-left: 15px;
      padding-right: 15px;
    }

    .value.padding-right {
      width: 20%;
      padding-left: 15px;
      padding-right: 23px;
    }
  }
}

th.text-right {
  text-align: right;
}

td.text-right {
  text-align: right;
}

.text-right {
  text-align: right;
}

.widget-body {
  .mt {
    margin-top: 2rem;
  }

  .mt.no-margin-top {
    margin-top: 0px;
  }

  .mt.billing-guest {
    background-image: url(assets/img/Logo.png) center;

    p {
      margin-bottom: 0px;
    }

    p:first-child {
      margin-bottom: 1em;
    }
  }
}

@media print {

  .nav-item,
  .btn {
    display: none;
  }

  .mt.billing-guest {
    margin-top: 2rem;

    .row {
      display: flex;
      flex-wrap: wrap;
      margin-right: -15px;
      margin-left: -15px;
    }

    .col-sm-6 {
      position: relative;
      min-height: 1px;
      width: 100%;
      padding-right: 15px;
      padding-left: 15px;
      max-width: 50%;
    }
  }
}

int-phone-prefix.form-control {
  padding: 0px;
  border: 0px;
}

int-phone-prefix.form-control.ng-invalid.ng-dirty {
  padding: 0px;
  border: 1px solid red;
}

span.select2-selection__arrow {
  b {
    margin-left: 3px !important;
  }
}

datetime.overbooking {
  .form-inline {
    .input-group.date {
      width: -webkit-fill-available;
    }
  }
}

.float-right-calendar {
  float: right;
  padding-top: 5px;
  padding-right: 6px;
  font-size: 22px;
}

.float-left-calendar {
  float: left;
  padding-top: 3px;
  padding-left: 12px;
  font-size: 25px;
}

.body-center-content {
  margin: auto;
  cursor: pointer;
  padding: 10% 0px;
  text-align: center;
}

a.today-button-calendar {
  border-radius: 3px !important;
  padding: 0 50px;
  background: #F0F4C3 !important;
}

.with-labels {
  .label {
    font-size: 12px;
    font-weight: bold;
    padding: 3px 7px;
    min-width: 100px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    text-transform: uppercase;
    filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, .352));
    -webkit-filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, .352));

    &.label-success {
      background: #4CAF50;
    }

    &.label-danger {
      background: #F44336;
    }

    &.label-warning {
      background: #CDDC39;
      color: #333 !important
    }

    &.label-primary {
      background: #2196F3;
    }
  }
}

.widget-body.common-room-booking-element {
  height: -webkit-fill-available !important;

  table {
    border-bottom: none;
  }
}

.image-modal-view {
  margin: auto;
  display: table;
  max-width: 100%;
}

.basic-guest-info {
  .increment-decrement {
    cursor: pointer;
    padding: 0px 10px;
  }

  .badge {
    width: 50px;
    margin: 0px 5px;
  }

  .input-group {
    margin-top: 0.5rem;
  }
}

.not-guest-detail-info {
  cursor: pointer;
  display: table;
  font-size: 1.8rem;
  font-weight: 500;
  margin: 120px auto;
}

.guest-dob {
  width: -webkit-fill-available !important;

  .input-group.date {
    width: -webkit-fill-available !important;
  }
}

.change-customer-type {
  .row.extra-padding {
    padding: 15px 0px 5px 15px;
  }
}

.alert.alert-danger.no-block {
  border: none;
  color: #ff0500;
  background-color: inherit;
}

.sub-transction {
  color: red
}

.multiplcation {
  color: #36ce36;
}

@media (max-width: 768px) {
  .table-scroll-res {
    overflow: auto;
  }
}

.inline-flex {
  display: inline-flex !important;
}