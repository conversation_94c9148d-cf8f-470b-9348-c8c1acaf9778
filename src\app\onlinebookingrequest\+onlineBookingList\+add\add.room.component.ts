import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RoomsService } from '../../../shared/services/room.service';
import { Select2OptionData } from 'ng2-select2';
// import * as data from '../../data';
declare var Messenger: any;
@Component({
    selector: 'add-room',
    templateUrl: '../onlineRequestRooms.actions.component.html'
})

export class AddRoomComponent implements OnInit {
    public pageName: string = "Add";
    select2Options: any = {
        width: '100%'
    };
    roomForm: FormGroup;
    //services
    private sub: any;
    private floor: any;
    private floorList: any;
    private getData: any;
    // Input and Outputs
    @Input() getHiddenAR;
    @Output() sendHiddenAR = new EventEmitter();

    public roomCat: any[];
    public wing: any[];

    constructor(
        private _fb: FormBuilder,
        private RS: RoomsService
    ) {
        this.getData = this.RS.getRoomCatandWingData()
            .subscribe((res) => {
                if (res.status == "success") {
                    if (res.data.roomcategories) {
                        this.roomCat = res.data.roomcategories;
                    } else {
                        this.roomCat = [];
                        Messenger().post({  hideAfter: 5,
                            message: "No Room Categories found.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                    if (res.data.buildings) {
                        this.wing = res.data.buildings;
                    } else {
                        this.wing = [];
                        Messenger().post({  hideAfter: 5,
                            message: "No Buildings found.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                }
            })
    }

    ngOnInit() {
        this.buildForm();
    }

    buildForm() {
        this.roomForm = this._fb.group({
            title: ['', Validators.required],
            room_category_id: ['', [Validators.required, CustomValidators.digits]],
            door_id: ['', []],
            building_id: ['', [Validators.required, CustomValidators.digits]],
            floor_id: ['', [Validators.required, CustomValidators.digits]],
            default_bed: ['0', [Validators.required, CustomValidators.digits]],
            max_bed: ['0', [Validators.required, CustomValidators.digits]],
            status: [true, Validators.required]
        })
    }
    // dropdowns
    getCategorylist(): Select2OptionData[] {
        if(this.roomCat){
            return jQuery.map(this.roomCat, function (obj) {
                return { id: obj.id, text: obj.name };
            })
        }
    }
    getWingList(): Select2OptionData[] {
        if(this.wing){
            return jQuery.map(this.wing, function (obj) {
                return { id: obj.id, text: obj.name };
            })
        }
    }
    getFloorList(): Select2OptionData[] {
        if(this.floorList) {
            return jQuery.map(this.floorList, function (obj) {
                return { id: obj.id, text: obj.name };
            })
        }
    }
    categoryChanged(event) {
        this.roomForm.controls['room_category_id'].patchValue(event.value);
    }
    wingChanged(event) {
        this.roomForm.controls['building_id'].patchValue(event.value);
        console.log("event :",event);
        this.floor = this.RS.getBuildingFloors(event.value).subscribe((res) => {
            this.floorList = res.data;
        });
    }
    floorChanged(event) {
        this.roomForm.controls['floor_id'].patchValue(event.value);        
    }
    // save
    saveRoom() {
        console.log("Saving....");
        if (this.roomForm.valid) {
            if(this.roomForm.value && this.roomForm.value.door_id == ''){
                this.roomForm.value.door_id = null;
            }
            console.log("Conditions true",this.roomForm.value);
            this.sub = this.RS.saveRoomCatandWingData(this.roomForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        Messenger().post({  hideAfter: 5,
                            message: res.message,
                            type: res.status,
                            showCloseButton: true
                        });
                        this.toggleChild(res.data);
                    }
                    else {
                        console.log("Room not added");
                    }
                },(err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.roomForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        }else{
            console.log("Conditions false")            
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    // toggle to mains
    toggleChild(data) {
        this.getHiddenAR = !this.getHiddenAR;
        let result;
        if (data) {
            result = { 'getHiddenAR': this.getHiddenAR, 'data': data }
        } else {
            result = { 'getHiddenAR': this.getHiddenAR }
        }
        this.sendHiddenAR.emit(result);
    }
}