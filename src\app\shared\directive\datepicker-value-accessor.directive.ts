import { Directive, forwardRef, ElementRef, HostListener } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Directive({
  selector: 'bs-datepicker-inline[formControlName], bs-datepicker-inline[formControl], bs-datepicker-inline[ngModel]',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DatepickerValueAccessor),
      multi: true
    }
  ]
})
export class DatepickerValueAccessor implements ControlValueAccessor {
  private onChange: any = () => {};
  private onTouched: any = () => {};

  constructor(private elementRef: ElementRef) {}

  @HostListener('bsValueChange', ['$event'])
  onValueChange(value: any) {
    this.onChange(value);
    this.onTouched();
  }

  writeValue(value: any): void {
    if (value) {
      this.elementRef.nativeElement.bsValue = value;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.elementRef.nativeElement.disabled = isDisabled;
  }
} 