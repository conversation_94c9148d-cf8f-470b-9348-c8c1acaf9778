import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { CustomValidators } from 'ng2-validation';
import { Select2OptionData } from "ng2-select2";
import { UserService } from './../../shared/services/user.service';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'add-reference-user',
    templateUrl: '../referenceUser.actions.html'
})

export class AddReferenceUserComponent implements OnInit {
    referenceUserForm: FormGroup;
    public pageType: string = "Add";
    public customerTypes: string[];
    public select2Option: any = {
        width: '100%',
        multiple: true
    };
    @Input() getHiddenaddRU;
    @Input() customerTypeList: Array<Select2OptionData>;
    @Output() sendHiddenaddRU = new EventEmitter();
    @Output() listSavedData = new EventEmitter();

    private sub: any;
    constructor(
        private _fb: FormBuilder,
        private US: UserService,
        public translate: TranslateService
    ) { 
        translate.get('REF_USER.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.buildForm();
    }

    buildForm() {
        this.referenceUserForm = this._fb.group({
            name: ['', Validators.required],
            contact: ['', [Validators.required, CustomValidators.digits]],
            customer_type_ids: [[], Validators.required]
        })
    }
    toggleChild() {
        this.referenceUserForm.reset();
        this.getHiddenaddRU = !this.getHiddenaddRU;
        this.sendHiddenaddRU.emit(this.getHiddenaddRU);
    }
    saveReferenceUser() {
        if (this.referenceUserForm.valid) {
            this.sub = this.US.saveReferenceUser(this.referenceUserForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.referenceUserForm.reset();
                        this.customerTypes = [];
                        let response = res.data;
                        this.listSavedData.emit(response);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.referenceUserForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    customerTypeChanged(event: any) {
        let customerType = <FormControl>this.referenceUserForm.controls['customer_type_ids'];
        let selectedCustomerIds: any[] = customerType.value || [];
        if (event && event.id) {
            selectedCustomerIds.push(event.id);
            customerType.patchValue(selectedCustomerIds);
        }
    }

    removedCustomerType(event: any) {
        let customerType = <FormControl>this.referenceUserForm.controls['customer_type_ids'];
        let selectedCustomerIds: any[] = customerType.value || [];
        if (event && event.id) {
            const index = selectedCustomerIds.indexOf(event.id);
            if (index > -1) {
                selectedCustomerIds.splice(index, 1);
                customerType.patchValue(selectedCustomerIds);
            }
        }
    }
    
    ngOnDestroy(){
        if(this.sub){
            this.sub.unsubscribe();
        }
    }
}