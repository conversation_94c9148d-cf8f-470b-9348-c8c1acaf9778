<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-user"></i>&nbsp;&nbsp;{{pageType}} {{'REF_USER.ADD_PAGE.REF_USER' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="clearfix"></div>
  <div class="widget-body">
    <div class="">
      <fieldset>
        <form [formGroup]="referenceUserForm" (ngSubmit)="saveReferenceUser()">
        <div class="form-group row">
          <label for="normal-field" class="col-md-12  col-form-label ">{{'REF_USER.ADD_PAGE.PER_NAME' | translate:param}}</label>
          <div class="col-md-12 ">
            <span class="errMsg __fromBackend" *ngIf="referenceUserForm.controls.name.errors?.backend">{{referenceUserForm.controls.name.errors?.backend}}</span>
            <input type="text"  class="form-control" formControlName="name" name="name" placeholder="">
            <span class="errMsg" *ngIf="!referenceUserForm.controls.name.valid && !referenceUserForm.controls.name.pristine">
              <span [hidden]="!referenceUserForm.controls.name.errors.required">{{'REF_USER.ADD_PAGE.VALID_MSG.PER_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>

        <div class="form-group row">
          <label for="normal-field" class="col-md-12  col-form-label ">{{'REF_USER.ADD_PAGE.CUST_TYPE' | translate:param}}</label>
          <div class="col-md-12 ">
            <span class="errMsg __fromBackend" *ngIf="referenceUserForm.controls.customer_type_ids.errors?.backend">{{referenceUserForm.controls.customer_type_ids.errors?.backend}}</span>
            <ng-select [items]="customerTypeList"
                      [multiple]="true"
                      [clearable]="true"
                      bindLabel="text"
                      bindValue="id"
                      formControlName="customer_type_ids"
                      (change)="customerTypeChanged($event)"
                      (clear)="removedCustomerType($event)"
                      placeholder="Select customer types">
            </ng-select>
            <span class="errMsg" *ngIf="!referenceUserForm.controls.customer_type_ids.valid && !referenceUserForm.controls.customer_type_ids.pristine">
              <span [hidden]="!referenceUserForm.controls.customer_type_ids.errors.required">{{'REF_USER.ADD_PAGE.VALID_MSG.CUST_TYPE_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>

        <div class="form-group row">
          <label for="normal-field" class="col-md-12  col-form-label ">{{'REF_USER.ADD_PAGE.CONT_NO' | translate:param}}</label>
          <div class="col-md-12 ">
            <span class="errMsg __fromBackend" *ngIf="referenceUserForm.controls.contact.errors?.backend">{{referenceUserForm.controls.contact.errors?.backend}}</span>
           
              <input type="text" maxlength="10" minlength="10"  class="form-control" formControlName="contact"  placeholder="">
           
            <span class="errMsg" *ngIf="!referenceUserForm.controls.contact.valid && !referenceUserForm.controls.contact.pristine">
              <span [hidden]="!referenceUserForm.controls.contact.errors.required">{{'REF_USER.ADD_PAGE.VALID_MSG.CONT_REQ' | translate:param}}</span>
              <span [hidden]="!referenceUserForm.controls.contact.errors.digits">{{'REF_USER.ADD_PAGE.VALID_MSG.ONLY_DIGIT_REQ' | translate:param}} </span>
            </span>
          </div>
        </div>

        <div class="form-group row">
          <div class="col-md-12">
            <div class="">
              <button type="submit" [disabled]="!referenceUserForm.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'REF_USER.ADD_PAGE.SAVE' | translate:param}}</button>
              <button type="button" (click)="toggleChild()" class="btn btn-sm btn-secondary">{{'REF_USER.ADD_PAGE.CLOSE' | translate:param}}</button>
            </div>
          </div>
        </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
