import { _<PERSON><PERSON><PERSON> } from './../shared/globals/config';
import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { UserService } from './../shared/services/user.service';
import { PasswordValidation } from './../shared/directive/confirmPassword.directive';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

import * as CryptoJS from 'crypto-js';

declare var Messenger: any;

@Component({
    selector: 'set-password',
    templateUrl: 'setPassword.component.html',
    providers: [UserService, TranslateEventService]
})

export class SetPasswordComponent implements OnInit {
    setPasswordForm: FormGroup;
    config: any;// New Change ****
    private sub: any;
    private idService: any;
    id: any;
    public routeSegments: string[];
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(

        config: AppConfig,
        private _fb: FormBuilder,
        private US: UserService,
        private route: ActivatedRoute,
        private router: Router,
        public translate: TranslateService,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        this.idService = this.route.params.subscribe((params) => {
            this.id = params['id'];
        });
        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() {
        this.setPasswordForm = this._fb.group({
            new_password: ['', [Validators.required]],
            cfm_password: ['', [Validators.required]]
        }, { validator: PasswordValidation.MatchPassword })
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    passwordConfirming(c: AbstractControl) {
        return c.get('new_password').value === c.get('cfm_password').value
    }

    setPassword() {
        // encrypt passwords
        let new_password =
            CryptoJS.AES.encrypt(this.setPasswordForm.value.new_password, _secretKey);

        let confirm_password =
            CryptoJS.AES.encrypt(this.setPasswordForm.value.cfm_password, _secretKey);


        let np_bytes = CryptoJS.AES.decrypt(new_password, _secretKey);
        let cp_bytes = CryptoJS.AES.decrypt(confirm_password, _secretKey);

        if (np_bytes.toString(CryptoJS.enc.Utf8) === cp_bytes.toString(CryptoJS.enc.Utf8)) {
            // if new password == confirm password
            let data = {
                password: new_password.toString()
            }

            if (this.setPasswordForm.valid) {
                // replace this from condition "this.routeSegments.indexOf('resetpassword')"
                if (this.router.url.split('/').indexOf('resetpassword') != -1) {
                    // forgot password
                    this.sub = this.US.forgetPassword(data, this.id)
                        .subscribe((res) => {
                            if (res.status == "success") {
                                Messenger().post({
                                    hideAfter: 5,
                                    message: "Password has been reset.",
                                    type: "info",
                                    showCloseButton: true
                                });
                                this.router.navigate(['login']);
                            } else {
                                // console.log(res);

                            }
                        }, (err) => {
                            this.router.navigate(['login']);

                            // console.log(err);
                        })

                } else {
                    // resetting password for the first time
                    this.sub = this.US.setPassword(data, this.id)
                        .subscribe((res) => {
                            if (res.status == "success") {
                                Messenger().post({
                                    hideAfter: 5,
                                    message: "Password set successfully. Login with this credentials.",
                                    type: "info",
                                    showCloseButton: true
                                });
                                this.router.navigate(['login']);
                            } else {
                                // console.log(res);

                            }
                        }, (err) => {
                            this.router.navigate(['login']);
                            // console.log(err);
                        })
                }
            }

        }

    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        if (this.idService) {
            this.idService.unsubscribe();
        }
        if (this.sub) {
            this.sub.unsubscribe();
        }
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}