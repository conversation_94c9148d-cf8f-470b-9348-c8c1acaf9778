<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-sitemap"></i>&nbsp;&nbsp;{{pageName}} {{'ROOM.ROOM_MANAGE.ADD_PAGE.ROOM'| translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item "><a href="javascript:void(0)" (click)="toggleChild()">{{'ROOM.ROOM_MANAGE.ADD_PAGE.ROOM_MANAGE'| translate:param}}</a></li>
    <li class="breadcrumb-item active">{{pageName}} {{'ROOM.ROOM_MANAGE.ADD_PAGE.ROOM'| translate:param}}</li>
  </ol>

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="roomForm" (ngSubmit)="saveRoom()">

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_MANAGE.ADD_PAGE.NO_TITLE'| translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="roomForm.controls.title.errors?.backend">{{roomForm.controls.title.errors?.backend}}</span>
              <input type="text" formControlName="title" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!roomForm.controls.title.valid && !roomForm.controls.title.pristine">
              <span [hidden]="!roomForm.controls.title.errors.required">{{'ROOM.ROOM_MANAGE.ADD_PAGE.VALID_MSG.TIT_REQ'| translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_MANAGE.ADD_PAGE.DOOR_ID'| translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="roomForm.controls.title.errors?.backend">{{roomForm.controls.title.errors?.backend}}</span>
              <input type="text" formControlName="door_id" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!roomForm.controls.title.valid && !roomForm.controls.title.pristine">
              <span [hidden]="!roomForm.controls.title.errors.required">{{'ROOM.ROOM_MANAGE.ADD_PAGE.VALID_MSG.TIT_REQ'| translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'ROOM.ROOM_MANAGE.ADD_PAGE.CATEGORY'| translate:param}}</label>
            <div class="col-md-8 ">
              <input type="hidden" formControlName="room_category_id">
              <ng-select formControlName="room_category_id" [items]="getCategorylist()" bindLabel="text" bindValue="id" (change)="categoryChanged($event)" [style.width.px]="250"></ng-select>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_MANAGE.ADD_PAGE.WING_BUILD'| translate:param}}</label>
            <div class="col-md-8 ">
              <input type="hidden" formControlName="building_id">
              <ng-select formControlName="building_id" [items]="getWingList()" bindLabel="text" bindValue="id" (change)="wingChanged($event)" [style.width.px]="250"></ng-select>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_MANAGE.ADD_PAGE.FLOOR'| translate:param}}</label>
            <div class="col-md-8 ">
              <input type="hidden" formControlName="floor_id">
              <ng-select formControlName="floor_id" [items]="getFloorList()" bindLabel="text" bindValue="id" (change)="floorChanged($event)" [style.width.px]="250"></ng-select>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_MANAGE.ADD_PAGE.DEF_BED'| translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="roomForm.controls.default_bed.errors?.backend">{{roomForm.controls.default_bed.errors?.backend}}</span>
              <input type="text" formControlName="default_bed" class="form-control" placeholder="">
               <span class="errMsg" *ngIf="!roomForm.controls.default_bed.valid && !roomForm.controls.default_bed.pristine">
              <span [hidden]="!roomForm.controls.default_bed.errors.required">{{'ROOM.ROOM_MANAGE.ADD_PAGE.VALID_MSG.DEF_BED_REQ'| translate:param}}</span>
               <span [hidden]="!roomForm.controls.default_bed.errors.digits">{{'ROOM.ROOM_MANAGE.ADD_PAGE.VALID_MSG.NUM_ALLOW'| translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_MANAGE.ADD_PAGE.MAX_BED'| translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="roomForm.controls.max_bed.errors?.backend">{{roomForm.controls.max_bed.errors?.backend}}</span>
              <input type="text" formControlName="max_bed" class="form-control" placeholder="">
               <span class="errMsg" *ngIf="!roomForm.controls.max_bed.valid && !roomForm.controls.max_bed.pristine">
              <span [hidden]="!roomForm.controls.max_bed.errors.required">{{'ROOM.ROOM_MANAGE.ADD_PAGE.VALID_MSG.MAX_BED_REQ'| translate:param}}</span>
               <span [hidden]="!roomForm.controls.max_bed.errors.digits">{{'ROOM.ROOM_MANAGE.ADD_PAGE.VALID_MSG.ONLY_NUM_ALLOW'| translate:param}}</span>
              </span>
            </div>
          </div>


          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'ROOM.ROOM_MANAGE.ADD_PAGE.STATUS'| translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio1" [value]="true">
                  <label for="radio1">
                      {{'ROOM.ROOM_MANAGE.ADD_PAGE.ACTIVE'| translate:param}}
                      </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio2" [value]="false">
                  <label for="radio2">
                      {{'ROOM.ROOM_MANAGE.ADD_PAGE.INACTIVE'| translate:param}}
                      </label>
                </div>
              </div>
            </div>
          </div>


          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" [disabled]="!roomForm.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'ROOM.ROOM_MANAGE.ADD_PAGE.SAVE'| translate:param}}</button>
                <button (click)="toggleChild()" class="btn btn-sm btn-secondary">{{'ROOM.ROOM_MANAGE.ADD_PAGE.CANCEL'| translate:param}}</button>
              </div>
            </div>
          </div>
        </form>
        <!--<pre>
          {{ roomForm.value | json}}
        </pre>-->
      </fieldset>
    </div>
  </div>
</section>
