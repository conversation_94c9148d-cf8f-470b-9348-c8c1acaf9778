import { Routes, RouterModule } from '@angular/router';
import { Layout } from './layout.component';
import { AuthGuard } from './../shared/guards/auth-guard.service';

const routes: Routes = [
  {
    path: '', component: Layout, children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'shift', loadChildren: () => import('../shift/user-shift.module').then(m => m.UserShiftModule) },
      { path: 'dashboard', loadChildren: () => import('../dashboard/dashboard.module').then(m => m.DashboardModule) },
      {
        path: 'dharamshala',
        loadChildren: () => import('../dharamshala/dharamshala.module').then(m => m.DharamshalaModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'expenses',
        loadChildren: () => import('../expenses/expenses.module').then(m => m.ExpensesModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'amenities',
        loadChildren: () => import('../amenities/amenities.module').then(m => m.AmenitiesModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'customer',
        loadChildren: () => import('../customerType/customerType.module').then(m => m.CustomerTypeModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'user',
        loadChildren: () => import('../user/user.module').then(m => m.UserModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'reference',
        loadChildren: () => import('../referenceUser/referenceUser.module').then(m => m.ReferenceUserModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'guest',
        loadChildren: () => import('../guest/guest.module').then(m => m.GuestModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'booking',
        loadChildren: () => import('../booking/booking.module').then(m => m.BookingModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'inventory',
        loadChildren: () => import('../inventory/inventory.module').then(m => m.InventoryModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'reservation',
        loadChildren: () => import('../reservation/reservation.module').then(m => m.ReservationModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'building',
        loadChildren: () => import('../building/building.module').then(m => m.BuildingModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'room',
        loadChildren: () => import('../room/room.module').then(m => m.RoomModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'onlinebooking',
        loadChildren: () => import('../onlinebookingrequest/onlinebookingrequest.module').then(m => m.OnlineBookingRequestModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'menu',
        loadChildren: () => import('../menu/menu.module').then(m => m.MenuModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'role',
        loadChildren: () => import('../role/role.module').then(m => m.RoleModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'settings',
        loadChildren: () => import('../settings/setting.module').then(m => m.SettingModule)
      },
      {
        path: 'discountReferenceUser',
        loadChildren: () => import('../discountReferenceUser/discountReferenceUser.module').then(m => m.DiscountReferenceUserModule),
        canActivateChild: [AuthGuard]
      },
      {
        path: 'roomMaintenanceReason',
        loadChildren: () => import('../roomMaintenanceReason/roomMaintenanceReason.module').then(m => m.RoomMaintenanceReasonModule),
        canActivateChild: [AuthGuard]
      }
    ]
  }
];

export const ROUTES = RouterModule.forChild(routes);
