import { UserRoleService } from './../../shared/services/userrole.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

declare var jQuery : any
@Component({
    selector: 'roles-permission',
    templateUrl: './rolesPermission.component.html',
    styleUrls: ['./rolesPermission.scss']
})
export class RolesPermissionComponent implements OnInit {
    // service variables
    config: any;// New Change ****
    private userRole: any;
    private assignedActions: any;
    private actionService: any;

    // data variables
    public userRoleData: any[];
    public userRoleModalData: any[];
    public userRoleModalActionsData: any[];
    public assignedactionsData: any[];

    // selected values
    public roleId: number;
    public roleModel: number;

    public canViewRecords: boolean;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private URS: UserRoleService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // get roles and model lists and save to local
        this.canViewRecords = true;
        this.userRole = this.URS.getuserRoleModal()
            .subscribe((res) => {
                if (res.status == "success") {
                    this.userRoleData = res.data.roles;
                    this.userRoleModalData = res.data.modelList.model;
                    // console.log("this.userRoleData : ",this.userRoleData);
                    // console.log("this.userRoleModalData : ",this.userRoleModalData);
                    // console.log(this.userRoleData)
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
         // New Change ****
         this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });    
    }

    ngOnInit() { }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    getModelLists(id) {
        // set role id on change
        this.roleId = id;
        this.assignedactionsData = [];
        this.userRoleModalActionsData = null; // to hide action column
        // unselect model group on role change
        jQuery('.radioReset').each(function(){
            jQuery(this).prop('checked', false);
        })
        // console.log("Selected Role's Models : ",this.roleModel);
    }

    changeUserRoleModel(model) {
        // set model name on change
        this.roleModel = model;
        this.assignedactionsData = [];
        this.userRoleModalActionsData = null;
        // get assigned actions on selected role and model
        this.assignedActions = this.URS.getAssignedRoles(this.roleId, this.userRoleModalData[this.roleModel].role).subscribe((res) => {
            if (res.status == "success") {
                // console.log("Get Assigned Roles : ",res);
                this.userRoleModalActionsData = this.userRoleModalData[model].action;
                // if model have action assigned or not
                if (res.data) {
                    this.assignedactionsData = res.data.action;
                } else {
                    this.assignedactionsData = [];
                }
            }
        })
    }

    changeAction(action) {
        // set action to that specific role and model
        // console.log("Modified Actions List : ",action);
        let data = {
            role_id: this.roleId,
            model: this.userRoleModalData[this.roleModel].role,
            action: action.name,
            status: action.checked
        }
        // console.log("Modified Actions List : ",data);        
        // set actions
        this.actionService = this.URS.setActionStatus(data)
            .subscribe((res) => {
                if (res.status == "success") {
                    // console.log(res);
                } else {
                    action.checked = !action.checked
                }
            }, (err) => {
                action.checked = !action.checked
            })
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if (this.userRole) {
            this.userRole.unsubscribe();
        }
        if (this.assignedActions) {
            this.assignedActions.unsubscribe();
        }
        if (this.actionService) {
            this.actionService.unsubscribe();
        }

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}