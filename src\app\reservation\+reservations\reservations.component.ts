import { DharamshalaService } from './../../shared/services/dharamshala.service';
import { <PERSON><PERSON><PERSON><PERSON>, Renderer2 } from '@angular/core';
import { Location } from '@angular/common';
import { AuthGuard } from './../../shared/guards/auth-guard.service';
import { _secretKey_auth, _secretKey } from './../../shared/globals/config';
import { apiUrl } from './../../api-env';
import { Select2OptionData } from 'ng2-select2/ng2-select2';
import { Router } from '@angular/router';
import { CustomValidators } from 'ng2-validation';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl, AbstractControl } from '@angular/forms';
import { GeneralValidations } from "../../shared/directive/general.validation";
import { ModalDirective } from 'ngx-bootstrap/modal';
import { TabsetComponent } from 'ngx-bootstrap/tabs';
import { BookingService } from './../../shared/services/booking.service';
import { Component, ViewChild, ElementRef, OnChanges, OnInit, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { BsDatepickerInlineDirective } from 'ngx-bootstrap/datepicker';
import { FileUploader } from 'ng2-file-upload';
import { allowedMimeTypes } from "../../dharamshala/+dharamshalaList/allowedFileTypes";
import { DomSanitizer } from '@angular/platform-browser';
import { paymentType } from "../data";
// import { Renderer } from '@angular/core'; Removed whilde updating angular version 4 from 2 [it is depreacted]
import Moment from 'moment';
import * as _ from "lodash";
import { extendMoment } from 'moment-range';
import * as CryptoJS from 'crypto-js';
import { TranslateService } from '@ngx-translate/core';
import { AppConfig } from 'app/app.config';
import { TranslateEventService } from 'app/shared/services/translation.service';
import { Subject, Subscription } from 'rxjs';
import { ReservationServices } from 'app/shared/services/reservation.services';
import { PaymentRequestService } from 'app/shared/services/payment.service';
// import {HttpHeaders} from "@angular/common/http";
import { HttpClient } from '@angular/common/http';
import { data } from 'jquery';
import { BsDatepickerConfig } from 'ngx-bootstrap/datepicker';

declare var jQuery: any;
declare var Messenger: any;
const moment = extendMoment(Moment);
const URL = apiUrl + 'guest/add/image';
declare var datepicker: any;
@Component({
  selector: 'reservations',
  templateUrl: './reservations.component.html',
  styleUrls: ['./reservation.component.scss'],
  encapsulation: ViewEncapsulation.None
})

export class ReservationsComponent implements OnInit, OnDestroy {
  @ViewChild('billPrint') billPrint: ElementRef;
  // @ViewChild('viewGuestDetailsModal') viewGuestDetailsModal: ModalDirective;
  @ViewChild('fundtypeID') fundtypeID: ElementRef;
  @ViewChild('invoiceHtml') invoiceHtml: ElementRef;
  @ViewChild('commonRoomBody') commonRoomBody: ElementRef;
  @ViewChild('extraBookingBody') extraBookingBody: ElementRef;
  @ViewChild('addGuestTabs') addGuestTabs: TabsetComponent;
  @ViewChild('returnAmountValue') returnAmountValue: ElementRef;
  @ViewChild('reservationTabs', {static: false}) reservationTabs?: TabsetComponent;
  @ViewChild('totalPayableAmount') totalPayableAmount: ElementRef;
  @ViewChild('datepickerupdate1') datepickerupdate1: BsDatepickerInlineDirective;
  @ViewChild('datepickerupdate2') datepickerupdate2: BsDatepickerInlineDirective;
  @ViewChild('addGuestDatePicker1') addGuestDatePicker1: BsDatepickerInlineDirective;
  @ViewChild('addGuestDatePicker2') addGuestDatePicker2: BsDatepickerInlineDirective;
  @ViewChild('splitDatePickerStart') splitDatePickerStart: BsDatepickerInlineDirective;
  @ViewChild('splitDatePickerEnd') splitDatePickerEnd: BsDatepickerInlineDirective;
  @ViewChild('viewDetailsModal') public viewDetailsModal: ModalDirective;
  @ViewChild('viewDocumentModals') public viewDocumentModals: ModalDirective;
  @ViewChild('successModal') public successModal: ModalDirective;
  @ViewChild('acceptBookingModal') public acceptBookingModal: ModalDirective;
  @ViewChild('cancelBookingRequest') public cancelBookingRequest: ModalDirective;
  @Output() goBack = new EventEmitter();
  public uploader: FileUploader = new FileUploader({
    url: URL,
    allowedMimeType: allowedMimeTypes
  });
  // ---PDF ----//
  public PDFData: any;
  public ViewData : string = 'payment';
  public selectedAction : string = 'Action'
  public eventData : any ; 
  public roomIndex : any ;
  public eventMain : any ;
  public guestDetailss : any ;
  viewComp: boolean = false;
  isPoliceInquiry: boolean = false;
  public pdfURL: any;
  public time: any;
  public disabled: boolean = true;
  public uploadedImages: any[] = [];
  public monthNames: string[] = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
  public dayNames: string[] = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  public currMoment = moment();
  public currDate = this.currMoment.toDate();
  public currDateDate = this.currMoment.get('date');
  public currDateDateCursor = this.currDateDate == 1 ? this.currDateDate : this.currDateDate - 1;
  public currDayCursorActive: boolean = false;
  public currMonth = this.currMoment.get('month');
  public currYear = this.currMoment.get('year');
  public currMonthStartDate: any;
  public currMonthEndDate: any;
  public ranges: any[] = [];
  public originalRanges: any[] = [];
  public weekRange: any[] = [];
  public isPrevMonth: boolean;
  public isPrevDay: boolean;
  public bookingSelectionDates: any[] = [];
  public tempBookingObjArray: any;
  public room: any[] = [];
  public weekRangePagination: number = 7;
  public totalWeeks: number = 0;
  public weekCount: number = -1;
  public viewTotal: number = 0;
  public showWeeks: boolean = false;
  public totalRoomCatRoomList: any[];
  public minDate: any = void 0;
  public maxDate: any = void 0;
  public minEndDate: any;
  public dateDisabled: { date: Date, mode: string }[];
  public customerTypeList: any[];
  public stayTypeList: any[];
  public originalStayTypesList: any[];
  public customerTypeListMain: any[];
  public refereceUser: any[];
  public referenceUserCustomerTypeChange: any[];
  public isReferenceNeccessary: boolean;
  public isNoteNeccessary: boolean;
  public referenceUsersOriginal: any[];
  public referenceUsersMain: any[];
  public refereceUserSelected: any;
  public selectedReferenceUserCustomerTyepChange: any;
  public checkoutReferenceUserSelected: any;
  public agentList: any[];
  public mainAgentsList: any[];
  public originalAgentList: any[];
  public agentLocationList: any[];
  public agentSelected: any;
  public agentLocationSelected: any;
  public masterBookingObj: JSON;
  public is_reference: boolean = false;
  public isCheckoutReference: boolean = false;
  public is_comment_necessary: boolean = false;
  public is_agent: boolean = false;
  public is_agent_location: boolean = false;
  public isReset: boolean = false;
  public bookingTypeSelection: string = "0";
  public roomChargesDetails: any[] = [];
  public selectedCustomerType: any;
  public totalPayments: number = 0;
  public isRequiredReciept: boolean;
  public totalPaybles: number = 0;
  public perDayCharge: number = 0;  // per day room charge after discount
  public paidAdvanceAmount: number = 0;
  public perDayPayment: number = 0; // per day room charge for update payment
  public isAdvancePayment: boolean = false;
  public bankAdvanceAmount: number = 0; // total advance amount paid in card, bank or cheque mode
  public bankTotalPayable: number = 0; // total amount which can be paid by bank,card and cheque mode which is equal to total booking amount
  public totalDays: number = 0;
  public advancePayments: any[] = [];
  public DateRangesWithBookingArray: any[] = [];
  public tabs: any[] = [];
  public wasCheckIn: any[] = [];
  public wasChechkout: any[] = [];
  public refunds: any;
  public fundAmount: any;
  public fndType: Array<Select2OptionData> = [];
  public disableFund: boolean = true;
  public selectedFundValue: any;
  public selectCustomerValue: any;
  public selectStayTypeValue: any;
  public allGuestObject: any;
  public is_splitbooking: boolean = false;
  public splitAvailableRoom: any[] = [];
  public alottedRooms: any[] = [];
  public splitBookingOBJ: any = {};
  public splitbookingAllSelectedDates: any[] = [];
  public splitbookingFormValid: boolean = true;
  public PanCardFileuploaders: any[] = [];
  public AadharCardFileuploaders: any[] = [];
  public Fileuploaders: any[] = [];
  public isDateDisplayTypeWeek: boolean = true;
  public bookingEndDate: any;
  public bookingStartDate: any;
  public hasChangedDateRange: boolean = false;
  public isLastReservationTab: boolean = false;
  public customerList: any[] = [];
  public showCustomerList: boolean = false;
  public selectedCustormerFormIndex: number;
  public selectFormIndex: number;
  public showLoader: boolean = false;
  public categoryTypeLoading: boolean = false;
  public totalCustomerDiscount: any;
  public customerDiscountType: any;
  public showModal: boolean = false;
  public showMore: boolean = false;
  public overBookingList: any = [];
  public allOverBookings: any;
  public sameDay: boolean = false;
  public earlyCheckInTime: string;
  public isCommonRoomCategory: boolean;
  public isOverBooking: boolean;
  public isExtraBooking: boolean;
  public imageData: any;
  public countryList: any;
  public selectedCountry: any;
  public plusMinus: any = 0;
  public isLastGuestAddTab: any = 0;
  public addGuestParams: any;
  public minAddGuestDate: any;
  public maxAddGuestDate: any;
  public notesArray: any[];
  public invoiceDataForView: any;
  public printWindow: any;
  public refundAmount: any;
  public refundAmountCause: string;
  public bookingSaveOnly: boolean = false;
  public bookingSaveAndCheckin: boolean = false;
  public canShowCardSwipeAmount: boolean = false;
  public bookingCheckoutDetails: any;
  public bookingCheckoutDetailsType: string;
  public customerTypeChangeNote: string;
  public canDeleteRoomMainGuest: string;
  public activeCancellationPolicies: any[];
  public activeCancellationPoliciesOriginal: any[];
  private activeCancellationPoliciesAPI: any;
  public selectedActiveCancellationPolicies: any;
  public isReferenceNeccessaryCancel: boolean;
  public isNoteNeccessaryCancel: boolean;
  public referenceUserCancelBooking: any[];
  public selectedReferenceUserCancelBooking: any;
  public selectedBookingBreed: any;
  public bookinfBreedCancel: boolean;
  public modalAlertType: string;
  public apiValue: any;
  public spliteData: any = [];
  public amountAccept = false;
  public panNo: any[] = [];
  public pdfFlag = false;
  public tabData: any;
  public tabDataID: any;
  public dynamicCheckInData: any;
  public dynamicSplitBookingData: any;
  public dynamicCheckInOutDates: any;
  public dynamicCheckinSuccess: any;
  public dynamicCheckinResponseMessage: any;
  public dynamicTimerTime: any;
  //public checkedData: boolean;

  showData: boolean = false;
  showError: boolean = false;

  @ViewChild('smModal') public smModal: ModalDirective;
  @ViewChild('staticTabs') staticTabs: TabsetComponent;
  @ViewChild('invoiceModal') invoiceModal: ModalDirective;
  @ViewChild('imageModal') public imageModal: ModalDirective;
  @ViewChild('viewDocumentModal') public viewDocumentModal: ModalDirective;
  @ViewChild('docImageModal') public docImageModal: ElementRef;
  @ViewChild('staticModal') public staticModal: ModalDirective;
  @ViewChild('refundAlert') public refundAlert: ModalDirective;
  @ViewChild('addGuestModal') public addGuestModal: ModalDirective;
  @ViewChild('bookingBreedModal') public bookingBreedModal: ModalDirective;
  @ViewChild('checkoutInvoiceModal') public checkoutInvoiceModal: ModalDirective;
  @ViewChild('checkoutBypassModal') public checkoutBypassModal: ModalDirective;
  @ViewChild('changeCustomerTypeModal') public changeCustomerTypeModal: ModalDirective;
  @ViewChild('deleteGuestConfirmationTypeModal') public deleteGuestConfirmationTypeModal: ModalDirective;
  @ViewChild('roomMaintenanceModal') public roomMaintenanceModal: ModalDirective;
  @ViewChild('checkInDynamicModal') public checkInDynamicModal: ModalDirective;
  @ViewChild('splitbookingDynamicModal') public splitbookingDynamicModal: ModalDirective;
  @ViewChild('TransferDynamicModal') public TransferDynamicModal: ModalDirective;

  @ViewChild('checkoutFlowDynamicModal') public checkoutFlowDynamicModal: ModalDirective;
  @ViewChild('cleanRoomFlowDynamicModal') public cleanRoomFlowDynamicModal: ModalDirective;

  //form
  private guestForm: FormGroup;
  private bookingForm: FormGroup;
  private addGuestForm: FormGroup;
  private addEditNoteForm: FormGroup;
  private overBookingForm: FormGroup;
  private splitBookingForm: FormGroup;
  private cancelBookingForm: FormGroup;
  private paymentAtcheckout: FormGroup;
  private checkInProcessform: FormGroup;
  public customerTypeChangeForm: FormGroup;
  checkInData: any;
  splitBookingData: any;
  TransferRoomData: any
  ModuleError: any;
  public SelectedRoom: any = ''
  // services
  private sub: any;
  private checkIn: any;
  private saveNote: any;
  private saveProof: any;
  private getCharges: any;
  private addGuestAPI: any;
  private viewBooking: any;
  private getGuestInfo: any;
  private cancelBooking: any;
  private saveGuestInfo: any;
  private getBookingNote: any;
  private bookingDateGet: any;
  private saveBookingData: any;
  private payFinalPayments: any;
  private updatePaymentApi: any;
  private printBillService: any;
  private roomAvailibility: any;
  private checkCancellation: any;
  private getAddGuestParams: any;
  private singleOverBooking: any;
  private getOverBookingNote: any;
  private getCheckoutDetails: any;
  private deleteBookingGuest: any;
  private getBookingsByRangeAPI: any;
  private commonRoomBookingList: any;
  private deleteGuestSubcription: any;
  private customertypelistservice: any;
  private makeSplitBookingService: any;
  private deleteSingleOverBooking: any;
  private refundAlertSubscription: any;
  private guestRequiredFieldsCheck: any;
  private confirmSingleOverBooking: any;
  private deleteGuestOnHiddenSubscription: any;
  public AvailableTransferRoom: any
  public AvailablePermission: any
  public ShowDownloadReceptButton: any

  cardSelected = false;
  config: any;
  cardExtendNewDate: any

  public datepickerOpts = {

    autoclose: true,
    todayBtn: 'linked',
    todayHighlight: true,
    icon: 'fa fa-calendar',
    endDate: new Date(),
    format: 'dd/mm/yyyy',
    containerClass: 'theme-dark-blue',
    dateInputFormat: 'DD/MM/YYYY'
  }
  public datepickerOptsguest = {
    autoclose: true,
    todayBtn: 'linked',
    todayHighlight: true,
    icon: 'fa fa-calendar',
    endDate: (new Date()).setDate((new Date()).getDate() + 1),
    format: 'dd/mm/yyyy'
  }
  public datepickerOpts2 = {
    autoclose: true,
    todayBtn: 'linked',
    todayHighlight: true,
    icon: 'fa fa-calendar',
    format: 'dd/mm/yyyy'
  }
  public countryCustomOptions: Select2OptionData = {
    width: '100%',
  }
  public bookingTypeOptions: Select2OptionData = {
    width: '100%',
    minimumResultsForSearch: Infinity
  };
  public bookingTypeOptions2: Select2OptionData = {
    width: '100%',
    minimumResultsForSearch: Infinity
  };
  public cleaningTypeOption: Select2OptionData = {
    width: '30%',
    minimumResultsForSearch: Infinity
  };
  public bookingType: Array<Select2OptionData> = [
    {
      id: '0',
      text: 'Single Booking'
    },
    // {
    //   id: '1',
    //   text: 'Group Booking'
    // }
  ];
  public bookingBreeds: any[] = [
    { id: '0', text: 'Main Booking' },
    { id: '1', text: 'Extra Booking' },
  ];
  public selectedRoomCategory: string;
  public roomCategoryType: Array<Select2OptionData> = [];
  public sourceType: Array<Select2OptionData> = [
    {
      id: '0',
      text: 'On the spot'
    },
    {
      id: '1',
      text: 'Telephonic'
    }
  ]
  public paymentType: Array<Select2OptionData> = paymentType;
  public proofTypeList: Array<Select2OptionData> = [
    {
      id: '0',
      text: 'Driving Licence'
    },
    {
      id: '1',
      text: 'Aadhaar Card'
    },
    {
      id: '2',
      text: 'Pan Card'
    },
    {
      id: '3',
      text: 'Voter ID'
    },
    {
      id: '4',
      text: 'Passport'
    },
    {
      id: '5',
      text: 'Others'
    }
  ]
  public guestProofTypeList: Array<Select2OptionData> = [
    {
      id: '0',
      text: 'Driving Licence'
    },
    {
      id: '3',
      text: 'Voter ID'
    },
    {
      id: '4',
      text: 'Passport'
    },
    {
      id: '5',
      text: 'Others'
    }
  ]
  public searchTypeList: Array<Select2OptionData> = [
    {
      id: '0',
      text: 'Phone Number'
    },
    {
      id: '1',
      text: 'Email'
    },
    {
      id: '2',
      text: 'Aadhar Card'
    },
    {
      id: '3',
      text: 'Pan Card'
    },
  ]

  public maintenance_reason_id = '';
  public sourceSelection: string = '0';
  public paymentTypeSelection: string = '0';
  public allBookings: any[] = [];
  public originalAllBookings: any[] = [];
  public extraBookings: any[] = [];
  public maintenanceResaons: any[] = [];
  public cleanHoursOccupied: any[] = [{ "text": "1 Hr", "id": "1" }, { "text": "2 Hr", "id": "2" }, { "text": "3 Hr", "id": "3" }];
  public selectedCleanHr = '';
  public is_disable: boolean = false;
  public canShow: boolean = true;
  imageUrls: string[] = [];
  public image: any = {};
  public guestDocument: any;
  public printWindow2: any;
  public documentBase64: any;
  public $destroy = new Subject();
  private langChangeSub: Subscription;
  public extendEndDate: any;
  public extendStartDate: any;
  is_admin;
  display: any;
  tempConfirmData: any;
  checkoutData: { currentOperation: string; startDateAndTime: any; endDateAndTime: any; roomId: any; doorId: any; };
  cleanRoomData: { currentOperation: string; startDateAndTime: any; endDateAndTime: any; roomId: any; doorId: any; };
  showMntReason: boolean = false;

  selectedBooking: any;
  selectedPayment: any;
  paymentScreenShort: any;
  cancelReason: any;
  cancelErrorMessage: string;
  cancelButtenLoader: boolean = false;
  transferRoomId: number;
  customerTypeId: any;
  showReferenceUserField: boolean = true;
  successModalStyle: string = '';
  acceptBookingStyle: string = '';
  cancleBookingStyle: string = '';
  acceptErrorMessage: any;
  acceptButtonLoader: any;
  reference_user_name: string;
  roomsList: any;
  cancelId: any;
  resendPaymentLink: boolean = false;
  acceptBookingData: any;
  customerTypeLists: any = [
    {
        id: 1,
        customer_name: 'Satsangi'
    },
    {
        id: 3,
        customer_name: 'Normal'
    }
]
  //List of rooms where card validation is compulsory.
  allowCardValidationForRoomList: any = ["AC delux", "Ac", "Non A/C", "Non A/C Singalbed"];
  allowCardValidFlag: boolean = true;
  imageUrl: any;
  bsConfig: Partial<BsDatepickerConfig>;

  constructor(
    public translate: TranslateService,// New Change ****
    private router: Router,
    private _fb: FormBuilder,
    private BS: BookingService,
    private services: PaymentRequestService,
    private renderer: Renderer2,
    private authGuard: AuthGuard, private reservation_S: ReservationServices,
    private DS: DharamshalaService,
    private sanitizer: DomSanitizer,
    config: AppConfig,// New Change ****
    private TS: TranslateEventService, // New Change ****
    private http: HttpClient

  ) {
    // console.log("this.authGuard.isAdmin() : ", this.authGuard.isAdmin());
    this.config = config.getConfig();// New Change ****
    let currentLang = localStorage.getItem('currentLang'); // New Change ****
    translate.setDefaultLang(currentLang);// New Change ****
    this.minDate = new Date();
    (this.minDate).setDate((this.minDate).getDate() - 1);
    // console.log("-----min------------", this.minDate)
    this.dateDisabled = [{ date: this.minDate, mode: 'day' }];
    // get all rooms details and room bookings
    this.getAllRoomsDetails();
    this.is_admin = localStorage.getItem('admin_role')
    // New Change ****
    this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
      this.changeLang(res);
    });
    this.bsConfig = {
      containerClass: 'theme-default',
      showWeekNumbers: false,
      dateInputFormat: 'YYYY-MM-DD'
    };
  }

  paymentDetails() {
    this.ViewData = 'payment';
  }

  enableAddGuests(item: any):boolean {
    if (item === 'checkin') {
      return false;
    } else {
      return true;
    }
  }

  guestDetails() {
    this.ViewData = 'guest';
  }

  // viewGuestDetail(guestData) {
  //   this.guestDetailss = guestData;
  //   this.isPoliceInquiry = false;
  //   this.viewComp = true;
  //   this.viewGuestDetailsModal.show()
  // }

  // closeComp() {
  //   this.viewComp = false;
  //   this.isPoliceInquiry = false; 
  //   this.viewGuestDetailsModal.hide();
  // }

  getAllRoomsDetails() {
    this.sub = this.BS.getRoomInfo()
      .subscribe((res) => {
        if (res.status == "success") {
          this.totalRoomCatRoomList = res.data.roomDetails;
          this.allOverBookings = res.data.overBookingCountNew;
          this.maintenanceResaons = jQuery.map(res.data.maintenanceResaons, function (obj) {
            return { id: obj.id, text: obj.title.toUpperCase() };
          });
          this.earlyCheckInTime = res.data.early_checkin_time.early_check_in_time;
          let keys = Object.keys(this.allOverBookings);
          for (let i = 0; i < keys.length; i++) {
            this.allOverBookings[keys[i]].forEach(booking => {
              booking.start += '.000Z';
            });
          }
          res.data.roomDetails.forEach(element => {
            // code for roomCategory array push goes here...
            this.roomCategoryType.push({ id: (<number>element.room_category_id).toString(), text: element.room_cat_name });
            element.rooms.forEach(ele => {
              this.room.push(ele)
            });
          });
          if (this.roomCategoryType.length > 0) {
            this.selectedRoomCategory = this.roomCategoryType[0].id;
          }
          // if (res.data.bookingData) {
          //   this.allBookings = res.data.bookingData;
          //   this.originalAllBookings = res.data.bookingData;
          // }
          setTimeout(() => {
            this.fillData();
          })
        };
      });
  }
  changeLang(lang: string) {
    // New Change ****
    this.translate.use(lang);
  }
  getDateFormat(date) {
    if (!date) return null;
    return new Date(date);
  }
  selectRow(event) {
    this.unSelectRow();
    // this.renderer.setElementClass(event.target.parentElement, 'is-selected', true);
    this.renderer.addClass(event.target.parentElement, 'is-selected');
  }
  hasOverBooking(room_category_id: number, item: any) {
    let matchDate = item.fulldate + 'T00:00:00.000Z';
    if ((room_category_id in this.allOverBookings) && this.allOverBookings[room_category_id]) {
      return this.findIndex(matchDate, "start", this.allOverBookings[room_category_id]) - 1 >= 0;
    }
    return false;
  }
  getOverBookingCount(room_category_id: number, item: any) {
    let matchDate = item.fulldate + 'T00:00:00.000Z';
    if ((room_category_id in this.allOverBookings) && this.allOverBookings[room_category_id]) {
      if (this.findIndex(matchDate, "start", this.allOverBookings[room_category_id]) - 1 >= 0) {
        return this.allOverBookings[room_category_id][this.findIndex(matchDate, "start", this.allOverBookings[room_category_id]) - 1].count;
      }
    }
    return 0;
  }
  openOverBookingNote(details: any) {
    this.overBookingForm = undefined;
    let cat_id = details[1];
    let note_date = details[0].fulldate + 'T00:00:00.000Z';
    this.getOverBookingNote = this.BS.getOverBookingNote(cat_id, note_date)
      .subscribe((res) => {
        if (res.status == "success") {
          details['details'] = details;
          details['cat_id'] = cat_id;
          details['note_date'] = note_date;
          details['action_type'] = 'over_booking';
          if (res.data.length > 0) {
            this.overBookingList = res.data;
          }
          else {
            this.initOverBookingForm(cat_id, note_date);
            this.overBookingList = [];
          }
          this.addNewTab("Over-Booking Notes", details);
          console.log('571',details);
        }
      });
  }
  closeNewOverBooking() {
    this.overBookingForm = undefined;
  }
  addNewOverBooking(cat_id: number, note_date: any) {
    this.initOverBookingForm(cat_id, note_date);
  }
  comfirmOverBooking(overBooking: any) {
    this.confirmSingleOverBooking = this.BS.confirmSingleOverBooking(overBooking)
      .subscribe(res => {
        if (res.status == 'success') {
          let index2 = 0;
          this.staticTabs.tabs.forEach((ele, index) => {
            if (ele.active == true) {
              index2 = index;
            }
          })
          this.removeTabHandler(this.staticTabs.tabs[index2])
          this.staticTabs.tabs[0].active = true;
          this.fillData();
        }
      })
  }
  deleteOverBooking(overBooking: any) {
    this.deleteSingleOverBooking = this.BS.deleteSingleOverBooking(overBooking)
      .subscribe(res => {
        if (res.status == 'success') {
          // console.log("Delete overbooking response : ", res.data);
          this.overBookingList.splice([this.findIndex(res.data.booking_id, "booking_id", this.overBookingList) - 1], 1);
          let index = this.findIndex(res.data.overBookingdata.start, "start", this.allOverBookings[res.data.overBookingdata.room_category_id]) - 1;
          if (this.allOverBookings[res.data.overBookingdata.room_category_id][index].count == 1) {
            this.allOverBookings[res.data.overBookingdata.room_category_id].splice([index], 1);
            let index2 = 0;
            this.staticTabs.tabs.forEach((ele, index) => {
              if (ele.active == true) {
                index2 = index;
              }
            })
            this.removeTabHandler(this.staticTabs.tabs[index2])
            this.staticTabs.tabs[0].active = true;
          } else {
            let count = this.allOverBookings[res.data.overBookingdata.room_category_id][index].count;
            for (let i = 0; i < res.data.bookingRooms.length; i++) {
              if (count == 1) {
                this.allOverBookings[res.data.overBookingdata.room_category_id].splice([index], 1);
                let index2 = 0;
                this.staticTabs.tabs.forEach((ele, index) => {
                  if (ele.active == true) {
                    index2 = index;
                  }
                })
                this.removeTabHandler(this.staticTabs.tabs[index2])
                this.staticTabs.tabs[0].active = true;
              } else {
                if (_.findIndex(this.allOverBookings[res.data.overBookingdata.room_category_id][index].room_ids, ['room_id', res.data.bookingRooms[i].room_id]) > -1) {
                  this.allOverBookings[res.data.overBookingdata.room_category_id][index].count -= 1;
                  count -= 1;
                }
              }
            };
          }
          if (res.data.returnAmount) {
            this.refundAmount = res.data.returnAmount;
            this.refundAmountCause = 'over-booking';
            this.showRefundAmountAlert();
          }
        }
      });

  }
  initOverBookingForm(cat_id: any, date: string, element?: any) {
    this.datepickerOpts2['startDate'] = new Date(date.toString());
    this.overBookingForm = this._fb.group({
      id: [element ? element.id : '0'],
      room_category_id: [cat_id],
      note_date: [date],
      name: [element ? element.name : '', Validators.required],
      contact: [element ? element.contact : '', Validators.required],
      email: [element ? element.email : ''],
      booking_from: [element ? new Date(element.booking_from.toString()) : new Date(date.toString()), Validators.required],
      booking_to: [element ? new Date(element.booking_to.toString()) : new Date().toString()],
      message: [element ? element.message : '']
    });
  }
  saveOverBooking() {
    this.overBookingForm.value.booking_from = moment(this.overBookingForm.value.booking_from).format('YYYY-MM-DD') + 'T00:00:00.000Z';
    this.overBookingForm.value.booking_to = moment(this.overBookingForm.value.booking_to).format('YYYY-MM-DD') + 'T00:00:00.000Z';
    let overBooking = this.overBookingForm.value;
    if (this.overBookingForm.valid) {
      this.singleOverBooking = this.BS.addEditSingleOverBooking(overBooking)
        .subscribe(res => {
          if (res.status == "success") {
            if (this.overBookingList && this.overBookingList.length > 0 && this.findIndex(res.data.id, "id", this.overBookingList) - 1 >= 0) {
              this.overBookingList[this.findIndex(res.data.id, "id", this.overBookingList) - 1] = res.data;
            }
            else {
              if (!this.allOverBookings[res.data.room_category_id]) {
                this.allOverBookings[res.data.room_category_id] = [];
              }
              let index = this.findIndex(res.data.note_date, "note_date", this.allOverBookings[res.data.room_category_id]) - 1;
              this.overBookingList.push(res.data);
              if (index >= 0) {
                this.allOverBookings[res.data.room_category_id][index].count += 1;
              }
              else {
                this.allOverBookings[res.data.room_category_id].push({
                  count: 1,
                  note_date: res.data.note_date,
                  room_category_id: res.data.room_category_id
                });
              }
            }
            this.overBookingForm = undefined;
          }
        })
    }
    else {
      let keys = Object.keys(this.overBookingForm.controls);
      for (let i = 0; i < keys.length; i++) {
        this.overBookingForm.controls[keys[i]].markAsDirty();
        this.overBookingForm.controls[keys[i]].markAsTouched();
      }
    }
  }
  isCurrentDay(item: any) {
    if (moment().format('YYYY-MM-DD') == item.fulldate) {
      return true;
    }
    return false;
  }
  isSameDayBooking() {
    let bookingDate = this.bookingForm.value.booking.dates[0].start;
    return moment(moment().format('YYYY-MM-DD')).diff(bookingDate, 'days') == 0;
  }

  truncateString(value: string, length: number) {
    if (value) {
      return value.substring(0, length);
    }
  }
  fillData() {
    if (this.DateRangesWithBookingArray && this.DateRangesWithBookingArray.length > 0) {
      this.showLoader = true;
    }
    this.getMonthDateRange(this.currYear, this.currMonth);
  }
  showMessage(msg) {
    // console.log(msg)
  }
  getCatName(id) {
    let name;
    for (var i = 0; i < this.room.length; i++) {
      let element = this.room[i];
      if (element.room_category_id == id) {
        // *** --- element.name changed to element.room_cat_name --- *** //
        name = element.room_cat_name;
        break;
      }
    }
    return name;
  }
  addNewTab(type, item): void {
    let start, end;
    // console.log("itemitemitemitemitem  : ",item);
    start = item instanceof Array ? item[0].start : item.start;
    end = item instanceof Array ? item[0].end : item.end;
    let date = start && end ? ` - ${start} / ${end}` : '';
    this.tabs.push({
      title: `${type}${date}`,
      content: item
    });
    // console.log("TABS : ", this.tabs);
    setTimeout(() => {
      this.staticTabs.tabs[this.tabs.length].active = true;
    })
  }
  removeTabHandler(tab: any): void {
    this.tabs.splice(this.tabs.indexOf(tab), 1);
  }
  removeTabByIndex(tabIndex: number) {
    this.tabs.splice(tabIndex, 1);
  }
  getDate: any

  ngOnInit() {


    this.customertypelistservice = this.BS.getCustomerTypes()
      .subscribe((res) => {
        if (res.status == "success") {
          if (res.data.customers.length) {
            this.customerTypeListMain = res.data.customers;
            this.customerTypeList = jQuery.map(res.data.customers, function (obj) {
              return { id: obj.id, text: obj.name.toUpperCase() };
            });
            this.customerTypeList.sort(function (x, y) {
              let a = x.text.toUpperCase(),
                b = y.text.toUpperCase();
              return a == b ? 0 : a > b ? 1 : -1;
            });

          }
          // un-comment code after code upload on server
          if (res.data.stayTypes && res.data.stayTypes.length) {
            this.originalStayTypesList = res.data.stayTypes;
            let defaultStayIndex = this.findIndex(true, "is_default", res.data.stayTypes) - 1;
            this.stayTypeList = jQuery.map(res.data.stayTypes, function (stayType) {
              return { id: stayType.id, text: stayType.name };
            });
            this.selectStayTypeValue = defaultStayIndex > -1 ? res.data.stayTypes[defaultStayIndex].id : this.stayTypeList[0].id;
          }
          let index = _.findIndex(res.data.customers, ['is_default', true]);
          // console.log("this.selectStayTypeValue : ", this.selectStayTypeValue);
          this.selectCustomerValue = index > -1 ? res.data.customers[index].id : this.customerTypeList[0].id;
          // console.log("Reference Response: ",res.data.referenceUsers);
          if (res.data.referenceUsers.length) {
            this.referenceUsersMain = res.data.referenceUsers;
            let refereceUser: any[];
            refereceUser = jQuery.map(res.data.referenceUsers, function (obj) {
              // console.log("object : ",obj);
              return { id: obj.id, text: obj.name.toUpperCase() };
            });
            this.refereceUser = refereceUser;
            this.referenceUsersOriginal = refereceUser;
            let tempRefereceUser = this.getFilteredReferences(this.selectCustomerValue);
            if (tempRefereceUser.length > 0) {
              this.refereceUser = tempRefereceUser;
            } else {
              this.refereceUser = refereceUser;
            }
            this.refereceUser.sort(function (x, y) {
              let a = x.text.toUpperCase(),
                b = y.text.toUpperCase();
              return a == b ? 0 : a > b ? 1 : -1;
            });

            // console.log("local reference user : ",refereceUser);
            // console.log("this.refereceUser : ",this.referenceUsersMain);
            // console.log("BOOOOOo",this.refereceUser[0].id);
            this.refereceUserSelected = this.refereceUser[0].id;
            this.checkoutReferenceUserSelected = this.refereceUser[0].id;
          }
          // console.log("referenceUsersMain : ", this.referenceUsersMain);
          // console.log("refereceUser : ", this.refereceUser);
          // console.log("referenceUsersOriginal : ", this.referenceUsersOriginal);
          if (res.data.agents.length) {
            this.mainAgentsList = res.data.agents;
            let agentList = jQuery.map(res.data.agents, function (obj) {
              return { id: obj.location, text: obj.name };
            });
            this.agentList = agentList;
            this.originalAgentList = agentList;
            this.agentSelected = this.mainAgentsList[this.findIndex(this.agentList[0].id, "location", this.mainAgentsList) - 1].id;
          }
          if (res.data.cities.length) {
            let locationTracker = [];
            let mainAgentsList = [];
            this.mainAgentsList.forEach(agent => {
              mainAgentsList.push(agent.location);
            });
            this.agentLocationList = jQuery.map(res.data.cities, (obj) => {
              _.findIndex
              if ((locationTracker.indexOf(obj.id) == -1) && (mainAgentsList.indexOf(obj.id) > -1)) {
                locationTracker.push(obj.id);
                return { id: parseInt(obj.id), text: obj.text };
              }
            });
          }
          this.agentLocationSelected = this.agentLocationList[0].id;
        }
      })
    this.initForm();
    this.initAddGuestForm();
    this.inItCustomerTypeChangeForm();
    this.checkInDynamicModal.onHide.subscribe((res) => {
      clearInterval(this.dynamicTimerTime);
      this.resetFlagForCheckIn();
    });

    this.splitbookingDynamicModal.onHide.subscribe((res) => {
      clearInterval(this.dynamicTimerTime);
      this.resetFlagForCheckIn();
    });

    this.TransferDynamicModal.onHide.subscribe((res) => {
      clearInterval(this.dynamicTimerTime);
      this.resetFlagForCheckIn();
    });

    this.checkoutFlowDynamicModal.onHide.subscribe((res) => {
      clearInterval(this.dynamicTimerTime);
      this.resetFlagForCheckIn();
    });

    this.cleanRoomFlowDynamicModal.onHide.subscribe((res) => {
      clearInterval(this.dynamicTimerTime);
      this.resetFlagForCheckIn();
    });
    // setTimeout(() => {
    //   this.changeCustomerTypeModal.show();
    // }, 3000);
  }
  /**
   * form builder
   *
   * @memberof ReservationsComponent
   */
  initForm() {
    this.bookingForm = this._fb.group({
      booking: this._fb.group({
        dates: this._fb.array([]),
        reservation_date: [''],
        booking_type: [''],
        customer_type: [''],
        customer_name: [''],
        reference_user: [''],
        stay_type: [''],
        agent_id: ['']
      }),
      guest: this._fb.array([]),
      items: this._fb.array([]),
      payments: this._fb.group({
        source: [this.sourceSelection, [Validators.required]],
        payment_mode: [this.paymentTypeSelection, [Validators.required]],
        // payment_reciept_number: ['', Validators.required],
        payment_verification_id: [null],
        payment_amount: [0, [CustomValidators.digits]],
        payment_date: [''],

      })
    });
    if (this.selectCustomerValue) {
      if (this.customerTypeListMain[this.findIndex(this.selectCustomerValue, "id", this.customerTypeListMain) - 1].is_reference_necessary) {
        this.bookingForm.controls['booking'].get('reference_user').setValidators(Validators.required);
      }
    }
  }
  /**
 * add guest to guest object to form
 *
 * @param {any} [element] if element then init data of element or just init blank guest
 * @memberof ReservationsComponent
 */
  initBookingGuest(element?, index?) {
    // console.log("Executing Initialization ");
    let guest = <FormArray>this.bookingForm.controls['guest'];
    guest.push(this._fb.group({
      id: [''],
      name: ['', [Validators.required]],
      contact: ['', [Validators.required]],
      email: ['', [CustomValidators.email]],
      search_type: ['0'],
      aadharcard_number: ['', [Validators.pattern('^[2-9]{1}[0-9]{3}\[0-9]{4}\[0-9]{4}$')]],
      pancard_number: ['', [this.validPanField]],
      guestFiles: [''],
      room_title: (element) ? element.room_title : '',
      isBillingGuest: [(index == 0 ? true : false), Validators.required]
    }));
  }
  getPreviousDateFormat(date) {
    return moment(moment(date).subtract(1, 'day')).format('YYYY-MM-DD');
  }
  getNextDateFormat(date) {
    return moment(moment(date).add(1, 'day')).format('YYYY-MM-DD');
  }
  /**
   * add dates to dates object to form
   *
   * @param {any} [element] if element then init data of element or just init blank date
   * @memberof ReservationsComponent
   */
  initBookingDates(element?, index?) {
    // console.log("InitBooking Datesssssssssss");
    let dates = <FormArray>this.bookingForm.controls['booking'].get('dates');
    let group = this._fb.group({
      start: [(element.start) ? moment(element.start).format('YYYY-MM-DD') + 'T00:00:00.000Z' : ''],
      end: [(element.end) ? this.getNextDateFormat(element.end) + 'T00:00:00.000Z' : ''],
      check_in: [
        // (element.start ?
        // moment(moment().format('YYYY-MM-DD')).diff(moment(element.start), 'days') == 0 ?
        //   moment() : moment(element.end).format('YYYY-MM-DD') + 'T02:30:00.000Z' : '')
        null],
      check_out: [
        // (element.end ? moment(element.end).format('YYYY-MM-DD') + 'T02:30:00.000Z' : '')
        null],
      room_id: (element) ? element.room_id : '',
      room_category_id: (element) ? element.room_category_id : '',
      total_days: (element) ? element.total_days : '',
      room_title: (element) ? element.room_title : '',
      adult: ['', [Validators.required, CustomValidators.digits, CustomValidators.gt(0)]],
      child: ['0', [Validators.required, CustomValidators.digits]]
    });
    if (index === 0) {
      // console.log("group-------------", group.controls['end'].value)
      group.get('check_in').setValidators(Validators.required);
      group.get('check_out').setValidators(Validators.required);
      group.controls['check_in'].patchValue(group.controls['start'].value);
      group.controls['check_out'].patchValue(group.controls['end'].value);
    }
    dates.push(group);

    // console.log("Date pushed : ", dates.value);
  }
  formatCheckoutDate(checkout, bookingDetails?: any) {
    if (checkout) {
      if (bookingDetails) {
        // console.log("checkout.........")
        let index = this.findIndex(bookingDetails.booking.stay_type_id, "id", this.originalStayTypesList) - 1;
        if (index > -1) {
          let duration = this.originalStayTypesList[index].duration;
          if (bookingDetails.bookingRoom.check_in) {
            let checkIn = moment(parseInt(bookingDetails.bookingRoom.check_in));
            if ((checkIn.hours() + duration) < 24) {
              return checkout;
            }
          } else if (duration < 24) {
            return checkout;
          }
        }
      }
      return moment(checkout).add(1, 'days');
    }
    return '';
  }
  changeDateDisplayType(event: any) {
    // console.log("Date Type selection : ",this.isDateDisplayTypeWeek);
    this.showLoader = event;
    setTimeout(() => {
      this.isDateDisplayTypeWeek = !this.isDateDisplayTypeWeek;
      this.getMonthDateRange(this.currYear, this.currMonth);
      this.removeSelection();
    }, 100);
  }
  bookingTypeChanged(event) {
    // console.log("Booking Type Selection : ",event.value);
    this.bookingTypeSelection = event.value;
    this.bookingForm.controls['booking'].get('booking_type').patchValue(event.value);
    this.removeSelection();
  }
  categoryFilterChanged = (event) => {
    this.allowCardValidFlag = true; //false; // Make it default to false when require. When it is true, it will always applicable for all the rooms - with card validation
    let selectedRoomObj = this.roomCategoryType.filter(item => item.id == event?.id);
    if (selectedRoomObj && selectedRoomObj.length > 0) {
      if (this.allowCardValidationForRoomList.indexOf(selectedRoomObj[0].text) > -1) {
        this.allowCardValidFlag = true;
      }
    }

    this.selectedRoomCategory = event?.id;
    this.showLoader = true;
    this.categoryTypeLoading = true;
    this.removeSelection();
    // this.weekCount = -1;
    setTimeout(() => {
      this.getMonthDateRange(this.currYear, this.currMonth);
    }, 0);
  }
  stayTypeChanged(event) {
    console.log("stayTypeChanged");

    this.bookingForm.controls['booking'].get('stay_type').patchValue(parseInt(event.id));
    if (event.id) {
      let stayType = parseInt(event.id);
      if (stayType) {
        stayType = this.originalStayTypesList[this.findIndex(stayType, "id", this.originalStayTypesList) - 1].duration;
        let dates = <FormArray>this.bookingForm.controls['booking'].get('dates');
        let start = <FormControl>dates.controls[0].get('start');
        if (stayType < 24) {
          dates.controls[0].get('end').patchValue(start.value);
          this.limitDateRangeToSingleDay();
          this.bookingDateChanged(start.value, 'end');
        } else if (stayType >= 24) {
          dates.controls[0].get('end').patchValue(new Date(this.getNextDateFormat(start.value)));
          this.enableMultipleDayBooking();
          this.bookingDateChanged(new Date(this.getNextDateFormat(start.value)), 'end');
        }
      }
    }
  }
  customerTypeChanged(event) {
    // Defensive: event should have id and text
    if (!event || typeof event.id === 'undefined' || typeof event.text === 'undefined') {
      return;
    }
    let index = this.findIndex(parseInt(event.id), "id", this.customerTypeList) - 1;
    let booking = <FormGroup>this.bookingForm.controls['booking'];
    let dates = <FormArray>booking.controls['dates'];
    let currentIndex = <FormGroup>dates.controls[0];
    booking.get('customer_type').patchValue(event.id);
    if (this.customerTypeList && index >= 0 && this.customerTypeList[index]) {
      booking.get('customer_name').patchValue(this.customerTypeList[index].text);
    } else {
      booking.get('customer_name').patchValue('');
    }
    if (event.id) {
      let mainIndex = this.findIndex(parseInt(event.id), "id", this.customerTypeListMain) - 1;
      if (this.customerTypeListMain && mainIndex >= 0 && this.customerTypeListMain[mainIndex]) {
        let mainType = this.customerTypeListMain[mainIndex];
        if (mainType.is_reference_necessary) {
          booking.get('reference_user').setValidators(Validators.required);
          booking.get('reference_user').updateValueAndValidity();
        } else {
          booking.get('reference_user').clearValidators();
          booking.get('reference_user').updateValueAndValidity();
        }
        if (mainType.is_comment_necessary) {
          let data = mainType;
          if (!currentIndex.get('note')) {
            currentIndex.addControl('note', new FormControl(''));
          }
          if (data.id != 3) {
            currentIndex.get('note').setValidators(Validators.required);
            currentIndex.get('note').updateValueAndValidity();
          } else {
            currentIndex.get('note').clearValidators();
            currentIndex.get('note').updateValueAndValidity();
          }
          this.is_comment_necessary = true;
        } else {
          this.is_comment_necessary = false;
          if (currentIndex.get('note')) {
            currentIndex.removeControl('note');
          }
        }
      }
    }
    this.refereceUser = this.getFilteredReferences(event.id);
    if (this.refereceUser && this.refereceUser.length > 0) {
      this.refereceUser.sort(function (x, y) {
        let a = x.text ? x.text.toUpperCase() : '',
          b = y.text ? y.text.toUpperCase() : '';
        return a == b ? 0 : a > b ? 1 : -1;
      });
    }
    // console.log("Booking Form : ", this.bookingForm);
  }
  getFilteredReferences(selectedValue: any) {
    let referencesAllowed = [];
    this.referenceUsersMain.forEach(reference => {
      if (reference.customer_type_ids) {
        // console.log("Customer check here", reference);
        reference.customer_type_ids.forEach((customerIds: any[]) => {
          if ((customerIds.indexOf(selectedValue) >= 0) && (referencesAllowed.indexOf(reference.id) < 0)) {
            referencesAllowed.push(reference.id);
          }
        });
      }
    });
    let refereceUser = [];
    referencesAllowed.forEach(id => {
      refereceUser.push(this.referenceUsersOriginal[this.findIndex(id, "id", this.referenceUsersOriginal) - 1]);
    });
    return refereceUser;
  }
  referenceTypeChanged(event) {
    this.refereceUserSelected = event.id;
    this.setReferenceUser(event.id);
  }
  checkoutReferenceTypeChanged(event: any) {
    this.checkoutReferenceUserSelected = event.value;
    this.setCheckoutReferenceUser(event.value);
  }
  AgentTypeChanged(event) {
    // console.log("Triggered agentTypeChanged function ...", event, this.mainAgentsList);
    let agentId = this.mainAgentsList[this.findIndex(parseInt(event.value), "location", this.mainAgentsList) - 1].id;
    this.agentSelected = agentId;
    this.bookingForm.controls['booking'].get('agent_id').patchValue(agentId);
  }
  agentLocationChanged(event) {
    this.agentLocationSelected = event.value;
    // console.log("Agent location list : ",this.agentLocationList);
    // console.log('Agent details  : ', this.agentLocationSelected, this.agentList);
    if (this.is_agent_location) {
      this.agentList = this.originalAgentList;
      this.agentList = this.agentList.filter(agent => {
        return (parseInt(agent.id) === parseInt(this.agentLocationSelected));
      });
      // this.agentSelected = this.agentList[0].id;
      if (this.agentList.length && this.agentList.length > 0) {
        this.agentSelected = this.mainAgentsList[this.findIndex(this.agentList[0].id, "location", this.mainAgentsList) - 1].id;
      }
      else {
        this.agentSelected = '';
      }
    }
    // this.bookingForm.controls['booking'].get('agent_id').patchValue(event.value);
  }
  hasAgent(index) {
    // console.log("Has triggered hasAgent function....", index);
    this.is_agent = !this.is_agent;
    let booking = <FormGroup>this.bookingForm.controls['booking'];
    let dates = <FormArray>booking.controls['dates'];
    let currentIndex = <FormGroup>dates.controls[index];
    if (this.is_agent) {
      currentIndex.addControl('agent_receipt_no', new FormControl('', Validators.required));
      this.bookingForm.controls['booking'].get('agent_id').patchValue(this.agentSelected);
    } else {
      currentIndex.removeControl('agent_receipt_no');
      this.bookingForm.controls['booking'].get('agent_id').patchValue('');
    }
  }
  hasAgentLocation(index) {
    this.agentList = this.originalAgentList;
    let booking = <FormGroup>this.bookingForm.controls['booking'];
    let dates = <FormArray>booking.controls['dates'];
    let currentIndex = <FormGroup>dates.controls[index];
    this.is_agent_location = !this.is_agent_location;
    this.agentList = this.agentList.filter(agent => {
      return (parseInt(agent.id) === parseInt(this.agentLocationSelected));
    });
    // this.agentSelected = (this.agentList.length && this.agentList.length > 0) ? this.agentList[0].id : '';
    if (this.agentList.length && this.agentList.length > 0) {
      this.agentSelected = this.mainAgentsList[this.findIndex(this.agentList[0].id, "location", this.mainAgentsList) - 1].id;
    }
    else {
      this.agentSelected = '';
    }
    if (this.is_agent_location) {
      currentIndex.addControl('agent_receipt_no', new FormControl('', Validators.required));
      this.bookingForm.controls['booking'].get('agent_id').patchValue(this.agentSelected);
    } else {
      currentIndex.removeControl('agent_receipt_no');
      this.bookingForm.controls['booking'].get('agent_id').patchValue('');
    }
    this.is_agent = !this.is_agent;
  }
  /**
   * get month range that is first day of month and last day of given month
   *
   * @param {any} year
   * @param {any} month
   * @memberof ReservationsComponent
   */
  getMonthDateRange(year, month): void {
    // this.showLoader = true;
    // console.log("HAAAAAAAAAAAAAAAAAAAAAAAAh");
    let startDate = moment([year, month]);
    // console.log("Start Date : ",startDate);
    let endDate = moment(startDate).endOf('month');
    // console.log("Start Date : ", endDate);
    // set month start and end date
    this.currMonthStartDate = startDate.toDate();
    // console.log("Start Date : ", this.currMonthStartDate);
    this.currMonthEndDate = endDate.toDate();
    // console.log("Start Date : ", this.currMonthEndDate);
    this.createDateArray();
  }
  /**
   * create date array range for timeline
   *
   * @memberof ReservationsComponent
   */
  createDateArray(): void {
    this.ranges = [];
    for (let i = 1; i <= this.currMonthEndDate.getDate(); i++) {
      let day = moment([this.currYear, this.currMonth, i]);
      let orignalDate = day.format('YYYY-MM-DD');
      let todayDateMoment = this.currMoment
      //adding previous month dates
      if (i == 1) {
        let weekFirst = moment([this.currYear, this.currMonth, i]).startOf('week');
        for (let j = 1; j <= 7; j++) {
          let orignalDate = weekFirst.format('YYYY-MM-DD');
          if (weekFirst.get('month') < this.currMonth || (weekFirst.get('month') - this.currMonth) == 11) {
            this.addToRanges(orignalDate, weekFirst, todayDateMoment)
          }
          weekFirst = weekFirst.add(1, 'days');
        }
      }
      // adding current month dates
      this.addToRanges(orignalDate, day, todayDateMoment);
      // adding next month dates
      if (i == this.currMonthEndDate.getDate()) {
        let weekFirst = moment([this.currYear, this.currMonth, i]).startOf('week');
        for (let j = 1; j <= 7; j++) {
          let orignalDate = weekFirst.format('YYYY-MM-DD');
          if (weekFirst.get('month') > this.currMonth) {
            this.addToRanges(orignalDate, weekFirst, todayDateMoment)
          }
          weekFirst = weekFirst.add(1, 'days');
        }
      }
    }
    this.originalRanges = JSON.parse(JSON.stringify(this.ranges));
    let monthStart = this.findIndex(1, "date", this.ranges);
    let spliceAt;
    if (this.currDayCursorActive) {
      spliceAt = this.currDateDateCursor;
      // console.log("currDayCursorActive");
    }
    else {
      spliceAt = this.weekCount * 7;
      // console.log("Not Activeeeeeee");
      // this.currDateDateCursor = spliceAt;
    }
    this.totalWeeks = this.ranges.length / 7;
    let monthLastWeekStart = moment().endOf('month');
    monthLastWeekStart = moment(monthLastWeekStart).startOf('week').get('date');
    // console.log("Last Week of month : ",monthLastWeekStart);
    if (this.weekCount == -1) {
      let currentWeek = moment().startOf('week');
      let currWeekCounter = this.findIndex(currentWeek.get('date'), "date", this.ranges) / 7;
      this.weekCount = parseInt(currWeekCounter.toString().split(".")[0]);
      if (monthLastWeekStart == currentWeek.get('date')) {
        // console.log("If condition : ", monthLastWeekStart, currentWeek.get('date'), this.findIndex(currentWeek.get('date'), "date", this.ranges, moment().month() + 1) - 1);
        spliceAt = this.findIndex(currentWeek.get('date'), "date", this.ranges, moment().month() + 1) - 1;
      }
      else {
        // console.log("Else condition : ", this.currDateDate, moment().endOf('month'), monthLastWeekStart, moment().month() + 1, currentWeek.get('date'), this.findIndex(currentWeek.get('date'), "date", this.ranges, moment().month() + 1) - 1);
        spliceAt = this.findIndex(this.currDateDateCursor, "date", this.ranges, moment().month() + 1) - 1;
      }
      this.currDateDateCursor = spliceAt;
      // console.log("currentWeek : ",currentWeek.get('date'));
      // console.log("currWeekCounter : ",currWeekCounter);
      // console.log("this.weekCount : ", this.weekCount);
      // console.log("spliceAt", this.findIndex(this.currDateDateCursor, "date", this.ranges, monthStart) - 1, this.ranges);
      // console.log("Initializedddddddddddddddddd", this.currDateDate - 1, this.findIndex(this.currDateDate - 1, "date", this.ranges), spliceAt);
    }
    if (this.isPrevMonth) {
      this.weekCount = Math.floor(this.ranges.length / 7 - 1);
      // console.log("BHOOOOOOOOOOO", Math.floor(this.ranges.length / 7 - 1));
      this.currDateDateCursor = (Math.floor(this.ranges.length / 7 - 1) * 7);
      spliceAt = this.currDateDateCursor;
      this.isPrevMonth = false;
    }
    // console.log("Pagination range : ", this.weekRangePagination);
    // console.log("Last occurrence of this.weekCount : ", this.weekCount);
    // console.log("this.totalWeeks : ", this.totalWeeks);
    this.weekRange = this.isDateDisplayTypeWeek ? this.ranges.splice((spliceAt), this.weekRangePagination) : this.ranges;
    // console.log("this.ranges : ", this.ranges);
    // console.log("this.originalRanges : ", this.originalRanges);
    // console.log("Splice at no. : ", spliceAt);
    // console.log("this.currDateDateCursor : ",this.currDateDateCursor);
    // console.log("this.weekRange : ", this.weekRange);
    this.getCurrentDisplayBookings().then((res) => {
      this.buildDateRangesWithBooking();
      setTimeout(() => {
        this.initSelectables();
        // console.log("loading set to false")
      }, 0);
      this.currDayCursorActive = false;
      // console.log("Current Day Cursor Made inActive",this.currDayCursorActive);
      // console.log("CUrrent Cursor Position : ",this.currDateDateCursor);
    })
  }

  getCurrentDisplayBookings() {
    let range = {
      'fromDate': this.weekRange[0].fulldate,
      'toDate': this.weekRange[this.weekRange.length - 1].fulldate,
      'room_category': this.selectedRoomCategory
    };
    return new Promise((resolve, reject) => {
      this.getBookingsByRangeAPI = this.BS.getBookingsByRange(range)
        .subscribe((res) => {
          if (res.status === 'success') {
            // console.log("Fetched Data-----------", res.data.bookings)
            this.allBookings = res.data.bookings;
            this.allBookings.map((bk) => {
              // bk['main_guest'] = bk.name;
              // let indx = this.allBookings.findIndex(x => x.guest_id == bk.guest_no);
              // if (indx > -1) {
              //   bk['main_guest'] = this.allBookings[indx].name;
              // } else {
                bk['main_guest'] = bk.name;
              // }
              return bk;
            })
            this.originalAllBookings = res.data.bookings;
            this.extraBookings = res.data.extraBookings;
            this.allBookings = [...this.allBookings, ...res.data.underMaintenanceBookings];
            this.originalAllBookings = [...this.originalAllBookings, ...res.data.underMaintenanceBookings];
            this.allOverBookings = res.data.overBookingCountNew;
            let keys = Object.keys(this.allOverBookings);
            for (let i = 0; i < keys.length; i++) {
              this.allOverBookings[keys[i]].forEach(booking => {
                booking.start += '.000Z';
              });
            }
            resolve(true);
            // console.log("AllBookings-----------", this.allBookings)
          }
        });
    })
  }
  /**
   * create date object for timeline
   *
   * @param {any} orignalDate looping date in (YYYY-MM-DD)
   * @param {any} day looping date object from moment
   * @param {any} todayDateMoment today's date.
   * @memberof ReservationsComponent
   */
  addToRanges(orignalDate, day, todayDateMoment) {
    // console.log("AddToRanges Counter : ");
    let dateObj = {};
    dateObj['fulldate'] = orignalDate;
    dateObj['daynumber'] = day.get('day');
    dateObj['timestamp'] = new Date(orignalDate).getTime();
    dateObj['day'] = this.dayNames[day.get('day')];
    dateObj['date'] = day.get('date');
    dateObj['monthnumber'] = day.get('month') + 1;
    dateObj['month'] = this.monthNames[day.get('month')];
    dateObj['year'] = day.get('year');
    dateObj['is_booked'] = false;
    dateObj['is_starting'] = false;
    dateObj['is_ending'] = false;
    dateObj['is_outdated'] = false;
    dateObj['is_blocked'] = false;
    dateObj['is_disabled'] = day.isBefore(todayDateMoment, 'day');
    // console.log("Pushed object : ",dateObj);
    this.ranges.push(dateObj)
  }
  /**
   * build array of dates and rooms with flag true as dates are booked and other important informations.
   *
   * @memberof ReservationsComponent
   */
  buildDateRangesWithBooking() {
    this.DateRangesWithBookingArray = [];
    this.allBookings = _.filter(this.originalAllBookings, booking => {
      return ((moment(moment(booking.start).format('YYYY-MM-DD')).isSameOrAfter(this.weekRange[0].fulldate) ||
        moment(moment(booking.end_date).format('YYYY-MM-DD')).isSameOrBefore(this.weekRange[this.weekRange.length - 1].fulldate)) ||
        (moment(moment(booking.start).format('YYYY-MM-DD')).isSameOrBefore(this.weekRange[0].fulldate) &&
          moment(moment(booking.end_date).format('YYYY-MM-DD')).isSameOrAfter(this.weekRange[this.weekRange.length - 1].fulldate)));
    });
    this.totalRoomCatRoomList.forEach((room) => {
      if (this.selectedRoomCategory == (<number>room.room_category_id).toString()) {
        let row = {};
        row['category_id'] = room.room_category_id;
        row['room'] = [];
        room.rooms.forEach((val, index) => {
          let innerrow: any = {};
          innerrow['dates'] = [];
          this.weekRange.forEach((element) => {
            // make copy
            let status = jQuery.extend({}, element);
            // --- Added new condition for common room to maintain multiple booking_id --- //
            if (val.is_common_room_category) {
              status['booking_id'] = [];
            }
            status['guestCount'] = 0;
            status['isCommonRoom'] = val.is_common_room_category ? true : false;
            // console.log("INDEX : ",_.findIndex(this.allBookings, ['room_id', val.id]));
            if (_.findIndex(this.allBookings, ['room_id', val.id]) > -1) {
              this.allBookings.forEach((dat) => {
                // let currDate = moment(element.fulldate, 'YYYY-MM-DD');
                // let eleStartDate = moment(dat.start, 'YYYY-MM-DD');
                // let eleEndDate = moment(dat.end_date, 'YYYY-MM-DD');
                let range = moment.range(moment(dat.start, 'YYYY-MM-DD'), moment(dat.end_date, 'YYYY-MM-DD'));
                if (val.id === dat.room_id) {
                  if (range.contains(moment(element.fulldate, 'YYYY-MM-DD')) && val.id === dat.room_id) {
                    let today = new Date();
                    let dateObj = moment(today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate(), 'YYYY-MM-DD');
                    status['is_booked'] = true;
                    // status['booking_id'] = dat.booking_id;
                    status['booking_status'] = dat.current_status;
                    status['customer_name'] = dat.customer_name;
                    status['customer_id'] = dat.customer_id;
                    status['booking_note'] = dat.booking_note;
                    status['room_id'] = val.id;
                    status['door_id'] = val.door_id;
                    status['all_guest_details_added'] = dat.all_guest_details_added;
                    status['username'] = dat.name;
                    status['main_person'] = dat.main_guest;
                    status['stay_type'] = dat.stay_type_id;
                    status['duration'] = dat.duration ? dat.duration : 24;
                    status['expected_check_in'] = dat.expected_check_in;
                    status['exp_check_in'] = dat.expected_check_in;
                    status['exp_check_out'] = dat.expected_check_out;
                    status['start'] = moment(dat.start).format('DD MMM');
                    status['end'] = moment(dat.end_date).format('DD MMM');
                    status['is_admin'] = this.is_admin
                    if (dat.current_status === 'maintenance') {
                      status['main_person'] = dat.maintenance_reason;
                    }
                    // code for inserting extra-bookings on existing booking *PENDING*


                    // --- moment(dat.start, 'YYYY-MM-DD').isBefore(dateObj) && !moment(dat.start, 'YYYY-MM-DD').isSame(dateObj) ---  condition has been change --- //
                    if (moment(element.fulldate, 'YYYY-MM-DD').isBefore(dateObj) && !moment(element.fulldate, 'YYYY-MM-DD').isSame(dateObj)) {
                      status['is_outdated'] = true;
                    }
                    if (val.is_common_room_category && dat.is_common_room_category) {
                      status['guestCount'] += (dat.adult + dat.child);
                      // --- Changes done to status['booking_id'] for common room case --- //
                      status['booking_id'].push(dat.booking_id);
                    }
                    // --- Added new condition for common room to maintain multiple booking_id --- //
                    else if (!val.is_common_room_category) {
                      status['booking_id'] = dat.booking_id;
                      // console.log("CHanged to string", status['booking_id']);
                    }
                  }
                  if (moment(element.fulldate, 'YYYY-MM-DD').isSame(moment(dat.start, 'YYYY-MM-DD')) || dat.is_common_room_category) {
                    status['is_starting'] = true;
                    // status
                  }
                  if (moment(element.fulldate, 'YYYY-MM-DD').isSame(moment(dat.end_date, 'YYYY-MM-DD')) || dat.is_common_room_category) {
                    status['is_ending'] = true;
                  }
                  if (range.contains(moment(element.fulldate, 'YYYY-MM-DD')) && val.id === dat.room_id) {
                    this.insertExtraBookings(status);
                  }
                }
              })
            }
            innerrow['room_id'] = val.id;
            innerrow['room_category_id'] = val.room_category_id;
            innerrow['room_title'] = val.title;
            innerrow['dates'].push(status);

          })
          row['room'].push(innerrow);
        })
        this.DateRangesWithBookingArray.push(row);
        // console.log("this.DateRangesWithBookingArray : ", this.DateRangesWithBookingArray);
      }
    })
  }
  insertExtraBookings(cellData: any) {
    let index = _.findIndex(this.extraBookings, { 'start': (cellData.fulldate + 'T00:00:00.000Z'), 'room_id': cellData.room_id });
    if (index > -1) {
      let data = JSON.parse(JSON.stringify(cellData));
      data = jQuery.extend(data, this.extraBookings[index]);
      cellData['extraBooking'] = data;
      cellData['extraBooking'].booking_status = cellData['extraBooking'].current_status;
    }
  }
  getStayTypeIcon(id: number) {
    let index = this.findIndex(id, "id", this.originalStayTypesList) - 1;
    if (index > -1 && (this.originalStayTypesList && this.originalStayTypesList.length > 0)) {
      return this.originalStayTypesList[index].stay_type_icon;
    }
    return null;
  }
  getBookingCustomerType(item: any) {
    if (item.booking_status === 'maintenance') return 'Room under maintenance';
    let index = this.findIndex(item.stay_type, "id", this.originalStayTypesList) - 1;
    if (index > -1) {
      return item.customer_name + `, ${this.originalStayTypesList[index].name}`;
    }
    return item.customer_name;
  }
  /**
   * Find array index based on parameters passed
   * @param {any} searchTerm search parameter value
   * @param {any} property search parameter
   * @param {any} array array to be searched
   * @returns returns index if found or returns -1
   * @memberof ReservationsComponent
   */
  findIndex(searchTerm, property, array, monthnumber?: number) {
    if (monthnumber) {
      for (var i = 0, len = array.length; i < len; i++) {
        if ((array[i][property] === searchTerm) && (array[i]["monthnumber"] === monthnumber)) return (i + 1);
      }
      return -1;
    }
    else {
      for (var i = 0, len = array.length; i < len; i++) {
        if (array[i][property] === searchTerm) return (i + 1);
      }
      return -1;
    }
  }
  /**
   * initialize jquery selecatbles and its events
   * @memberof ReservationsComponent
   * @method selected : will create date objets and push to a array for processing, this method will executes on every cell selected
   * @method stop : will merge date ranges, this will excutes when selection process stops.
   */
  initSelectables() {
    // console.log("HEYYYYYYYYYYY BUDDDDDYYYYYYY 1111111111",moment().format());
    let OBJ = {};
    OBJ['booking'] = {};
    OBJ['booking']['dates'] = [];
    // this.showLoader = false;
    jQuery('.__date_range.selection_area').selectable({
      filter: ".__booking-selectables",
      selected: (event, ui) => {
        let checkBox = jQuery(ui.selected).find('.checkbox_date');
        // console.log("1 : this.bookingSelectionDates : ", checkBox);
        if (this.bookingSelectionDates.length > 0) {
          let canAdd = true
          this.bookingSelectionDates.forEach(element => {
            if (element.timestamp == checkBox.data('timestamp')) {
              //when same cell selected
              canAdd = false;
            }
          });
          if (canAdd) {
            let dateObj = {};
            dateObj['room_category_id'] = checkBox.data('categoryid');
            dateObj['timestamp'] = checkBox.data('timestamp');
            dateObj['roomid'] = checkBox.data('roomid');
            dateObj['fulldate'] = checkBox.data('fulldate');
            dateObj['is_disabled'] = checkBox.data('isdisabled');
            dateObj['isCommonRoom'] = checkBox.data('iscommonroom');
            this.bookingSelectionDates.push(dateObj);
            checkBox.prop('checked', true);
          }
        } else {
          // if there no elements that is its first element
          let dateObj = {};
          dateObj['room_category_id'] = checkBox.data('categoryid');
          dateObj['timestamp'] = checkBox.data('timestamp');
          dateObj['roomid'] = checkBox.data('roomid');
          dateObj['fulldate'] = checkBox.data('fulldate');
          dateObj['is_disabled'] = checkBox.data('isdisabled');
          dateObj['isCommonRoom'] = checkBox.data('iscommonroom');
          this.bookingSelectionDates.push(dateObj);
          checkBox.prop('checked', true);
        }
      },
      stop: (event, ui) => {
        // console.log("HEYYYYYYYYYYY BUDDDDDYYYYYYY 222222");
        let obj = {};
        // --- Edited by len condition includes 2 checks now, which initially was only for "this.is_splitbooking" --- //
        if (this.is_splitbooking && this.tempBookingObjArray) {
          // console.log("Splitbooking was true");
          // console.log("this.tempBookingObjArray : ", this.tempBookingObjArray);
          OBJ = this.tempBookingObjArray;
        }
        if (this.isReset) {
          OBJ['booking'] = {};
          OBJ['booking']['dates'] = [];
          this.isReset = false;
        }
        // --- Grouping Rooms With Same roomid --- //
        let bookingSelectionDates = _.groupBy(this.bookingSelectionDates, 'roomid');
        // --- Extracting Keys For Individual Groups Generated Above --- //
        let rKeys = _.keys(bookingSelectionDates);
        // --- Iterating Over Every Generated Group Using their Corresponding Keys --- //
        let isCommonRoomAllowed: boolean = true;
        rKeys.forEach(elementEle => {
          this.hasChangedDateRange = false;
          this.bookingSelectionDates = bookingSelectionDates[elementEle];
          // console.log("Heyyyyyyyyyyyyyyy : ", this.bookingSelectionDates);
          // console.log("this.bookingSelectionDates : ", this.bookingSelectionDates);
          //--- Check if dates are selected and atleast 1 Customer-Type is available --- //
          if (this.bookingSelectionDates.length > 0 && this.customerTypeList && this.customerTypeList.length > 0) {
            // selected dates are more then 1
            let canAdd = true;
            OBJ['booking']['reservation_date'] = moment(new Date(), 'YYYY-MM-DD');
            OBJ['booking']['booking_type'] = this.bookingTypeSelection;
            OBJ['booking']['customer_type'] = this.selectCustomerValue;
            OBJ['booking']['customer_name'] = this.customerTypeList[this.findIndex(parseInt(this.selectCustomerValue), "id", this.customerTypeList) - 1].text;
            OBJ['booking']['reference_user'] = '';
            OBJ['booking']['stay_type'] = this.selectStayTypeValue; // Confirm this
            obj['start'] = this.bookingSelectionDates[0].fulldate;
            obj['end'] = this.bookingSelectionDates[this.bookingSelectionDates.length - 1].fulldate;
            obj['room_id'] = this.bookingSelectionDates[0].roomid;
            // obj['door_id'] = this.room.filter(item => item.id == this.bookingSelectionDates[0].roomid)[0]?.door_id;
            obj['room_title'] = this.room[this.findIndex(this.bookingSelectionDates[0].roomid, "id", this.room) - 1].title;
            obj['room_category_id'] = this.bookingSelectionDates[0].room_category_id;
            obj['elements'] = [];
            obj['total_days'] = this.bookingSelectionDates.length;
            this.bookingSelectionDates.forEach((ele) => {
              if ((this.bookingTypeSelection == '0' && obj['room_id'] == ele.roomid) || this.bookingTypeSelection == '1') {
                // console.log("Element is accepted for room ID : ", obj['room_id']);
                if (ele.isCommonRoom && this.bookingTypeSelection == '1') {
                  // console.log("YESSSSSSSSSSSSSSSSS");
                  this.uncheck(ele.timestamp);
                  isCommonRoomAllowed = false;
                  canAdd = false;
                }
                else {
                  obj['elements'].push(ele.timestamp);
                }
              }
              else {
                this.uncheck(ele.timestamp);
              }
            });
            if (this.tempBookingObjArray && this.tempBookingObjArray.booking && this.tempBookingObjArray.booking.dates) {
              this.tempBookingObjArray.booking.dates.forEach((eleMain) => {
                for (var i = 0; i < eleMain.elements.length; i++) {
                  var element = eleMain.elements[i];
                  if (obj['elements'].indexOf(element) > -1) {
                    canAdd = false;
                    // unchecking overlaping elements
                    obj['elements'].forEach(ele => {
                      // uncheck all element except already added element
                      if (ele != element && eleMain['elements'].indexOf(ele) == -1) {
                        this.uncheck(ele);
                      }
                    });
                    break;
                  }
                }
              });
            }

            if (canAdd) {
              // console.log("can add first stage");
              let canRealyAdd = true;

              if (this.tempBookingObjArray && this.tempBookingObjArray.booking && this.tempBookingObjArray.booking.dates) {
                // console.log("this.tempBookingObjArray : ", this.tempBookingObjArray);

                // single booking **************************************************

                if (this.bookingTypeSelection === '0') {
                  // console.log("Booking type is  : ", 'Single Booking');
                  for (var i = 0; i < this.tempBookingObjArray.booking.dates.length; i++) {
                    // chek if room is in same category and same id then merge selection
                    if (this.tempBookingObjArray.booking.dates[i].room_id == obj['room_id']) {
                      let currStart = this.tempBookingObjArray.booking.dates[i].start;
                      let currEnd = this.tempBookingObjArray.booking.dates[i].end;

                      // if in begining merge in begining
                      if (moment(obj['end']).diff(moment(currStart), 'days') == -1) {
                        this.tempBookingObjArray.booking.dates[i].start = obj['start'];
                        // update elements
                        obj['elements'].forEach(element => {
                          this.tempBookingObjArray.booking.dates[i].elements.push(element)
                        });
                        // update days
                        this.tempBookingObjArray.booking.dates[i].total_days += obj['elements'].length;

                        canRealyAdd = false
                      } else if (moment(obj['start']).diff(moment(currEnd), 'days') == 1) {
                        //merge in ending
                        this.tempBookingObjArray.booking.dates[i].end = obj['end'];
                        // update elements
                        obj['elements'].forEach(element => {
                          this.tempBookingObjArray.booking.dates[i].elements.push(element)
                        });
                        // update days
                        this.tempBookingObjArray.booking.dates[i].total_days += obj['elements'].length;

                        canRealyAdd = false;
                      } else {
                        // no match found and uncheck checkbox to prevent selection
                        canRealyAdd = false;
                        obj['elements'].forEach(ele => {
                          // uncheck all element except already added element
                          this.uncheck(ele);
                        });
                      }
                      //end of if (same room selection)
                    }
                    //****************** --- Added by len -- Please Confirm Before Pushing Code --- *******************//
                    else {
                      canRealyAdd = false;
                      obj['elements'].forEach(ele => {
                        // uncheck all elements with different room_id
                        this.uncheck(ele);
                      });
                    }
                  }//end for loop
                } else {
                  // console.log("Booking type is  : ", 'Group Booking');
                  // Group booking **************************************************

                  for (var i = 0; i < this.tempBookingObjArray.booking.dates.length; i++) {
                    let start = moment(obj['start'], 'YYYY-MM-DD');
                    let end = moment(obj['end'], 'YYYY-MM-DD');
                    let dateArray: any[] = this.enumerateDaysBetweenDates(start, end);
                    let length = this.tempBookingObjArray.booking.dates[0].elements.length;
                    let selectionDateStart = moment(this.tempBookingObjArray.booking.dates[0].start, 'YYYY-MM-DD');
                    let selectionDateEnd = moment(this.tempBookingObjArray.booking.dates[0].end, 'YYYY-MM-DD');
                    let range = moment().range(selectionDateStart, selectionDateEnd);
                    let diff1 = start.diff(selectionDateStart, 'days');
                    let diff2 = end.diff(selectionDateEnd, 'days');
                    // --- Takes a step to unchecking if rooms are different --- //
                    if (this.tempBookingObjArray.booking.dates[i].room_id != obj['room_id']) {
                      if (((diff1 > 0 && diff1 < length) || (diff2 < 0 && diff2 > -length)) && !this.hasChangedDateRange) {
                        // console.log("unchecked room at index : ", i, obj['room_id']);
                        obj['elements'].forEach(ele => {
                          this.uncheck(ele);
                          canRealyAdd = false;
                        });
                        this.showNotification("Start and End Dates Don't Match for all Rooms!", 'error', true, 5);
                        break;
                      }
                    }
                    // --- Checks for same room and also prevents single selection if multiple rooms were already added to tempBookingObjArray --- //
                    if (this.tempBookingObjArray.booking.dates[i].room_id == obj['room_id'] && this.tempBookingObjArray.booking.dates.length == rKeys.length) {
                      let currStart = this.tempBookingObjArray.booking.dates[i].start;
                      let currEnd = this.tempBookingObjArray.booking.dates[i].end;

                      // If in begining merge in begining
                      if (moment(obj['end']).diff(moment(currStart), 'days') == -1) {
                        // console.log("Merging dates immidiately before existing range : ", obj['end']);
                        this.tempBookingObjArray.booking.dates[i].start = obj['start'];
                        // Update elements
                        obj['elements'].forEach(element => {
                          this.tempBookingObjArray.booking.dates[i].elements.push(element)
                        });
                        // update days
                        this.tempBookingObjArray.booking.dates[i].total_days += obj['elements'].length;
                        // --- Notifies if existing date range has changed by storing it in a global field --- //
                        this.hasChangedDateRange = (this.tempBookingObjArray.booking.dates.length > 1) ? true : false;
                        // console.log("changed this.hasChangedDateRange with same room : ", this.hasChangedDateRange);
                        canRealyAdd = false;
                      } else if (moment(obj['start']).diff(moment(currEnd), 'days') == 1) {
                        // console.log("Merging dates immidiately after existing range : ", obj['end']);
                        //merge in ending
                        this.tempBookingObjArray.booking.dates[i].end = obj['end'];
                        // update elements
                        obj['elements'].forEach(element => {
                          this.tempBookingObjArray.booking.dates[i].elements.push(element)
                        });
                        // update days
                        this.tempBookingObjArray.booking.dates[i].total_days += obj['elements'].length;
                        // --- Notifies if existing date range has changed by storing it in a global field --- //
                        this.hasChangedDateRange = (this.tempBookingObjArray.booking.dates.length > 1) ? true : false;
                        canRealyAdd = false;
                      } else {
                        // console.log("Unchecking unwanted dates : ", obj['end']);
                        // no match found and uncheck checkbox to prevent selection
                        canRealyAdd = false;
                        obj['elements'].forEach(ele => {
                          // uncheck all element except already added element
                          this.uncheck(ele);
                        });
                      }
                      canRealyAdd = false;
                      //end of if (same room selection)
                    } else {
                      // Group booking different room id or cetegory id
                      let tempElements = [];
                      dateArray.forEach((element, index) => {
                        if (!range.contains(moment(element))) {
                          this.uncheck(obj['elements'][index]);
                          tempElements.push(obj['elements'][index]);
                        }
                      })
                      let diff1State = false;
                      let diff2State = false;
                      if (diff1 <= 0) {
                        // --- stores data regarding the expansion of existing Date Range --- //
                        diff1State = true;
                        obj['start'] = this.tempBookingObjArray.booking.dates[0].start;
                      }
                      if (diff2 >= 0) {
                        // --- stores data regarding the expansion of existing Date Range --- //
                        diff2State = true;
                        obj['end'] = this.tempBookingObjArray.booking.dates[0].end;
                      }
                      // --- Notifies if existing date range has changed by storing it in a global field --- //
                      // console.log("Different room Condition 1 : ", (this.tempBookingObjArray.booking.dates.length > 1));
                      // console.log("Different room Condition 2 : ", (diff1State === true || diff2State === true));
                      this.hasChangedDateRange = (this.tempBookingObjArray.booking.dates.length > 1) && (diff1State === true || diff2State === true) ? true : false;
                      // console.log("this.hasChangedDateRange : ", this.hasChangedDateRange);
                      tempElements.forEach((element) => {
                        obj['elements'].splice(obj['elements'].indexOf(element), 1)
                      })
                      obj['total_days'] = obj['total_days'] - tempElements.length;

                      if (dateArray.length == tempElements.length) {
                        canRealyAdd = false;
                        this.showNotification("Start and End Dates Don't Match for all Rooms!", "error", true, 5);
                        if (this.tempBookingObjArray.booking.dates.length != rKeys.length) {
                          break;
                        }
                      }
                      tempElements = [];
                      // no match found and uncheck checkbox to prevent selection
                    }
                  }
                  // group booking *****************************************************
                }
              }
              // push if the date range is different then available objects
              if (canRealyAdd) {
                OBJ['booking']['dates'].push(obj);
                this.tempBookingObjArray = OBJ;
                // console.log("this.tempBookingObjArrayyyyyyyyyyyyyyyyyyyyyy : ", this.tempBookingObjArray);
              }
            }
          }
          else {
            this.bookingSelectionDates.forEach(ele => {
              this.uncheck(ele.timestamp);
            });
            this.showNotification("Please add a Customer-Type first!", "error", true, 5);
          }
          this.bookingSelectionDates = [];
          obj = {};
        });
        if (!isCommonRoomAllowed) {
          this.showNotification("Group Booking is not allowed in Common Room", "error");
        }
      }

    });
    this.showLoader = false;
    this.categoryTypeLoading = false;
  }

  enumerateDaysBetweenDates = function (startDate, endDate) {
    let now = startDate.clone(), dates = [];

    while (now.isBefore(endDate) || now.isSame(endDate)) {
      dates.push(now.format('YYYY-MM-DD'));
      now.add(1, 'days');
    }
    return dates;
  };
  uncheck = (ele) => {
    jQuery("[data-timestamp=" + ele + "]").prop('checked', false);
  }
  initializeCalendar() {
    this.currYear = this.currMoment.get('year');
    this.currMonth = this.currMoment.get('month');
    this.weekCount = -1;
    this.currDateDateCursor = this.currDateDate == 1 ? this.currDateDate : this.currDateDate - 1;
  }
  /**
   * Navigates on the calendar to the current day
   * @memberof ReservationComponent
   */
  navigateToCurrentDay(event?: boolean) {
    if (this.currDateDateCursor != this.findIndex(this.currDateDate - 1, "date", this.originalRanges) - 1) {
      this.showLoader = event ? event : this.showLoader;
      setTimeout(() => {
        this.initializeCalendar();
        this.getMonthDateRange(this.currYear, this.currMonth);
        this.removeSelection();
      }, 100);
    }
    else {
      this.sameDay = true;
      setTimeout(() => {
        this.sameDay = false;
      }, 500);
    }
  }
  /**
   * Pagination events next, prev month; next, prev year; next, prev week
   * @memberof ReservationsComponent
  / */
  nextMonth = (event?: boolean) => {
    this.showLoader = event ? event : this.showLoader;
    setTimeout(() => {
      this.weekCount = 0;
      this.currDateDateCursor = 1;
      this.currMonth++;
      if (this.currMonth >= 12) {
        this.currMonth = 0;
        this.nextYear();
      }
      else {
        this.getMonthDateRange(this.currYear, this.currMonth);
      }
      this.removeSelection();
    }, 100);
  }
  prevMonth = (event?: boolean) => {
    this.showLoader = event ? event : this.showLoader;
    setTimeout(() => {
      this.isPrevMonth = true;
      this.currMonth--;
      if (this.currMonth <= -1) {
        this.currMonth = 11;
        this.prevYear();
      }
      else {
        this.getMonthDateRange(this.currYear, this.currMonth);
      }
      this.removeSelection();
    }, 100);
  }
  nextYear = (event?: boolean) => {
    this.showLoader = event ? event : this.showLoader;
    setTimeout(() => {
      this.weekCount = 0;
      this.currDateDateCursor = 1;
      this.currYear++;
      this.getMonthDateRange(this.currYear, this.currMonth);
      this.removeSelection();
    }, 100);
  }
  prevYear = (event?: boolean) => {
    this.showLoader = event ? event : this.showLoader;
    setTimeout(() => {
      this.weekCount = 0;
      // this.currDateDateCursor = 0;
      this.currYear--;
      this.getMonthDateRange(this.currYear, this.currMonth);
      this.removeSelection();
    }, 100);
  }
  nextWeek = (event?: boolean) => {
    this.showLoader = event ? event : this.showLoader;
    setTimeout(() => {
      this.weekCount++;
      // console.log("INcreamented Week");
      this.currDateDateCursor = (this.weekCount * 7);
      if (this.weekCount > (this.totalWeeks - 1)) {
        this.weekCount = 0;
        this.nextMonth();
      }
      else {
        this.getMonthDateRange(this.currYear, this.currMonth);
      }
      this.removeSelection();
    }, 100);
  }
  prevWeek = (event?: boolean) => {
    this.showLoader = event ? event : this.showLoader;
    setTimeout(() => {
      this.weekCount--;
      this.currDateDateCursor = this.isPrevDay && !event ? (this.weekCount * 7) + 6 : (this.weekCount * 7);
      if (this.weekCount < 0) {
        this.weekCount = (this.totalWeeks - 1);
        this.prevMonth();
      }
      else {
        this.getMonthDateRange(this.currYear, this.currMonth);
      }
      this.removeSelection();
    }, 100);
  }

  nextDay = (event?: boolean) => {
    this.showLoader = event ? event : this.showLoader;
    setTimeout(() => {
      this.currDateDateCursor++;
      // console.log("******************Current DayCursor : ",this.currDateDateCursor);
      // console.log("Week Count : ", this.weekCount);
      // console.log("Last Week : ", Math.floor(this.totalWeeks) - 1);
      this.currDayCursorActive = true;
      // console.log("Current Cursor : ",this.currDateDateCursor);
      // console.log("Condition 1 : ",(this.currDateDateCursor >= (this.weekCount * 7) + 7));
      // console.log("Condition 2 : ",(this.weekCount == Math.floor(this.totalWeeks) - 1));
      if ((this.currDateDateCursor >= (this.weekCount * 7) + 7) || (this.weekCount == Math.floor(this.totalWeeks) - 1)) {
        this.nextWeek();
        // console.log("Yes it increamented the week");
      }
      else {
        this.getMonthDateRange(this.currYear, this.currMonth);
      }
      this.removeSelection();
    }, 100);
  }

  prevDay = (event?: boolean) => {
    this.showLoader = event ? event : this.showLoader;
    setTimeout(() => {
      this.currDateDateCursor--;
      this.isPrevDay = true;
      this.currDayCursorActive = true;
      if (this.currDateDateCursor < (this.weekCount * 7)) {
        this.prevWeek();
      }
      else {
        this.getMonthDateRange(this.currYear, this.currMonth);
      }
      this.removeSelection();
    }, 100);
  }

  /**
    * ------------------------------------------------------------------------------
    * Context menu events
    * @memberof ReservationsComponent
    * ------------------------------------------------------------------------------
    */

  // check if somethig is selected or not
  checkDateSelectes = () => {
    return (this.tempBookingObjArray);
  }
  canBlockRooms = () => {
    return (this.tempBookingObjArray && this.authGuard.isAdmin());
  }
  /**
   * Remove selected dates.
   * @memberof ReservationsComponent
   */
  removeSelection = (item?) => {
    this.hasChangedDateRange = false;
    if (this.tempBookingObjArray) {
      this.tempBookingObjArray.booking.dates.forEach((ele) => {
        ele.elements.forEach(element => {
          jQuery("[data-timestamp=" + element + "]").prop('checked', false);
        });
      })

      this.tempBookingObjArray = undefined;
    }
    this.bookingSelectionDates = [];
    this.isReset = true;
  }

  blockRooms = (item: any) => {
    // console.log("itemmmmmmmm : ",item);
  }
  initializeStayTypes() {
    this.stayTypeList = jQuery.map(this.originalStayTypesList, function (stayType) {
      return { id: stayType.id, text: stayType.name };
    });
  }
  /**
   * open reservation modal and form
   * @memberof ReservationsComponent
   */
  openReservation = (item: any, isOverBooking?: boolean, isExtraBooking?: boolean, underMaintenance?: boolean) => {
    this.DS.getAssignedDharamshala().subscribe((dharamshalaRes) => {
      console.log(dharamshalaRes.status, "dharamshalaRes.status", dharamshalaRes.data);

      if (dharamshalaRes.status === 'success' && dharamshalaRes.data) {
        this.initializeStayTypes();
        // --- Flag set for identifying Common-Room --- //
        this.isCommonRoomCategory = item.isCommonRoom;
        this.isOverBooking = isOverBooking ? isOverBooking : false;
        if (isExtraBooking) {
          this.isExtraBooking = true;
          let today = moment();
          let currentHour = today.hours();
          let todayFullDate = moment(today.format('YYYY-MM-DD'));
          let dharamshalaCheckInHours = moment(dharamshalaRes.data.check_in).hours();
          let stayTypeList = _.filter(this.originalStayTypesList, (stayType) => {
            if (item.expected_check_in) {
              return (
                (
                  moment(item.fulldate).diff(todayFullDate, 'day') == 0 &&
                  (moment(item.expected_check_in).hours() - currentHour > 0) &&
                  stayType.duration <= Math.abs(moment(item.expected_check_in).hours() - currentHour)
                )
                ||
                (
                  (moment(item.fulldate).diff(todayFullDate, 'day') > 0) &&
                  (stayType.duration <= (moment(item.expected_check_in).hours() - dharamshalaCheckInHours))
                )
              );
            }
          });
          this.stayTypeList = jQuery.map(stayTypeList, function (stayType) {
            return { id: stayType.id, text: stayType.name };
          });
        } else {
          this.isExtraBooking = false;
        }
        if (this.stayTypeList.length === 0) {
          this.showNotification('Extra Booking is not available on current room!', 'error');
          this.removeSelection();
        } else {
          this.selectStayTypeValue = this.stayTypeList[0].id;
          console.log(this.tempBookingObjArray, "this.tempBookingObjArray");

          if (this.tempBookingObjArray) {
            if (this.tempBookingObjArray.booking.dates) {
              this.tempBookingObjArray.booking.dates.sort((a, b) => {
                return (a.room_category_id - b.room_category_id)
              })
            }
            for (var i = 0; i < this.tempBookingObjArray.booking.dates.length; i++) {
              if (i == 0) {
                this.bookingEndDate = new Date(this.tempBookingObjArray.booking.dates[i].end);
                this.bookingStartDate = new Date(this.tempBookingObjArray.booking.dates[i].start);
              }

              let element1 = this.tempBookingObjArray.booking.dates[i];
              let element2 = this.tempBookingObjArray.booking.dates[(i + 1 < this.tempBookingObjArray.booking.dates.length) ? i + 1 : i];

              let ele1 = moment(element1.start, 'YYYY-MM-DD');
              let ele2 = moment(element2.end, 'YYYY-MM-DD');
              if (ele2.diff(ele1, 'days') == -1 && element1.room_category_id == element2.room_category_id) {
                element1.start = element2.start
                //update elements
                element2.elements.forEach(element => {
                  element1.elements.push(element);
                });
                // update total days
                element1.total_days += element2.total_days;
                // delete second element
                this.tempBookingObjArray.booking.dates.splice((i + 1 < this.tempBookingObjArray.booking.dates.length) ? i + 1 : i, 1);
              }

              ele1 = moment(element1.end, 'YYYY-MM-DD');
              ele2 = moment(element2.start, 'YYYY-MM-DD');

              if (ele2.diff(ele1, 'days') == 1 && element1.room_category_id == element2.room_category_id) {
                element1.end = element2.end;

                //update elements
                element2.elements.forEach(element => {
                  element1.elements.push(element);
                });
                // update total days
                element1.total_days += element2.total_days
                // delete second element
                this.tempBookingObjArray.booking.dates.splice((i + 1 < this.tempBookingObjArray.booking.dates.length) ? i + 1 : i, 1);
              }
            } //  end of for loop
            this.initForm();
            let temp = this.tempBookingObjArray;
            temp.dates = [];
            this.bookingForm.patchValue(this.tempBookingObjArray);
            this.bookingForm.controls['booking'].get('stay_type').patchValue(this.selectStayTypeValue);
            this.tempBookingObjArray.booking.dates.forEach((element, index) => {
              this.initBookingDates(element, index);
              this.initBookingGuest(element, index);
            });
            // console.log("Form Init---------", this.bookingForm.value)
            if (this.customerTypeListMain[this.findIndex(this.selectCustomerValue, "id", this.customerTypeListMain) - 1].is_comment_necessary) {
              let booking = <FormGroup>this.bookingForm.controls['booking'];
              let dates = <FormArray>booking.controls['dates'];
              let currentIndex = <FormGroup>dates.controls[0];
              console.log(dates, "checkout date---------");

              let checkNote = this.customerTypeListMain[this.findIndex(this.selectCustomerValue, "id", this.customerTypeListMain) - 1]
              if (checkNote.id == 3) {
                currentIndex.addControl('note', new FormControl(''));
              } else {
                currentIndex.addControl('note', new FormControl('', Validators.required));
              }
              this.is_comment_necessary = true;
            }

            if (underMaintenance) {
              let bookingForm = this.modifyBookingForm();
              bookingForm['maintenance_reason_id'] = this.maintenance_reason_id;

              if (this.maintenance_reason_id != null && this.maintenance_reason_id != undefined) {
                let roomCleancardArr = this.maintenanceResaons.filter(item => item.text == 'ROOM CLEANING CARD');
                if (roomCleancardArr && roomCleancardArr.length > 0 && roomCleancardArr[0].id == this.maintenance_reason_id) {
                  this.roomMaintenanceModal.hide();
                  // Room clean popup start
                  this.dynamicCheckinSuccess = false;
                  let currDt = new Date();
                  let expiryDt = moment(currDt).add(this.selectedCleanHr, 'hours');

                  let getRoomIdByRoomTitle = this.room.filter(e => e.title == bookingForm.booking.dates[0].room_title)[0].id
                  let getDoorIdByRoomId = this.room.filter(e => e.title == bookingForm.booking.dates[0].room_title)[0].door_id;

                  if (getDoorIdByRoomId && getDoorIdByRoomId != '') {
                    this.cleanRoomData = {
                      "currentOperation": "CleanRoom",
                      "startDateAndTime": moment(currDt).format('YYMMDDHHmm'),
                      "endDateAndTime": expiryDt.format('YYMMDDHHmm'),
                      "roomId": getRoomIdByRoomTitle,
                      "doorId": getDoorIdByRoomId
                    };
                    this.cleanRoomFlowDynamicModalAction();
                    this.reservation_S.getCleanRoomPreProcess(this.cleanRoomData).subscribe((resdata) => {

                      //Success
                      if (resdata['status'] == true || resdata['status'] == 'true') {
                        this.showData = true;
                        this.showError = false;
                        this.dynamicCheckinSuccess = resdata.status;
                        this.dynamicCheckinResponseMessage = resdata.message;

                        this.BS.markRoomUnderMaintenance(bookingForm).subscribe((res) => {
                          if (res.status === 'success') {
                            let dates = <FormArray>this.bookingForm.controls['booking'].get('dates');
                            for (let i = 0; i < dates.length; i++) {
                              dates.controls[i].get('start').patchValue(undefined);
                              dates.controls[i].get('end').patchValue(undefined);
                            }
                            this.removeSelection();
                            this.fillData();
                            this.roomMaintenanceModal.hide();
                          }
                        })

                      } else if (resdata['status'] == false || resdata['status'] == 'false') {
                        this.dynamicCheckinSuccess = true;
                        this.dynamicCheckinResponseMessage = resdata.message;
                        this.showData = true;
                        this.showError = false;
                      }
                    }, (error) => {
                      this.showData = false;
                      this.showError = true;
                    });

                    // Room clean popup end

                  } else {
                    this.BS.markRoomUnderMaintenance(bookingForm).subscribe((res) => {
                      if (res.status === 'success') {
                        let dates = <FormArray>this.bookingForm.controls['booking'].get('dates');
                        for (let i = 0; i < dates.length; i++) {
                          dates.controls[i].get('start').patchValue(undefined);
                          dates.controls[i].get('end').patchValue(undefined);
                        }
                        this.removeSelection();
                        this.fillData();
                        this.roomMaintenanceModal.hide();
                      }
                    })
                  }

                } else {
                  this.BS.markRoomUnderMaintenance(bookingForm).subscribe((res) => {
                    if (res.status === 'success') {
                      let dates = <FormArray>this.bookingForm.controls['booking'].get('dates');
                      for (let i = 0; i < dates.length; i++) {
                        dates.controls[i].get('start').patchValue(undefined);
                        dates.controls[i].get('end').patchValue(undefined);
                      }
                      this.removeSelection();
                      this.fillData();
                      this.roomMaintenanceModal.hide();
                    }
                  })

                }
              }
            } else {
              //open form modal
              setTimeout(() => {
                this.staticModal.show();
                let dates = <FormGroup>this.bookingForm.controls['booking'].get('dates');
                let currentIndex = <FormGroup>dates.controls[0];
                currentIndex.get('start').patchValue(new Date(this.tempBookingObjArray.booking.dates[0].start));
                let duration = this.originalStayTypesList[this.findIndex(this.selectStayTypeValue, "id", this.originalStayTypesList) - 1].duration;
                if (duration < 24) {
                  this.limitDateRangeToSingleDay();
                  currentIndex.get('end').patchValue(new Date(this.tempBookingObjArray.booking.dates[0].end));
                } else {
                  currentIndex.get('end').patchValue(new Date(this.getNextDateFormat(this.tempBookingObjArray.booking.dates[0].end)));
                  this.minEndDate = new Date(this.getNextDateFormat(this.tempBookingObjArray.booking.dates[0].start));
                }
                // this.bookingForm.controls.booking['controls'].dates['controls'][0].controls['check_out'].patchValue(this.bookingForm.controls.booking['controls'].dates['controls'][0].controls['end'].value);
                // console.log("currentIndex-----", currentIndex)
                // currentIndex.get('check_out').patchValue(currentIndex.get('end').value)
              }, 0);
            }
          }
          else {
            this.showNotification('Please select a date for reservation!', 'error');
          }
        }
      } else {
        this.showNotification('No Dharamshala is assigned!', 'error');
        this.removeSelection();
      }
    }, (err) => {
      this.showNotification('No Dharamshala is assigned!', 'error');
      this.removeSelection();
    });
  }

  limitDateRangeToSingleDay() {
    if (this.tempBookingObjArray) {
      this.minEndDate = new Date(this.tempBookingObjArray.booking.dates[0].start);
      this.minDate = new Date(this.tempBookingObjArray.booking.dates[0].start);
      this.maxDate = new Date(this.tempBookingObjArray.booking.dates[0].start);
    }
  }
  enableMultipleDayBooking() {
    if (this.tempBookingObjArray) {
      this.minEndDate = new Date(this.getNextDateFormat(this.tempBookingObjArray.booking.dates[0].start));
      this.minDate = new Date(this.tempBookingObjArray.booking.dates[0].start);
      this.maxDate = void 0;
    }
  }

  modifyBookingForm() {
    let bookingForm = _.cloneDeep(this.bookingForm.value);
    bookingForm['is_common_room_category'] = this.isCommonRoomCategory;
    bookingForm['is_over_booking'] = this.isOverBooking;
    bookingForm.booking['start'] = moment(bookingForm.booking.dates[0].start).format('YYYY-MM-DD') + 'T00:00:00.000Z';
    bookingForm.booking['end'] = moment(this.getPreviousDateFormat(bookingForm.booking.dates[0].end)).format('YYYY-MM-DD') + 'T00:00:00.000Z';
    for (let i = 0; i < bookingForm.booking.dates.length; i++) {
      bookingForm.booking.dates[i] = this.omitKeys(bookingForm.booking.dates[i], 'start', 'end');
      bookingForm.booking.dates[i]['guest'] = bookingForm.guest[i];
    }
    if (bookingForm.payments['payment_mode'] == '1') {
      bookingForm.payments['payment_amount'] = this.totalPayments;
    }
    bookingForm = this.omitKeys(bookingForm, 'guest');
    return bookingForm;
  }
  reservationData;
  markAsUnderMaintenance() {
    this.openReservation(this.reservationData, false, false, true);
  }
  roomMaintenanceModel = (item) => {
    this.roomMaintenanceModal.show();

    this.reservationData = item;
    this.selectedCleanHr = this.cleanHoursOccupied[0].id;
  }

  verifyCard = (item) => {
    //popup =-> open
    this.getVerifyCard();
  }

  getVerifyCard() {
    //Backend API call
    let payload = {
      "currentOperation": "Verify",
      "startDateAndTime": null,
      "endDateAndTime": null,
      "roomId": null,
      "doorId": null,
    };
    this.showData = false;
    this.reservation_S.verifyProcess(payload).subscribe((data) => {

      //Success
      console.log('Verify card API Response: ' + JSON.stringify(data));

      if (data['status'] == true || data['status'] == 'true') {
        this.showData = true;
        this.showError = false;


      } else {
        this.showData = false;
        this.showError = true;
      }

    }, (error) => {
      this.showData = false;
      this.showError = true;
      console.log('ERROR:: ' + error);
    });
  }

  checkInDynamicModalAction = (item) => {

    console.log(item)
    console.table(this.guestForm)
    if (this.guestForm.invalid) {
      return;
    }
    this.getCheckInData(item);
    this.showData = false;
    this.showError = false;
    let getDoorIdByRoomId = this.room.filter(e => e.id == item.room_id)[0].door_id;
    if (getDoorIdByRoomId && getDoorIdByRoomId != '') {
      this.checkInDynamicModal.show();
      this.dynamicTimer(2);
    }
  }
  splitBookingDynamicModalAction = () => {
    this.showData = false;
    this.showError = false;
    this.splitbookingDynamicModal.show();
    this.dynamicTimer(2);
  }

  TransferRoomDynamicMOdelAction = () => {
    this.showData = false;
    this.showError = false;
    this.TransferDynamicModal.show();
    this.dynamicTimer(2);
  }



  checkoutFlowDynamicModalAction = () => {
    this.showData = false;
    this.showError = false;
    setTimeout(() => {
      this.checkoutFlowDynamicModal.show();
    }, 1000);
    this.dynamicTimer(2);
  }

  cleanRoomFlowDynamicModalAction = () => {
    this.showData = false;
    this.showError = false;
    setTimeout(() => {
      this.cleanRoomFlowDynamicModal.show();
    }, 1000);
    this.dynamicTimer(2);
  }

  getCheckInData(item) {
    const currentReservationsData = this.guestForm.controls['guests'];
    console.log(this.room, "this.roomthis.roomthis.room");

    let getDoorIdByRoomId = this.room.filter(e => e.id == item.room_id)[0].door_id;
    this.checkInData = {
      "currentOperation": "CheckIN",
      "startDateAndTime": moment(this.dynamicCheckInOutDates.expStartDate).format('YYMMDDHHmm'),
      "endDateAndTime": moment(this.dynamicCheckInOutDates.expEndDate).format('YYMMDDHHmm'),
      "roomId": item.room_id,
      "doorId": getDoorIdByRoomId, // || (door_id) need to change based on response
    };
    this.dynamicCheckInData = this.checkInData;
    this.showData = false;
  }

  changeRoomMain(event: any) {
    setTimeout(() => {
      if (event) {
        this.maintenance_reason_id = event.id;
        console.log(this.maintenance_reason_id);
        let roomCleancardArr = this.maintenanceResaons.filter(item => item.text == 'ROOM CLEANING CARD');
        console.log(roomCleancardArr);
        if (roomCleancardArr && roomCleancardArr.length > 0 && roomCleancardArr[0].id == this.maintenance_reason_id) {
          this.showMntReason = true;
        } else {
          this.showMntReason = false;
        }
      }
    })
  }
  changeCleanRoomHours(event) {
    console.log(event.value, ">>>>>>>>>>");

    this.selectedCleanHr = event.value;
  }
  markRoomAsActive(item) {
    this.BS.markRoomAsActive(item.booking_id).subscribe((res) => {
      if (res.status === 'success') {
        //do something
        this.fillData();
      }
    });
  }
  /**
   * Opens a list of bookings for a given common room
   *
   * @memberof ReservationComponent
   * @param { any } event booking params
   */
  openCommonRoomBookings(item: any) {
    // console.log("Common Room Booking : ",item);
    this.commonRoomBookingList = this.BS.getCRBookingList(item.room_id, item.fulldate)
      .subscribe(res => {
        if (res.status == "success") {
          item['booking_details'] = res.data;
          item.booking_details.forEach(booking => {
            booking['is_outdated'] = item.is_outdated;
            booking['is_disabled'] = item.is_disabled;
            booking['fulldate'] = item.fulldate;
            booking['booking_status'] = booking.current_status;
            booking['startFullDate'] = booking['start'];
            booking['endFullDate'] = booking['end'];
            booking['start'] = moment(booking['start']).format('DD MMM');
            booking['end'] = moment(booking['end']).format('DD MMM');
          });
          item['action_type'] = 'common_room';
          this.addNewTab("Booking", _.cloneDeep(item));
          console.log('2496',item);
          setTimeout(() => {
            window.document.addEventListener('click', event => {
              this.unSelectRow();
            });
          }, 0);
        }
      });
  }
  /**
   *Opens a modal pop-up if an extra-booking exist on the calendar cell
   * @param {any} item : Extra booking details
   */
  checkForExtraBooking(item: any) {
    this.bookinfBreedCancel = false;
    return new Promise((resolve, reject) => {
      if (item.hasOwnProperty('extraBooking')) {
        this.bookingBreedModal.onHidden.subscribe(() => {
          if (this.bookinfBreedCancel) {
            reject();
          } else {
            if (this.selectedBookingBreed == 1)
              resolve(item.extraBooking);
            else
              resolve(item);
          }
        });
        this.bookingBreedModal.show();
      } else { resolve(item); }
    });
  }
  bookingBreedChanged(event: any) {
    if (event) {
      this.selectedBookingBreed = event.value;
    }
  }
  unSelectRow() {
    if (this.commonRoomBody) {
      for (let i = 0; i < this.commonRoomBody.nativeElement.children.length; i++) {
        if (this.commonRoomBody.nativeElement.children[i].classList.value.indexOf('is-selected') >= 0) {
          // this.renderer.setElementClass(this.commonRoomBody.nativeElement.children[i], "is-selected", false);
          this.renderer.removeClass(this.commonRoomBody.nativeElement.children[i], "is-selected");
        }
      }
    }
  }
  added = (val: any, array: any[]): boolean => {
    array.push(val);
    return true
  }
  cleanreservation() {
    this.removeSelection();
  }
  cancelReservationProcess() {
    this.cleanreservation();
    this.staticModal.hide();
    window.location.reload();
  }
  /**
   * reference user checkbox check event to set value to form if there is any reference.
   * @memberof ReservationsComponent
   */
  hasReference() {
    this.is_reference = !this.is_reference;
    this.setReferenceUser();
  }
  hasCheckoutReference() {
    this.isCheckoutReference = !this.isCheckoutReference;
    this.setCheckoutReferenceUser();
  }
  setReferenceUser(selectedReference?: any) {
    if (this.is_reference) {
      this.bookingForm.controls['booking'].get('reference_user').patchValue(selectedReference ? selectedReference : this.refereceUserSelected);
    } else {
      this.bookingForm.controls['booking'].get('reference_user').patchValue('');
    }
  }
  setCheckoutReferenceUser(selectedCheckoutReference?: any) {
    if (this.isCheckoutReference) {
      // console.log("setting reference .... ");
      this.paymentAtcheckout.controls['reference_user'].patchValue(selectedCheckoutReference ? selectedCheckoutReference : this.checkoutReferenceUserSelected);
    } else {
      this.paymentAtcheckout.controls['reference_user'].patchValue('');
    }
  }
  getAddGuestRoomChargesandExtracharges(index: number) {
    if (this.addGuestParams) {
      let adult = <FormControl>this.addGuestForm.controls['adult'];
      let child = <FormControl>this.addGuestForm.controls['child'];
      let extra = this.addGuestParams.std_occupancy - this.addGuestParams.booked_occupancy <= 0 ?
        -adult.value : this.addGuestParams.std_occupancy - this.addGuestParams.booked_occupancy - adult.value;
      this.addGuestParams['extraAdult'] = extra < 0 ? Math.abs(extra) : 0;
      this.addGuestParams['extraChild'] = extra < 0 ? child.value : (extra - child.value) < 0 ? Math.abs(extra - child.value) : 0;
      this.addGuestParams['totalDays'] = moment(moment(this.addGuestForm.value.end).format('YYYY-MM-DD')).diff(moment(this.addGuestForm.value.start).format('YYYY-MM-DD'), 'days') + 1;
      this.isLastGuestAddTab = this.addGuestTabs.tabs.length == (index + 1) ? true : false;
      // console.log("this.addGuestParams", this.addGuestParams);
    }
  }
  /**
   * to get room category charges and other stuff for payments.
   * @memberof ReservationsComponent
   */
  getRoomChargesandExtracharges(index: number) {
    console.log("function working", index);
    // console.log("Condition Check : ", this.reservationTabs.tabs.length == (index + 1), "index", index);
    this.isLastReservationTab = this.reservationTabs.tabs.length == (index + 1) ? true : false;
    let chargesOBJ = {};
    let data: any[] = [];
    // if (this.roomChargesDetails.length == 0) {
    this.bookingForm.value.booking.dates.forEach((ele) => {
      data.push(ele.room_category_id);
    })
    chargesOBJ['room'] = data;
    chargesOBJ['customer_type'] = this.bookingForm.value.booking.customer_type;
    // get charges for selected room's room categery
    if (chargesOBJ['room'] != "" && chargesOBJ['customer_type'] != "") {
      this.getCharges = this.BS.getRoomCharges(chargesOBJ)
        .subscribe((res) => {
          if (res.status == "success") {
            console.log("response in 2718", res);
            this.roomChargesDetails = res.data;
            if (this.roomChargesDetails.length) {
              let stayTypeIndex = this.bookingForm.value.booking.stay_type;
              stayTypeIndex = stayTypeIndex ? parseInt(stayTypeIndex) : stayTypeIndex;
              stayTypeIndex = this.findIndex(stayTypeIndex, "id", this.originalStayTypesList) - 1;
              this.roomChargesDetails.forEach((item) => {
                item.charges = item.charges * this.originalStayTypesList[stayTypeIndex].charge / 100;
                item.extra_adult_charges = item.extra_adult_charges * this.originalStayTypesList[stayTypeIndex].charge / 100;
                item.extra_child_charges = item.extra_child_charges * this.originalStayTypesList[stayTypeIndex].charge / 100;
              });
            }
            // console.log("---------roomChargesDetails-------", this.roomChargesDetails)
            this.findTotal();
            this.applyAdvancePayment();
            this.addtoAdvancePment();
          }
        })
    }
    // }
  }

  bookingDateChanged(value: any, key: string, indx?) {
    console.log("inside bookingDateChanged");

    let dates = <FormArray>this.bookingForm.controls['booking'].get('dates');
    let date = dates.controls[indx] as FormGroup;

    let hasChangedDate = false;
    let stayType = this.bookingForm.controls['booking'].get('stay_type').value;
    let duration = this.originalStayTypesList[this.findIndex(stayType, "id", this.originalStayTypesList) - 1].duration;
    date.controls['check_in'].patchValue("");
    date.controls['check_out'].patchValue("");
    console.log(this.bookingForm.value.booking.dates.length, "this.bookingForm.value.booking.dates.length");

    for (let i = 0; i < this.bookingForm.value.booking.dates.length; i++) {

      // var checkOuttime =  moment(dates.controls[i].get('time').value).format('YYYY-MM-DD');
      // console.log(checkOuttime , "checkOuttime");
      // return;
      let start = moment(dates.controls[i].get('start').value).format('YYYY-MM-DD');
      let end = moment(dates.controls[i].get('end').value).format('YYYY-MM-DD');

      if ((key == 'start' && moment(end).isSameOrBefore(value, 'days') && duration >= 24)
        ||
        (key == 'end' && moment(value).isSameOrBefore(start, 'days') && duration >= 24)) {

        this.showNotification(`Invalid Date ${key} Selected!`, 'error', true, 5);
        let selectedDate = key == 'start' ? this.bookingStartDate : this.bookingEndDate;
        console.log(selectedDate, "selectedDate");

        if (key == 'start') {
          this.datepickerupdate1.bsValue = selectedDate;
        }
        else {
          this.datepickerupdate2.bsValue = selectedDate;
        }
        dates.controls[i].get(key).patchValue(selectedDate);
        break;
      }
      else {

        let valueFull = moment(value).format('YYYY-MM-DD');
        console.log(valueFull, "valueFull");

        let totalDays = (key == 'start' ? moment(end).diff(valueFull, 'days') : moment(valueFull).diff(start, 'days')) + 1;
        if (duration >= 24) {
          totalDays -= 1;
        }
        // console.log("Total Days : ", totalDays);
        dates.controls[i].get(key).patchValue(value);
        console.log("elsssssssss", value);

        if (key == 'start') {
          this.minEndDate = new Date(this.getNextDateFormat(value));
          console.log(this.minEndDate, "this.minEndDate------");

          // if (moment(moment().format('YYYY-MM-DD')).diff(moment(value), 'days') == 0) {
          //   console.log("Today", value);
          //   dates.controls[i].get('check_in').patchValue(moment(value).format('YYYY-MM-DD') + 'T02:30:00.000Z');
          // }
          // else {
          //   console.log("ELSE Today: ", value);
          //   dates.controls[i].get('check_in').patchValue(moment(value).format('YYYY-MM-DD') + 'T02:30:00.000Z');
          // }
        } else if (key == 'end' && duration < 24) {
          console.log(key, ">>>>ifff");
          this.minEndDate = this.minDate;

          console.log(this.minEndDate, "minEndDate");

        }
        dates.controls[i].get('total_days').patchValue(totalDays);
        this.bookingEndDate = key == 'end' ? value : this.bookingEndDate;
        this.bookingStartDate = key == 'start' ? value : this.bookingStartDate;
        hasChangedDate = true;

      }
    }
    console.log(date.controls['end'].value)
    date.controls['check_in'].patchValue(date.controls['start'].value);
    date.controls['check_out'].patchValue(date.controls['end'].value);
    // console.log(indx, "-----Booking Form-------", this.bookingForm.value)
    if (hasChangedDate) {
      this.findTotal();
    }
    // console.log("this.bookingForm.value.booking.dates : ", this.bookingForm.value.booking.dates);
  }


  valueChanged() {
    // console.log("this.bookingForm.value.booking.dates : ",this.bookingForm.value.booking.dates);
    this.findTotal();
  }
  parseIntCustom(a: string): number {
    return parseInt(a);
  }
  /**
   * finds total amount and total payble excluding discount
   * @memberof ReservationsComponent
   */
  findTotal() {
    // let totalDays = this.bookingForm.value.booking.dates[0].total_days * this.bookingForm.value.booking.dates.length;
    if (this.roomChargesDetails.length > 0) {
      let subtotal: number;
      let Adultcharges: number;
      let childcharges: number;
      let totalPayments: number = 0;
      let totalDiscount: number = 0;
      let totalDay: number = 0;
      this.bookingForm.value.booking.dates.forEach((ele, index) => {
        totalDay = ele.total_days
        Adultcharges =
          this.isCommonRoomCategory ? (ele.adult * this.roomChargesDetails[index].charges) :
            (((ele.adult - this.roomChargesDetails[index].std_occupancy) >= 0) ?
              ((ele.adult - this.roomChargesDetails[index].std_occupancy) * this.roomChargesDetails[index].extra_adult_charges) : 0);
        childcharges =
          this.isCommonRoomCategory ? (ele.child * this.roomChargesDetails[index].charges) : (((ele.adult - this.roomChargesDetails[index].std_occupancy) >= 0) ?
            ((ele.child) * this.roomChargesDetails[index].extra_child_charges) :
            (ele.child - (this.roomChargesDetails[index].std_occupancy - ele.adult)) >= 0 ?
              (ele.child - (this.roomChargesDetails[index].std_occupancy - ele.adult)) * this.roomChargesDetails[index].extra_child_charges :
              0);
        totalPayments +=
          this.isCommonRoomCategory ? (ele.total_days  * (Adultcharges + childcharges)) :
            (ele.total_days * (this.roomChargesDetails[index].charges + Adultcharges + childcharges));

        if (this.isCommonRoomCategory) {
          if (this.roomChargesDetails[index].discount_type == "amount") {
            let tempDiscount = ele.total_days * this.roomChargesDetails[index].discount_value * (parseInt(ele.adult) + parseInt(ele.child));
            if (tempDiscount > (ele.total_days * (Adultcharges + childcharges))) {
              totalDiscount += (ele.total_days * (Adultcharges + childcharges));
            } else {
              totalDiscount += tempDiscount;
            }
          } else if (this.roomChargesDetails[index].discount_type == "percentage") {
            totalDiscount += ele.total_days * (((Adultcharges ? Adultcharges : 0) + (childcharges ? childcharges : 0)) * this.roomChargesDetails[index].discount_value / 100);
          }
        } else {
          if (this.roomChargesDetails[index].discount_type == "amount") {
            let tempDiscount = ele.total_days * this.roomChargesDetails[index].discount_value;
            if (tempDiscount > (ele.total_days * (this.roomChargesDetails[index].charges + Adultcharges + childcharges))) {
              totalDiscount += (ele.total_days * (this.roomChargesDetails[index].charges + Adultcharges + childcharges));
            } else {
              totalDiscount += tempDiscount;
            }
          } else if (this.roomChargesDetails[index].discount_type == "percentage") {
            totalDiscount += ele.total_days * ((this.roomChargesDetails[index].charges + (Adultcharges ? Adultcharges : 0) + (childcharges ? childcharges : 0)) * this.roomChargesDetails[index].discount_value / 100);
          }
        }
      })
      this.totalPayments = totalPayments;
      this.totalPaybles = this.totalPayments;
      this.totalCustomerDiscount = totalDiscount;
      this.totalPaybles = this.totalPayments - this.totalCustomerDiscount;
      // console.log(this.totalPaybles);
      this.totalDays = totalDay;
      // console.log(this.totalDays);
      this.bankAdvanceAmount = this.totalPaybles;
      this.perDayCharge = this.totalPaybles / this.totalDays;  // to calculate per day room charge after discount
      console.log(this.perDayCharge);
      console.log(this.totalPaybles , "tp")
      this.viewTotal = JSON.parse(JSON.stringify(this.totalPaybles));
    }
  }
  /**
   * get advance payment input value and recalculate total payables
   *
   * @param {any} event event from input box
   * @memberof ReservationsComponent
   */
  applyAdvancePayment() {
    if (this.bookingForm.controls['payments'].get('payment_mode').value != '0' && this.bookingForm.controls['payments'].get('payment_amount').value > 10000) {

      this.bookingForm.controls['payments'].get('payment_verification_id').setValidators([Validators.required])
      this.bookingForm.controls['payments'].get('payment_verification_id').markAsTouched()
      this.bookingForm.controls['payments'].get('payment_verification_id').markAsDirty()
      this.bookingForm.controls['payments'].get('payment_verification_id').updateValueAndValidity()
      // console.log("done!", this.bookingForm.controls['payments'])
    } else {
      this.bookingForm.controls['payments'].get('payment_verification_id').clearValidators()
      this.bookingForm.controls['payments'].get('payment_verification_id').markAsPristine()
    }

    // console.log("+++++++++",this.bookingForm.controls['payments'].get('payment_amount').value)
    // console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>applyAdvancePayment>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    if (this.bookingForm.controls['payments'].get('payment_mode') && this.bookingForm.controls['payments'].get('payment_mode').value == '1') {
      this.canShowCardSwipeAmount = true;
    }
    let payment_amount = <FormControl>this.bookingForm.controls['payments'].get('payment_amount');
    console.log(payment_amount , 1234);
    let cardCharges = <FormControl>this.bookingForm.controls['payments'].get('card_charge');
    let viewTotal = this.viewTotal;
    // console.log("payment_amount---",payment_amount)
    // console.log("cardCharges-----",cardCharges)
    // console.log("viewTotal----", viewTotal)
    // console.log(this.bookingForm.controls['payments'].get('payment_amount').valid)
    this.totalPaybles = viewTotal - payment_amount.value;
    // console.log("totalPaybles--1--", this.totalPaybles)
    if (this.canShowCardSwipeAmount && (this.totalPaybles + ((cardCharges ? parseInt(cardCharges.value) : 0) * this.totalPayments / 100) < 0)) {
      // console.log("---IF-------")
      this.canShowCardSwipeAmount = false;
    }
    // console.log("payment_amount.value------->", payment_amount.value)
    this.totalPayments = parseInt(payment_amount.value);

    if (this.paymentTypeSelection === '0' && this.bookingForm.controls['payments'].get('payment_amount').value > 9999) {
      this.addRemoveExtraItems(this.bookingForm.controls['payments'].get('payment_amount').value, this.bookingForm.get('items'));
    }
    else if (this.paymentTypeSelection === '0' && this.bookingForm.controls['payments'].get('payment_amount').value < 9999 && this.bookingForm.get('items').value.length > 0) {
      let items = this.bookingForm.get('items');
      let guests = <FormArray>items;
      while (guests.length !== 0) {
        guests.removeAt(0);
      }
    }
  }
  /**
   * on advance payment input box blur event add entry with current date to table.
   *
   * @param {any} event
   * @memberof ReservationsComponent
   */
  addtoAdvancePment() {

    let payment_amount = <FormControl>this.bookingForm.controls['payments'].get('payment_amount');
    let data = {};
    if (payment_amount.value != undefined && this.advancePayments) {
      data['date'] = moment();
      data['amount'] = payment_amount.value;
      if (this.advancePayments.length == 0 && payment_amount.value != "") {
        this.advancePayments.push(data);
        // console.log("Data pushed", this.advancePayments);
      }
      else if (payment_amount.value == "") {
        // console.log("Array made empty)");
        this.advancePayments = [];
      }
      else {
        // console.log("data updated");
        this.advancePayments[0] = data;
      }
    }
  }
  /**
   * Source dropdown change event
   *
   * @param {any} event
   * @memberof ReservationsComponent
   */
  sourceTypeSelectionChanged(event) {
    this.sourceSelection = event.id;
    this.bookingForm.controls['payments'].get('source').patchValue(event.id);
  }
  checkTabIndex(index: number) {
    // console.log("Condition Check : ", this.reservationTabs.tabs.length == (index + 1), "index : ", index);
    this.isLastReservationTab = this.reservationTabs.tabs.length == (index + 1) ? true : false;
    if(index == 2){
      this.getRoomChargesandExtracharges(2)
    }
  }
  checkAddGuestTabIndex(index: number) {
    // console.log("Condition Check : ", this.reservationTabs.tabs.length == (index + 1), "index : ", index);
    this.isLastGuestAddTab = this.addGuestTabs.tabs.length == (index + 1) ? true : false;
  }
  nextReservationTab() {
    let canNavigate = true;
    let index = _.findIndex(this.reservationTabs.tabs, ['active', true]);
    let booking = <FormGroup>this.bookingForm.controls['booking'];
    let dates = <FormArray>this.bookingForm.controls['booking'].get('dates');
    let guests = <FormArray>this.bookingForm.controls['guest'];
    if (index == 0 && booking.invalid) {
      canNavigate = false;
      booking.get('reference_user').markAsDirty();
      booking.get('reference_user').markAsTouched();
      // console.log("Marked Dirty");
    }
    if (index == 0 && dates.invalid) {
      canNavigate = false;
      dates.controls.forEach(date => {
        // console.log("Marked Dirty for dates");
        date.get('check_out').markAsDirty();
        date.get('check_out').markAsTouched();
        date.get('check_in').markAsDirty();
        date.get('check_in').markAsTouched();
        date.get('adult').markAsDirty();
        date.get('adult').markAsTouched();
        date.get('child').markAsDirty();
        date.get('child').markAsTouched();
        if (date.get('agent_receipt_no')) {
          date.get('agent_receipt_no').markAsDirty();
          date.get('agent_receipt_no').markAsTouched();
        }
        if (date.get('note')) {
          date.get('note').markAsDirty();
          date.get('note').markAsTouched();
        }
      });
    }
    if (index == 1 && guests.invalid) {
      console.log(guests)
      canNavigate = false;
      guests.controls.forEach(guest => {
        guest.get('name').markAsDirty();
        guest.get('name').markAsTouched();
        guest.get('contact').markAsDirty();
        guest.get('contact').markAsTouched();
        guest.get('aadharcard_number').markAsDirty();
        guest.get('aadharcard_number').markAsTouched();
        guest.get('pancard_number').markAsDirty();
        guest.get('pancard_number').markAsTouched();
        guest.get('PC_proof').markAsDirty();
        guest.get('PC_proof').markAsTouched();
        guest.get('AC_proof').markAsDirty();
        guest.get('AC_proof').markAsTouched();
      });
    }
    if (canNavigate) {
      // console.log("Index : ", index + 1);
      this.reservationTabs.tabs[index + 1].active = true;
      index += 1;
      this.isLastReservationTab = this.reservationTabs.tabs.length == (index + 1) ? true : false;
      if (index == 2) {
        this.getRoomChargesandExtracharges(2)
      }
    }
  }
  cardSwipeChargesChanged() {
    // console.log("+++++++++++++++++++++++++++++++++++++cardSwipeChargesChanged++++++++++++++++++++++++++++++++++++++")
    let receipt = <FormGroup>this.bookingForm.controls['payments'];
    let cardCharges = <FormControl>this.bookingForm.controls['payments'].get('card_charge');
    if (this.bookingForm.controls['payments'].get('payment_mode').value == '1') {
      this.canShowCardSwipeAmount = true;
    }
    // console.log("receipt----",receipt)
    // console.log("cardCharges----",cardCharges)
    // console.log("totalPaybles----",this.totalPaybles)
    // console.log("totalPayments---",this.totalPayments)
    // console.log("(parseInt(cardCharges.value) * this.totalPayments / 100)----",this.totalPaybles + (parseInt(cardCharges.value) * this.totalPayments / 100))
    if (this.canShowCardSwipeAmount && (this.totalPaybles + (parseInt(cardCharges.value) * this.totalPayments / 100) < 0)) {
      // console.log("----IF----")
      this.canShowCardSwipeAmount = false;
    }
    let setCardSwipeCharges = 0;
    // console.log("")
    if (parseInt(receipt.get('card_charge').value) > 0) {
      setCardSwipeCharges = ((parseInt(receipt.get('card_charge').value) * (this.totalPayments / 100)) + this.totalPayments);
      // console.log("----If ---1------")
    }
    else {
      setCardSwipeCharges = this.totalPayments;
    }
    // console.log("setCardSwipeCharges-----",setCardSwipeCharges)
    receipt.controls['payment_amount'].clearValidators();
    receipt.controls['payment_amount'].setValidators([Validators.required]);
    // receipt.controls['payment_amount'].updateValueAndValidity();
    // console.log("+++++++++++++++++++++++++++++++++++++cardSwipeChargesChanged++++++++++++++++++++++++++++++++++++++")
  }

  /**
   * advancepayment dropdown change event
   *
   * @param {any} event
   * @memberof ReservationsComponent
   */
  advancePaymentSelectionChanged(event) {
    console.log(event);
    
    let receipt = <FormGroup>this.bookingForm.controls['payments'];
    this.paymentTypeSelection = event.id;
    //////////////////////////////////////////////////////////////////////////////////////////////////////////
    // console.log("", event.value)
    this.cardSelected = false;
    // console.log(event.value)
    this.bookingForm.controls['payments'].get('payment_verification_id').setValidators([Validators.required])
    if (event.id == 0) {
      this.cardSelected = true;
      // window.alert(event.value)
      // console.log("2-----------------------")
      // this.bookingForm.controls['payments'].get('payment_verification_id').markAsDirty();
      // this.bookingForm.controls['payments'].get('payment_verification_id').markAsTouched()
    }
    //  else if (event.value == 1 || event.value == 2 || event.value == 3) {
    // console.log("3-----------------------")
    // receipt.get('payment_verification_id').clearValidators();
    // this.bookingForm.controls['payments'].get('payment_verification_id').clearValidators();
    // this.bookingForm.controls['payments'].get('payment_verification_id').errors = [];
    // this.bookingForm.controls['payments'].get('payment_verification_id').markAsPristine();
    // this.bookingForm.controls['payments'].get('payment_verification_id').markAsUntouched();
    // }
    this.bookingForm.controls['payments'].get('payment_mode').patchValue(event.id);
    if (event.id == 1) {

      this.canShowCardSwipeAmount = true;
      receipt.addControl('payment_reciept_number', new FormControl('', Validators.required));
      receipt.addControl('card_charge', new FormControl('', [Validators.required, CustomValidators.lte(100)]));
      let setCardSwipeCharges = 0;
      if (parseInt(receipt.get('card_charge').value) > 0) {
        setCardSwipeCharges = ((parseInt(receipt.get('card_charge').value) * (this.totalPayments / 100)) + this.totalPayments);
      }
      else {
        setCardSwipeCharges = this.totalPayments;
      }
      // receipt.controls['payment_amount'].setValidators([Validators.required, CustomValidators.equal(setCardSwipeCharges.toString())]);
      // receipt.controls['payment_amount'].updateValueAndValidity();
      receipt.controls['payment_reciept_number'].updateValueAndValidity();
      receipt.controls['card_charge'].updateValueAndValidity();
      if (receipt.get('bank_name')) {
        receipt.removeControl('bank_name');
        receipt.removeControl('bank_cheque_no');
      }
    }
    else if (event.id == 2 || event.id == 3) {
      this.canShowCardSwipeAmount = false;
      receipt.addControl('bank_name', new FormControl('', Validators.required));
      receipt.addControl('bank_cheque_no', new FormControl('', Validators.required));
      receipt.controls['payment_amount'].setValidators(Validators.required);
      receipt.controls['payment_amount'].updateValueAndValidity();
      receipt.controls['bank_name'].updateValueAndValidity();
      receipt.controls['bank_cheque_no'].updateValueAndValidity();
      if (receipt.get('payment_reciept_number')) {
        receipt.removeControl('payment_reciept_number');
        receipt.removeControl('card_charge');
      }
    }
    else {
      this.canShowCardSwipeAmount = false;
      if (receipt.get('payment_reciept_number')) {
        receipt.removeControl('payment_reciept_number');
        receipt.removeControl('card_charge');
      }
      if (receipt.get('bank_name')) {
        receipt.removeControl('bank_name');
        receipt.removeControl('bank_cheque_no');
      }
      receipt.controls['payment_amount'].clearValidators();
      receipt.controls['payment_amount'].setValidators(CustomValidators.digits);
      receipt.controls['payment_amount'].updateValueAndValidity();
      // this.bookingForm.controls['payments'].get('payment_reciept_number').patchValue('');
    }

    if (this.paymentTypeSelection !== '0' && this.bookingForm.get('items').value.length > 0) {
      let items = this.bookingForm.get('items');
      let guests = <FormArray>items;
      while (guests.length !== 0) {
        guests.removeAt(0);
      }
    }
    // console.log("this.bookingForm : ", this.bookingForm);
  }
  /**
   * Gets Customer contact suggestions from existing Customer List
   * @param {number} index : Selected User's Index
   * @param {string} contact : User's contact number
   */
  getCustomerList(formIndex: number, type?: string) {
    let selectedGuests = [];
    let guest = <FormArray>this.bookingForm.controls['guest'];
    guest.controls.forEach(guestI => {
      if (guestI.value.id) {
        selectedGuests.push(guestI.value.id);
      }
    });
    let contact = type === 'split' ? this.splitBookingForm.value.dates[formIndex].guest_contact : this.bookingForm.value.guest[formIndex].contact;
    if (contact.length > 3) {
      let mobile = { text: contact, ids: selectedGuests };
      this.BS.getCustomerList(mobile).subscribe(res => {
        this.customerList = res.data;
        this.showCustomerList = true;
        this.selectedCustormerFormIndex = formIndex;
      });
    }
    else if (this.customerList.length > 0 && (contact.length <= 3)) {
      this.resetCustomerList();
    }
    else {
      // console.log("Nothing Happenedddddddddd");
    }
  }

  /**
   * Inputs selected customer details to the Form at the corresponding index
   *
   * @param customer {any} : Selected customer details
   * @param formIndex {number} : Form Index for patching selected customer details
   * @param customerIndex {number} : Index in suggested customer-list
   */
  selectCustomer(customer: any, formIndex: number, customerIndex: number, type?: string) {

    if (type === 'split') {
      let user = <FormArray>this.splitBookingForm.controls['dates'];
      let time = "";
      let userControl = <FormGroup>user.controls[formIndex];
      userControl.controls['guest_contact'].patchValue(this.customerList[customerIndex].contact);
      userControl.controls['guest_email'].patchValue(this.customerList[customerIndex].email);
      //add controlller
      userControl.controls['time']
      userControl.controls['guest_name'].patchValue(this.customerList[customerIndex].name);
      userControl.controls['guest_id'].patchValue(this.customerList[customerIndex].id);
      this.showCustomerList = false;
      this.selectedCustormerFormIndex = undefined;
    }

    // else if (type === 'guest') {
    //   let user = <FormArray>this.guestForm.controls['guests'];
    //   // let userControl = <FormGroup>user.controls[formIndex];
    //   user.controls[formIndex].patchValue(this.customerList[customerIndex]);
    // console.log("Userrrrrr :",user);
    //   // user.controls[formIndex].patchValue(this.customerList[customerIndex].email);
    //   // user.controls[formIndex].patchValue(this.customerList[customerIndex].name);
    //   this.showCustomerList = false;
    //   this.selectedCustormerFormIndex = undefined;
    // }
    else {
      let user = <FormArray>this.bookingForm.controls['guest'];
      // console.log("user.controls[formIndex] : ", user.controls[formIndex]);
      // console.log("this.customerList[customerIndex] : ", this.customerList[customerIndex]);
      user.controls[formIndex].patchValue(this.customerList[customerIndex]);
      this.showCustomerList = false;
      this.selectedCustormerFormIndex = undefined;
    }
  }

  /**
   * Hides the suggested customer-list
   */
  hideCustomerList() {
    this.resetCustomerList();
  }

  /**
   * Resets the suggested customer-list to a Blank-Array
   */
  resetCustomerList() {
    this.customerList = [];
    this.showCustomerList = false;
    this.selectedCustormerFormIndex = undefined;
  }
  /**param
   * Save booking form submit function
   *
   * @memberof ReservationsComponent
   */
  saveBooking(event: any, type?: string) {
    console.log("return", event, type);
    // this.getCheckInData(item);
    // console.log(">>>>>>>>>>>>>>>>>>>>", item );

    // sending booking data
    // console.log("eventtttttttttttt : ",event);
    // console.log("typeeeeeeeeeeeee : ",type);
    if (this.bookingForm.valid) {
      if (type == 'check-in') {
        this.bookingSaveAndCheckin = true;
      }
      else {
        this.bookingSaveOnly = true;
      }
      let bookingForm = _.cloneDeep(this.bookingForm.value);
      // console.log(this.bookingForm.value)
      bookingForm['is_common_room_category'] = this.isCommonRoomCategory;
      bookingForm['is_over_booking'] = this.isOverBooking;
      bookingForm['is_extra_booking'] = this.isExtraBooking;
      bookingForm.booking['start'] = moment(bookingForm.booking.dates[0].start).format('YYYY-MM-DD') + 'T00:00:00.000Z';
      let stayType = bookingForm.booking['stay_type'];
      let duration = this.originalStayTypesList[this.findIndex(stayType, "id", this.originalStayTypesList) - 1].duration;
      if (duration >= 24) {
        bookingForm.booking['end'] = this.getPreviousDateFormat(bookingForm.booking.dates[0].end) + 'T00:00:00.000Z';
      } else {
        bookingForm.booking['end'] = moment(bookingForm.booking.dates[0].end).format('YYYY-MM-DD') + 'T00:00:00.000Z';
      }
      for (let i = 0; i < bookingForm.booking.dates.length; i++) {
        bookingForm.booking.dates[i] = this.omitKeys(bookingForm.booking.dates[i], 'start', 'end');
        bookingForm.booking.dates[i]['guest'] = bookingForm.guest[i];
      }
      if (bookingForm.payments['payment_mode'] == '1') {
        // console.log("this.totalPayments--2--->>",this.totalPayments)
        bookingForm.payments['payment_amount'] = this.totalPayments;
      }
      bookingForm.payments['payment_date'] = moment(new Date()).format('YYYY-MM-DD') + 'T00:00:00.000Z';
      bookingForm = this.omitKeys(bookingForm, 'guest');
      console.log(bookingForm, "bookingFormbookingFormbookingFormbookingForm");

      this.saveBookingData = this.BS.saveThisBooking(bookingForm)
        .subscribe((res) => {
          if (res.status == "success") {
            if (bookingForm.booking.dates[0].note && bookingForm.booking.dates[0].note != '') {
              this.is_comment_necessary = false;
            }
            this.currDayCursorActive = true;
            this.fillData();
            if (bookingForm.booking.dates[0].note && bookingForm.booking.dates[0].note != '') {
              this.updateBookingCalendarField(res.data.bookingRsponse[0].booking_id, bookingForm.booking.dates[0].note, 'booking_note');
            }
            this.staticModal.hide();
            this.bookingSaveOnly = false;
            this.bookingSaveAndCheckin = false;
            this.reservationTabs.tabs[0].active = true;
            this.isLastReservationTab = false;
            this.roomChargesDetails = [];
            let dates = <FormArray>this.bookingForm.controls['booking'].get('dates');
            for (let i = 0; i < dates.length; i++) {
              dates.controls[i].get('start').patchValue(undefined);
              dates.controls[i].get('end').patchValue(undefined);
            }
            if (type == 'check-in') {
              this.addGuestInfo(res.data.bookingRsponse[0], 'checkIn');
              this.removeSelection();
            }
            else {
              this.removeSelection();
            }
            this.paymentTypeSelection = "0";
            this.is_reference = false;
            this.is_agent = false;
            this.is_agent_location = false;
          }
          else {
            this.bookingSaveOnly = false;
            this.bookingSaveAndCheckin = false;
          }
        }, error => {
          this.bookingSaveOnly = false;
          this.bookingSaveAndCheckin = false;
        });
    }
  }
  copyToAllGuestAtCheckIn(billingGuest: any) {
    // console.log("billingGuest : ", billingGuest);
    let guests = <FormArray>this.guestForm.controls['guests'];
    for (let i = 0; i < guests.controls.length; i++) {
      if (!guests.controls[i].value.is_billingUser) {
        console.log(':::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::');
        console.log(billingGuest);
        console.log(':::::::::::***********::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::');

        let guest = <FormGroup>guests.controls[i];
        guest.controls['address'].patchValue(billingGuest.address);
        guest.controls['city'].patchValue(billingGuest.city);
        guest.controls['country'].patchValue(billingGuest.country);
        guest.controls['zip'].patchValue(billingGuest.zip);
        guest.controls['is_billingUser'].patchValue(false);
        guest.updateValueAndValidity();
      }
    }
  }

  fillUserInAll(customer: any) {
    let guests = <FormArray>this.bookingForm.controls['guest'];
    for (let i = 0; i < guests.controls.length; i++) {
      if (!guests.controls[i].value.isBillingGuest) {
        let guest = <FormGroup>guests.controls[i];
        guest.controls['contact'].patchValue(customer.value.contact);
        guest.controls['email'].patchValue(customer.value.email);
        guest.controls['name'].patchValue(customer.value.name);
        if(customer.value.aadharcard_number !== null){
        guest.controls['aadharcard_number'].patchValue(customer.value.aadharcard_number);}
        console.log(customer.value.pancard_number,'pan')
        if(customer.value.pancard_number !== null){
        guest.controls['pancard_number'].patchValue(customer.value.pancard_number);}
        guest.controls['document'].patchValue(customer.value.document);
        guest.controls['isBillingGuest'].patchValue(false);
        guest.updateValueAndValidity();
      }
    }
  }
  changeCustomerModel(customer) {
    this.bookingForm.value.guest.map(guest => {
      return guest.isBillingGuest = false;
    })
    customer.value.isBillingGuest = true;
  }
  /**
   * Omit Specific Keys from a given object
   *
   * @param {string} a key to be omitted
   * @param {string} b key to be omitted
   * @memberof ResvervationsComponent
  */
  omitKeys(iterateeObject: Object, a: string, b?: string) {
    return b ? _.omit(iterateeObject, [a, b]) : _.omit(iterateeObject, [a]);
  }
  preShowBookingDetails(event, index, main) {
    this.ViewData = 'payment'
    this.eventData = event;
    this.roomIndex = index
    this.eventMain = main;
    console.log(event, 'booking data');
    
    if (event.booking_status == 'block') {
      this.BS.viewOnlineBookingDetails(event.booking_id)
      .subscribe((res) => {
        console.log(res, 'selected booking detail')
        if (res.data.bookingPaymentData.length > 0) {
          this.services.getonlinepayment(res.data.bookingPaymentData[0].id).subscribe((res)=>{
            console.log(res, 'online payment details')  
            this.selectedPayment = res.data   

          })
        }
        //  else {
          this.getRoomslist(res.data)
          this.selectedBooking = res.data
          this.viewDetailsModal.show()
        // }
      }, (err)=>{
        console.log(err,'get online booking error')
      })
    } else {
    this.checkForExtraBooking(event).then((res) => {
      this.showBookingDetails(res);
      this.selectedBookingBreed = undefined;
    }).catch((err) => { });
  }
  }

  approvePaymentRequest() {

    this.acceptButtonLoader = true
    console.log("approve data", this.selectedPayment);

    this.services.approveOnlinePayment(this.selectedPayment).subscribe((response) => {
        if (response && response.status === 'success')
            this.successModalStyle = 'alert alert-success text-capitalize';
            this.acceptErrorMessage = "payment Accapted Now Reserving Room"
            this.services.createBooking(response.data).subscribe((data) => {
                if (data.status === "success") {
                    this.successModalStyle = 'alert alert-success text-capitalize';
                    this.acceptErrorMessage = data.message;

                    setTimeout(() => {
                        this.acceptButtonLoader = false
                        // this.getPaymentData();
                        this.selectedPayment = null
                        this.successModal.hide();
                        this.acceptBookingModal.hide();
                        this.viewDetailsModal.hide();
                        this.getCurrentDisplayBookings();
                        // this.acceptPaymentModal.hide();
                        this.acceptErrorMessage = ''
                    }, 1500);
                    
                }

            }, (error) => {
                this.successModalStyle = 'alert alert-danger text-capitalize';
                this.acceptErrorMessage = error.message;
                this.acceptButtonLoader = false
                console.log('got error in saving the data in booking rooms', error);
            })
    }, (error) => {
        this.acceptButtonLoader = false
        this.successModalStyle = 'alert alert-danger text-capitalize';
        this.acceptErrorMessage = "Failed To Approve The Payment"
        console.log('Approval failed', error);
    })
}

  closeSuccessmodel() {
    this.successModal.hide()
    setTimeout(() => {
      this.viewDetailsModal.show()
    }, 500);
  }

  cancelOnlinePaymentRequest() {
    if (!this.cancelReason) {
        this.cancleBookingStyle = 'alert alert-danger text-capitalize';
        return this.cancelErrorMessage = "Please Add Reason for Cancellation";
    }
    this.cancelButtenLoader = true
    if (this.selectedBooking.bookingPaymentData.length > 0) {
      this.services.cancelOnlinePayment(this.cancelId, this.cancelReason).subscribe((response) => {
        if (response.status === "success") {
            //set success message and remove loder
            this.cancleBookingStyle = 'alert alert-success text-capitalize';
            this.cancelErrorMessage = response.message;
            this.cancelButtenLoader = false
  
            //fetch new data again
            // this.getPaymentData();
            setTimeout(() => {
                this.cancelBookingRequest.hide();
                this.viewDetailsModal.hide()
                this.getCurrentDisplayBookings()
                this.cancelErrorMessage = '';
                this.cancelReason = ''
            }, 3000);
        }
        }, (error) => {
            console.log('error in cancelling booking', error);
            if (error) {
                this.cancleBookingStyle = 'alert alert-danger text-capitalize';
                this.cancelErrorMessage = error.message;
                this.cancelButtenLoader = false
                setTimeout(() => {
                    this.cancelBookingRequest.hide();
                    this.cancelErrorMessage = '';
                    this.cancelReason = '';
                }, 3000);
                // return this.getPaymentData()
            }
            this.cancelButtenLoader = false
        }
      );
    }
    else {
      this.cancelOnlineBookingRequest()
    }
}

cancelOnlineBookingRequest() {
  if (!this.cancelReason) {
      return this.cancelErrorMessage = "Please Add Reason for Cancellation"
  }
  this.cancelButtenLoader = true
  this.BS.cancelOnlineBooking(this.cancelId, this.cancelReason).subscribe(
      (response) => {

          if (response.status === "success") {
              this.cancleBookingStyle = 'alert alert-success'
              this.cancelErrorMessage = response.message;
              this.cancelButtenLoader = false
              // console.log("Cancellation successful", response);
              setTimeout(() => {
                  this.cancelBookingRequest.hide();
                  this.viewDetailsModal.hide();
                  this.getCurrentDisplayBookings();
                  this.cancelErrorMessage = '';
                  this.cancelReason = '';
              }, 3000);
              // return this.getOnlineBookingData()
          }

      }, (error) => {
          console.log('error in cancelling booking');
          if (error) {
              this.cancleBookingStyle = 'alert alert-danger'
              this.cancelErrorMessage = error.message;
              this.cancelButtenLoader = false
              setTimeout(() => {
                  this.cancelBookingRequest.hide();
                  this.cancelErrorMessage = '';
                  this.cancelReason = '';
              }, 3000);
              // return this.getOnlineBookingData()
          }

          this.cancelButtenLoader = false
      }
  );
}
  onRoomChange(event: any): void {
    const selectedId = event.target.value;
    this.transferRoomId = selectedId ? Number(selectedId) : null;
    console.log('Selected room ID:', this.transferRoomId);
  }

  changeCustomerType(event: any) {
    this.customerTypeId = event.target.value;
    if(event.target.value == 1) {
        this.showReferenceUserField = false;
    }
    console.log(this.customerTypeId, "customerTypeId");
  }

  updateRoomDetails(data: any) {
    let formatData = {
        online_booking_id: data.id ,
        new_room_id: this.transferRoomId ? this.transferRoomId : null,
        new_room_title: this.transferRoomId && this.roomsList.find(room => room.id === this.transferRoomId).title,
        customer_type: this.customerTypeId ? this.customerTypeId : null,
        reference_user_name: this.reference_user_name ? this.reference_user_name : '',
        room_category_id: this.selectedBooking.room_category_id,
        total_amount: this.selectedBooking.total_amount,
        total_days: this.selectedBooking.total_days,
        selectedBooking: this.selectedBooking
    }

    console.log(formatData, "data fromatted");
    this.acceptButtonLoader = true
    this.BS.transferBookingRoom(formatData).subscribe((response) => {
        if (response.status === "success") {
            this.acceptButtonLoader = false
            this.acceptErrorMessage = response.message
            console.log(response, "got response");
            this.successModalStyle = 'alert alert-success'
            this.successModal.show()
            this.timeoutMethod();
        } else {
            this.acceptErrorMessage = response.message
            this.acceptButtonLoader = false
            console.log("error response", response);
            this.successModalStyle = 'alert alert-danger'
            this.successModal.show()
            this.timeoutMethod();
        }
        this.showReferenceUserField = true;
    }, (error) => {
        this.acceptErrorMessage = error.message
        this.acceptButtonLoader = false
        console.log(error, "got error");
        this.successModalStyle = 'alert alert-danger'
        this.successModal.show()
        this.timeoutMethod();
    })
  }

  getRoomslist(data: any) {

    let modifyData = {
        start_date: data.start,
        end_date: data.end,
        room_category_id: data.roomCategoryDetails.id
    }

    console.log(modifyData);

    this.BS.getAvailableRooms(modifyData).subscribe((response) => {
        if (response.status === "success") {
            this.roomsList = response.data;
            console.log(this.roomsList, "got rooms list");
        }
    }, (error) => {
        console.log("got error", error);
    })
  }

  timeoutMethod() {
    setTimeout(() => {
        this.selectedBooking = null;
        this.roomsList = null;
        this.customerTypeId = null;
        this.transferRoomId = null;
        this.acceptErrorMessage = ''
        this.successModal.hide()
        this,this.viewDetailsModal.hide();
    }, 1500);
  }

  acceptmodal(booking: any) {
    this.acceptBookingData = booking;
    this.resendPaymentLink = (this.selectedBooking.active && this.selectedBooking.bookingPaymentData.length == 0) ? true : false;
    this.acceptBookingModal.show()
  }

  canclemodal() {    
    if (this.selectedBooking.bookingPaymentData.length > 0) {
      this.cancelId = this.selectedBooking.bookingPaymentData[0].id
    } else {
      this.cancelId = this.selectedBooking.id
    }
    console.log('Canceling booking with id:', this.cancelId);
    this.cancelBookingRequest.show();
  }

  viewDocumentProofs(img: any) {
    console.log(this.imageUrls, 'before');
    for (const url of img) {
      this.imageUrls.push(url)
    }
    console.log(this.imageUrls, 'after');
    console.log(this.selectedPayment, 'this.selectedPaymentthis.selectedPayment');
    
    if (this.selectedPayment && this.selectedPayment.room_detail.guestFiles.length > 0) {
      console.log(this.imageUrls, 'last');
      this.imageUrls.push(this.selectedPayment.room_detail.guestFiles[0])
    }
    
    this.viewDocumentModals.show();
  }

  closeDocumentModal() {
    this.viewDocumentModals.hide();
    this.viewDetailsModal.hide();
    this.imageUrls = [];
    setTimeout(()=>{
        this.viewDetailsModal.show();
    },500)
  }

  closeAcceptBooking() {
    this.acceptBookingModal.hide()
    this.viewDetailsModal.hide();
    setTimeout(()=>{
      this.viewDetailsModal.show();
    },500)
  }

  closeRejectBooking() {
    this.cancelBookingRequest.hide()
    this.viewDetailsModal.hide();
    setTimeout(()=>{
      this.viewDetailsModal.show();
    },500)
  }

  gobackModel() {
    this.viewDetailsModal.hide();
    this.selectedBooking = null;
    this.transferRoomId = null;
    this.customerTypeId = null;
    this.showReferenceUserField = true
  }

  /**
   * show booking details
   *
   * @param {any} item booking object
   * @memberof ReservationsComponent
   */
  showBookingDetails(item) {
    let items: any[] = [];
    // console.log("this.DateRangesWithBookingArray : ",this.DateRangesWithBookingArray);
    this.DateRangesWithBookingArray.forEach(category => {
      category.room.forEach(room => {
        for (let i = 0; i < room.dates.length; i++) {
          if (item.is_common_room_category && room.dates[i].isCommonRoom) {
            if (room.dates[i].booking_id && (<any[]>room.dates[i].booking_id).indexOf(item.booking_id) >= 0) {
              let data = _.cloneDeep(room.dates[i]);
              // console.log("HEREEEE : ",room);
              // console.log("ID to be found : ",item.booking_id);
              data.booking_id = item.booking_id;
              items.push(data);
              break;
            }
          } else {
            if (room.dates[i].booking_id && item.booking_id == room.dates[i].booking_id) {
              items.push(room.dates[i]);
              break;
            } else if (room.dates[i].hasOwnProperty('extraBooking') && room.dates[i]['extraBooking'].booking_id == item.booking_id) {
              items.push(room.dates[i]['extraBooking']);
              break;
            }
          }
        }
      });
    });
    this.viewBooking = this.BS.viewBookingDetails(item.booking_id)
      .subscribe((res) => {
        console.log(localStorage.getItem('avialablebill'), typeof (localStorage.getItem('avialablebill')), "localStorage.getItem('avialablebill')");

        //  this.AvailablePermission = localStorage.getItem('avialablebill')
        //   if (res.data.bookingRoom[0]['current_status']  != 'reserved' ) {
        //       this.ShowDownloadReceptButton = true
        //       console.log(res.data.bookingRoom[0]['current_status']  , "res.data.bookingRoom[0]['current_status']");

        //   }else if(this.AvailablePermission == 'true'  && res.data.bookingRoom[0]['current_status']  == 'reserved'){
        //     console.log("check");

        //     this.ShowDownloadReceptButton = true
        //   }else if(res.data.bookingRoom[0]['current_status']  == 'reserved' && this.AvailablePermission == null){
        //     this.ShowDownloadReceptButton = false
        //     console.log("nnnnnnnnnnn");

        //   }



        //   console.log();



        let bookingRooms: any[] = _.cloneDeep(res.data.bookingRoom);
        let hasEarlyCheckInCharges = 0;
        items.forEach(item => {
          for (let j = 0; j < bookingRooms.length; j++) {
            item['booking_details'] = _.cloneDeep(res.data);
            item['action_type'] = "view";
            if (item.room_id == res.data.bookingRoom[j].room_id && item.booking_id == res.data.bookingRoom[j].booking_id) {
              if (res.data.bookingRoom[j].early_checkin_charge) {
                hasEarlyCheckInCharges += res.data.bookingRoom[j].early_checkin_charge;
              }
              // console.log("HEYYYYYYYYYYYYYY",res.data.bookingRoom[j]);
              // console.log("Itemmmmm : ",item.booking_details.ExtraGuest);
              item.booking_details.bookingRoom = res.data.bookingRoom[j];
              if (item.booking_details.ExtraGuest) {
                item.booking_details.ExtraGuest.forEach(extraGuest => {
                  if (extraGuest['BookingRooms.room_id'] == item.booking_details.bookingRoom.room_id) {
                    item.booking_details.bookingRoom.adult += extraGuest['BookingRooms.adult'];
                    item.booking_details.bookingRoom.child += extraGuest['BookingRooms.child'];
                  }
                });
              }
              break;
            }
          }
        })
        if (res.status == "success") {
          items[0]['totalEarlyCheckInCharge'] = hasEarlyCheckInCharges;
          let data = {
            action_type: 'view',
            data: items,
            start: (<string>items[0].start).indexOf('T00:00:00.000Z') > -1 ? moment(items[0].start).format('DD MMM') : items[0].start,
            end: (<string>items[0].end).indexOf('T00:00:00.000Z') > -1 ? moment(items[0].end).format('DD MMM') : items[0].end
          };
          this.addNewTab("View", _.cloneDeep(data));
          console.log('3414',data);

        }
      })
  }
  printBill(booking_id: any) {
    this.printBillService = this.BS.getBookingBillDetails(booking_id).subscribe(res => {
      let data = res.data;
      // console.log("Bill Response : ", res.data);
      let adult = 0; let child = 0;
      let hasEarlyCheckInCharge = 0;
      for (let i = 0; i < data.bookingRoom.length; i++) {
        adult += data.bookingRoom[i].adult;
        child += data.bookingRoom[i].child;
        for (let j = 0; j < data.bookingRoomCharge.length; j++) {
          if (data.bookingRoom[i].id == data.bookingRoomCharge[j].booking_room_id) {
            if (data.bookingRoom[i].early_checkin_charge) {
              hasEarlyCheckInCharge += data.bookingRoom[i].early_checkin_charge;
            }
            data.bookingRoom[i]['bookingCharges'] = data.bookingRoomCharge[j];
            break;
          }
        }
      }
      // console.log("HEyyyyyyyyyyyyyyyyyyyyyy",data);
      data['booking_details'] = data;
      data['early_check_in_charges'] = hasEarlyCheckInCharge;
      data['adult'] = adult;
      data['child'] = child;
      data['action_type'] = "invoice";
      for (let i = 0; i < data.fundsList.length; i++) {
        data.fundsList[i]['amount'] = 0;
        if (data.bookingFund.length > 0) {
          for (let j = 0; j < data.bookingFund.length; j++) {
            if (data.fundsList[i].id == data.bookingFund[j]['fund_id']) {
              data.fundsList[i]['amount'] += data.bookingFund[j]['amount'];
            }
          }
          if (data.fundsList[i]['id'] == data.defaultFund.fund_id) {
            // console.log("Default Fund : ", (data.paidAmount - data.fundAmount));
            data.fundsList[i]['amount'] += (data.paidAmount - data.fundAmount);
          }
        }
        else if (data.fundsList[i].id == data.defaultFund.fund_id) {
          data.fundsList[i]['amount'] = data.paidAmount;
        }
      }
      this.invoiceDataForView = data;
      // console.log("this.invoiceDataForView : ", this.invoiceDataForView);
      this.invoiceModal.show();
      // this.addNewTab("In-Voice", data);
    })
  }

  getHtml(bookingId: number) {
    let data = {
      data: this.invoiceHtml.nativeElement.innerHTML
    }
    this.BS.saveHtmltoPDF(bookingId, data).subscribe((res) => {
      console.log(bookingId, data, ">>>>>>>>>>>>>");

      if (res) {
        this.PDFData = null;
        let file: any = new Blob([res], { type: 'application/pdf' });
        let url = window.URL.createObjectURL(file)
        // console.log(url);
        this.pdfURL = url;
        const reader = new FileReader();

        reader.readAsDataURL(file);
        reader.addEventListener('loadend', (e: any) => {
          let PDFData = reader.result;
          var winparams = `dependent=yes,locationbar=no,scrollbars=yes,menubar=yes,resizable,screenX=50,screenY=50,width=850,height=1050`;
          var htmlPop = `<embed width=100% height=100% type="application/pdf" src="${PDFData}"></embed>`;
          this.printWindow = window.open("", "PDF", winparams).document.write(htmlPop);
        });
        // Start reading the blob as text.
      }

    })
  }
  PaymentReceipt: boolean = true;

  ViewBillPopUpPDF(data) {
    this.getHtml2(data.booking['id'])
  }


  getHtml2(bookingId: number) {
    let data = {
      data: ""
    }
    this.acceptButtonLoader = true;
    this.BS.saveHtmltoPDF(bookingId, data).subscribe((res) => {
      if (res) {
        this.acceptButtonLoader = false;
        this.PDFData = null;
        let file: any = new Blob([res], { type: 'application/pdf' });
        console.log(File, ">>>>>>>>");

        let url = window.URL.createObjectURL(file)
        console.log(url, ">>>>>>>>");
        // console.log(url);
        this.pdfURL = url;
        const reader = new FileReader();

        reader.readAsDataURL(file);
        reader.addEventListener('loadend', (e: any) => {
          let PDFData = reader.result;
          var winparams = `dependent=yes,locationbar=no,scrollbars=yes,menubar=yes,resizable,screenX=50,screenY=50,width=850,height=1050`;
          var htmlPop = `<embed width=100% height=100% type="application/pdf" src="${PDFData}"></embed>`;
          // console.log("print------->", htmlPop)
          this.printWindow = window.open("", "PDF", winparams).document.write(htmlPop);
        });
        // Start reading the blob as text.
      }

    })
  }
  /**
   *
   * helper function to disable anble functionality of context menu options
   * @memberof ReservationsComponent
   */
  public checkedInEnabled(item: any): boolean {
    return (item[0].booking_status !== 'maintenance' &&
      item[0].booking_status === 'reserved' && item[0].booking_status !== 'checkout' &&
      ((!item[0].is_outdated && item[0].fulldate == moment().format('YYYY-MM-DD')) ||
        (item[0].is_outdated && item[0].is_ending && moment(moment().format()).diff(moment(item[0].fulldate), 'days') == 1)))
      ||
      (item[0].extraBooking && item[0].extraBooking.booking_status === 'reserved' && item[0].extraBooking.booking_status !== 'checkout' &&
        ((!item[0].extraBooking.is_outdated && item[0].extraBooking.fulldate == moment().format('YYYY-MM-DD')) ||
          (item[0].extraBooking.is_outdated && item[0].extraBooking.is_ending && moment(moment().format()).diff(moment(item[0].extraBooking.fulldate), 'days') == 1)));
  }
  public checkedInEnabledCommonRoom(item: any): boolean {
    return (item.booking_status === 'reserved' && !item.is_outdated && item.fulldate == moment().format('YYYY-MM-DD'));
  }
  public checkedEdit(item: any): boolean {
    return (item.booking_status === 'checkin');
  }
  public checkedOutEnabled(item: any): boolean {
    return ((item[0].booking_status === 'checkin' && item[0].booking_status !== 'checkout') ||
      (item[0].hasOwnProperty('extraBooking') && (item[0].extraBooking.current_status == 'checkin' || item[0].extraBooking.booking_status == 'checkin')));
  }
  public roomCheckedOutEnabled(item: any): boolean {
    return (item[0].booking_status === 'checkin' && !item[0].hasOwnProperty('extraBooking'));
  }
  public canMarkRoomActive(item: any): boolean {
    return (item[0].booking_status === 'maintenance');
  }
  public commonRoomCheckOutEnabled(item: any): boolean {
    return (item.booking_status === 'checkin' && item.endFullDate == moment().format('YYYY-MM-DD') + 'T00:00:00.000Z');
  }
  public enableBillPrint(item: any): boolean {
    return item[0].booking_status === 'reserved' || item[0].booking_status === 'checkin';
  }
  public canChangeCustomerType(item: any) {
    return ((item[0].booking_status === 'reserved' || item[0].booking_status === 'checkin') && !item[0].is_outdated && item[0].is_admin === 'true');
  }
  public enableBillPrintCommonRoom(item: any): boolean {
    return item.booking_status === 'checkout';
  }
  public paymentUpdateEnabled(item: any): boolean {
    return (item[0].booking_status !== 'maintenance' && item[0].booking_status !== 'checkout' && item[0].booking_status !== 'block');
  }
  public enableGuest(item: any): boolean {
    return (item[0].booking_status === 'checkin') || (item[0].hasOwnProperty('extraBooking') && item[0].extraBooking.booking_status === 'checkin');
  }
  public enableGuestCommonRoom(item: any): boolean {
    return (item.booking_status === 'checkin');
  }
  public enableAddGuest(item: any): boolean {
    return (item[0].booking_status === 'checkin' && !item[0].is_outdated);
  }
  public enableNoShow(item: any): boolean {
    // return true;
    return (item[0].booking_status === 'reserved' && !item[0].is_outdated && moment(moment(item[0].start).set('year', moment(item[0].fulldate).get('year'))).isSame(moment(), 'day'));
  }
  public enableSplitBooking(item: any): boolean {
    return (item[0].booking_status !== 'maintenance' && item[0].booking_status !== 'checkout' && item[0].duration >= 24 && item[0].booking_status !== 'block')
  }

  public enableTransferBooking(item: any): boolean {
    return (item[0].booking_status !== 'maintenance' && item[0].booking_status !== 'checkout' && item[0].booking_status !== 'block')
  }
  public enableRoomCleaning(item: any): boolean {

    return (item[0].booking_status != 'checkin')
  }
  public enableCancel(item: any): boolean {
    return (item[0].booking_status === 'reserved' && !item[0].is_outdated);
  }
  public displayCancel = (item: any): boolean => {
    return  (item[0].booking_status !== 'maintenance' && !item[0].isCommonRoom && !this.tempBookingObjArray) && (  item[0].is_admin === "true")
  }
  public isNormalRoom = (item: any): boolean => {
    return (item[0].booking_status !== 'maintenance' && !item[0].isCommonRoom && !this.tempBookingObjArray);
  }
  public showViewCommonRoomBookings(item: any) {
    return (item[0].booking_status !== 'maintenance' && item[0].isCommonRoom && item[0].booking_status);
  }
  public overBooking = (item: any): boolean => {
    return (item[0].booking_status !== 'maintenance' && !item[0].is_disabled && this.tempBookingObjArray && !item[0].isCommonRoom);
  }
  public enableExtraBooking = (item: any): boolean => {
    return (!item[0].hasOwnProperty('extraBooking') && item[0].is_starting && !item[0].is_disabled && this.tempBookingObjArray && !item[0].isCommonRoom);
  }
  showCheckoutAlert(item: any) {
    return item.booking_status === 'checkin' && item.is_ending && (moment(item.fulldate)).diff(moment(moment().format('YYYY-MM-DD')), 'days') == 0;
  }
  shouldBeVisible = (item: any) => {
    return (this.tempBookingObjArray && item[0].booking_status && !item[0].is_outdated);
  }
  commonRoomBooking = (item: any) => {
    return (item[0].booking_status !== 'maintenance' && item[0].isCommonRoom && item[0].booking_status && !item[0].is_outdated);
  }

  preProcessRoomCheckOut(item) {
    this.BS.getInvidualRoomCheckout(item.booking_id, item.room_id).subscribe((res) => {
      if (res.status === 'success') {
        item['is_early_checkout'] = res.data.is_early_checkout;
        this.modalAlertType = 'checkout';
        this.showModalAlert('checkout', item);
      }
    });
  }
  processRoomCheckOut(item) {
    this.BS.individualRoomCheckout(item.booking_id, { room_id: item.room_id, is_early_checkout: item.is_early_checkout })
      .subscribe((res) => {
        if (res.status === 'success') {
          this.fillData();
        }
      });
  }
  /**
   *
   * @param item
   */
  updatePayment(item) {

     
    //console.log("Initiated payment update...", item);
    //console.log(item.payments);
    //this.paidAdvanceAmount = item.


    this.preProcessCheckOut(item, 'payment');
  }


  resetFlagForCheckIn() {
    this.reservation_S.resetFlagForCheckIn().subscribe((data) => {

    }, (error) => {
      console.log('ERROR:: ' + error);
    });
  }

  /**
   * make checkIn process on reserved booking
   *
   * @param {any} item
   * @memberof ReservationsComponent
   */
  processCheckIn(item, flag, allGuestStatusAddedStatus?) {
    this.dynamicCheckinSuccess = false;
    if (this.earlyCheckInTime) {
      // let doorLockData = {
      //   GuestSN: 123456,
      //   GuestIdx: 789456,
      //   DoorID: "abcdef",
      //   EndTime: "1810110618",
      //   BeginTime: "1810110618"
      // };
      // this.BS.readCard(doorLockData).subscribe((doorLockRes) => {
      //   if (doorLockRes.readCardResult.CardReadStatus) {
      // this.showNotification("Guest Card Read Successfully","success");
      this.bookingDateGet = this.BS.getBookingDateDetails(item.booking_id)
        .subscribe((bookingRoomDate) => {
          let current = moment().format();
          let earlyCheckInMoment = moment(this.earlyCheckInTime).format();
          let checkIn = moment().format('YYYY-MM-DD') + earlyCheckInMoment.substr(earlyCheckInMoment.indexOf('T'));
          let data = { is_early_check_in: moment(current).isBefore(moment(checkIn)) };
          let bookingStart;
          if (bookingRoomDate.data.bookingRoom) {
            bookingStart = moment(bookingRoomDate.data.bookingRoom.start).format('YYYY-MM-DD');
            if (moment(moment().format('YYYY-MM-DD')).isBefore(bookingStart, 'day')) {
              data['is_early_check_in'] = true;
            }
          }

          if (this.checkInData && this.checkInData['doorId'] && this.checkInData['doorId'] != '') {
            //Popup code Start

            this.reservation_S.getCheckInData(this.checkInData).subscribe((data) => {
              if (data['status'] == true || data['status'] == 'true') {

                this.showData = true;
                this.showError = false;
                this.dynamicCheckinSuccess = data.status;
                this.dynamicCheckinResponseMessage = data.message;

                this.checkIn = this.BS.executeCheckIn(item.booking_id, item.room_id, data)
                  .subscribe((res) => {
                    if (res.status == "success") {
                      this.DateRangesWithBookingArray.forEach((element) => {
                        element.room.forEach(element1 => {
                          element1.dates.forEach(ele => {
                            if (ele.booking_id === item.booking_id) {
                              // this.wasCheckIn.indexOf(item.booking_id) >= 0 ? null : this.wasCheckIn.push(item.booking_id);
                              if (allGuestStatusAddedStatus) {
                                ele.all_guest_details_added = true;
                                this.allBookings[_.findIndex(this.allBookings, ['booking_id', item.booking_id])].all_guest_details_added = allGuestStatusAddedStatus;
                                this.originalAllBookings[_.findIndex(this.originalAllBookings, ['booking_id', item.booking_id])].all_guest_details_added = allGuestStatusAddedStatus;
                              }
                              ele.booking_status = "checkin";
                              this.originalAllBookings[_.findIndex(this.originalAllBookings, ['booking_id', item.booking_id])].current_status = 'checkin';
                            } else if (ele.hasOwnProperty('extraBooking') && item.booking_id === ele.extraBooking.booking_id) {
                              if (allGuestStatusAddedStatus) {
                                ele.extraBooking.all_guest_details_added = true;
                                this.extraBookings[_.findIndex(this.extraBookings, ['booking_id', item.booking_id])].all_guest_details_added = allGuestStatusAddedStatus;
                              }
                              ele.extraBooking.booking_status = "checkin";
                              this.extraBookings[_.findIndex(this.extraBookings, ['booking_id', item.booking_id])].current_status = 'checkin';
                            }
                          });
                        });
                      })
                      if (flag) {
                        setTimeout(() => {
                          let index2 = 0;
                          this.staticTabs.tabs.forEach((ele, index) => {
                            if (ele.active == true) {
                              index2 = index;
                            }
                          })
                          // this.staticTabs.tabs.splice(index2, 1);
                          this.removeTabHandler(this.staticTabs.tabs[index2])
                          this.staticTabs.tabs[0].active = true;
                        })
                      }
                      this.removeSelection();
                    }
                  }, (err) => {
                    this.addGuestInfo(item, 'checkIn', true);
                  })

              } else if (data['status'] == false || data['status'] == 'false') {
                this.dynamicCheckinSuccess = true;
                this.dynamicCheckinResponseMessage = data.message;
                this.showData = true;
                this.showError = false;
              }
            }, (error) => {
              //Failure -> TODO : Show respective error to the user and do not close popup.
              this.showData = false;
              this.showError = true;
              // console.log('ERROR:: ' + error);
            });

            //Popup Code

          } else {
            this.checkIn = this.BS.executeCheckIn(item.booking_id, item.room_id, data)
              .subscribe((res) => {
                if (res.status == "success") {
                  this.DateRangesWithBookingArray.forEach((element) => {
                    element.room.forEach(element1 => {
                      element1.dates.forEach(ele => {
                        if (ele.booking_id === item.booking_id) {
                          // this.wasCheckIn.indexOf(item.booking_id) >= 0 ? null : this.wasCheckIn.push(item.booking_id);
                          if (allGuestStatusAddedStatus) {
                            ele.all_guest_details_added = true;
                            this.allBookings[_.findIndex(this.allBookings, ['booking_id', item.booking_id])].all_guest_details_added = allGuestStatusAddedStatus;
                            this.originalAllBookings[_.findIndex(this.originalAllBookings, ['booking_id', item.booking_id])].all_guest_details_added = allGuestStatusAddedStatus;
                          }
                          ele.booking_status = "checkin";
                          this.originalAllBookings[_.findIndex(this.originalAllBookings, ['booking_id', item.booking_id])].current_status = 'checkin';
                        } else if (ele.hasOwnProperty('extraBooking') && item.booking_id === ele.extraBooking.booking_id) {
                          if (allGuestStatusAddedStatus) {
                            ele.extraBooking.all_guest_details_added = true;
                            this.extraBookings[_.findIndex(this.extraBookings, ['booking_id', item.booking_id])].all_guest_details_added = allGuestStatusAddedStatus;
                          }
                          ele.extraBooking.booking_status = "checkin";
                          this.extraBookings[_.findIndex(this.extraBookings, ['booking_id', item.booking_id])].current_status = 'checkin';
                        }
                      });
                    });
                  })
                  if (flag) {
                    setTimeout(() => {
                      let index2 = 0;
                      this.staticTabs.tabs.forEach((ele, index) => {
                        if (ele.active == true) {
                          index2 = index;
                        }
                      })
                      // this.staticTabs.tabs.splice(index2, 1);
                      this.removeTabHandler(this.staticTabs.tabs[index2])
                      this.staticTabs.tabs[0].active = true;
                    })
                  }
                  this.removeSelection();
                }
              }, (err) => {
                this.addGuestInfo(item, 'checkIn', true);
              })
          }


        }, (err) => {
          this.showNotification("Failed to checkin, please try again!", "error");
        });
    }
    else {
      this.showNotification("Please add a early-check-time in default settings", "error");
    }
  }
  initCheckInForm(data) {
    this.checkInProcessform = this._fb.group({
      name: [],
      contact: [],
      email: [],
      proof_type: ['0'],
      abletocheckin: false,
      booking_id: [data.booking_id],
      room_id: [data.room_id],
      proof: []
    })
    this.checkInProcessform.patchValue(data.user_data);
    this.checkInProcessform.controls['proof_type'].patchValue('0');
  }
  proofTypeChanged(event) {
    // console.log("ProofTypeChanged : ", event);
    this.checkInProcessform.controls['proof_type'].patchValue(event.id);
  }
  searchTypeChanged(event, j) {
    let guest = <FormGroup>this.bookingForm.controls['guest'];
    let currentIndex = <FormGroup>guest.controls[j];
    currentIndex.get('search_type').patchValue(event.id);
    this.resetCustomerList();
    this.showCustomerList = false;
  }

  getList(event, j) {
    let selectedGuests = [];
    let guest = <FormArray>this.bookingForm.controls['guest'];
    let searchType = guest.value[j].search_type;
    guest.controls.forEach(guestI => {
      if (guestI.value.id) {
        selectedGuests.push(guestI.value.id);
      }
    });
    if (event.target.value.length > 3) {
      let searchValue = { text: event.target.value, ids: selectedGuests, type: searchType };
      this.BS.getCustomerList(searchValue).subscribe(res => {
        this.customerList = res.data;
        this.showCustomerList = true;
        this.selectedCustormerFormIndex = j;
      });
    }
    else if (this.customerList.length > 0 && (event.target.value.length <= 3)) {
      this.resetCustomerList();
    }
    else {
      // console.log("Nothing Happenedddddddddd");
    }

  }
  guestProofTypeChanged(event: any, index: any) {
    // console.log("Guest is changeddddddddddddddddddddddddddd : ", event);
    let guestArray = <FormArray>this.guestForm.controls['guests'];
    let guest = <FormGroup>guestArray.controls[index];
    if (event.value == '5') {
      guest.addControl('other_proof_type', new FormControl('', Validators.required));
    }
    else if (guest.get('other_proof_type')) {
      guest.removeControl('other_proof_type');
    }
    guest.controls['proof_type'].patchValue(event.value);
    // console.log("this.guestForm.value : ", this.guestForm.value);

  }
  guestCountryChanged(event: any, index: any) {
    let guestArray = <FormArray>this.guestForm.controls['guests'];
    let guest = <FormGroup>guestArray.controls[index];
    guest.controls['country'].patchValue(event.id);
    // console.log("event : ", event);
    // console.log("form values : <", this.guestForm.value);
  }

  viewUploadedImg(item: any) {
      this.imageData = item;
      this.imageData.url = item.url;
      let mimeTypeList: any[] = item ? item.mimetype.split('/') : [];
      this.imageData['isImage'] = mimeTypeList.indexOf('application') >= 0 ? false : true;

    // console.log("mimeTypeList : ",mimeTypeList);
    // console.log("Item : ", this.imageData);
    this.imageModal.show();

  }
  viewDocImg(item: any) {
    const getImageModal = document.getElementById('docModel');
    getImageModal.classList.add('in', 'show');
    this.imageUrl = item;
    // this.imageData = {
    //   isImage : true,
    //   proof:{url:item}

    // }

    // console.log("mimeTypeList : ",mimeTypeList);
    // console.log("Item : ", this.imageData);
    // this.docImageModal.show();
  }
  hideDocModel(){
    const getImageModal = document.getElementById('docModel');
    getImageModal.classList.remove('in', 'show');
  }
  initImageUploadEvents() {
    this.uploader.onWhenAddingFileFailed = (item: any, filter: any, option: any) => {
      this.showNotification(`${item.type} format is not supported!`, "error", true, 5);
    };
    this.uploader.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
      //things to do on completion
      if (response) {
        let res = JSON.parse(response);
        this.addUploadedImages(res);
      }
    };
    this.uploader.onBeforeUploadItem = (item: any) => {
      // image uploaded - add token of auth
      let token = this.authGuard.ud.session_id;
      let timestamp = (+ new Date()).toString();
      let generatedToken = CryptoJS.AES.encrypt(token,
        _secretKey_auth);
      this.uploader.authToken = generatedToken;
    }
  }
  /**
   * save user details with proof upload
   *
   * @memberof ReservationsComponent
   */
  saveProofs(guest_id) {
    if (this.checkInProcessform.valid) {
      this.saveProof = this.BS.saveCustomerDetailsForCheckIn(guest_id, this.checkInProcessform.value)
        .subscribe((res) => {
          if (res.status == "success") {
            this.checkInProcessform.controls['abletocheckin'].patchValue(true);
          }
        })
    }
  }
  preRequestBookingGuestTypeChange(item) {
    this.checkForExtraBooking(item).then((res: any) => {
      this.requestBookingGuestTypeChange(res);
    }).catch((err) => { });
  }
  requestBookingGuestTypeChange(item: any) {
    this.isCheckoutReference = false;
    this.initializeCheckoutDetails();
    this.bookingCheckoutDetails = item;
    this.bookingCheckoutDetailsType = 'customer-type-change';
    this.selectedCustomerType = item.customer_id;
    this.changeCustomerTypeModal.onHidden.subscribe(() => {
      this.selectedReferenceUserCustomerTyepChange = [];
      if (this.bookingCheckoutDetailsType === 'customer-type-change' && this.refundAmount) {
        this.showRefundAmountAlert();
      }
      this.customerTypeChangeFormReset();
      this.initializeCheckoutDetails();
    });
    this.customerTypeChangeForm.controls['customer_id'].patchValue(item.customer_id);
    this.isReferenceNeccessary = this.checkIfReferenceIsNeccessary();
    this.isNoteNeccessary = this.checkIfNoteIsNeccessary();
    // Pop-up for changing customer type before checkout
    this.changeCustomerTypeModal.show();
  }
  showRefundAmountAlert() {
    this.refundAlertSubscription = this.refundAlert.onHidden.subscribe(() => {
      this.unsubscribeRefundAlert();
      this.refundAmount = undefined;
      this.refundAmountCause = undefined;
    });
    this.refundAlert.show();
  }
  changeBookingGuestType() {
    if (this.bookingCheckoutDetails) {
      let paymentDate = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';
      this.customerTypeChangeForm.get('customer_id').patchValue(this.selectedCustomerType);
      if (this.customerTypeChangeForm.value.reference_id === '000000') {
        this.customerTypeChangeForm.get('reference_id').patchValue('');
      }
      this.sub = this.BS.changeBookingGuestType(this.bookingCheckoutDetails.booking_id, this.customerTypeChangeForm.value, paymentDate)
        .subscribe((res) => {
          if (res.status === 'success') {
            this.changeCustomerTypeModal.hide();
            let customerName = this.customerTypeListMain[this.findIndex(parseInt(this.selectedCustomerType), "id", this.customerTypeListMain) - 1].name;
            let stayTypeId = res.data.upgradedStayType ? res.data.stay_type_id : null;
            this.updateBookingTypeInfoOnCalendar(this.bookingCheckoutDetails.booking_id, this.selectedCustomerType, customerName, stayTypeId);
            if (res.data.returnAmount) {
              this.refundAmount = res.data.returnAmount;
              this.refundAmountCause = 'customer-type-change';
            }
          }
        });
    }
  }
  unsubscribeRefundAlert() {
    if (this.refundAlertSubscription) {
      this.refundAlertSubscription.unsubscribe();
    }
  }
  processCheckInAgain() {
    let data = {
      room_id: this.checkInProcessform.controls['room_id'].value,
      booking_id: this.checkInProcessform.controls['booking_id'].value
    }
    this.processCheckIn(data, true);
    this.checkInProcessform.reset();
  }
  processSplitBookingAgain() {
    let data = {
      room_id: this.checkInProcessform.controls['room_id'].value,
      booking_id: this.checkInProcessform.controls['booking_id'].value
    }
    this.processCheckIn(data, true);
    this.checkInProcessform.reset();
  }
  checkInAfterGuestSaved(item: any, allGuestdetailsAddedStatus) {
    // console.log("guestForm : ", item);
    let data = {
      room_id: item.room_id,
      booking_id: item.booking_id
    }
    this.processCheckIn(data, true, allGuestdetailsAddedStatus);
    // this.checkInProcessform.reset();
  }
  /**
     * when image has been uploaded but not saved, images will be list out to form control
     * @param {any} res : response - response from image upload api
     */
  addUploadedImages(res) {

    let control = this.checkInProcessform.controls['proof'];
    control.setValue({
      mimetype: res.data.mimetype,
      extension: res.data.extension,
      oldName: res.data.oldName,
      originalName: res.data.originalName,
      size: res.data.size,
      uploaded: res.data.uploaded,
      is_deleted: res.data.is_deleted,
      is_doc: res.data.is_doc
    })
  }
  cancelCheckout() {
    this.changeCustomerTypeModal.hide();
  }
  initializeCheckoutDetails() {
    this.selectedCustomerType = undefined;
    this.bookingCheckoutDetails = undefined;
    this.bookingCheckoutDetailsType = undefined;
  }
  checkoutAfterCustomerChange() {
    // console.log(this.customerTypeChangeForm.value)
    if (this.customerTypeChangeForm.valid) {
      // console.log("cust type------", this.bookingCheckoutDetailsType)
      if (this.bookingCheckoutDetailsType === 'customer-type-change') {
        this.changeBookingGuestType();
      } else {
        this.proceedToCheckoutOrPayment(this.bookingCheckoutDetails, this.bookingCheckoutDetailsType);
        this.changeCustomerTypeModal.hide();
      }
    } else {
      for (let key in this.customerTypeChangeForm.controls) {
        this.customerTypeChangeForm.controls[key].markAsDirty();
        this.customerTypeChangeForm.controls[key].markAsTouched();
      }
    }
  }
  checkoutCustomerTypeChanged(event: any) {
    console.log(event);
    
    if (this.bookingCheckoutDetails) {
      this.selectedCustomerType = event.id;
      this.referenceUserCustomerTypeChange = this.getFilteredReferences(event.id);
      console.log(this.referenceUserCustomerTypeChange);
      
      this.referenceUserCustomerTypeChange.sort(function (x, y) {
        let a = x.text.toUpperCase(),
          b = y.text.toUpperCase();
        return a == b ? 0 : a > b ? 1 : -1;
      });
      console.log(this.referenceUserCustomerTypeChange);

      this.referenceUserCustomerTypeChange.unshift({ id: "000000", text: 'Please select a reference type' });
      console.log(this.referenceUserCustomerTypeChange);

      this.isReferenceNeccessary = this.checkIfReferenceIsNeccessary();
      this.isNoteNeccessary = this.checkIfNoteIsNeccessary();
      this.selectedReferenceUserCustomerTyepChange = this.referenceUserCustomerTypeChange[0].id;
    }
  }
  preCheckoutReferenceTypeChanged(event: any) {
    if (this.customerTypeChangeForm && this.customerTypeChangeForm.get('reference_id')) {
      this.customerTypeChangeForm.controls['reference_id'].patchValue(event.id);
    }
  }
  checkIfNoteIsNeccessary() {
    this.customerTypeChangeForm.removeControl('note');
    if (!this.customerTypeListMain) {
      return false;
    }
    let index = this.findIndex(parseInt(this.selectedCustomerType), "id", this.customerTypeListMain) - 1;
    if (index > -1) {
      let isNoteNeccessary = this.customerTypeListMain[index].is_comment_necessary;
      if (isNoteNeccessary && !this.customerTypeChangeForm.get('note')) {
        if (this.customerTypeListMain[index].id == 3) {
          this.customerTypeChangeForm.addControl('note', new FormControl('', []));
        } else {
          this.customerTypeChangeForm.addControl('note', new FormControl('', [Validators.required]));
        }
      } else if (!isNoteNeccessary && this.customerTypeChangeForm.get('note')) {
        this.customerTypeChangeForm.removeControl('note');
      }
      return isNoteNeccessary;
    }
    return false;
  }
  checkIfReferenceIsNeccessary() {
    if (!this.customerTypeListMain) {
      return false;
    }
    let index = this.findIndex(parseInt(this.selectedCustomerType), "id", this.customerTypeListMain) - 1;
    if (index > -1) {
      let isReferenceNeccessary = this.customerTypeListMain[index].is_reference_necessary;
      if (isReferenceNeccessary && !this.customerTypeChangeForm.get('reference_id')) {
        this.customerTypeChangeForm.addControl('reference_id', new FormControl('', [Validators.required, CustomValidators.notEqual('000000')]));
      } else if (!isReferenceNeccessary) {
        if (this.customerTypeListMain[index].id == 3) {
          this.customerTypeChangeForm.removeControl('reference_id');
          this.customerTypeChangeForm.addControl('reference_id', new FormControl('', []));
        } else {
          this.customerTypeChangeForm.removeControl('reference_id');
        }
      }
      return isReferenceNeccessary;
    }
    return false;
  }
  inItCustomerTypeChangeForm() {
    this.customerTypeChangeForm = this._fb.group({
      customer_id: ['', Validators.required],
      reference_id: [''],
    });
  }
  preProcessCheckOut(event, type) {
    console.log("ssssssddddddddddddddd");


    this.checkForExtraBooking(event).then((res: any) => {
      if (type == 'payment') {
        console.log(1)
        this.processCheckOut(res, type);
      }
      else if (res.booking_status == 'checkin') {
        console.log(2)
        this.processCheckOut(res, type);
      } else {
        this.showNotification('Please check-in first', 'error');
      }
    }).catch((err) => { });
  }
  /**
   * make checkout process on cehcked in bookings
   *
   * @param {any} item
   * @memberof ReservationsComponent
   */
  processCheckOut(item, type: string) {
    this.isCheckoutReference = false;
    this.initializeCheckoutDetails();
    if (type === 'payment') {
      this.proceedToCheckoutOrPayment(item, type);
    }
    else {
      // code for checking if all default guest names are changed
      this.guestRequiredFieldsCheck = this.BS.guestRequiredFieldsCheck(item.booking_id)
        .subscribe((res) => {
          if (res.status === "success") {
            if (res.data) {
              // this.bookingCheckoutDetails = item;
              // this.bookingCheckoutDetailsType = type;
              // this.selectedCustomerType = item.customer_id;
              // this.changeCustomerTypeModal.onHidden.subscribe(() => {
              //   this.selectedReferenceUserCustomerTyepChange = [];
              //   this.customerTypeChangeFormReset();
              //   this.initializeCheckoutDetails();
              // });
              // this.customerTypeChangeForm.controls['customer_id'].patchValue(this.bookingCheckoutDetails.customer_id);
              // this.isReferenceNeccessary = this.checkIfReferenceIsNeccessary();
              // this.isNoteNeccessary = this.checkIfNoteIsNeccessary();

              // Pop-up for changing customer type before checkout
              // this.changeCustomerTypeModal.show();
              this.proceedToCheckoutOrPayment(item, type);
            }
            else {
              this.addGuestInfo(item, 'checkout');
            }
          }
        }, (err) => {
          this.showNotification("Error occured, please try again!", "error");
        });
    }
  }
  customerTypeChangeFormReset() {
    if (this.customerTypeChangeForm.get('note')) {
      this.customerTypeChangeForm.removeControl('note');
    }
    if (this.customerTypeChangeForm.get('reference_id')) {
      this.customerTypeChangeForm.removeControl('reference_id');
    }
    this.customerTypeChangeForm.reset();
  }
  updateBookingTypeInfoOnCalendar(bookingId, customerId, customerName, stayTypeId) {
    this.DateRangesWithBookingArray.forEach((category) => {
      category.room.forEach((room) => {
        room.dates.forEach((day) => {
          if (day.hasOwnProperty('booking_id') && bookingId == day.booking_id) {
            day.customer_id = customerId;
            day.customer_name = customerName;
            let index = stayTypeId ? this.findIndex(stayTypeId, "id", this.originalStayTypesList) - 1 : -1;
            if (stayTypeId && index > -1) {
              day.duration = this.originalStayTypesList[index].duration;
              day.stay_type = this.originalStayTypesList[index].id;
            }
          } else if (day.hasOwnProperty('extraBooking') && bookingId == day.extraBooking.booking_id) {
            day.extraBooking.customer_id = customerId;
            day.extraBooking.customer_name = customerName;
            let index = stayTypeId ? this.findIndex(stayTypeId, "id", this.originalStayTypesList) - 1 : -1;
            if (stayTypeId && index > -1) {
              day.extraBooking.duration = this.originalStayTypesList[index].duration;
              day.extraBooking.stay_type = this.originalStayTypesList[index].id;
            }
          }
        });
      });
    });
    let allbookingIndex = _.findIndex(this.allBookings, ['booking_id', bookingId]);
    let originalAllBookingsIndex = _.findIndex(this.originalAllBookings, ['booking_id', bookingId]);
    if (allbookingIndex > -1) {
      this.allBookings[allbookingIndex].customer_id = parseInt(customerId);
      this.allBookings[allbookingIndex].customer_name = customerName;
    }
    if (originalAllBookingsIndex > -1) {
      this.originalAllBookings[originalAllBookingsIndex].customer_id = parseInt(customerId);
      this.originalAllBookings[originalAllBookingsIndex].customer_name = customerName;
    }
  }
  proceedToCheckoutOrPayment(item, type: string) {
 
    this.paymentTypeSelection = '0';
    let customerId = this.selectedCustomerType ? this.selectedCustomerType : item.customer_id;
    let currentDate = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';
    // console.log("After Guest type select-----------------------")
    // console.log("customer type-----", this.customerTypeChangeForm.value, "--------", customerId)
    this.getCheckoutDetails = this.BS.getCheckoutDetails(this.customerTypeChangeForm.value, item.booking_id, type, customerId, false, currentDate)
      .subscribe((res) => {

        // console.log("===============",res.data.bookingPayment.length)
        if (res.status == "success") {
          this.apiValue = res.data
          item.customer_id = customerId;
          if (type === 'checkout' && item.customer_id) {
            item.customer_name = this.customerTypeListMain[this.findIndex(parseInt(item.customer_id), "id", this.customerTypeListMain) - 1].name;
            let stayTypeId = res.data.upgradedStayType ? res.data.stay_type_id : null;
            this.updateBookingTypeInfoOnCalendar(item.booking_id, customerId, item.customer_name, stayTypeId);
          }
          // console.log("this.DateRangesWithBookingArray : ", this.DateRangesWithBookingArray, item, this.selectedCustomerType, this.allBookings, this.originalAllBookings);
          item['action_type'] = "checkout";
          item['process_type'] = type;
          item['payments'] = res.data;
          this.fndType = jQuery.map(res.data.fund, function (obj) {
            return { id: obj.id, text: obj.name };
          });
          this.initFinalPaymentForm(item, type);
          if (res.data.defaultFund) {
            this.selectedFundValue = res.data.defaultFund.fund_id;
          } else {
            this.selectedFundValue = this.fndType.length > 0 ? this.fndType[0].id : undefined;
          }
          item['payments']['cardSwipeChargesOriginal'] = JSON.parse(JSON.stringify(item['payments']['cardSwipeCharges']));
          this.paymentAtcheckout.controls['custom_discount'].patchValue(res.data.bookingCustomDiscounts1);
          item['cardPaymentTotal'] = res.data.early_checkin_Charge + res.data.total_payable_amount - (res.data.paidAmount ? res.data.paidAmount : 0) - res.data.bookingCustomDiscounts1;
          this.fundAmount = 0;
          if (item.hasOwnProperty('is_extra_booking')) {
            item['start'] = moment(item.start).format('DD MMM');
            item['end'] = moment(item.end).format('DD MMM');
          }
          // console.log("Payment Date GET : ", item);
          // let needPaymentVerificationId = res.data.early_checkin_Charge + res.data.cardSwipeCharges + res.data.net_amount;
          // if ((type == 'checkout' || type == 'payment') && (needPaymentVerificationId >= 10000)) {
          //   this.paymentAtcheckout.addControl('payment_verification_id', new FormControl(''));
          // }
          this.paidAdvanceAmount=0;
          this.isAdvancePayment = false;
          this.bankTotalPayable = 0;
          for (let i = 0; i < item.payments.bookingPayment.length; i++) {
            if (item.payments.bookingPayment[i].is_advance) {
                // Add the amount to paidAdvanceAmount
                this.paidAdvanceAmount += item.payments.bookingPayment[i].amount;
            }
        }
        console.log(this.paidAdvanceAmount);
        console.log(item);
        //console.log(item.booking_status);

        this.perDayPayment = item.payments.net_amount / item.payments.stay_days;
        this.bankTotalPayable = item.cardPaymentTotal;
        console.log(this.bankTotalPayable , "bank")
        
        if(item.booking_status == "reserved")
        {
          this.isAdvancePayment = true;
        }


        
        //console.log(this.perDayPayment, "latest",this.isAdvancePayment);
        
    

          //console.log(this.paidAdvanceAmount)
          this.addNewTab(type == 'payment' ? "Update Payment" : "Checkout", _.cloneDeep(item));
          console.log('4303',item);
        
          

        }
        // this.updateFunds('splite','','checkout');
        if (type === 'checkout') {
          this.splitAmount('splite', type)
        }
      });
  }
  FundTypeChanges(event) {
    this.selectedFundValue = event.id;
    this.paymentAtcheckout.controls['fund_type'].patchValue(event.id);
  }
  fundTypeEnable(event) {
    //this.checkedData = event.target.checked;
    //console.log("---------------------chedkdsdsdsd data-------------------", this.checkedData);
    this.disableFund = !event.target.checked;
    if (event.target.checked) {
      this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
      this.paymentAtcheckout.controls['fund_type'].patchValue(this.selectedFundValue);
    } else {
      this.paymentAtcheckout.controls['fund_amount'].patchValue(0);
      this.paymentAtcheckout.controls['fund_type'].patchValue(null);
    }
    // console.log("funddata", event.target.checked)
    // console.log("fundamount", this.paymentAtcheckout.controls)
  }

  getPanNo(test) {
    this.panNo = [];
    if (test === "a") {
      if (this.bookingForm.controls['payments'].get('payment_verification_id').value) {

        this.panNo.push(this.bookingForm.controls['payments'].get('payment_verification_id').value);
      }
    } else if (test === "b") {
      if (this.paymentAtcheckout.get('payment_verification_id').value) {
        this.panNo.push(this.paymentAtcheckout.get('payment_verification_id').value);
      }
    }
    // console.log(this.panNo)
  }
  splitAmount(val: string, type: string) {
    this.spliteData = [];
    if (val === 'splite' && type === "checkout") {
      let b = 0;
      this.apiValue.bookingPayment.forEach(element => {
        console.log(element, ">>>>>>>>>>>>>>>>>>>>>>");

        if (element.payment_mode == 0) {
          if (element.is_advance == false) {
            b += +element.amount;
          }
        }
      });

      console.log(this.apiValue.paidAmount, "this.apiValue.paidAmountthis.apiValue.paidAmount");


      if (this.apiValue.is_early_checkout == true) {
        var leftAmount = this.apiValue.paidAmount - this.apiValue.net_amount
        // console.log("leftamount+++", leftAmount);
      }
      if (this.apiValue.is_early_checkout == true && leftAmount > 0) {
        this.paymentAtcheckout.value.payment_amount = +this.paymentAtcheckout.value.payment_amount + +this.apiValue.net_amount;
      } else {
        this.paymentAtcheckout.value.payment_amount = +this.paymentAtcheckout.value.payment_amount + +b;
        if (this.apiValue.returnAmount && this.apiValue.returnAmount > 0) {
          this.paymentAtcheckout.value.payment_amount = this.paymentAtcheckout.value.payment_amount - this.apiValue.returnAmount
        }
      }
      // if (this.paymentAtcheckout.value.payment_amount >= 10000 && this.paymentAtcheckout.get('payment_mode').value == "0") {
      //   this.cardSelected = true
      // } else {
      //   this.cardSelected = false
      // }
      console.log(this.paymentAtcheckout.value.payment_amount, "this.paymentAtcheckout.value.payment_amount");

      let apiData = {
        payment_amount: this.paymentAtcheckout.value.payment_amount,
        payment_verification_id: '',//this.paymentAtcheckout.controls['payment_verification_id'],
        payment_mode: this.paymentAtcheckout.get('payment_mode').value,
        booking_id: this.apiValue.booking_id,
      }
      this.BS.getSpliteData(apiData).subscribe((res) => {
        this.paymentAtcheckout.removeControl('items');
        this.paymentAtcheckout.addControl('items', this._fb.array([]));
        this.cardSelected = false
        if (res.data && res.status == "success") {
          this.spliteData = res.data
          for (let index = 0; index < this.spliteData.length; index++) {
            this.spliteNameCity(res.data[index])
          }
          this.cardSelected = true
        }
        if (res.message === 'Total Number' && this.paymentAtcheckout.get('payment_mode').value == "0") {
          this.paymentAtcheckout.controls['payment_verification_id'].disable();
        } else {
          this.paymentAtcheckout.controls['payment_verification_id'].enable()
        }

      }, (error: any) => {
        if (error) {
          // console.log(error)
        }
      })
    }
  }
  updateFunds(val: string, data?: any, type?: string) {
    // console.log("type ====== 1 checkout", type)
    if (type === 'checkout') {
      this.splitAmount(val, 'checkout');
    }
    // console.log(data, "-------------", type)
    if (type && data) {
      let amount = this.returnAmountValue.nativeElement.innerText === '' ? 0 : parseInt(this.returnAmountValue.nativeElement.innerText);
      // console.log("form valid----------", this.paymentAtcheckout.valid)
      if (amount > 0 && this.paymentAtcheckout.get('payment_mode').value != "1") {

        this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
        this.paymentAtcheckout.controls['fund_type'].patchValue(this.selectedFundValue);
        if (this.fundtypeID && !this.fundtypeID.nativeElement.checked) {
          // console.log("make positive : ",this.fundtypeID);
          this.renderer.setProperty(this.fundtypeID.nativeElement, 'checked', true);
        }
      }
      else {
        this.paymentAtcheckout.controls['fund_amount'].patchValue(0);
        this.paymentAtcheckout.controls['fund_type'].patchValue(null);
        if (this.fundtypeID) {
          this.renderer.setProperty(this.fundtypeID.nativeElement, 'checked', false);
        }
      }
      if (this.paymentAtcheckout.get('pavati_no')) {
        this.highLighValidationsForField('pavati_no');
      }
      this.manipulateReferenceAndNote(type);
      if (this.paymentAtcheckout.get('payment_mode').value == '1') {
        let indx = data.payments.customers.findIndex(x => x.id == parseInt(data.customer_id))
        if (indx > -1) {
          if (data.payments.customers[indx].discount_type && data.payments.customers[indx].discount_value && data.payments.customers[indx].discount_type == "percentage" && data.payments.customers[indx].discount_value == 100) {
            // console.log("free guest---------")
            if (this.paymentAtcheckout.controls['payment_amount'].value < 9) {
              this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, Validators.pattern('[^0-8]|[0-9][0-9]')])
            } else {
            }
          } else {
            data['cardPaymentTotal'] = this.checkoutTotalWithCardSwipeCharges(data.payments);
            // this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, CustomValidators.equal((<number>data['cardPaymentTotal']).toString())]);
          }
        } else {
          data['cardPaymentTotal'] = this.checkoutTotalWithCardSwipeCharges(data.payments);
          // this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, CustomValidators.equal((<number>data['cardPaymentTotal']).toString())]);
        }
        // this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
      }
      if (!(amount > 0)) {
        this.fundAmount = 0;
      }
    }

    if (data.booking_status.toLowerCase() === "reserved" || type.toLowerCase() === "checkout") {
      // data.payments.bookingPayment.map((e:any)=>{
      //   if(!e.is_advance && e.is_update_payment ){
      //     this.addRemoveExtraItems(this.paymentAtcheckout.get('payment_amount').value,this.paymentAtcheckout.get('items'));
      //   }
      // })
      if (this.paymentTypeSelection === '0' && this.paymentAtcheckout.get('payment_amount').value > 9999) {
        this.addRemoveExtraItems(this.paymentAtcheckout.get('payment_amount').value, this.paymentAtcheckout.get('items'));
      }
      else if (this.paymentTypeSelection === '0' && this.paymentAtcheckout.get('payment_amount').value < 9999 && this.paymentAtcheckout.get('items').value.length > 0) {
        let items = this.paymentAtcheckout.get('items');
        let guests = <FormArray>items;
        while (guests.length !== 0) {
          guests.removeAt(0);
        }
      }
    }
    // this.paymentVerification(data, type);
    // console.log("Formmmmmmmmmmmm : ", this.paymentAtcheckout.value);
  }
  // paymentVerification(data, type) {
  //   console.log("data", data, "type", type)
  //   if (type == 'checkout' || type == 'payment') {
  //     let customDiscount = this.paymentAtcheckout.controls['custom_discount'].value > 0 ? this.paymentAtcheckout.controls['custom_discount'].value : 0;
  //     let needPaymentVerificationId = data.payments.early_checkin_Charge + data.payments.cardSwipeCharges + data.payments.net_amount - customDiscount;
  //     if (!this.paymentAtcheckout.get('payment_verification_id') && needPaymentVerificationId >= 10000) {
  //       this.paymentAtcheckout.addControl('payment_verification_id', new FormControl('', Validators.required));
  //       this.highLighValidationsForField('payment_verification_id');
  //     } else if (this.paymentAtcheckout.get('payment_verification_id')) {
  //       this.highLighValidationsForField('payment_verification_id');
  //       if (needPaymentVerificationId < 10000) {
  //         this.paymentAtcheckout.removeControl('payment_verification_id');
  //       }
  //     }
  //   }
  // }
  
  highLighValidationsForField(field: string) {
    this.paymentAtcheckout.get(field).markAsDirty();
    this.paymentAtcheckout.get(field).markAsTouched();
  }
  updateCheckoutCardSwipeCharges(data: any) {
    let indx = data.payments.customers.findIndex(x => x.id == parseInt(data.customer_id))
    if (indx > -1) {
      if (data.payments.customers[indx].discount_type && data.payments.customers[indx].discount_value && data.payments.customers[indx].discount_type == "percentage" && data.payments.customers[indx].discount_value == 100) {
        if (this.paymentAtcheckout.controls['payment_amount'].value < 9) {
          this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, Validators.pattern('[^0-8]|[0-9][0-9]')])
        } else {
        }
      } else {
        data['cardPaymentTotal'] = this.checkoutTotalWithCardSwipeCharges(data.payments);
        // this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, CustomValidators.equal((<number>data['cardPaymentTotal']).toString())]);
      }
    } else {
      data['cardPaymentTotal'] = this.checkoutTotalWithCardSwipeCharges(data.payments);
      // this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, CustomValidators.equal((<number>data['cardPaymentTotal']).toString())]);
    }
    // this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
  }
  manipulateReferenceAndNote(type?: string) {
    let totalPayable = parseInt((<string>this.totalPayableAmount.nativeElement.innerText).split('.')[0].replace(/,/g, ''));
    // && this.paymentAtcheckout.get('payment_mode').value != "1"
    if (typeof totalPayable == 'number' && totalPayable > 0 && type == "checkout") {
      // console.log("Adding reference control....");
      if (!this.paymentAtcheckout.get('reference_user')) {
        this.paymentAtcheckout.addControl('reference_user', new FormControl('', [Validators.required]));
        this.highLighValidationsForField('reference_user');
      }
      if (!this.paymentAtcheckout.get('note')) {
        this.paymentAtcheckout.addControl('note', new FormControl('', [Validators.required]));
        this.highLighValidationsForField('note');
      }
      this.is_disable = false;
      // this.paymentAtcheckout.addControl('note', new FormControl('', [Validators.required]));
    }
    else {
      // console.log("Removing reference control....");
      if (this.paymentAtcheckout.get('reference_user')) {
        this.paymentAtcheckout.removeControl('reference_user');
      }
      if (this.paymentAtcheckout.get('note')) {
        this.paymentAtcheckout.removeControl('note');
      }
    }
  }
  checkoutTotalWithCardSwipeCharges(data: any) {
    let customDiscount = 0;
    if (this.paymentAtcheckout.get('custom_discount') && this.paymentAtcheckout.get('custom_discount').value !== '') {
      customDiscount = this.paymentAtcheckout.get('custom_discount').value;
    }
    let cardCharge = this.paymentAtcheckout.get('card_charge') ? parseInt(this.paymentAtcheckout.get('card_charge').value) : 0;
    let totalAmount = 0;
    if (data.bookingPayment && data.bookingPayment.length != 0) {
      let paidAm = 0;
      for (let i = 0; i < data.bookingPayment.length; i++) {
        paidAm = paidAm + data.bookingPayment[i].amount;
      }
      totalAmount = data.early_checkin_Charge + data.total_payable_amount - paidAm - customDiscount;
    } else {
      totalAmount = data.early_checkin_Charge + data.total_payable_amount - data.paidAmount - customDiscount - data.cardSwipeChargesOriginal;
    }
    // let cardSwipeCharges1 = ((totalAmount * cardCharge) / 100);
    // data['cardSwipeCharges'] = cardSwipeCharges1;
    // console.log('cardSwipeCharges111', cardSwipeCharges1, (totalAmount + cardSwipeCharges1))
    // return (totalAmount + cardSwipeCharges)

    let cardSwipeCharges = (((this.paymentAtcheckout.get('payment_amount').value * cardCharge) / 100))
    data['cardSwipeCharges'] = cardSwipeCharges;
    return (totalAmount - (+this.paymentAtcheckout.get('payment_amount').value))

  }
  /**
   * initialise payment form at checkout process
   *
 * @param {any} item
 * @memberof ReservationsComponent
 */
  initFinalPaymentForm(item, type?: string) {
    this.paymentAtcheckout = this._fb.group({
      payment_amount: [0],
      fund_amount: [0],
      fund_type: [],
      payment_mode: [this.paymentType[0].id],
      payment_date: [moment().format('YYYY-MM-DD') + 'T00:00:00.000Z'], //todo : this is patiya
      // payment_reciept_number: [''],
      room_id: [item.room_id],
      is_early_checkout: [false],
      booking_id: [item.payments.booking_id],
      custom_discount: [0, CustomValidators.digits],
      items: this._fb.array([])
    });
    this.paymentAtcheckout.addControl('payment_verification_id', new FormControl(''));
    if (type == "checkout") {
      this.paymentAtcheckout.addControl('pavati_no', new FormControl(null));
    }
    let indx = item.payments.customers.findIndex(x => x.id == item.customer_id);
    if (indx > -1) {
      if (item.payments.customers[indx].discount_type && item.payments.customers[indx].discount_value && item.payments.customers[indx].discount_type == "percentage" && item.payments.customers[indx].discount_value == 100) {
        // console.log("free guest---------")
        if (this.paymentAtcheckout.controls['payment_amount'].value < 9) {
          this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, Validators.pattern('[^0-8]|[0-9][0-9]')])
        } else {

        }
      } else {
        this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required]);
        // console.log("Other Guest type-----------")
        // this.amountAccept = false;
      }
    }
    // console.log("item------", item)
    // currentIndex.addControl('note', new FormControl('', Validators.required));
    // console.log("this.paymentAtcheckoutForm : ",this.paymentAtcheckout.value);
    this.paymentAtcheckout.controls['is_early_checkout'].patchValue(item.payments.is_early_checkout);
  }
  // formArry function //
  spliteNameCity(data: any) {
    const a = this._fb.group({
      name: [data.name ? data.name : null, [Validators.required]],
      city: [data.city ? data.city : null, [Validators.required]],
      panno: [data.pancard_number ? data.pancard_number : null, [Validators.required]]
    });
    (<FormArray>this.paymentAtcheckout.get('items')).push(a);
    this.paymentAtcheckout.get('items').setValidators([Validators.required])
    this.highLighValidationsForField('items')
  }
  // spliteNameCityAdvanc() {
  //   const a = this._fb.group({
  //     name: [null],
  //     city: [null],
  //     panno: [null]
  //   });
  //   (<FormArray>this.bookingForm.controls['payments'].get('items')).push(a);
  // }

  PaymentAtcheckoutSelectionChanged(event, data: any, type?: string) {
    // this.updateFunds('splite','','checkout');
    // console.log("type ======== 2", event.value)
    // if (type === 'checkout') {
    //   this.splitAmount('splite', type ,event.value)
    // }

    this.paymentTypeSelection = event.id
    this.paymentAtcheckout.controls['payment_mode'].patchValue(event.id);

    let b = 0;
    this.apiValue.bookingPayment.forEach(element => {
      if (element.payment_mode == 0) {
        b += +element.amount;
      }
    });

    // this.paymentAtcheckout.value.payment_amount = +this.paymentAtcheckout.value.payment_amount + +b;
    if (event.id == 1) {

      if (type === 'checkout') {
        this.splitAmount('splite', type)
      }
      // console.log('payment ', this.checkoutTotalWithCardSwipeCharges(data.payments))

      if (this.checkoutTotalWithCardSwipeCharges(data.payments) >= 10000) {
        this.paymentAtcheckout.get('payment_verification_id').setValidators([Validators.required])
        this.highLighValidationsForField('payment_verification_id');
      }

      this.paymentAtcheckout.addControl('payment_reciept_number', new FormControl('', Validators.required));
      this.paymentAtcheckout.addControl('card_charge', new FormControl(0, [Validators.required, CustomValidators.lte(100)]));
      data['cardPaymentTotal'] = this.checkoutTotalWithCardSwipeCharges(data.payments);
      this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required]);
      this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
      this.paymentAtcheckout.controls['payment_reciept_number'].updateValueAndValidity();
      this.paymentAtcheckout.controls['card_charge'].updateValueAndValidity();
      if (this.paymentAtcheckout.get('bank_name')) {
        this.paymentAtcheckout.removeControl('bank_name');
        this.paymentAtcheckout.removeControl('bank_cheque_no');
      }
    }
    else if (event.id == 2) {

      if (type === 'checkout') {
        this.splitAmount('splite', type)
      }

      if (this.checkoutTotalWithCardSwipeCharges(data.payments) >= 10000) {
        this.paymentAtcheckout.get('payment_verification_id').setValidators([Validators.required])
        this.highLighValidationsForField('payment_verification_id');
      }

      data.payments['cardSwipeCharges'] = data.payments['cardSwipeChargesOriginal'];
      this.paymentAtcheckout.addControl('bank_name', new FormControl('', Validators.required));
      this.paymentAtcheckout.addControl('bank_cheque_no', new FormControl('', Validators.required));
      this.paymentAtcheckout.controls['payment_amount'].setValidators(Validators.required);
      this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
      this.paymentAtcheckout.controls['bank_name'].updateValueAndValidity();
      this.paymentAtcheckout.controls['bank_cheque_no'].updateValueAndValidity();
      if (this.paymentAtcheckout.get('payment_reciept_number')) {
        this.paymentAtcheckout.removeControl('payment_reciept_number');
        this.paymentAtcheckout.removeControl('card_charge');
      }

    } else if (event.id == 3) {

      if (type === 'checkout') {
        this.splitAmount('splite', type)
      }
      if (this.checkoutTotalWithCardSwipeCharges(data.payments) >= 10000) {
        this.paymentAtcheckout.get('payment_verification_id').setValidators([Validators.required])
        this.highLighValidationsForField('payment_verification_id');
      }

      this.paymentAtcheckout.addControl('bank_name', new FormControl('', Validators.required));
      this.paymentAtcheckout.addControl('bank_cheque_no', new FormControl('', Validators.required));
      this.paymentAtcheckout.controls['payment_amount'].setValidators(Validators.required);
      this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
      this.paymentAtcheckout.controls['bank_name'].updateValueAndValidity();
      this.paymentAtcheckout.controls['bank_cheque_no'].updateValueAndValidity();
      if (this.paymentAtcheckout.get('payment_reciept_number')) {
        this.paymentAtcheckout.removeControl('payment_reciept_number');
        this.paymentAtcheckout.removeControl('card_charge');
      }
    }
    else {
      if (type === 'checkout') {
        this.splitAmount('splite', type)
      }
      this.paymentAtcheckout.get('payment_verification_id').clearValidators();
      this.paymentAtcheckout.get('payment_verification_id').markAsPristine();
      data.payments['cardSwipeCharges'] = data.payments['cardSwipeChargesOriginal'];
      if (this.paymentAtcheckout.get('payment_reciept_number')) {
        this.paymentAtcheckout.removeControl('payment_reciept_number');
        this.paymentAtcheckout.removeControl('card_charge');
      }
      if (this.paymentAtcheckout.get('bank_name')) {
        this.paymentAtcheckout.removeControl('bank_name');
        this.paymentAtcheckout.removeControl('bank_cheque_no');
      }

      this.paymentAtcheckout.controls['payment_amount'].clearValidators();
      this.paymentAtcheckout.controls['payment_amount'].setValidators(CustomValidators.digits);
      this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
    }
    // console.log('data jaa', data)
    this.manipulateReferenceAndNote(type);
  }
  makePositive(item, num) {
    // convert to positive from negative
    num = Math.abs(num);
    this.fundAmount = num;
    // this.paymentAtcheckout.controls['fund_amount'].patchValue(num);
    return num;
  }
  returnZero() {
    this.paymentAtcheckout.controls['fund_amount'].patchValue(0);
    this.paymentAtcheckout.controls['fund_type'].patchValue(null);
    if (this.fundtypeID) {
      this.renderer.setProperty(this.fundtypeID.nativeElement, 'checked', false);
    }
  }

  //Changed logic with card processing flow, asking for confirmation to bypass (Admin - only) or validate the card process
  confirmModal(tab1, tab2, type, tabz) {
    this.is_disable = true;
    this.tempConfirmData = tabz;
    this.tabData = tab1;
    this.tabDataID = tab2;

    if (type == 'checkout' && this.paymentAtcheckout.value.payment_amount === 0 && tab1.cardPaymentTotal > 0) {
      this.manipulateReferenceAndNote(type);
    }
    if (this.paymentAtcheckout.valid && type != 'payment') {
      setTimeout(() => {
        this.checkoutInvoiceModal.show();
      }, 1000);
    } else {
      this.pdfFlag = false;
      this.paymentAtcheckoutProcess(this.tabData, this.tabDataID, true)
    }
  }
  isActiveBtn: boolean = false;
  checkoutPDF(value) {
    if (value) {
      this.pdfFlag = true;
      // this.paymentAtcheckoutProcess(this.tabData, this.tabDataID, true)
    } else {
      this.pdfFlag = false;
      // this.paymentAtcheckoutProcess(this.tabData, this.tabDataID, true)
    }
    this.isActiveBtn = true;
    setTimeout(() => {
      this.isActiveBtn = false;
    }, 3000);

    if (this.tempConfirmData && this.tempConfirmData.content && this.tempConfirmData.content.door_id && this.tempConfirmData.content.door_id != '') {
      this.checkoutInvoiceModal.hide();
      if (this.is_admin == 'true') {
        setTimeout(() => {
          //Popup with bypass option for admin only
          this.checkoutBypassModal.show();
        }, 1000);
      } else {

        //Compulsory process through card.
        // setTimeout(() => {
        //   this.checkoutBypassModal.show();
        // }, 1000);

        this.paymentAtcheckoutProcess(this.tabData, this.tabDataID, true);
      }
    } else {
      this.checkoutInvoiceModal.hide();
      this.paymentAtcheckoutProcess(this.tabData, this.tabDataID, true);
    }
  }

  /**
   * The payment process if there ara some amount is still remaining to be paid.
   *
   * @memberof ReservationsComponent
   */
  paymentAtcheckoutProcess(data: any, customerId: number, isBypass?: boolean) {
    setTimeout(() => {
      this.checkoutBypassModal.hide();
    }, 100);

    if ((this.is_admin == 'false') && isBypass) {
      if (this.tempConfirmData && this.tempConfirmData.content && this.tempConfirmData.content.process_type != 'payment' && data && data.door_id && data.door_id != '') {

        //Popup code Start
        this.dynamicCheckinSuccess = false;
        this.checkoutData = {
          "currentOperation": "Checkout",
          "startDateAndTime": null,
          "endDateAndTime": null,
          "roomId": data.room_id,
          "doorId": data.door_id
        };
        this.checkoutFlowDynamicModalAction();

        this.reservation_S.getCheckoutPreProcess(this.checkoutData).subscribe((resdata) => {

          //Success
          if (resdata['status'] == true || resdata['status'] == 'true') {
            this.showData = true;
            this.showError = false;
            this.dynamicCheckinSuccess = resdata.status;
            this.dynamicCheckinResponseMessage = resdata.message;

            this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
            if (this.paymentAtcheckout.valid) {
              if (data.process_type == 'payment') {
                // console.log("-----------1-----------")
                let postData = this.paymentAtcheckout.value;
                if (postData.payment_mode == '1') {
                  // console.log("-----------2-----------")
                  // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
                }
                // console.log("this is postdata--------", postData['payment_amount'], "--------------", postData)
                // console.log("Payment Data : ", postData);
                this.updatePaymentApi = this.BS.updatePayment(postData.booking_id, postData, 'update-payment').
                  subscribe((res) => {
                    if (res) {
                      this.is_disable = false
                    }
                    if (res.status == "success") {
                       console.log("-----------3-----------")
                      if (res.data && res.data.is_early_checkout) {
                        if (this.pdfFlag) {
                          // console.log("res---1-->", res)
                          this.getHtml2(res.data.booking_id)
                          this.pdfFlag = false;
                          this.checkoutInvoiceModal.hide();
                        } else {
                          this.checkoutInvoiceModal.hide();
                        }
                      } else {
                        if (res.data) {
                          if (this.pdfFlag) {
                            // console.log("res---11-->", res)
                            this.getHtml2(res.data)
                            this.pdfFlag = false;
                            this.checkoutInvoiceModal.hide();
                          } else {
                            this.checkoutInvoiceModal.hide();
                          }
                        }
                      }
                      let index2 = 0;
                      this.staticTabs.tabs.forEach((ele, index) => {
                        if (ele.active == true) {
                          // console.log("-----------4-----------")
                          index2 = index;
                        }
                      })
                      this.removeTabHandler(this.staticTabs.tabs[index2])
                      this.staticTabs.tabs[0].active = true;
                      this.paymentTypeSelection = '0';
                    }
                  });
              }
              else {
                // console.log("-----------5-----------")
                let postData = this.paymentAtcheckout.value;
                if (postData.payment_mode == '1') {
                  // console.log("-----------4-----------")
                  // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
                }
                postData['isAdmin'] = this.authGuard.isAdmin();
                this.payFinalPayments = this.BS.payFinalPayments(postData.booking_id, postData, customerId)
                  .subscribe((res) => {
                    if (res) {
                      this.is_disable = false
                    }
                    if (res.status == "success") {
                      // console.log("-----------6-----------")
                      if (res.data && res.data.is_early_checkout) {
                        if (this.pdfFlag) {
                          // console.log("res---2-->", res)
                          this.getHtml2(res.data.booking_id)
                          this.pdfFlag = false;
                          this.checkoutInvoiceModal.hide();
                        } else {
                          this.checkoutInvoiceModal.hide();
                        }
                      } else {
                        if (res.data) {
                          if (this.pdfFlag) {
                            // console.log("res---12-->", res)
                            this.getHtml2(res.data)
                            this.pdfFlag = false;
                            this.checkoutInvoiceModal.hide();
                          } else {
                            this.checkoutInvoiceModal.hide();
                          }
                        }
                      }
                      let selected_booking_id;
                      if (res.data.is_early_checkout) {
                        // console.log("-----------7-----------")
                        selected_booking_id = res.data.booking_id;
                        // console.log("-1-1-1-1-1-1-1-1-1-1-1-")
                        this.removeCheckedOutBooking(res.data.booking_id);
                      } else {
                        selected_booking_id = res.data;
                        // console.log("2-2-2-2-2-2-2-2-2-2-2-2-2-")
                        this.removeCheckedOutBooking(res.data);
                      }
                      let index2 = 0;
                      this.staticTabs.tabs.forEach((ele, index) => {
                        if (ele.active == true) {
                          index2 = index;
                        }
                      });
                      // console.log("3-3-3-3-3-3-3-3-3-3-3-3-3-")
                      let index3 = this.findIndex(parseInt(selected_booking_id), "booking_id", this.originalAllBookings) - 1;
                      this.originalAllBookings.splice(index3, 1);
                      this.removeTabHandler(this.staticTabs.tabs[index2])
                      this.staticTabs.tabs[0].active = true;
                      this.paymentTypeSelection = '0';
                    }
                  })
              }
            }
            else {
              for (let field in this.paymentAtcheckout.controls) {
                this.paymentAtcheckout.get(field).markAsTouched();
                this.paymentAtcheckout.get(field).markAsDirty();
              }
            }
          } else if (resdata['status'] == false || resdata['status'] == 'false') {
            this.dynamicCheckinSuccess = true;
            this.dynamicCheckinResponseMessage = resdata.message;
            this.showData = true;
            this.showError = false;
          }
        }, (error) => {
          this.showData = false;
          this.showError = true;
          this.is_disable = false;
        });
        //Popup Code end

      } else {
        this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
        if (this.paymentAtcheckout.valid) {
          if (data.process_type == 'payment') {
            // console.log("-----------1-----------")
            let postData = this.paymentAtcheckout.value;
            if (postData.payment_mode == '1') {
              // console.log("-----------2-----------")
              // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
            }
            // console.log("this is postdata--------", postData['payment_amount'], "--------------", postData)
            // console.log("Payment Data : ", postData);
            this.updatePaymentApi = this.BS.updatePayment(postData.booking_id, postData, 'update-payment').
              subscribe((res) => {
                if (res) {
                  this.is_disable = false
                }
                if (res.status == "success") {
                   console.log("-----------4-----------")
                  if (res.data && res.data.is_early_checkout) {
                    if (this.pdfFlag) {
                      // console.log("res---1-->", res)
                      this.getHtml2(res.data.booking_id)
                      this.pdfFlag = false;
                      this.checkoutInvoiceModal.hide();
                    } else {
                      this.checkoutInvoiceModal.hide();
                    }
                  } else {
                    if (res.data) {
                      if (this.pdfFlag) {
                        // console.log("res---11-->", res)
                        this.getHtml2(res.data)
                        this.pdfFlag = false;
                        this.checkoutInvoiceModal.hide();
                      } else {
                        this.checkoutInvoiceModal.hide();
                      }
                    }
                  }
                  let index2 = 0;
                  this.staticTabs.tabs.forEach((ele, index) => {
                    if (ele.active == true) {
                      // console.log("-----------4-----------")
                      index2 = index;
                    }
                  })
                  this.removeTabHandler(this.staticTabs.tabs[index2])
                  this.staticTabs.tabs[0].active = true;
                  this.paymentTypeSelection = '0';
                }
              });
          }
          else {
            // console.log("-----------5-----------")
            let postData = this.paymentAtcheckout.value;
            if (postData.payment_mode == '1') {
              // console.log("-----------4-----------")
              // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
            }
            postData['isAdmin'] = this.authGuard.isAdmin();
            this.payFinalPayments = this.BS.payFinalPayments(postData.booking_id, postData, customerId)
              .subscribe((res) => {
                if (res) {
                  this.is_disable = false
                }
                if (res.status == "success") {
                  // console.log("-----------6-----------")
                  if (res.data && res.data.is_early_checkout) {
                    if (this.pdfFlag) {
                      // console.log("res---2-->", res)
                      this.getHtml2(res.data.booking_id)
                      this.pdfFlag = false;
                      this.checkoutInvoiceModal.hide();
                    } else {
                      this.checkoutInvoiceModal.hide();
                    }
                  } else {
                    if (res.data) {
                      if (this.pdfFlag) {
                        // console.log("res---12-->", res)
                        this.getHtml2(res.data)
                        this.pdfFlag = false;
                        this.checkoutInvoiceModal.hide();
                      } else {
                        this.checkoutInvoiceModal.hide();
                      }
                    }
                  }
                  let selected_booking_id;
                  if (res.data.is_early_checkout) {
                    // console.log("-----------7-----------")
                    selected_booking_id = res.data.booking_id;
                    // console.log("-1-1-1-1-1-1-1-1-1-1-1-")
                    this.removeCheckedOutBooking(res.data.booking_id);
                  } else {
                    selected_booking_id = res.data;
                    // console.log("2-2-2-2-2-2-2-2-2-2-2-2-2-")
                    this.removeCheckedOutBooking(res.data);
                  }
                  let index2 = 0;
                  this.staticTabs.tabs.forEach((ele, index) => {
                    if (ele.active == true) {
                      index2 = index;
                    }
                  });
                  // console.log("3-3-3-3-3-3-3-3-3-3-3-3-3-")
                  let index3 = this.findIndex(parseInt(selected_booking_id), "booking_id", this.originalAllBookings) - 1;
                  this.originalAllBookings.splice(index3, 1);
                  this.removeTabHandler(this.staticTabs.tabs[index2])
                  this.staticTabs.tabs[0].active = true;
                  this.paymentTypeSelection = '0';
                }
              })
          }
        }
        else {
          for (let field in this.paymentAtcheckout.controls) {
            this.paymentAtcheckout.get(field).markAsTouched();
            this.paymentAtcheckout.get(field).markAsDirty();
          }
        }
      }
    }
    else if (!isBypass) {
      console.log("elseeeeee offffff");

      if (this.tempConfirmData && this.tempConfirmData.content && this.tempConfirmData.content.process_type != 'payment' && data && data.door_id && data.door_id != '') {

        //Popup code Start
        this.dynamicCheckinSuccess = false;
        this.checkoutData = {
          "currentOperation": "Checkout",
          "startDateAndTime": null,
          "endDateAndTime": null,
          "roomId": data.room_id,
          "doorId": data.door_id
        };
        this.checkoutFlowDynamicModalAction();

        this.reservation_S.getCheckoutPreProcess(this.checkoutData).subscribe((resdata) => {

          //Success
          if (resdata['status'] == true || resdata['status'] == 'true') {
            this.showData = true;
            this.showError = false;
            this.dynamicCheckinSuccess = resdata.status;
            this.dynamicCheckinResponseMessage = resdata.message;

            this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
            if (this.paymentAtcheckout.valid) {
              if (data.process_type == 'payment') {
                // console.log("-----------1-----------")
                let postData = this.paymentAtcheckout.value;
                if (postData.payment_mode == '1') {
                  // console.log("-----------2-----------")
                  // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
                }
                // console.log("this is postdata--------", postData['payment_amount'], "--------------", postData)
                // console.log("Payment Data : ", postData);
                this.updatePaymentApi = this.BS.updatePayment(postData.booking_id, postData, 'update-payment').
                  subscribe((res) => {
                    if (res) {
                      this.is_disable = false
                    }
                    if (res.status == "success") {
                       console.log("-----------5-----------")
                      if (res.data && res.data.is_early_checkout) {
                        if (this.pdfFlag) {
                          // console.log("res---1-->", res)
                          this.getHtml2(res.data.booking_id)
                          this.pdfFlag = false;
                          this.checkoutInvoiceModal.hide();
                        } else {
                          this.checkoutInvoiceModal.hide();
                        }
                      } else {
                        if (res.data) {
                          if (this.pdfFlag) {
                            // console.log("res---11-->", res)
                            this.getHtml2(res.data)
                            this.pdfFlag = false;
                            this.checkoutInvoiceModal.hide();
                          } else {
                            this.checkoutInvoiceModal.hide();
                          }
                        }
                      }
                      let index2 = 0;
                      this.staticTabs.tabs.forEach((ele, index) => {
                        if (ele.active == true) {
                          // console.log("-----------4-----------")
                          index2 = index;
                        }
                      })
                      this.removeTabHandler(this.staticTabs.tabs[index2])
                      this.staticTabs.tabs[0].active = true;
                      this.paymentTypeSelection = '0';
                    }
                  });
              }
              else {
                // console.log("-----------5-----------")
                let postData = this.paymentAtcheckout.value;
                if (postData.payment_mode == '1') {
                  // console.log("-----------4-----------")
                  // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
                }
                postData['isAdmin'] = this.authGuard.isAdmin();
                this.payFinalPayments = this.BS.payFinalPayments(postData.booking_id, postData, customerId)
                  .subscribe((res) => {
                    if (res) {
                      this.is_disable = false
                    }
                    if (res.status == "success") {
                      // console.log("-----------6-----------")
                      if (res.data && res.data.is_early_checkout) {
                        if (this.pdfFlag) {
                          // console.log("res---2-->", res)
                          this.getHtml2(res.data.booking_id)
                          this.pdfFlag = false;
                          this.checkoutInvoiceModal.hide();
                        } else {
                          this.checkoutInvoiceModal.hide();
                        }
                      } else {
                        if (res.data) {
                          if (this.pdfFlag) {
                            // console.log("res---12-->", res)
                            this.getHtml2(res.data)
                            this.pdfFlag = false;
                            this.checkoutInvoiceModal.hide();
                          } else {
                            this.checkoutInvoiceModal.hide();
                          }
                        }
                      }
                      let selected_booking_id;
                      if (res.data.is_early_checkout) {
                        // console.log("-----------7-----------")
                        selected_booking_id = res.data.booking_id;
                        // console.log("-1-1-1-1-1-1-1-1-1-1-1-")
                        this.removeCheckedOutBooking(res.data.booking_id);
                      } else {
                        selected_booking_id = res.data;
                        // console.log("2-2-2-2-2-2-2-2-2-2-2-2-2-")
                        this.removeCheckedOutBooking(res.data);
                      }
                      let index2 = 0;
                      this.staticTabs.tabs.forEach((ele, index) => {
                        if (ele.active == true) {
                          index2 = index;
                        }
                      });
                      // console.log("3-3-3-3-3-3-3-3-3-3-3-3-3-")
                      let index3 = this.findIndex(parseInt(selected_booking_id), "booking_id", this.originalAllBookings) - 1;
                      this.originalAllBookings.splice(index3, 1);
                      this.removeTabHandler(this.staticTabs.tabs[index2])
                      this.staticTabs.tabs[0].active = true;
                      this.paymentTypeSelection = '0';
                    }
                  })
              }
            }
            else {
              for (let field in this.paymentAtcheckout.controls) {
                this.paymentAtcheckout.get(field).markAsTouched();
                this.paymentAtcheckout.get(field).markAsDirty();
              }
            }
          } else if (resdata['status'] == false || resdata['status'] == 'false') {
            this.dynamicCheckinSuccess = true;
            this.dynamicCheckinResponseMessage = resdata.message;
            this.showData = true;
            this.showError = false;
          }
        }, (error) => {
          this.showData = false;
          this.showError = true;
          this.is_disable = false;
        });
        //Popup Code end

      } else {
        this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
        if (this.paymentAtcheckout.valid) {
          if (data.process_type == 'payment') {
            // console.log("-----------1-----------")
            let postData = this.paymentAtcheckout.value;
            if (postData.payment_mode == '1') {
              // console.log("-----------2-----------")
              // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
            }
            // console.log("this is postdata--------", postData['payment_amount'], "--------------", postData)
            // console.log("Payment Data : ", postData);
            this.updatePaymentApi = this.BS.updatePayment(postData.booking_id, postData, 'update-payment').
              subscribe((res) => {
                if (res) {
                  this.is_disable = false
                }
                if (res.status == "success") {
                   console.log("-----------6-----------")
                  if (res.data && res.data.is_early_checkout) {
                    if (this.pdfFlag) {
                      // console.log("res---1-->", res)
                      this.getHtml2(res.data.booking_id)
                      this.pdfFlag = false;
                      this.checkoutInvoiceModal.hide();
                    } else {
                      this.checkoutInvoiceModal.hide();
                    }
                  } else {
                    if (res.data) {
                      if (this.pdfFlag) {
                        // console.log("res---11-->", res)
                        this.getHtml2(res.data)
                        this.pdfFlag = false;
                        this.checkoutInvoiceModal.hide();
                      } else {
                        this.checkoutInvoiceModal.hide();
                      }
                    }
                  }
                  let index2 = 0;
                  this.staticTabs.tabs.forEach((ele, index) => {
                    if (ele.active == true) {
                      // console.log("-----------4-----------")
                      index2 = index;
                    }
                  })
                  this.removeTabHandler(this.staticTabs.tabs[index2])
                  this.staticTabs.tabs[0].active = true;
                  this.paymentTypeSelection = '0';
                }
              });
          }
          else {
            // console.log("-----------5-----------")
            let postData = this.paymentAtcheckout.value;
            if (postData.payment_mode == '1') {
              // console.log("-----------4-----------")
              // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
            }
            postData['isAdmin'] = this.authGuard.isAdmin();
            this.payFinalPayments = this.BS.payFinalPayments(postData.booking_id, postData, customerId)
              .subscribe((res) => {
                if (res) {
                  this.is_disable = false
                }
                if (res.status == "success") {
                  // console.log("-----------6-----------")
                  if (res.data && res.data.is_early_checkout) {
                    if (this.pdfFlag) {
                      // console.log("res---2-->", res)
                      this.getHtml2(res.data.booking_id)
                      this.pdfFlag = false;
                      this.checkoutInvoiceModal.hide();
                    } else {
                      this.checkoutInvoiceModal.hide();
                    }
                  } else {
                    if (res.data) {
                      if (this.pdfFlag) {
                        // console.log("res---12-->", res)
                        this.getHtml2(res.data)
                        this.pdfFlag = false;
                        this.checkoutInvoiceModal.hide();
                      } else {
                        this.checkoutInvoiceModal.hide();
                      }
                    }
                  }
                  let selected_booking_id;
                  if (res.data.is_early_checkout) {
                    // console.log("-----------7-----------")
                    selected_booking_id = res.data.booking_id;
                    // console.log("-1-1-1-1-1-1-1-1-1-1-1-")
                    this.removeCheckedOutBooking(res.data.booking_id);
                  } else {
                    selected_booking_id = res.data;
                    // console.log("2-2-2-2-2-2-2-2-2-2-2-2-2-")
                    this.removeCheckedOutBooking(res.data);
                  }
                  let index2 = 0;
                  this.staticTabs.tabs.forEach((ele, index) => {
                    if (ele.active == true) {
                      index2 = index;
                    }
                  });
                  // console.log("3-3-3-3-3-3-3-3-3-3-3-3-3-")
                  let index3 = this.findIndex(parseInt(selected_booking_id), "booking_id", this.originalAllBookings) - 1;
                  this.originalAllBookings.splice(index3, 1);
                  this.removeTabHandler(this.staticTabs.tabs[index2])
                  this.staticTabs.tabs[0].active = true;
                  this.paymentTypeSelection = '0';
                }
              })
          }
        }
        else {
          for (let field in this.paymentAtcheckout.controls) {
            this.paymentAtcheckout.get(field).markAsTouched();
            this.paymentAtcheckout.get(field).markAsDirty();
          }
        }
      }
    } else {

      this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
      if (this.paymentAtcheckout.valid) {

        if (data.process_type == 'payment') {
          // console.log("-----------1-----------")
          let postData = this.paymentAtcheckout.value;
          console.log(postData.payment_mode, "postData.payment_mode");

          if (postData.payment_mode == '1') {
            // console.log("-----------2-----------")
            // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
          }
          // console.log("this is postdata--------", postData['payment_amount'], "--------------", postData)
          console.log("Payment Data : ", postData, this.getHtml2);
          this.updatePaymentApi = this.BS.updatePayment(postData.booking_id, postData, 'update-payment').
            subscribe((res) => {
              if (res) {
                this.is_disable = false
              }
              if (res.status == "success") {
                //CHECKOUTPDF
                // this.getHtml2(postData.booking_id)
                 console.log("-----------7-----------")
                if (res.data && res.data.is_early_checkout) {
                  if (this.pdfFlag) {
                    // console.log("res---1-->", res)
                    this.getHtml2(res.data.booking_id)
                    this.pdfFlag = false;
                    this.checkoutInvoiceModal.hide();
                  } else {
                    this.checkoutInvoiceModal.hide();
                  }
                } else {
                  if (res.data) {
                    if (this.pdfFlag) {
                      // console.log("res---11-->", res)
                      this.getHtml2(res.data)
                      this.pdfFlag = false;
                      this.checkoutInvoiceModal.hide();
                    } else {
                      this.checkoutInvoiceModal.hide();
                    }
                  }
                }
                let index2 = 0;
                this.staticTabs.tabs.forEach((ele, index) => {
                  if (ele.active == true) {
                    // console.log("-----------4-----------")
                    index2 = index;
                  }
                })
                this.removeTabHandler(this.staticTabs.tabs[index2])
                this.staticTabs.tabs[0].active = true;
                this.paymentTypeSelection = '0';
              }
            });
        }
        else {
          // console.log("-----------5-----------")
          let postData = this.paymentAtcheckout.value;
          if (postData.payment_mode == '1') {
            // console.log("-----------4-----------")
            // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
          }
          postData['isAdmin'] = this.authGuard.isAdmin();
          this.payFinalPayments = this.BS.payFinalPayments(postData.booking_id, postData, customerId)
            .subscribe((res) => {
              if (res) {
                this.is_disable = false
              }
              if (res.status == "success") {
                // console.log("-----------6-----------")
                if (res.data && res.data.is_early_checkout) {
                  if (this.pdfFlag) {
                    // console.log("res---2-->", res)
                    this.getHtml2(res.data.booking_id)
                    this.pdfFlag = false;
                    this.checkoutInvoiceModal.hide();
                  } else {
                    this.checkoutInvoiceModal.hide();
                  }
                } else {
                  if (res.data) {
                    if (this.pdfFlag) {
                      // console.log("res---12-->", res)
                      this.getHtml2(res.data)
                      this.pdfFlag = false;
                      this.checkoutInvoiceModal.hide();
                    } else {
                      this.checkoutInvoiceModal.hide();
                    }
                  }
                }
                let selected_booking_id;
                if (res.data.is_early_checkout) {
                  // console.log("-----------7-----------")
                  selected_booking_id = res.data.booking_id;
                  // console.log("-1-1-1-1-1-1-1-1-1-1-1-")
                  this.removeCheckedOutBooking(res.data.booking_id);
                } else {
                  selected_booking_id = res.data;
                  // console.log("2-2-2-2-2-2-2-2-2-2-2-2-2-")
                  this.removeCheckedOutBooking(res.data);
                }
                let index2 = 0;
                this.staticTabs.tabs.forEach((ele, index) => {
                  if (ele.active == true) {
                    index2 = index;
                  }
                });
                // console.log("3-3-3-3-3-3-3-3-3-3-3-3-3-")
                let index3 = this.findIndex(parseInt(selected_booking_id), "booking_id", this.originalAllBookings) - 1;
                this.originalAllBookings.splice(index3, 1);
                this.removeTabHandler(this.staticTabs.tabs[index2])
                this.staticTabs.tabs[0].active = true;
                this.paymentTypeSelection = '0';
              }
            })
        }
      }
      else {
        for (let field in this.paymentAtcheckout.controls) {
          this.paymentAtcheckout.get(field).markAsTouched();
          this.paymentAtcheckout.get(field).markAsDirty();
        }
      }
    }
  }
  removeCheckedOutBooking(bookingId) {
    this.DateRangesWithBookingArray.forEach((element) => {
      element.room.forEach(element1 => {
        element1.dates.forEach(ele => {
          this.removeCheckedOutBookingProperties(ele, bookingId);
        });
      });
    });
  }
  removeCheckedOutBookingProperties(ele: any, bookingId) {
    if (ele.booking_id == bookingId) {
      ele['is_booked'] = false;
      delete ele['booking_id'];
      delete ele['booking_status'];
      delete ele['username'];
      delete ele['start'];
      delete ele['end'];
      if (ele['is_starting']) {
        ele['is_starting'] = false;
      }

      if (ele['is_ending']) {
        ele['is_ending'] = false;
      }
    } else if (ele.hasOwnProperty('extraBooking') && ele.extraBooking.booking_id == bookingId) {
      delete ele['extraBooking'];
    }
  }
  setReferenceAndNote(policyId: any) {
    let index = this.findIndex(parseInt(policyId), "id", this.activeCancellationPoliciesOriginal) - 1;
    if (index > -1) {
      this.isReferenceNeccessaryCancel = this.activeCancellationPoliciesOriginal[index].is_reference_required;
      this.isNoteNeccessaryCancel = this.activeCancellationPoliciesOriginal[index].is_note_required;
    }
    if (this.isReferenceNeccessaryCancel) {
      this.cancelBookingForm.controls['reference_id'].setValidators(Validators.required);
    } else {
      this.cancelBookingForm.controls['reference_id'].clearValidators();
    }
    if (this.isNoteNeccessaryCancel) {
      this.cancelBookingForm.controls['note'].setValidators(Validators.required);
    } else {
      this.cancelBookingForm.controls['note'].clearValidators();
    }
    this.cancelBookingForm.controls['note'].updateValueAndValidity();
    this.cancelBookingForm.controls['reference_id'].updateValueAndValidity();
  }
  clearReferenceAndNote() {
    this.cancelBookingForm.get('note').clearValidators();
    this.cancelBookingForm.get('reference_id').clearValidators();
  }
  getCancellationPolicyCharges(item: any) {
    if (item && item.id && (item.id != "000000")) {
      this.checkCancellation = this.BS.processCheckCancellation(this.refunds['room_id'], this.refunds['booking_id'], item.id)
        .subscribe((res) => {
          if (res.status == "success") {
            this.setReferenceAndNote(item.id);
            this.refunds['cancellationPolicyCalculation'] = res.data;
            let index = this.findIndex(parseInt(item.id), "id", this.activeCancellationPolicies) - 1;
            if (index > -1) {
              this.refunds.cancellationPolicyCalculation['policy'] = this.activeCancellationPolicies[index].text;
              this.refunds.cancellationPolicyCalculation['policy_id'] = this.activeCancellationPolicies[index].id;
            }
          }
        });
    } else {
      this.clearReferenceAndNote();
      if (this.refunds && this.refunds.hasOwnProperty('cancellationPolicyCalculation')) {
        delete this.refunds['cancellationPolicyCalculation'];
      }
    }
  }
  initializeCancellationPolicies() {
    this.refunds = undefined;
    this.activeCancellationPolicies = undefined;
    this.selectedActiveCancellationPolicies = undefined;
    this.activeCancellationPoliciesOriginal = undefined;
  }
  cancelBookingReferenceTypeChanged(item: any) {
    if (item && item.id && item.id != "000000") {
      this.cancelBookingForm.get('reference_id').patchValue(item.id);
    }
  }
  initCancelBookingForm() {
    this.cancelBookingForm = this._fb.group({
      reference_id: [''],
      note: [''],
      passcode: ['', Validators.required]
    });
  }
  /**
   * process booking cancellation.
   *
   * @param {any} item contains room_id, booking_id
   * @memberof ReservationsComponent
   */
  processCancel(item: any, isNoShow?: boolean) {
    // console.log("PROCESS CANCEL", item);
    this.initializeCancellationPolicies();
    this.referenceUserCancelBooking = this.getFilteredReferences(item.customer_id);
    this.referenceUserCancelBooking.unshift({ id: "000000", text: 'Please select a reference type' });
    let room_id = item.room_id;
    let booking_id = item.booking_id;
    this.activeCancellationPoliciesAPI = this.BS.getActivePolicies('active')
      .subscribe((res) => {
        if (res.status === "success") {
          this.initCancelBookingForm();
          this.activeCancellationPoliciesOriginal = res.data;
          this.activeCancellationPolicies = jQuery.map(res.data, function (obj) {
            return { id: obj.id, text: obj.name };
          });
          this.activeCancellationPolicies.unshift({ id: "000000", text: 'Please select a cancellation policy' });
          this.selectedActiveCancellationPolicies = this.activeCancellationPolicies[0].id;
          this.refunds = {};
          this.refunds['booking_id'] = booking_id;
          this.refunds['room_id'] = room_id;
          this.refunds['is_no_show'] = isNoShow ? true : false;
          this.refunds['extraBooking'] = item.hasOwnProperty('extraBooking') ? item.extraBooking.booking_id : null;
          this.smModal.show();
        }
      })
  }
  preProcessCancel(item: any, isNoShow?: boolean) {
    this.checkForExtraBooking(item).then((res: any) => {
      if (res.booking_status !== 'checkin') {
        this.processCancel(res, isNoShow);
      } else {
        this.showNotification('Cannot cancel a checked-in booking!', 'error');
      }
    });
  }
  processNoShowGuest(item: any) {
    this.preProcessCancel(item, true);
  }
  /**
   * will now cancel booking after getting payment if there is any.
   *
   * @memberof ReservationsComponent
   */
  willnowCancel() {
    if (this.cancelBookingForm.valid) {
      let validationJson = JSON.parse(JSON.stringify(this.cancelBookingForm.value));
      validationJson['passcode'] = CryptoJS.AES.encrypt(this.cancelBookingForm.value.passcode, _secretKey).toString();
      validationJson['extraBooking'] = this.refunds.extraBooking;

      let data = {
        room_id: this.refunds['room_id'],
        booking_id: this.refunds['booking_id'],
        is_no_show: this.refunds['is_no_show'],
        policy_id: this.refunds.cancellationPolicyCalculation.policy_id,
        validationCheck: validationJson
      };
      this.cancelBooking = this.BS.processCancelBooking(data)
        .subscribe((res) => {
          if (res.status == "success") {
            this.fillData();
            // this.removeCheckedOutBooking(this.refunds['booking_id']);
            // _.remove(this.originalAllBookings, booking => { return booking.booking_id == this.refunds['booking_id']; });
            this.smModal.hide();
          }
        })
    } else {
      Object.keys(this.cancelBookingForm.value).forEach(key => {
        this.cancelBookingForm.get(key).markAsDirty();
        this.cancelBookingForm.get(key).markAsTouched();
      });
    }
  }
  preGuestCheckIn(event, type) {
    if (event.hasOwnProperty('extraBooking')) {
      if (event.extraBooking.booking_status === 'reserved')
        this.addGuestInfo(event.extraBooking, type);
      else if (event.extraBooking.booking_status === 'checkin') {
        this.showNotification('You are already checked-in', 'error');
      }
    } else {
      this.checkForExtraBooking(event).then((res) => {
        this.addGuestInfo(res, type);
      }).catch((err) => { });
    }
  }
  preAddGuestInfo(event) {
    this.checkForExtraBooking(event).then((res: any) => {
      if (res.booking_status === 'checkin')
        this.addGuestInfo(res);
      else {
        this.showNotification('Please check-in first', 'error');
      }
    }).catch((err) => { });
  }
  viewDocumentProof(item: any) {
    this.canShow = false;
    this.imageUrls = [];
    for (const imgUrl of item) { 
        let mimetype: any[] = [];
        if (imgUrl.mimetype) {
            mimetype = imgUrl.mimetype.split('/');
        }
        this.image['isImage'] = mimetype.indexOf('application') >= 0 ? false : true;
        if (this.image['isImage'] == false) {
            this.guestDocument = this.BS.getFileBase64({ document_url: this.image['guest_document'] })
                .subscribe(res => {
                    if (res) {
                        this.documentBase64 = null;
                        let file: any = new Blob([res], { type: 'application/pdf' });
                        const reader = new FileReader();

                        reader.readAsDataURL(file);
                        reader.addEventListener('loadend', (e: any) => {
                            let documentBase64 = reader.result;
                            var winparams = `dependent=yes,locationbar=no,scrollbars=yes,menubar=yes,resizable,screenX=50,screenY=50,width=850,height=1050`;
                            var htmlPop = `<embed width=100% height=100% type="application/pdf" src="${documentBase64}"></embed>`;
                            this.printWindow2 = window.open("", "PDF", winparams).document.write(htmlPop);
                        });
                    }
                })
        }
        else {
            this.imageUrls.push(imgUrl.url)
        }
    }
    this.viewDocumentModal.show();
  }
  closeModal() {
    this.canShow = true;
    this.viewDocumentModal.hide()
    this.gobacksimon()
  }
  gobacksimon(data?: any){
    this.goBack.emit(data);
  }
  showNotification(message: string, type: string, showCloseButton: boolean = true, hideAfter: number = 3) {
    Messenger().post({
      type: type,
      message: message,
      hideAfter: hideAfter,
      showCloseButton: showCloseButton,
    });
  }
  /**
   * Guest add/edit details form
   *
   * @param {any} item
   * @memberof ReservationsComponent
   */
  addGuestInfo(item: any, type?: string, showMessage?: boolean) {
    // console.log("ITEM : ",item);
    this.getGuestInfo = this.BS.getGuestInfo(item.booking_id)
      .subscribe((res) => {
        if (res.status == "success") {
          this.allGuestObject = res.data;
          this.initGuestForm(type, item.booking_status ? item.booking_status : item.current_status);
          item['action_type'] = "guest";
          item['type'] = type;
          this.dynamicCheckInOutDates = res.data.bookingDates;
          if (item.hasOwnProperty('is_extra_booking')) {
            item['start'] = moment(item.start).format('DD MMM');
            item['end'] = moment(item.end).format('DD MMM');
          }
          if ((type === 'checkIn' && !showMessage) || (type === 'checkout')) {
            let message;
            if (type === 'checkout') {
              message = 'Please update appropriate guest details!';
            } else {
              message = 'Please add guest proof!';
            }
            this.showNotification(message, "error");
          }
          this.addNewTab("Guest", _.cloneDeep(item));
          console.log('5711',item);

        }
      });
  }
  addGuest(item: any, roomCategory: any) {
    console.log("inside add guet >><<");

    // console.log("uitem,mmmmmmmmmmmmmm : ", item);
    // console.log("Full date", moment({ y: moment(item.fulldate).year(), M: moment(item.start).month(), d: moment(item.start).get('date') }).format('YYYY-MM-DD') + 'T00:00:00.000Z');
    // do something
    this.getAddGuestParams = this.BS.getAddGuestParams(item.booking_id, roomCategory, item.customer_id, item.room_id)
      .subscribe(res => {
        if (res.status == "success") {
          this.addGuestParams = res.data;
          this.addGuestParams['item'] = item;
          this.addGuestParams['roomCategoryId'] = roomCategory;
          //console.log("this.addGuestParams : ", this.addGuestParams);
          this.initAddGuestForm().then(res => {
            this.addGuestTabs.tabs[0].active = true;
            this.addGuestModal.show();
            let current = new Date();
            // console.log(item.fulldate , 'item.fulldate');
            // console.log(current.getFullYear() + 1, 'current');

            // console.log("Response data : ", this.addGuestForm.value);
            let start = moment(item.start).set('year', moment(item.fulldate).get('year'));
            if (start.isBefore(moment(), 'day')) {
              start = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';
            } else {
              start = moment({ y: moment(item.fulldate).year(), M: moment(item.start).month(), d: moment(item.start).get('date') }).format('YYYY-MM-DD') + 'T00:00:00.000Z';
            }
            let end = moment({ y: moment(item.fulldate).year(), M: moment(item.end).month(), d: moment(item.end).get('date') }).format('YYYY-MM-DD') + 'T00:00:00.000Z';
            //console.log("--------------------End date-------------------",end);
            this.addGuestForm.controls['start'].patchValue(start);
            this.addGuestForm.controls['end'].patchValue(end);
            //console.log('set end date end date set end date', end);
            console.log(end, ">>>end<<<<<");

            this.minAddGuestDate = new Date(start)
            this.maxAddGuestDate = new Date(end)
            // .toJSON().split('T')[0]
            console.log(this.maxAddGuestDate, 'YYYY-MM-DD');
            // console.log("this.minAddGuestDate : ",end);
            // console.log("Formmmmmmm : ",this.addGuestForm.value);
          });
        }
      });
  }

  addGuestToRoom() {
    console.log("addddroom guest");

    // console.log("this.addGuestForm.value : ", this.addGuestForm.value);
    if (this.addGuestForm.valid && (this.addGuestForm.value.adult + this.addGuestForm.value.child) > 0) {
      let value = this.addGuestForm.value;
      value.start = moment(value.start).format('YYYY-MM-DD') + 'T00:00:00.000Z';
      value.end = moment(value.end).format('YYYY-MM-DD') + 'T00:00:00.000Z';
      value['room_id'] = this.addGuestParams.item.room_id;
      value['room_cat_id'] = this.addGuestParams.roomCategoryId;
      value['customer_id'] = this.addGuestParams.item.customer_id;
      this.addGuestAPI = this.BS.addGuest(this.addGuestParams.item.booking_id, value)
        .subscribe(res => {
          if (res.status == "success") {
            // console.log("Response Data : ", res.data);
            this.addGuestForm.controls['start'].patchValue(undefined);
            this.addGuestForm.controls['end'].patchValue(undefined);
            this.addGuestForm.reset();
            this.addGuestModal.hide();
            this.addGuestParams = undefined;
          }
        });
    }
    else {
      if (this.addGuestForm.controls['guests'].invalid) {
        this.addGuestTabs.tabs[1].active = true;
        this.nextGuestAddTab();
      }
      else if ((this.addGuestForm.value.adult + this.addGuestForm.value.child) == 0) {
        this.addGuestTabs.tabs[0].active = true;
        this.nextGuestAddTab();
      }
    }
  }

  getMinDate(minAddGuestDate) {
    // console.log("minAddGuestDate : ",minAddGuestDate);
    return moment(minAddGuestDate).subtract(1, 'days');
  }
  addGuestBookingDateChanged(value: any, key: string) {
    console.log("addGuestBookingDateChanged");

    let start = <FormControl>this.addGuestForm.controls['start'];
    let end = <FormControl>this.addGuestForm.controls['end'];
    console.log(start, end, "Invalid date selected");

    if ((key == 'start' && moment(end.value).isBefore(value, 'days'))
      || (key == 'end' && moment(value).isBefore(start.value, 'days'))) {
      this.showNotification("Invalid date selected!", "error");
      key == 'start' ? this.addGuestDatePicker1.bsValue = new Date(start.value) :
        this.addGuestDatePicker2.bsValue = new Date(end.value);
    }
    else {
      key == 'start' ? start.patchValue(value) : end.patchValue(value);
      // console.log("start : ",moment(start.value).format('YYYY-MM-DD'));
      // console.log("end : ",moment(end.value).format('YYYY-MM-DD'));
      // console.log("Difffff : ",moment(moment(end.value).format('YYYY-MM-DD')).diff(moment(moment(start.value).format('YYYY-MM-DD')), 'days') + 1);
      this.addGuestParams['totalDays'] = (moment(moment(end.value).format('YYYY-MM-DD')).diff(moment(moment(start.value).format('YYYY-MM-DD')), 'days')) + 1;
    }
    // console.log("Form value : ", this.addGuestParams);
  }
  nextGuestAddTab() {
    let canNavigate = true;
    let index = _.findIndex(this.addGuestTabs.tabs, ['active', true]);
    let adult = <FormControl>this.addGuestForm.controls['adult'];
    let child = <FormControl>this.addGuestForm.controls['child'];
    let guests = <FormArray>this.addGuestForm.controls['guests'];
    if (index == 0 && (adult.value + child.value) == 0) {
      // console.log("Cannot go to next tab");
      canNavigate = false;
      adult.markAsDirty();
      adult.markAsTouched();
    }
    if (index == 1 && guests.invalid) {
      canNavigate = false;
      guests.controls.forEach(guest => {
        guest.get('name').markAsDirty();
        guest.get('name').markAsTouched();
        guest.get('age').markAsDirty();
        guest.get('age').markAsTouched();
        guest.get('gender').markAsDirty();
        guest.get('gender').markAsTouched();
      });
    }
    if (canNavigate) {
      this.addGuestTabs.tabs[index + 1].active = true;
      index += 1;
      this.isLastGuestAddTab = this.addGuestTabs.tabs.length == (index + 1) ? true : false;
    }
  }
  previousTab() {
    this.addGuestTabs.tabs[0].active = true;
  }
  initAddGuestForm() {
    return new Promise<any>((resolve, reject) => {
      this.addGuestForm = this._fb.group({
        start: [],
        end: [],
        reservation_date: [moment(new Date(), 'YYYY-MM-DD')],
        adult: [0, Validators.required],
        child: [0, Validators.required],
        guests: this._fb.array([])
      });
      resolve(true);
    });
  }

  onActionChange(event: Event) {
    const selectedValue = (event.target as HTMLSelectElement).value;
    console.log("Select Tag event = ", event);
    let item = this.eventData
    let secondEvent = this.eventMain
    switch (selectedValue) {
      case 'Check In':
        this.preGuestCheckIn(item, 'checkIn');
        break;
      case 'Transfer Room':
        this.TransferRoom(item, secondEvent);
        break;
      case 'Extend CheckOut':
        this.doSplitBooking(item);
        break;
      case 'Check Out':
        this.preProcessCheckOut(item, 'checkout');
        break;
      default:
        break;
    }
  }
  

  initAddGuest(maturityType: string) {
    let guests = <FormArray>this.addGuestForm.controls['guests'];
    let control: FormGroup = this._fb.group({
      name: ['', Validators.required],
      dob: ['', Validators.required],
      age: ['', Validators.required],
      gender: ['', Validators.required],
      guest_maturity_type: [maturityType, Validators.required]
    });
    if (maturityType == "child") {
      control.get('age').setValidators([CustomValidators.lte(12), CustomValidators.gt(0)]);
    }
    else {
      control.get('age').setValidators(CustomValidators.gt(12));
    }
    guests.push(control);
  }

  removeGuest(paxType: string) {
    let guests = <FormArray>this.addGuestForm.controls['guests'];
    let index = _.findLastIndex(guests.value, ['guest_maturity_type', paxType]);
    guests.removeAt(index);
  }

  getIndividualPaxCharge(paxChargeKey: string) {
    if (!this.addGuestParams) return 0;
    let index = this.findIndex(parseInt(this.addGuestParams.item.stay_type), "id", this.originalStayTypesList) - 1;
    if (index < 0) return 0;
    let percentage = this.originalStayTypesList[index].charge;
    return (this.addGuestParams[paxChargeKey] * percentage / 100);
  }
  getTotalPaxTypeCharges(paxChargeKey: string, paxTypeKey: string) {
    if (!this.addGuestParams) return 0;
    let index = this.findIndex(parseInt(this.addGuestParams.item.stay_type), "id", this.originalStayTypesList) - 1;
    if (index < 0) return 0;
    let percentage = this.originalStayTypesList[index].charge;
    return ((this.addGuestParams[paxChargeKey] * percentage / 100) * this.addGuestParams[paxTypeKey] * this.addGuestParams.totalDays);
  }

  updateGuestArray(paxType: string, type: string) {
    let adult = <FormControl>this.addGuestForm.controls['adult'];
    let child = <FormControl>this.addGuestForm.controls['child'];
    let canAdd = ((adult.value + child.value) < (this.addGuestParams.max_occupancy - this.addGuestParams.booked_occupancy));
    if (type == 'add') {
      canAdd ? this.initAddGuest(paxType) : this.showNotification(`Maximum guest limit is ${this.addGuestParams.max_occupancy}`, "error");
      paxType == 'adult' ? adult.patchValue(canAdd ? (adult.value + 1) : adult.value) : child.patchValue(canAdd ? (child.value + 1) : child.value);
    }

    if (type == 'subtract') {
      (adult.value) >= 1 && paxType == 'adult' ? this.removeGuest(paxType) : (child.value) >= 1 && paxType == 'child' ? this.removeGuest(paxType) : null;
      paxType == 'adult' ? adult.patchValue(adult.value >= 1 ? (adult.value - 1) : adult.value) : child.patchValue(child.value >= 1 ? (child.value - 1) : child.value);
    }
    this.addGuestParams.extraAdult = adult.value;
    this.addGuestParams.extraChild = child.value;
    // console.log("form : ", this.addGuestForm.value);
  }

  addGuestAdultChanged(index: number) {
    let adult = <FormControl>this.addGuestForm.controls['adult'];
    let child = <FormControl>this.addGuestForm.controls['child'];
    let guests = <FormArray>this.addGuestForm.controls['guests'];;
    let age = guests.controls[index].get('age');
    age.setValidators([Validators.required, CustomValidators.gt(12)]);
    age.updateValueAndValidity();
    adult.patchValue(adult.value + 1);
    child.patchValue(child.value - 1);
  }

  addGuestChildChanged(index: number) {
    let adult = <FormControl>this.addGuestForm.controls['adult'];
    let child = <FormControl>this.addGuestForm.controls['child'];
    let guests = <FormArray>this.addGuestForm.controls['guests'];
    let age = guests.controls[index].get('age');
    age.setValidators([Validators.required, CustomValidators.lte(12), CustomValidators.gt(0)]);
    age.updateValueAndValidity();
    adult.patchValue(adult.value - 1);
    child.patchValue(child.value + 1);
  }

  initGuestForm(type: string, bookingStatus: string) {
    // console.log("this.guestForm : ", this.guestForm);
    this.guestForm = this._fb.group({
      // --- No need of Main guest Separate field, you can delete it later with the attached HTML markup --- //
      // main_guest: this.initGuest(this.allGuestObject.mainGuest),
      abletocheckin: [false],
      payment_date: [moment().format('YYYY-MM-DD') + 'T00:00:00.000Z'],
      guests: this._fb.array([])
    })
    // console.log("Heyyyyyyyyyyyyyyyyyyyyyyy");
    if (this.allGuestObject.guests.length > 0) {
      // console.log("Entered true condition : ");
      this.allGuestObject.guests.forEach((element, index) => {
        let control = <FormArray>this.guestForm.controls['guests'];

        let uploaders: FileUploader = new FileUploader({
          url: URL,
          allowedMimeType: allowedMimeTypes,
        });
        uploaders.onCancelItem = (item: any, filter: any, option: any) => {
          // console.log("Heyyyyyyyyyyyy yes");
        }
        uploaders.onWhenAddingFileFailed = (item: any, filter: any, option: any) => {
          this.showNotification(`${item.type} format is not supported!`, "error", true, 5);
        };
        uploaders.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
          //things to do on completion
          // console.log("Imageeeee OnComplete : ",response);
          if (response) {
            let res = JSON.parse(response);
            // this.addUploadedImages(res);
            let control = <FormArray>this.guestForm.controls['guests'];
            let cc = <FormControl>control.controls[index].get('PC_proof')
            let group = {
              mimetype: res.data.mimetype,
              extension: res.data.extension,
              oldName: res.data.oldName,
              originalName: res.data.originalName,
              size: res.data.size,
              uploaded: res.data.uploaded,
              is_deleted: res.data.is_deleted,
              is_doc: res.data.is_doc
            }
            cc.setValue(group)
          }
        };
        uploaders.onBeforeUploadItem = (item: any) => {
          console.log(item, 'item')
          // image uploaded - add token of auth
          let token = this.authGuard.ud.session_id;
          let timestamp = (+ new Date()).toString();
          let generatedToken = CryptoJS.AES.encrypt(token,
            _secretKey_auth);
          uploaders.authToken = generatedToken;
        };

        let uploaders2: FileUploader = new FileUploader({
          url: URL,
          allowedMimeType: allowedMimeTypes,
        });
        uploaders2.onCancelItem = (item: any, filter: any, option: any) => {
          // console.log("Heyyyyyyyyyyyy yes");
        }
        uploaders2.onWhenAddingFileFailed = (item: any, filter: any, option: any) => {
          this.showNotification(`${item.type} format is not supported!`, "error", true, 5);
        };
        uploaders2.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
          //things to do on completion
          // console.log("Imageeeee OnComplete : ",response);
          if (response) {
            let res = JSON.parse(response);
            // this.addUploadedImages(res);
            let control = <FormArray>this.guestForm.controls['guests'];
            let cc = <FormControl>control.controls[index].get('AC_proof')
            let group = {
              mimetype: res.data.mimetype,
              extension: res.data.extension,
              oldName: res.data.oldName,
              originalName: res.data.originalName,
              size: res.data.size,
              uploaded: res.data.uploaded,
              is_deleted: res.data.is_deleted,
              is_doc: res.data.is_doc
            }
            cc.setValue(group)
          }
        };
        uploaders2.onBeforeUploadItem = (item: any) => {
          console.log(item, 'item')
          // image uploaded - add token of auth
          let token = this.authGuard.ud.session_id;
          let timestamp = (+ new Date()).toString();
          let generatedToken = CryptoJS.AES.encrypt(token,
            _secretKey_auth);
          uploaders2.authToken = generatedToken;
        };



        let uploaders3: FileUploader = new FileUploader({
          url: URL,
          allowedMimeType: allowedMimeTypes,
        });
        uploaders3.onCancelItem = (item: any, filter: any, option: any) => {
          // console.log("Heyyyyyyyyyyyy yes");
        }
        uploaders3.onWhenAddingFileFailed = (item: any, filter: any, option: any) => {
          this.showNotification(`${item.type} format is not supported!`, "error", true, 5);
        };
        uploaders3.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
          //things to do on completion
          // console.log("Imageeeee OnComplete : ",response);
          if (response) {
            let res = JSON.parse(response);
            // this.addUploadedImages(res);
            let control = <FormArray>this.guestForm.controls['guests'];
            let cc = <FormControl>control.controls[index].get('proof')
            let group = {
              mimetype: res.data.mimetype,
              extension: res.data.extension,
              oldName: res.data.oldName,
              originalName: res.data.originalName,
              size: res.data.size,
              uploaded: res.data.uploaded,
              is_deleted: res.data.is_deleted,
              is_doc: res.data.is_doc
            }
            cc.setValue(group)
          }
        };
        uploaders3.onBeforeUploadItem = (item: any) => {
          console.log(item, 'item')
          // image uploaded - add token of auth
          let token = this.authGuard.ud.session_id;
          let timestamp = (+ new Date()).toString();
          let generatedToken = CryptoJS.AES.encrypt(token,
            _secretKey_auth);
          uploaders3.authToken = generatedToken;
        };
        console.log("uploaders5882", uploaders)

        this.PanCardFileuploaders.push(uploaders);
        this.AadharCardFileuploaders.push(uploaders2);

        this.Fileuploaders.push(uploaders3);
        console.log("this.Fileuploaders :  ", this.Fileuploaders);
        control.push(this.initGuest(element, type, bookingStatus))
      });
    }
    // console.log("Imageeeee Before this.guestForm.valuethis.guestForm.value: ", this.guestForm.value);
  }
  updateAge(index: number) {
    let guestArray = <FormArray>this.guestForm.controls['guests'];
    let guest = <FormGroup>guestArray.controls[index];
    let dob = guest.controls['dob'];
    setTimeout(() => {
      guest.controls['age'].patchValue(moment().diff(moment(dob.value).format('YYYY-MM-DD'), 'years'));
      guest.controls['age'].updateValueAndValidity();
    }, 0);
  }
  updateDob(index: number) {
    let guestArray = <FormArray>this.guestForm.controls['guests'];
    let guest = <FormGroup>guestArray.controls[index];
    let age = guest.controls['age'];
    let date = age.value == '' ? new Date(moment().subtract(1, 'days').format('YYYY-MM-DD')) : new Date(moment().subtract(age.value, 'years').format('YYYY-MM-DD'));
    setTimeout(() => {
      guest.controls['dob'].setValue(date);
      guest.controls['dob'].updateValueAndValidity();
    }, 0);
  }
  updateAddGuestDOB(index: number) {
    let guestArray = <FormArray>this.addGuestForm.controls['guests'];
    let guest = <FormGroup>guestArray.controls[index];
    let age = guest.controls['age'];
    let date = age.value == '' ? new Date(moment().subtract(1, 'days').format('YYYY-MM-DD')) : new Date(moment().subtract(age.value, 'years').format('YYYY-MM-DD'));
    setTimeout(() => {
      guest.controls['dob'].setValue(date);
      guest.controls['dob'].updateValueAndValidity();
    }, 0);
  }
  updateAddGuestAge(index: number) {
    let guestArray = <FormArray>this.addGuestForm.controls['guests'];
    let guest = <FormGroup>guestArray.controls[index];
    let dob = guest.controls['dob'];
    setTimeout(() => {
      guest.controls['age'].patchValue(moment().diff(moment(dob.value).format('YYYY-MM-DD'), 'years'));
      guest.controls['age'].updateValueAndValidity();
    }, 0);
  }
  //Remove Document from required bcz there is not
  initGuest(guest, type: string, bookingStatus: string) {
    let control: FormGroup;
    if (guest.is_billingUser) {
      control = this._fb.group({
        name: ['', [Validators.required, GeneralValidations.spaceCharCheck]],
        contact: ['', Validators.required],
        email: ['', [CustomValidators.email, Validators.required]],
        document: [''],
        gender: [, Validators.required],
        guest_maturity_type: ['', Validators.required],
        zip: ['', [CustomValidators.digits, Validators.required]],
        dob: ['', Validators.required],
        is_billingUser: [false, Validators.required],
        pancard_number: ['', [this.validPanField]],
        aadharcard_number: ['', [Validators.pattern('^[2-9]{1}[0-9]{3}\[0-9]{4}\[0-9]{4}$')]],
        proof_value: [''],
        proof_type: [''],
        country: [103, Validators.required],
        address: ['', Validators.required],
        PC_proof: [''],
        AC_proof: [''],
        proof: [''],
        city: ['', Validators.required],
        age: ['', Validators.required],
        id: ['', Validators.required],
        show: [false],
        // can_delete: [false],
        booking_id: [guest.booking_id],
        guest_id: [guest.id],
        room_id: [guest.room_id],
        room_name: [guest.room_name]
        // status: [true],
      });
    }
    else {
      control = this._fb.group({
        name: ['', [Validators.required, GeneralValidations.spaceCharCheck]],
        contact: [''],
        email: ['', CustomValidators.email],
        document: [''],
        // gender: [, Validators.required],
        gender: [],
        // guest_maturity_type: ['', Validators.required],
        guest_maturity_type: [''],
        zip: ['', CustomValidators.digits],
        // dob: ['', Validators.required],
        dob: [''],
        is_billingUser: [false],
        PC_proof: [''],
        AC_proof: [''],
pancard_number: ['', [this.validPanField]],
 aadharcard_number: ['', [Validators.pattern('^[2-9]{1}[0-9]{3}\[0-9]{4}\[0-9]{4}$')]],
        proof_value: [],
        proof_type: [],
        country: [103],
        address: [],
        proof: [],
        city: [],
        // age: ['', Validators.required],
        age: [''],
        id: [],
        show: [false],
        // can_delete: [false],
        booking_id: [guest.booking_id],
        guest_id: [guest.id],
        room_id: [guest.room_id],
        room_name: [guest.room_name]
        // status: [true],
      });
    }
    if (!guest.is_billingUser && bookingStatus == "reserved") {
      control.addControl('can_delete', new FormControl(false));
    }
    if (guest.proof_type == "5") {
      control.addControl('other_proof_type', new FormControl('', Validators.required));
    }
    if (guest.is_billingUser || type === 'checkout') {
      if (type === 'checkout') {
        control.get('name').setValidators(GeneralValidations.defaultNameCheck);
        control.get('gender').setValidators(Validators.required);
        control.get('guest_maturity_type').setValidators(Validators.required);
        control.get('dob').setValidators(Validators.required);
      }
      if (guest.guest_maturity_type == "child") {
        control.get('age').setValidators([Validators.required, CustomValidators.lte(12)]);
      }
      else {
        control.get('age').setValidators([Validators.required, CustomValidators.gt(12)]);
      }
    }
    guest.proof_type = guest.proof_type ? guest.proof_type : '0';
    guest.dob = guest.dob ? new Date(guest.dob.toString()) : null;
    guest.age = guest.age ? guest.age : guest.dob ? moment().diff(moment(guest.dob), 'years') : null;
    control.patchValue(guest);
    control.controls['country'].patchValue(guest.country ? guest.country : 103);
    return control;
  }

  adultChanged(index: number) {
    let guests = <FormArray>this.guestForm.controls['guests'];
    let age = guests.controls[index].get('age');
    let dob = guests.controls[index].get('dob');
    age.clearValidators();
    age.setValidators([Validators.required, CustomValidators.gt(12)]);
    age.updateValueAndValidity();
    if (age.value <= 12) {
      age.setErrors({ 'gt': true });
      age.markAsDirty();
      age.markAsTouched();
      age.updateValueAndValidity();
    }
  }

  childChanged(index: number) {
    let guests = <FormArray>this.guestForm.controls['guests'];
    let age = guests.controls[index].get('age');
    let dob = guests.controls[index].get('dob');
    age.clearValidators();
    age.setValidators([Validators.required, CustomValidators.lte(12)]);
    age.updateValueAndValidity();
    if (age.value > 12) {
      age.setErrors({ 'lte': true });
      age.markAsDirty();
      age.markAsTouched();
      age.updateValueAndValidity();
    }
  }

  deleteUploadedImg(item: any, index: number) {
    // console.log("Proooofffffffffffffffff :", item);
    let guests = <FormArray>this.guestForm.controls['guests'];
    let guest = <FormGroup>guests.controls[index];
    guest.controls['proof'].patchValue(null);
    // console.log("guest control : ",guest.controls['proof']);
  }
  saveGuestDetails(content: any) {

    console.log("CONTENT : ",this.guestForm.value);
    console.log("formValue : ",this.guestForm);

    if (this.guestForm.valid) {
      let guests = this.guestForm.value;
      guests.guests.forEach(guest => {
        if(guest.PC_proof){
          guest.PC_proof['displayname'] = guest.name + '_' + 'Pan Card' +
          '_' + Math.floor(Math.random() * 100000) + '.' + guest.PC_proof.extension;
        }
        if(guest.AC_proof){
          guest.AC_proof['displayname'] = guest.name + '_' + 'Aadhaar Card' +
          '_' + Math.floor(Math.random() * 100000) + '.' + guest.AC_proof.extension;
        }
        if (guest.proof) {
          guest.proof['displayname'] = guest.name + '_' + this.proofTypeList[this.findIndex(guest.proof_type, "id", this.proofTypeList) - 1].text +
            '_' + Math.floor(Math.random() * 100000) + '.' + guest.proof.extension;
        }
      });
      console.log("212222 : ",this.guestForm.value);

      this.saveGuestInfo = this.BS.saveGuest(this.guestForm.value, content.booking_id)
        .subscribe((res) => {
          if (res.status == "success") {
            this.PanCardFileuploaders.forEach((res) => {
              res.queue = [];
            });
            this.AadharCardFileuploaders.forEach((res) => {
              res.queue = [];
            });
            this.Fileuploaders.forEach((res) => {
              res.queue = [];
            });
            let abletocheckin = this.guestForm.controls['abletocheckin'];
            abletocheckin.patchValue(true);
            if (content.type === 'checkIn') {
              this.checkInAfterGuestSaved(content, res.data.all_guest_details_added);
            }
            else if (content.type === 'checkout') {
              Messenger().hideAll();
              let index2 = 0;
              this.staticTabs.tabs.forEach((ele, index) => {
                if (ele.active == true) {
                  index2 = index;
                }
              })
              this.removeTabHandler(this.staticTabs.tabs[index2])
              this.staticTabs.tabs[0].active = true;
              if (res.data.all_guest_details_added) {
                this.updateBookingCalendarField(content.booking_id, res.data.all_guest_details_added, 'all_guest_details_added');
                let allbookingIndex = _.findIndex(this.allBookings, ['booking_id', content.booking_id]);
                let originalAllBookingsIndex = _.findIndex(this.originalAllBookings, ['booking_id', content.booking_id]);
                if (allbookingIndex > -1) {
                  this.allBookings[allbookingIndex].all_guest_details_added = res.data.all_guest_details_added;
                }
                if (originalAllBookingsIndex > -1) {
                  this.originalAllBookings[originalAllBookingsIndex].all_guest_details_added = res.data.all_guest_details_added;
                }
              }
              this.showNotification("You can checkout now! Try again", "info");
            }
            else {
              window.location.reload();
            }
          }
        })
    }
    else {
      let guests = <FormArray>this.guestForm.controls['guests'];
      for (let i = 0; i < guests.controls.length; i++) {
        let guest = <FormGroup>guests.controls[i];
        let keys = Object.keys(guest.controls);
        for (let j = 0; j < keys.length; j++) {
          if (guest.controls['is_billingUser'].value && keys[j] == 'show') {
            guest.controls[keys[j]].patchValue(true);
          }
          guest.controls[keys[j]].markAsTouched();
          guest.controls[keys[j]].markAsDirty();
        }
      }
    }
  }
  showMoreOn(index: number) {
    let guests = <FormArray>this.guestForm.controls['guests'];
    let show = guests.controls[index].get('show');
    show.patchValue(!show.value);
  }
  deleteGuest(bookingId: number, item, index: number) {
    let guestForm = <FormArray>this.guestForm.controls['guests'];
    let roomMemberCount = 0;
    guestForm.value.forEach((guest) => {
      if (guest.room_id == item.value.room_id) {
        roomMemberCount += 1;
      }
    });
    if (roomMemberCount <= 1) {
      this.modalAlertType = 'delete-guest';
      this.showModalAlert('delete-guest', item, index);
    } else {
      this.proccedToGuestDelete(item, index);
    }
  }
  listenToModalAlertEvents(item, index) {
    this.deleteGuestSubcription = this.deleteGuestConfirmationTypeModal.onHide.subscribe(() => {
      if (this.canDeleteRoomMainGuest === 'delete-guest') {
        this.proccedToGuestDelete(item, index);
      } else if (this.canDeleteRoomMainGuest === 'checkout') {
        this.processRoomCheckOut(item);
      }
    });
    this.deleteGuestOnHiddenSubscription = this.deleteGuestConfirmationTypeModal.onHidden.subscribe(() => {
      this.unsubscribeDeleteGuest();
      this.canDeleteRoomMainGuest = undefined;
      this.modalAlertType = undefined;
    });
  }
  showModalAlert(type: string, item: any, index?: number) {
    this.modalAlertType = type;
    this.listenToModalAlertEvents(item, index);
    this.deleteGuestConfirmationTypeModal.show();
  }
  unsubscribeDeleteGuest() {
    if (this.deleteGuestSubcription) {
      this.deleteGuestSubcription.unsubscribe();
    }
    if (this.deleteGuestOnHiddenSubscription) {
      this.deleteGuestOnHiddenSubscription.unsubscribe();
    }
  }
  proccedToGuestDelete(item: any, index: number) {
    this.deleteBookingGuest = this.BS.deleteBookingGuest(item.value.booking_id, item.value.id, item.value.room_id)
      .subscribe((res) => {
        if (res.status) {
          let guests = <FormArray>this.guestForm.controls['guests'];
          let roomMemberCount = 0;
          let guestIndex;
          guests.removeAt(index);
          guests.value.forEach((guest, index) => {
            if (guest.room_id == item.value.room_id) {
              roomMemberCount += 1;
              guestIndex = index;
            }
          });
          if (roomMemberCount == 1 && !guests.controls[guestIndex].get('is_billingUser').value) {
            guests.controls[guestIndex].get('can_delete').patchValue(true);
          }
          // Deletes Room cell from calendar after main guest of the room is deleted
          if (this.canDeleteRoomMainGuest) {
            let allbookingIndex = _.findIndex(this.allBookings, (booking) => {
              return booking.booking_id == item.value.booking_id && booking.room_id == item.value.room_id;
            });
            let originalAllBookingsIndex = _.findIndex(this.originalAllBookings, (booking) => {
              return booking.booking_id == item.value.booking_id && booking.room_id == item.value.room_id;
            });
            if (allbookingIndex > -1 && originalAllBookingsIndex > -1) {
              this.allBookings.splice(allbookingIndex, 1);
              this.originalAllBookings.splice(originalAllBookingsIndex, 1);
            }
            for (let i = 0; i < this.DateRangesWithBookingArray.length; i++) {
              let element = this.DateRangesWithBookingArray[i];
              for (let j = 0; j < element.room.length; j++) {
                let element2 = element.room[j];
                if (element2.room_id == item.value.room_id) {
                  for (let k = 0; k < element2.dates.length; k++) {
                    let ele = element2.dates[k];
                    if (ele.booking_id == item.value.booking_id) {
                      ele['is_booked'] = false;
                      delete ele['booking_id'];
                      delete ele['booking_status'];
                      delete ele['username'];
                      delete ele['start'];
                      delete ele['end'];
                      if (ele['is_starting']) {
                        ele['is_starting'] = false;
                      }
                      if (ele['is_ending']) {
                        ele['is_ending'] = false;
                      }
                      break;
                    };
                  };
                  break;
                };
              };
            };
          }
        }
      });
  }
  deleteNote(note: any) {
    let index = this.findIndex(note.id, "id", this.notesArray) - 1;
    this.notesArray.splice(index, 1);
    let notes = <FormArray>this.addEditNoteForm.controls['note'];
    notes.removeAt(index);
  }
  addNote(noteArray: any[]) {
    let length = noteArray.length;
    this.initNote(null, parseInt(noteArray[length - 1].id))
    this.notesArray.push({ id: parseInt(noteArray[length - 1].id) + 1, message: '' });
  }
  preAddEditBookingNote(item: any) {
    this.checkForExtraBooking(item).then((res: any) => {
      this.addEditBookingNote(res);
    }).catch((err) => { });
  }
  /**
   * Add/Edit booking note
   *
   */
  addEditBookingNote(item: any) {
    // console.log("item------------------------",item)
    this.getBookingNote = this.BS.getBookingNote(item.booking_id)
      .subscribe((res) => {
        if (res.status == "success") {
          this.addEditNoteForm = this._fb.group({
            note: this._fb.array([])
          });
          this.notesArray = res.data.note;
          if (res.data.note && res.data.note.length > 0) {
            res.data.note.forEach(note => {
              this.initNote(note);
            });
          }
          else {
            this.initNote(null);
            this.notesArray = [{ id: 1, message: '' }];
          }
          if (item.hasOwnProperty('is_extra_booking')) {
            item['start'] = moment(item.start).format('DD MMM');
            item['end'] = moment(item.end).format('DD MMM');
          }
          item['details'] = item;
          item['isAdd'] = res.data.note ? false : true;
          item['action_type'] = 'booking_note';
          this.addNewTab("Add/Edit Note", _.cloneDeep(item));
          console.log('6501',item);

        }
      });
  }
  initNote(note?: any, newID?: number) {
    let formNote = <FormArray>this.addEditNoteForm.controls['note'];
    let control = this._fb.group({
      id: [note ? note.id : newID ? (newID + 1) : 1],
      message: [note ? note.message : '', Validators.required]
    });
    formNote.push(control);
  }
  /**
   * save edit/add functionality
   * @param booking_id
   */
  saveBookingNote(content, tabIndex) {
    console.log("???>?>????");

    if (this.addEditNoteForm.valid) {
      this.saveNote = this.BS.saveBookingNote(content.booking_id, this.addEditNoteForm.value)
        .subscribe((res) => {
          if (res.status == "success") {
            this.removeTabByIndex(tabIndex);
            this.updateBookingCalendarField(content.booking_id, this.addEditNoteForm.value, 'booking_note');
          }
        });
    }
  }
  updateBookingCalendarField(id: any, fieldValue: any, fieldName: string) {
    this.DateRangesWithBookingArray.forEach((element) => {
      element.room.forEach(element2 => {
        element2.dates.forEach(ele => {
          if (ele.booking_id == id) {
            ele[fieldName] = fieldValue;
          }
        });
      });
    });
  }
  /**
   * split booking functionality
   *
   * @param {any} item
   * @memberof ReservationsComponent
   */
  doSplitBooking(item) {
    console.log(item, " ----------item");
    const endDayMonth = item.end;
    const startDayMonth = item.start;
    const year = item.year;

    // Combine into a full date string in a standard format
    const fullEndDateStr = `${endDayMonth} ${year}`; // "05 Aug 2025"
    const fullStartDateStr = `${startDayMonth} ${year}`; // "05 Aug 2025"
    this.extendEndDate = new Date(fullEndDateStr);
    this.extendStartDate = new Date(fullStartDateStr);

    this.viewBooking = this.BS.viewBookingDetails(item.booking_id)
      .subscribe((res) => {
        console.log(res, "ressssssssss", "already cherck");

        // console.log("res status", res.status);
        //console.log("res status", res.data.ExtraGuest.length);
        //return false;
        if (res.status == "success" && res.data.ExtraGuest.length >= 0) {
          this.is_splitbooking = true;
          // console.log("this.tempBookingObjArray : ", this.tempBookingObjArray);
          // console.log("this.bookingSelectionDates : ", this.bookingSelectionDates);
          let itemDetails = res.data;
          itemDetails.bookingRoom = _.head(_.filter(itemDetails.bookingRoom, ['room_id', item.room_id]));
          item['details'] = itemDetails;
          item['action_type'] = "split";
          this.splitBookingOBJ = itemDetails;
          this.initSplitBookingform(item);
          this.addNewTab("Extend Checkout", _.cloneDeep(item));
          console.log('6567',item);
          // console.log("item : ", item);
        }
        else {
          this.showNotification("Split-Booking is not allowed with Extra Added Guest!", "error");
        }
      });
  }
  initSplitBookingform(item) {
    console.log("item", item);

    this.splitBookingForm = this._fb.group({
      dates: this._fb.array([])
    })
    this.setSplitDates(item);
  }
  setSplitDates(item?) {
    this.splitAvailableRoom = [];
    let control = <FormArray>this.splitBookingForm.controls['dates'];
    let start = "";
    let end = "";
    let guest_id = "";
    let guest_name = "";
    let guest_contact = "";
    let guest_email = "";
    let availibility = false;
    let mainGuest = false;
    let room_category_id = "";
    let room_id = "";
    let room_cat_name = "";
    let room_title = "";
    let booking_id = "";
    let time = "";
    let CheckOutTime = "";
    // let total_days = ""
    if (item) {
      time = moment(item.details.bookingRoom.start, 'YYYY-MM-DD').format('YYYY-MM-DD');
      start = moment(item.details.bookingRoom.start, 'YYYY-MM-DD').format('YYYY-MM-DD');
      end = moment(item.details.bookingRoom.end, 'YYYY-MM-DD').format('YYYY-MM-DD');
      guest_id = item.details.bookingRoom.guest_id;
      guest_name = item.details.booking['guest.name'];
      guest_contact = item.details.booking['guest.contact'];
      guest_email = item.details.booking['guest.email'];
      availibility = false;
      mainGuest = true;
      room_category_id = item.details.bookingRoom['room.roomcategory.id'];
      room_id = item.details.bookingRoom['room.id'];
      room_cat_name = item.details.bookingRoom['room.roomcategory.name'];
      room_title = item.details.bookingRoom['room.title'];
      booking_id = item.details.bookingRoom.booking_id;
      CheckOutTime = item.details.bookingRoom.time;
      // total_days = item.details.booking['total_days']

    }
    let group = this._fb.group({
      booking_id: [booking_id],
      start: [start, Validators.required],
      end: [end, Validators.required],
      guest_id: [guest_id],
      guest_name: [guest_name, Validators.required],
      guest_contact: [guest_contact, [Validators.required]],
      guest_email: [guest_email, CustomValidators.email],
      availibility: [availibility],
      mainGuest: [mainGuest],
      room_category_id: [room_category_id, Validators.required],
      room_cat_name: [room_cat_name],
      room_id: [room_id, Validators.required],
      room_title: [room_title],
      CheckOutTime: [CheckOutTime],
      // total_days: 5,


    })
    control.push(group);
  }
  deleteDates(val) {
    let control = <FormArray>this.splitBookingForm.controls['dates'];
    control.removeAt(val);
  }
  patchMain(booking) {
    let control = <FormArray>this.splitBookingForm.controls['dates'];
    let values = jQuery.extend({}, control.controls[0].value);
    booking.controls['guest_id'].patchValue(values.guest_id);
    booking.controls['guest_name'].patchValue(values.guest_name);
    booking.controls['guest_contact'].patchValue(values.guest_contact);
    booking.controls['guest_email'].patchValue(values.guest_email);
  }
  checkSameDate(event, i, booking, type: string) {
    let control = <FormArray>this.splitBookingForm.controls['dates'];
    if (i > 0) {
      for (var j = 0; j < control.value.length - 1; j++) {
        let element = control.value[j];
        let range = moment().range(moment(element.start, 'YYYY-MM-DD'), moment(element.end, 'YYYY-MM-DD'));
        if (range.length != 0) {
          this.splitAvailableRoom = [];
          this.splitbookingFormValid = true;
          this.unSelectAvailableRoom(booking);
        } else {
          this.splitAvailableRoom = [];
          this.splitbookingFormValid = false;
          this.showNotification("Selected Date in not Valid", "error");
          break;
        }
      }

    }
    else {
      let element = control.value[0];
      let start = element.start;
      let end = element.end;
      let range = moment().range(moment(element.start, 'YYYY-MM-DD'), moment(element.end, 'YYYY-MM-DD'));
      // console.log("O INDEX CHANGES....", element.start, element.end, event, type);
      // console.log("Condition 1 : ",moment(element.start,'YYYY_MM_DD').isAfter(moment(element.end,'YYYY-MM-DD')));
      // console.log("Condition 2 : ",moment(element.end,'YYYY_MM_DD').isBefore(moment(element.start,'YYYY-MM-DD')));
      if (type === "start") {
        if (moment(element.end, 'YYYY_MM_DD').isBefore(moment(event, 'YYYY-MM-DD'))) {
          this.showNotification("Selected Date in not Valid", "error", true, 5);
          control.controls[0].get('start').patchValue(new Date(start));
          this.splitDatePickerStart._datePicker.activeDate = new Date(start);
          // console.log('this.splitBookingForm.value : ',this.splitBookingForm.value);
        }
      }
      else {
        if (moment(element.start, 'YYYY_MM_DD').isAfter(moment(event, 'YYYY-MM-DD'))) {
          this.showNotification("Selected Date in not Valid", "error", true, 5);
          control.controls[0].get('end').patchValue(new Date(end));
          this.splitDatePickerEnd._datePicker.activeDate = new Date(end);
          // console.log('this.splitBookingForm.value : ',this.splitBookingForm.value);
        }
      }
    }
    // else {
    //   let element = control.value[i];
    //   let range = moment().range(moment(element.start, 'YYYY-MM-DD'), moment(element.end, 'YYYY-MM-DD'));
    // console.log("Range : ", range);
    //   this.splitAvailableRoom = [];
    //   this.splitbookingFormValid = true;
    //   this.unSelectAvailableRoom(booking);
    // }
  }
  TransferRoom(item, event: Event) {
    console.log(item.booking_id, "item.booking_id");

    this.viewBooking = this.BS.viewBookingDetails(item.booking_id)
      .subscribe((res) => {
        console.log(res, "1111111>>>>>>>>>>>");

        // console.log("res status", res.status);
        //console.log("res status", res.data.ExtraGuest.length);
        //return false;
        if (res.status == "success" && res.data.ExtraGuest.length >= 0) {
          this.is_splitbooking = true;
          // console.log("this.tempBookingObjArray : ", this.tempBookingObjArray);
          // console.log("this.bookingSelectionDates : ", this.bookingSelectionDates);
          let itemDetails = res.data;


          itemDetails.bookingRoom = _.head(_.filter(itemDetails.bookingRoom, ['room_id', item.room_id]));
          item['details'] = itemDetails;
          item['action_type'] = "TransferRoom";
          this.splitBookingOBJ = itemDetails;
          this.initSplitBookingform(item);
          this.addNewTab("Transfer Room", _.cloneDeep(item));
          console.log('6729',item);

          // console.log("item : ", item);
        }
        else {
          this.showNotification("Transfer room is not allowed with Extra Added Guest!", "error");
        }
      });
  }


  searchAvailibility(val: any, formIndex: number) {

    let selectedRooms = <FormArray>this.splitBookingForm.controls['dates'];
    console.log("Booking Details : ", selectedRooms);
    this.selectFormIndex = formIndex;
    let data = jQuery.extend({}, val.value);
    data.start = moment(data.start, 'YYYY-MM-DD').format('YYYY-MM-DD');
    data.end = moment(data.end, 'YYYY-MM-DD').format('YYYY-MM-DD');
    if (!data.room_category_id || data.room_category_id == '') {
      data.room_category_id = selectedRooms.controls[0].get('room_category_id').value;
    }
    this.roomAvailibility = this.BS.getRoomAvailibility(data)
      .subscribe((res) => {
        if (res.status == "success") {
          this.splitAvailableRoom = [];
          let data = res.data;
          selectedRooms.controls.forEach(element => {
            let index = this.findIndex(element.value.room_id, "room_id", data) - 1;
            if (index > -1) {
              data.splice(index, 1);
            }
          });
          this.splitAvailableRoom = data;
          // console.log(data, "-----------data>>>>>>>");

        }
      }, (err) => {
        this.splitAvailableRoom = [];
      })
  }
  public SelectRoomBookingId: any
  public selectedRoomDoorId: any
  selectAvailableRoom(room, booking, tabz) {
    let TempJson = { id: room['room_id'] }
    this.reservation_S.GetDoorId(TempJson).subscribe((data: any) => {
      this.selectedRoomDoorId = data.data.Room['door_id']
    })

    this.SelectRoomBookingId = booking.value['booking_id']

    this.SelectedRoom = room['room_id']

    this.AvailableTransferRoom = room.room_title
    booking.controls['room_id'].patchValue(room.room_id);
    booking.controls['room_title'].patchValue(room.room_title);
    booking.controls['room_category_id'].patchValue(room.roomCategory_id);
    booking.controls['room_cat_name'].patchValue(room.roomCategory_name);
    booking.controls['availibility'].patchValue(true);
    this.alottedRooms.push(room.room_id);
    this.splitAvailableRoom = [];
  }
  unSelectAvailableRoom(booking) {
    console.log(booking, "unseleeavailabe");

    booking.controls['room_id'].patchValue("");
    booking.controls['room_title'].patchValue("");
    booking.controls['room_category_id'].patchValue("");
    booking.controls['room_cat_name'].patchValue("");
    booking.controls['availibility'].patchValue(false);
  }
  makeSplitBooking(id, room_id, tabz) {
    if (this.splitbookingFormValid) {
      let orignalStart = moment(this.splitBookingOBJ.bookingRoom.start, 'YYYY-MM-DD').format('YYYY-MM-DD');
      let orignalEnd = moment(this.splitBookingOBJ.bookingRoom.end, 'YYYY-MM-DD').format('YYYY-MM-DD');
      let orignalDateArray = this.enumerateDaysBetweenDates(moment(orignalStart, 'YYYY-MM-DD'), moment(orignalEnd, 'YYYY-MM-DD'));
      let deletedArray: any[] = [];
      this.splitBookingForm.value.dates.forEach(iDate => {
        let iRange = moment().range(moment(iDate.start, 'YYYY-MM-DD'), moment(iDate.end, 'YYYY-MM-DD'));
        let newArray: any[] = [];
        orignalDateArray.forEach((date) => {
          if (!iRange.contains(moment(date, 'YYYY-MM-DD'))) {
            newArray.push(date);
          }
        })
        orignalDateArray = newArray;
        newArray = [];
      });
      let i = 0;
      do {
        let TempObj = {};
        let element = orignalDateArray[i];
        TempObj['start'] = element;
        TempObj['end'] = element;
        for (var j = i + 1; j < orignalDateArray.length; j++) {
          if (moment(TempObj['end']).diff(orignalDateArray[j], 'days') == -1) {
            TempObj['end'] = orignalDateArray[j];
            i = j;
          }
        }
        i++;
        let range = this.enumerateDaysBetweenDates(moment(TempObj['start'], 'YYYY-MM-DD'), moment(TempObj['end'], 'YYYY-MM-DD'))
        TempObj['total_days'] = range.length;
        deletedArray.push(TempObj);
      } while (i < orignalDateArray.length)

      this.splitBookingForm.value['deleted'] = deletedArray;


      if (tabz.content.details.bookingRoom.current_status == 'checkin' && tabz && tabz.content && tabz.content.door_id && tabz.content.door_id != '') {
        //Popup code Start
        this.dynamicCheckinSuccess = false;
        let date;

        let newStartDate, newEndDate;
        this.splitBookingForm.value.dates.forEach(element => {
          element.start = moment(element.start, 'YYYY-MM-DD').format('YYYY-MM-DD');
          element.end = moment(element.end, 'YYYY-MM-DD').format('YYYY-MM-DD');


          var oldEndDate = new Date(element.CheckOutTime);
          console.log(oldEndDate, "tempNewDatetempNewDate121212");

          let tempNewDate = new Date(element.end);
          tempNewDate.setHours(oldEndDate.getHours());
          tempNewDate.setMinutes(oldEndDate.getMinutes());

          let newDateCard = new Date(element.end);
          newDateCard.setHours(oldEndDate.getHours());
          newDateCard.setMinutes(oldEndDate.getMinutes());
          newDateCard.setDate(newDateCard.getDate() + 1)
          newDateCard = moment(newDateCard).format('YYMMDDHHmm');
          console.log(newDateCard, "ChecknewEndDate");
          this.cardExtendNewDate = newDateCard

          newStartDate = moment(this.splitBookingOBJ.bookingRoom.expected_check_in).format('YYMMDDHHmm');
          // tempNewDate.setDate(tempNewDate.getDate() + 1)

          newEndDate = moment(tempNewDate).format('YYMMDDHHmm');
          console.log(newEndDate, "newEndDate");
          let range = this.enumerateDaysBetweenDates(moment(element.start, 'YYYY-MM-DD'), moment(element.end, 'YYYY-MM-DD'))
          element['total_days'] = range.length;
        });



        this.splitBookingData = {
          "currentOperation": "ExtendCheckout",
          "startDateAndTime": newStartDate,
          "endDateAndTime": this.cardExtendNewDate,
          "roomId": room_id,
          "doorId": tabz.content.door_id
          // "time" : time
          // || (door_id) need to change based on response
        };
        this.splitBookingDynamicModalAction();



        this.reservation_S.getSplitBookingPreProcess(this.splitBookingData).subscribe((data) => {
          console.log(this.splitBookingForm.value, "this.splitBookingData");
          console.log(this.time, " tietime");
          //Success
          if (data['status'] == true || data['status'] == 'true') {
            this.showData = true;
            this.showError = false;
            this.dynamicCheckinSuccess = data.status;
            this.dynamicCheckinResponseMessage = data.message;


            //put it at the end of card processing method.
            this.makeSplitBookingService = this.BS.saveSplitBooking(id, room_id, this.splitBookingForm.value)
              .subscribe((res) => {
                console.log(this.splitBookingForm.value.dates, "this.splitBookingForm.value 19 , 12");

                if (res.status == "success") {
                  // window.location.reload();
                  let index2 = 0;
                  this.staticTabs.tabs.forEach((ele, index) => {
                    if (ele.active == true) {
                      index2 = index;
                    }
                  })
                  this.removeTabHandler(this.staticTabs.tabs[index2])
                  this.staticTabs.tabs[0].active = true;
                  this.fillData();
                }
              })
          } else if (data['status'] == false || data['status'] == 'false') {
            this.dynamicCheckinSuccess = true;
            this.dynamicCheckinResponseMessage = data.message;
            this.showData = true;
            this.showError = false;
          }
        }, (error) => {
          this.showData = false;
          this.showError = true;
        });

        //Popup Code end
      } else {
        let date;

        let newStartDate, newEndDate;
        this.splitBookingForm.value.dates.forEach(element => {
          element.start = moment(element.start, 'YYYY-MM-DD').format('YYYY-MM-DD');
          element.end = moment(element.end, 'YYYY-MM-DD').format('YYYY-MM-DD');
          var oldEndDate = new Date(element.CheckOutTime);
          let tempNewDate = new Date(element.end);
          tempNewDate.setHours(oldEndDate.getHours());
          tempNewDate.setMinutes(oldEndDate.getMinutes());
          if(element.CheckOutTime)
          element.CheckOutTime =tempNewDate;
          newStartDate = moment(this.splitBookingOBJ.bookingRoom.expected_check_in).format('YYMMDDHHmm');
          newEndDate = moment(tempNewDate).format('YYMMDDHHmm');
          console.log(newEndDate, "newEndDate");
          let range = this.enumerateDaysBetweenDates(moment(element.start, 'YYYY-MM-DD'), moment(element.end, 'YYYY-MM-DD'))
          element['total_days'] = range.length;
        });

        //put it at the end of card processing method.
        this.makeSplitBookingService = this.BS.saveSplitBooking(id, room_id, this.splitBookingForm.value)
          .subscribe((res) => {
            console.log("else without card");

            if (res.status == "success") {
              // window.location.reload();
              let index2 = 0;
              this.staticTabs.tabs.forEach((ele, index) => {
                if (ele.active == true) {
                  index2 = index;
                }
              })
              this.removeTabHandler(this.staticTabs.tabs[index2])
              this.staticTabs.tabs[0].active = true;
              this.fillData();
            }
          })
      }
    } else {
      this.showNotification("Dates are not Valid", "error", true, 5);
    }

  }

  acceptBookingRequest(data: any) {
    console.log(data, "Accept Data");
    if (data.bookingPaymentData.length > 0) {
      this.approvePaymentRequest()
    } else {

      this.acceptButtonLoader = true;
      this.BS.approveOnlineBooking(this.acceptBookingData).subscribe((response) => {
          if (response.status === "success") {
              this.acceptBookingStyle = 'alert alert-success'
              this.acceptErrorMessage = response.message;
              this.acceptButtonLoader = false
              // console.log("acceptlation successful", response);
              setTimeout(() => {
                  this.acceptBookingModal.hide();
                  this.viewDetailsModal.hide();
                  this.acceptErrorMessage = ''
              }, 2800);
          }
  
      }, (error) => {
          console.log('error in acceptling booking', error);
          if (error) {
              this.acceptBookingStyle = 'alert alert-danger'
              this.acceptErrorMessage = error.message;
              this.acceptButtonLoader = false
              setTimeout(() => {
                  this.acceptBookingModal.hide();
                  this.viewDetailsModal.hide();
                  this.acceptErrorMessage = ''
              }, 3000);
          }
      }
      )
    }
}

  //transferroom
  maketransferRoom(id, room_id, tabz) {


    console.log(room_id, ">>>>>>>>>>");


    if (this.splitbookingFormValid) {
      let orignalStart = moment(this.splitBookingOBJ.bookingRoom.start, 'YYYY-MM-DD').format('YYYY-MM-DD');
      let orignalEnd = moment(this.splitBookingOBJ.bookingRoom.end, 'YYYY-MM-DD').format('YYYY-MM-DD');
      let orignalDateArray = this.enumerateDaysBetweenDates(moment(orignalStart, 'YYYY-MM-DD'), moment(orignalEnd, 'YYYY-MM-DD'));
      let deletedArray: any[] = [];
      this.splitBookingForm.value.dates.forEach(iDate => {
        let iRange = moment().range(moment(iDate.start, 'YYYY-MM-DD'), moment(iDate.end, 'YYYY-MM-DD'));
        let newArray: any[] = [];
        orignalDateArray.forEach((date) => {
          if (!iRange.contains(moment(date, 'YYYY-MM-DD'))) {
            newArray.push(date);
          }
        })
        orignalDateArray = newArray;
        newArray = [];
      });
      let i = 0;
      do {
        let TempObj = {};
        let element = orignalDateArray[i];
        TempObj['start'] = element;
        TempObj['end'] = element;
        for (var j = i + 1; j < orignalDateArray.length; j++) {
          if (moment(TempObj['end']).diff(orignalDateArray[j], 'days') == -1) {
            TempObj['end'] = orignalDateArray[j];
            i = j;
          }
        }
        i++;
        let range = this.enumerateDaysBetweenDates(moment(TempObj['start'], 'YYYY-MM-DD'), moment(TempObj['end'], 'YYYY-MM-DD'))
        TempObj['total_days'] = range.length;
        deletedArray.push(TempObj);
      } while (i < orignalDateArray.length)

      this.splitBookingForm.value['deleted'] = deletedArray;


      if (tabz.content.details.bookingRoom.current_status == 'checkin' && tabz && tabz.content && tabz.content.door_id && tabz.content.door_id != '') {
        //Popup code Start

        this.dynamicCheckinSuccess = false;
        let date;

        let newStartDate, newEndDate;
        this.splitBookingForm.value.dates.forEach(element => {
          element.start = moment(element.start, 'YYYY-MM-DD').format('YYYY-MM-DD');
          element.end = moment(element.end, 'YYYY-MM-DD').format('YYYY-MM-DD');
          console.log(element.CheckOutTime, "this.splitBookingForm.value");

          var oldEndDate = new Date(element.CheckOutTime);
          let tempNewDate = new Date(element.end);
          tempNewDate.setHours(oldEndDate.getHours());
          tempNewDate.setMinutes(oldEndDate.getMinutes());
          if(element.CheckOutTime)
          element.CheckOutTime =tempNewDate;
          newStartDate = moment(this.splitBookingOBJ.bookingRoom.expected_check_in).format('YYMMDDHHmm');
          newEndDate = moment(tempNewDate).format('YYMMDDHHmm');
          console.log(newEndDate, "newEndDate");
          let range = this.enumerateDaysBetweenDates(moment(element.start, 'YYYY-MM-DD'), moment(element.end, 'YYYY-MM-DD'))
          element['total_days'] = range.length;
        });
        console.log(this.SelectedRoom, room_id, "---------------");
        let door_id

        this.TransferRoomData = {
          "currentOperation": "TransferRoom",
          "startDateAndTime": newStartDate,
          "endDateAndTime": newEndDate,
          "newRoomId": this.SelectedRoom || room_id,
          "newDoorId": this.selectedRoomDoorId,
          "RoomId": room_id,
          "doorId": tabz.content.door_id,
        };
        this.TransferRoomDynamicMOdelAction();



        this.reservation_S.getTransferRoomBookingPreProcess(this.TransferRoomData).subscribe((data) => {
          console.log(this.time, " tietime");
          //Success
          if (data['status'] == true || data['status'] == 'true') {
            this.SelectedRoom = ''
            this.showData = true;
            this.showError = false;
            this.dynamicCheckinSuccess = data.status;
            this.dynamicCheckinResponseMessage = data.message;


            //put it at the end of card processing method.
            this.makeSplitBookingService = this.BS.saveTransferRoomBooking(id, room_id, this.splitBookingForm.value)
              .subscribe((res) => {
                if (res.status == "success") {
                  // window.location.reload();
                  let index2 = 0;
                  this.staticTabs.tabs.forEach((ele, index) => {
                    if (ele.active == true) {
                      index2 = index;
                    }
                  })
                  this.removeTabHandler(this.staticTabs.tabs[index2])
                  this.staticTabs.tabs[0].active = true;
                  this.fillData();
                }
              })
          } else if (data['status'] == false || data['status'] == 'false') {
            this.dynamicCheckinSuccess = true;
            this.dynamicCheckinResponseMessage = data.message;
            this.showData = true;
            this.showError = false;
          }
        }, (error) => {
          this.showData = false;
          this.showError = true;
        });

        //Popup Code end
      } else {
        let date;

        let newStartDate, newEndDate;
        this.splitBookingForm.value.dates.forEach(element => {
          element.start = moment(element.start, 'YYYY-MM-DD').format('YYYY-MM-DD');
          element.end = moment(element.end, 'YYYY-MM-DD').format('YYYY-MM-DD');
          console.log(element.CheckOutTime, "this.splitBookingForm.value");

          var oldEndDate = new Date(element.CheckOutTime);
          let tempNewDate = new Date(element.end);
          tempNewDate.setHours(oldEndDate.getHours());
          tempNewDate.setMinutes(oldEndDate.getMinutes());

          newStartDate = moment(this.splitBookingOBJ.bookingRoom.expected_check_in).format('YYMMDDHHmm');
          newEndDate = moment(tempNewDate).format('YYMMDDHHmm');
          console.log(newEndDate, "newEndDate");
          let range = this.enumerateDaysBetweenDates(moment(element.start, 'YYYY-MM-DD'), moment(element.end, 'YYYY-MM-DD'))
          element['total_days'] = range.length;
        });
        //put it at the end of card processing method.
        this.makeSplitBookingService = this.BS.saveTransferRoomBooking(id, room_id, this.splitBookingForm.value)
          .subscribe((res) => {
            if (res.status == "success") {
              // window.location.reload();
              let index2 = 0;
              this.staticTabs.tabs.forEach((ele, index) => {
                if (ele.active == true) {
                  index2 = index;
                }
              })
              this.removeTabHandler(this.staticTabs.tabs[index2])
              this.staticTabs.tabs[0].active = true;
              this.fillData();
            }
          })
      }
    } else {
      this.showNotification("Dates are not Valid", "error", true, 5);
    }

  }
  takeBillPrint() {
    window.print();
  }


  calculation(mainAmount, chargesPerc = 5) {
    return (mainAmount * 100) / (100 - chargesPerc);
  }
  ngOnDestroy() {
    if (this.sub) {
      this.sub.unsubscribe();
    }
    if (this.checkIn) {
      this.checkIn.unsubscribe();
    }
    if (this.saveNote) {
      this.saveNote.unsubscribe();
    }
    if (this.saveProof) {
      this.saveProof.unsubscribe();
    }
    if (this.getCharges) {
      this.getCharges.unsubscribe();
    }
    if (this.addGuestAPI) {
      this.addGuestAPI.unsubscribe();
    }
    if (this.viewBooking) {
      this.viewBooking.unsubscribe();
    }
    if (this.getGuestInfo) {
      this.getGuestInfo.unsubscribe();
    }
    if (this.cancelBooking) {
      this.cancelBooking.unsubscribe();
    }
    if (this.saveGuestInfo) {
      this.saveGuestInfo.unsubscribe();
    }
    if (this.getBookingNote) {
      this.getBookingNote.unsubscribe();
    }
    if (this.bookingDateGet) {
      this.bookingDateGet.unsubscribe();
    }
    if (this.saveBookingData) {
      this.saveBookingData.unsubscribe();
    }
    if (this.payFinalPayments) {
      this.payFinalPayments.unsubscribe();
    }
    if (this.updatePaymentApi) {
      this.updatePaymentApi.unsubscribe();
    }
    if (this.printBillService) {
      this.printBillService.unsubscribe();
    }
    if (this.roomAvailibility) {
      this.roomAvailibility.unsubscribe();
    }
    if (this.getAddGuestParams) {
      this.getAddGuestParams.unsubscribe();
    }
    if (this.singleOverBooking) {
      this.singleOverBooking.unsubscribe();
    }
    if (this.getCheckoutDetails) {
      this.getCheckoutDetails.unsubscribe();
    }
    if (this.getOverBookingNote) {
      this.getOverBookingNote.unsubscribe();
    }
    if (this.checkCancellation) {
      this.checkCancellation.unsubscribe();
    }
    if (this.commonRoomBookingList) {
      this.commonRoomBookingList.unsubscribe();
    }
    if (this.customertypelistservice) {
      this.customertypelistservice.unsubscribe();
    }
    if (this.makeSplitBookingService) {
      this.makeSplitBookingService.unsubscribe();
    }
    if (this.deleteSingleOverBooking) {
      this.deleteSingleOverBooking.unsubscribe();
    }
    if (this.guestRequiredFieldsCheck) {
      this.guestRequiredFieldsCheck.unsubscribe();
    }
    if (this.confirmSingleOverBooking) {
      this.confirmSingleOverBooking.unsubscribe();
    }
    if (this.getBookingsByRangeAPI) {
      this.getBookingsByRangeAPI.unsubscribe();
    }
  }
  dynamicTimer(minute) {
    // let minute = 1;
    let seconds: number = minute * 60;
    let textSec: any = "0";
    let statSec: number = 60;

    const prefix = minute < 10 ? "0" : "";

    this.dynamicTimerTime = setInterval(() => {
      seconds--;
      if (statSec != 0) statSec--;
      else statSec = 59;

      if (statSec < 10) {
        textSec = "0" + statSec;
      } else textSec = statSec;

      this.display = `${prefix}${Math.floor(seconds / 60)}:${textSec}`;

      if (seconds == 0) {
        if (this.checkInDynamicModal) this.checkInDynamicModal.hide();
        if (this.splitbookingDynamicModal) this.splitbookingDynamicModal.hide();
        if (this.TransferDynamicModal) this.TransferDynamicModal.hide();

        if (this.checkoutFlowDynamicModal) this.checkoutFlowDynamicModal.hide();
        if (this.cleanRoomFlowDynamicModal) this.cleanRoomFlowDynamicModal.hide();
        this.guestForm.value.abletocheckin = false;
        clearInterval(this.dynamicTimerTime);
      }
    }, 1000);
  }

  //do nothing method
  doNothing() {
    console.log('do Nothin called')
  }

  addRemoveExtraItems(paymentValue, formValue) {
    if (paymentValue > 9999) {
      let items = this.bookingForm.get('items');
      let itemNumber = Math.ceil(paymentValue / 8000);
      let guests = <FormArray>formValue;
      if (items.value.length != itemNumber) {
        while (guests.length !== 0) {
          guests.removeAt(0);
        }
        for (let i = 0; i < itemNumber; i++) {
          let control: FormGroup = this._fb.group({
            name: ['', Validators.required],
            city: ['', Validators.required],
            panno: ['', Validators.required],
          });
          guests.push(control);
        }
      }
    }
  }

  // UploadAll Document
  uploadAllHandler() {
    console.log('Temp Blabla');
    this.uploader.uploadAll();
  }


  private validPanField(fc: FormControl) {
    console.log(fc, "fc 7097")
    let regex = /[A-Z]{5}[0-9]{4}[A-Z]{1}/
    return (fc.value == null || fc.value == 0 || regex.test(fc.value)) ? null : {
      validateInput: {
        pattern: true
      }
    };
  }
}
