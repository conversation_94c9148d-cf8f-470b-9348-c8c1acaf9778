{"compilerOptions": {"target": "es5", "module": "esnext", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "noEmitHelpers": true, "strictNullChecks": false, "skipLibCheck": true, "baseUrl": "./src", "paths": {}, "lib": ["dom", "es2015", "es2016", "es2017", "es2018", "esnext"], "types": ["<PERSON><PERSON><PERSON>", "node", "source-map", "uglify-js", "webpack", "@angular/core", "@angular/common"]}, "exclude": ["node_modules", "dist"], "awesomeTypescriptLoaderOptions": {"forkChecker": true, "useWebpackText": true}, "compileOnSave": false, "buildOnSave": false, "atom": {"rewriteTsconfig": false}}