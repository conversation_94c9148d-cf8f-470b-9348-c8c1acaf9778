import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'commaSeparated'
})

export class CommaSeparatedPipe implements PipeTransform {
    transform(value: any, ...args: any[]): any {
        let returnValue = '';
        if(value && value !== '') {
            let newValue = JSON.parse(value);
            if(newValue instanceof Array) {
                newValue.forEach((item, index) => {
                    returnValue += item;
                    if(index < newValue.length - 1) {
                        returnValue += ', ';
                    }
                });
            }
        }
        return returnValue;
    }
}