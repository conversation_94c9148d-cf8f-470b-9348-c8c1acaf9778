import { FormGroup, FormBuilder } from '@angular/forms';
import { Component, OnInit, OnDestroy } from '@angular/core';
import * as moment from "moment";
import { BookingService } from 'app/shared/services/booking.service';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subscription, Subject } from 'rxjs';

@Component({
    selector: 'pending-payment-collections',
    templateUrl: 'pending-payment-collections.component.html',
    styleUrls: ['pending-payment-collections.component.scss']
})

export class PendingPaymentCollectionsComponent implements OnInit, OnDestroy {
    config: any;// New Change ****
    data: any[] = [];
    searchQuery: String;
    searchForm: FormGroup;
    canViewRecords: boolean;
    totalAmount: number = 0;
    originalData: any[] = [];
    public datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        format: 'dd/mm/yyyy',
        todayHighlight: true,
        icon: 'fa fa-calendar',
    }
    private csvSub: Subscription;
    private getReportDataSub: Subscription;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private BS: BookingService,
        private fb: FormBuilder,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        
        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
     }

    ngOnInit() {
        this.initForm()
        this.canViewRecords = true;
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    initForm() {
        this.searchForm = this.fb.group({
            toDate: [''],
            fromDate: ['']
        });
    }
    getSearchParams() {
        if (this.searchForm.valid) {
            let searchParams = JSON.parse(JSON.stringify(this.searchForm.value));
            searchParams.fromDate = moment(searchParams.fromDate).format('YYYY-MM-DD');
            searchParams.toDate = moment(searchParams.toDate).format('YYYY-MM-DD');
            return searchParams;
        }
        else {
            for (let field in this.searchForm.controls) {
                this.searchForm.controls[field].markAsDirty();
                this.searchForm.controls[field].markAsTouched();
            }
        }
    }
    toDataChange(event) {
        this.searchForm.controls['toDate'].patchValue(event);
       }
    searchReports() {
        // console.log("FORM : ",this.searchForm.value);
        let searchParams = this.getSearchParams();
        this.getReportData(searchParams);
    }
    getReportData(searchParams) {
        this.getReportDataSub = this.BS.getPendingPaymentCollectionsReport(searchParams)
            .subscribe((res) => {
                if (res.status === 'success') {
                    this.originalData = res.data;
                    this.initializeData();
                    this.updateAmountSummary();
                }
            });
    }
    search() {
        if (this.searchForm && this.data) {
            this.initializeData();
            if (this.data && this.searchQuery && this.searchQuery.trim() != '') {
                this.data = this.data.filter(data => {
                    let searchTarget = '';
                    Object.keys(data).forEach(key => {
                        searchTarget += data[key];
                    })
                    return (searchTarget.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
                });
                this.updateAmountSummary();
            }
            else {
                this.initializeData();
            }
        }
    }
    clearSearch() {
        this.initializeData();
        this.searchQuery = undefined;
    }
    printRecords() {
        if (this.data && this.data.length > 0) {
            let searchParams = this.getSearchParams();
            this.csvSub = this.BS.getCsvReport(searchParams, 'pending-payment-collections')
                .subscribe(res => {
                    let parsedResponse = res;
                    let blob = new Blob([parsedResponse], { type: 'text/csv' });
                    let url = window.URL.createObjectURL(blob);
                    if (navigator.msSaveOrOpenBlob) {
                        navigator.msSaveBlob(blob, 'pending-payment-collections' + '.csv');
                    } else {
                        let a = document.createElement('a');
                        a.href = url;
                        a.download = 'pending-payment-collections' + '.csv';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    }
                    window.URL.revokeObjectURL(url);
                },
                    err => console.log("error : ", err)
                );
        }

    }
    initializeData() {
        this.data = this.originalData;
        this.updateAmountSummary();
    }
    initializeSummary() {
        this.totalAmount = 0;
    }
    updateAmountSummary() {
        this.initializeSummary();
        this.data.forEach(data => {
            this.totalAmount += data.amount;
        });
    }
    ngOnDestroy(): void {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if (this.getReportDataSub)
            this.getReportDataSub.unsubscribe();
        if (this.csvSub)
            this.csvSub.unsubscribe();
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();    
    }
}