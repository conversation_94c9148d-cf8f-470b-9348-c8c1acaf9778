import { Select2OptionData } from 'ng2-select2';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import * as moment from "moment";
import { ModalDirective } from 'ngx-bootstrap/modal';
import { BookingService } from 'app/shared/services/booking.service';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

@Component({
    selector: 'view-report-details',
    templateUrl: './view.report-details.component.html',
    styleUrls: ['../report.component.scss']
})

export class ViewReportDetailsComponent implements OnInit, OnDestroy {
    @ViewChild('imageModal') imageModal: ModalDirective;
    private sub: any;
    private currentGuestReport: any;
    config: any;// New Change ****
    id: number;
    imageUrls: string[] = [];
    @Input() reportType: any;
    @Input() selectedData: any;
    @Input() referenceTypeList: any;
    @Input() isPoliceInquiry: boolean;
    @Output() goBack = new EventEmitter();
    public data: any;
    public image: any = {};
    public printWindow2: any;
    public guestDocument: any;
    public selectedReport: any;
    public documentBase64: any;
    public canShow: boolean = true;
    public bookingType: any = [
        {
            id: '0',
            text: 'Single Booking'
        },
        {
            id: '1',
            text: 'Group Booking'
        }
    ];
    public paymentType: Array<Select2OptionData> = [
        {
          id: '0',
          text: 'Cash Payment'
        },
        {
          id: '1',
          text: 'Card Payment'
        },
        {
          id: '2',
          text: 'Cheque Payment'
        }
    ];
    note = []
    public bookingPayments: any[];
    private bookingPaymentsAPI: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        private BS: BookingService,
        public translate: TranslateService,// New Change ****
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        
        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    public guestName = (a: any) => {
        return (<string>a.guests.name).toString().toLowerCase();
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    public guestMaturityType = (a: any) => {
        return (<string>a.guests.guest_maturity_type).toString().toLowerCase();
    }

    public referenceName = (a: any) => {
        if (a.reference_id) {
            return this.referenceTypeList[this.findIndex(a.reference_id, "id", this.referenceTypeList)].text;
        }
    }

    ngOnInit() {
        let id = this.selectedData.id;
        this.selectedReport = this.selectedData;
        console.log("SELECTED Data : ", this.selectedData);
        this.currentGuestReport = this.BS.getCurrentGuestReport(id, {})
        .subscribe((res) => {
            if (res.status == "success") {
                this.data = res.data;
                console.log(this.data[0]);
                
            }
        });
        if(this.reportType === 'pending-payments') {
            this.bookingPaymentsAPI = this.BS.getBookingPayments(id)
            .subscribe((res) => {
                if(res.status == "success") {
                    this.bookingPayments = res.data;
                }
            })
        }
    }

    getNoteTitle(note: any) {
        if(note && note.message && note.message !== "") {
            return (<string>note.message).substr(0, 10) + '...';
        }
    }

    getBalanceAmount() {
        let discount = this.getTotalDiscount();
        let amountPaid = this.getAmountPaid();
        let totalAmount = this.getTotalAmount();
        totalAmount = totalAmount - discount - amountPaid;
        return totalAmount;
    }
    getTotalAmount() {
        let fundAmount = this.selectedReport.fund_amount ? this.selectedReport.fund_amount : 0;
        let total_amount = this.selectedReport.total_amount ? this.selectedReport.total_amount : 0;
        let cardSwipeCharges = this.selectedReport.cardswipecharges ? this.selectedReport.cardswipecharges : 0;
        let earlycheckincharge = this.selectedReport.earlycheckincharge ? this.selectedReport.earlycheckincharge : 0;
        let extra_pax_charges = this.selectedReport.extra_pax_charges ? this.selectedReport.extra_pax_charges : 0;
        let refundable_amount = this.selectedReport.refundable_amount ? this.selectedReport.refundable_amount : 0;
        return (total_amount + fundAmount + cardSwipeCharges + earlycheckincharge + extra_pax_charges - refundable_amount);
    }
    getAmountPaid() {
        return this.selectedReport.amount_paid ? 
            (this.selectedReport.amount_paid - (this.selectedReport.return_amount ? parseInt(this.selectedReport.return_amount) : 0)) : 0;
    }
    getTotalDiscount() {
        let discount = this.selectedReport.discount ? this.selectedReport.discount : 0;
        let custom_discount = this.selectedReport.custom_discount ? this.selectedReport.custom_discount : 0;
        return discount + custom_discount;
    }
    formatCheckoutDate(checkout) {
        return moment(checkout).add(1,'days');
    }
    viewDocumentProof(item: any) {
        this.canShow = false;
        this.imageUrls = [];
        for (const imgUrl of item.guest_Doc) { 
            let mimetype: any[] = [];
            if (item.mimetype) {
                mimetype = item.mimetype.split('/');
            }
            this.image['isImage'] = mimetype.indexOf('application') >= 0 ? false : true;
            if (this.image['isImage'] == false) {
                this.guestDocument = this.BS.getFileBase64({ document_url: this.image['guest_document'] })
                    .subscribe(res => {
                        if (res) {
                            this.documentBase64 = null;
                            let file: any = new Blob([res], { type: 'application/pdf' });
                            const reader = new FileReader();
    
                            reader.readAsDataURL(file);
                            reader.addEventListener('loadend', (e: any) => {
                                let documentBase64 = reader.result;
                                var winparams = `dependent=yes,locationbar=no,scrollbars=yes,menubar=yes,resizable,screenX=50,screenY=50,width=850,height=1050`;
                                var htmlPop = `<embed width=100% height=100% type="application/pdf" src="${documentBase64}"></embed>`;
                                this.printWindow2 = window.open("", "PDF", winparams).document.write(htmlPop);
                            });
                        }
                    })
            }
            else {
                this.imageUrls.push(imgUrl)
            }
        }
        this.imageModal.show();
    }

    printDocument() {
        window.print();
    }

    closeModal() {
        this.canShow = true;
        this.imageModal.hide()
        this.gobacksimon()
    }

    findIndex(value: any, property: any, targetArray: any[]) {
        for(let i = 0; i < targetArray.length; i++) {
            if(targetArray[i][property] == value) {
                return i;
            }
        }
        return -1;
    }

    gobacksimon(data?: any){
        this.goBack.emit(data);
        this.selectedReport = "";
    }
    ngOnDestroy() {
        if(this.bookingPaymentsAPI) {
            this.bookingPaymentsAPI.unsubscribe();
        }
        if(this.currentGuestReport) {
            this.currentGuestReport.unsubscribe();
        }

        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
         // New Change ****
         if (this.langChangeSub)
         this.langChangeSub.unsubscribe();
    }
}