import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hil<PERSON> } from "@angular/core";
import { Component, OnInit } from "@angular/core";
import { FormArray, FormBuilder, FormControl, FormGroup, Validators, AbstractControl, ValidatorFn } from "@angular/forms";
import { forkJoin } from 'rxjs/observable/forkJoin';
import { OnlineBooking } from "./onlineBooking.service";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { ModalDirective } from 'ngx-bootstrap/modal';
import { ActivatedRoute } from "@angular/router";
import { Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { apiUrl } from "app/api-env";
import { debounceTime, switchMap, catchError, takeUntil } from 'rxjs/operators';

interface ApiResponse {
  status: string;
  data?: any;
  message?: string;
}

@Component({
  selector: "app-online-booking",
  templateUrl: "onlinebooking.component.html",
  styleUrls: ["./onlinebooking.style.scss"],
})
export class OnlineBookingComponent implements OnInit, OnDestroy {
  searchForm: FormGroup;
  searchValidError: any;
  baseURL: string = apiUrl
  invalidForm: boolean = false
  adultCountNumber = 1;
  childrenCountNumber = 0;
  counterError: string;
  openSelectionSection = false;
  getAvailableRoom: any;
  isCustomerBookingFormActive: boolean = false;
  userInformation = {};
  userInfoControl: FormGroup;
  selectedAadharFile: File | null = null;
  aadharUploadError: any;
  selectedPanFile: File | null = null;
  responseAadharFile: any;
  responsePanFile: any;
  total_days: number;
  room_title: any;
  room_id: number;
  room_category_id: number;
  submitted: boolean;
  selectedRoom: any;
  guestFiles: any[] = [];
  authorizationToken: any;
  showEmptyTemplate: boolean;
  showTokenErrorMsg: any
  totalPeople: number = 1;
  disableAadharUpload: boolean = false;
  disablePanUpload: boolean = false;
  showFormError: any;
  searchTerm: string = '';
  selectedSearchType: any
  isListEnable: boolean = false

  searchTypeList: any = [
    {
      id: 0,
      name: "phone number"
    },
    {
      id: 1,
      name: "email id"
    },
    {
      id: 2,
      name: "aadhar card"
    },
    {
      id: 3,
      name: "pan card"
    }
  ]

  // Array to hold filtered users based on the search term
  userList: any[]

  // Variable to hold the selected user
  selectedUser: any;
  max_occupancy: number
  allowedFileTypes: any = ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'];
  aadharCardPattern: any = '^[2-9]{1}[0-9]{11}$';
  panCardPattern: any = '^[A-Z]{5}[0-9]{4}[A-Z]{1}$';
  testingData: any
  setListError: string;
  private cancelRequest$ = new Subject<void>();  // Subject to cancel previous requests
  checkInDate: any;
  checkOutDate: any;

  // @ViewChild('bookingModal') bookingModal: ModalDirective;
  @ViewChild('aadharFileInput') aadharFileInput: ElementRef;
  @ViewChild('panFileInput') panFileInput: ElementRef;
  @ViewChild('successMessageModal') public successMessageModal: ModalDirective;
  @ViewChild('errorMessageModal') public errorMessageModal: ModalDirective;

  constructor(private onlineService: OnlineBooking, private formBuilder: FormBuilder, private http: HttpClient, private route: ActivatedRoute) { }
  verifyToken() {
    this.route.queryParams.subscribe(params => {
      this.authorizationToken = params['token'];

      if (!this.authorizationToken) {
        this.showEmptyTemplate = true;
        this.showTokenErrorMsg = "you are not authorized to this page";
        return;
      }

      let headers = new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': this.authorizationToken
      });

      this.http.get(`${this.baseURL}bookingApprover/verify`, { headers })
        .subscribe((response: ApiResponse) => {
            let res = response;
            console.log("Response:", res);

            if (res.status === 'success') {
              this.userInfoControl.patchValue({
                contact: res.data.phone_number
              })
              this.showEmptyTemplate = false;
              this.showTokenErrorMsg = null;
            } else {
              this.showEmptyTemplate = true;
              this.showTokenErrorMsg = res.message || "You are not authorized";
            }
          },
          (error) => {
            let err = error;
            console.log("Error:", err);
            this.showEmptyTemplate = true;
            this.showTokenErrorMsg = err.message || "you are not authorized!";
          }
        );
    });
  }
  ageValidator(minimumAge: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      if (!control.value) {
        return null; // If there's no value, it's not an error yet
      }
  
      const birthDate = new Date(control.value);
      const currentDate = new Date();
      const age = currentDate.getFullYear() - birthDate.getFullYear();
      const monthDifference = currentDate.getMonth() - birthDate.getMonth();
  
      if (age < minimumAge || (age === minimumAge && monthDifference < 0)) {
        return { ageInvalid: `You must be at least ${minimumAge} years old.` };
      }
  
      return null; // Valid
    };
  }  
  ngOnInit() {

    //user token verification method called 
    this.verifyToken();

    //form build for searching rooms by date
    this.searchForm = this.formBuilder.group({
      checkInDate: new FormControl(null, [Validators.required]),
      checkOutDate: new FormControl(null, [Validators.required]),
    });
    //form build for user data storing
    this.userInfoControl = this.formBuilder.group({
      old_guest_id: [],
      name: ['', [Validators.required, Validators.minLength(3)]],
      contact: ['', [Validators.required, Validators.pattern('^[0-9]{10,12}$'), Validators.pattern('^[^01][0-9]{9,11}$')]],
      email: ['', [Validators.required, Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$')]],
      address: ['', [Validators.required]],
      birth_date: ['', [Validators.required, this.ageValidator(12)]],
      referance_user: ['', [Validators.minLength(3)]],
      AadharCardPhoto: [''],
      PanCardPhoto: [''],
      aadharcard_number: ['', [Validators.required, Validators.pattern(this.aadharCardPattern)]],
      pancard_number: ['', [Validators.pattern(this.panCardPattern)]],
    })
    //Empty search form
    this.searchForm.setValue({
      checkInDate: "",
      checkOutDate: "",
    });
  }
  ngOnDestroy(): void {
    this.cancelRequest$.next(); // Emit to cancel ongoing requests
    this.cancelRequest$.complete(); // Clean up the subject
  }

  selectSearchType(event: any) {
    this.selectedSearchType = event.target.value;
  }

  // API call function using RxJS operators
  searchApi(query: string): void {
    this.setListError = '';
    this.http.post(`${this.baseURL}onlinebooking/guest/search`, {
      text: query,
      ids: [],
      type: this.selectedSearchType || 0
    })
      .pipe(
        debounceTime(1000),
        takeUntil(this.cancelRequest$),
        switchMap(() => this.http.post(`${this.baseURL}onlinebooking/guest/search`, {
          text: query,
          ids: [],
          type: this.selectedSearchType
        })),
        catchError(error => {
          console.error('API error:', error);
          return [];
        })
      )
      .subscribe((response: ApiResponse) => {
        const data = response.data;
        console.log(data, "data finally got");
        if (data.length > 0) {
          this.userList = data;
        } else {
          this.setListError = 'no data found';
        }
      });
  }

  // Function to filter the users based on the search term
  searchUser(): void {
    if (this.searchTerm.length > 3) {
      this.isListEnable = true;

      console.log({ text: this.searchTerm, ids: [], type: this.selectedSearchType }, "data");

      // Debounced API call with switchMap to cancel previous requests
      this.searchApi(this.searchTerm);
    }
  }

  // Function to select a user from the dropdown
  selectUser(user: any) {
    this.isListEnable = false
    this.selectedUser = user;
    let aadharcard: any = '';
    let pancard: any = '';
    if (user.guestFiles.length > 0) {
      // only take first first Aadhar card
      aadharcard = user.guestFiles.find((files) => files.documentType == 2);

      // only take first first pan card
      pancard = user.guestFiles.find((files) => files.documentType == 3);
    }
    console.log("aadharcard", aadharcard);
    console.log("pancard", pancard);

    if (aadharcard) {
      this.guestFiles.push(aadharcard)
      this.userInfoControl.get('AadharCardPhoto').disable();
    }

    if (pancard) {
      this.guestFiles.push(pancard)
      this.userInfoControl.get('PanCardPhoto').disable();
    }

    this.testingData = {
      old_guest_id: user.id ? user.id : '',
      name: user.name ? user.name : '',
      contact: user.contact && user.contact.split('+91')[1] ? user.contact.split('+91')[1] : user.contact,
      email: user.email ? user.email : '',
      address: user.address ? user.address : '',
      birth_date: user.dob ? user.dob.split('T')[0] : '',
      aadharcard_number: user.aadharcard_number ? user.aadharcard_number : '',
      pancard_number: user.pancard_number ? user.pancard_number : '',
    }
    this.userInfoControl.patchValue(this.testingData)
  }

  onFocus() {
    this.isListEnable = true;
  }

  // This method will be called when the input loses focus
  onBlur() {
    setTimeout(() => {
      this.isListEnable = false;
    }, 200);
  }

  todayDate() {
    const today = new Date();
    let dd: number | string = today.getDate();
    let mm: number | string = today.getMonth() + 1;
    const yyyy = today.getFullYear();
  
    if (dd < 10) dd = '0' + dd;
    if (mm < 10) mm = '0' + mm;
  
    return `${yyyy}-${mm}-${dd}`;
  }
  checkoutValidation() {
    const checkInDate = this.searchForm.value.checkInDate;
    const newDate = new Date(checkInDate);
    newDate.setDate(newDate.getDate() + 1);
    let year = newDate.getFullYear();
    let month: number | string = newDate.getMonth() + 1;
    let day: number | string = newDate.getDate();
    if (day < 10) day = '0' + day;
    if (month < 10) month = '0' + month;
  
    return `${year}-${month}-${day}`;
  }  
  
  parseDate(dateString: string): Date {
    const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));
    return new Date(year, month - 1, day); // No need to add 2000 for years, since it's already in yyyy format
  }
  
  // Function to format Date to yyyy-mm-dd
  formatDate(date: Date): string {
    const day = ('0' + date.getDate()).slice(-2); // Ensure 2-digit day
    const month = ('0' + (date.getMonth() + 1)).slice(-2); // Ensure 2-digit month
    const year = date.getFullYear(); // Get the full year (yyyy)
    return `${year}-${month}-${day}`; // Format as yyyy-mm-dd
  }

  onSubmit() {
    if (this.searchForm.valid) {
      this.searchValidError = null
      //below is to get the total number of days user wants to stay 
      const checkIn = new Date(this.searchForm.value.checkInDate).getTime();
      const checkOut = new Date(this.searchForm.value.checkOutDate).getTime();
      const timeDiff = checkOut - checkIn;
      this.total_days = timeDiff / (1000 * 3600 * 24);

      //-----------------------------------------------------
      let checkOutDateObj = this.parseDate(this.searchForm.value.checkOutDate);
      checkOutDateObj.setDate(checkOutDateObj.getDate() - 1); // Subtract 1 day
      this.checkOutDate = this.formatDate(checkOutDateObj);
      this.checkInDate = this.searchForm.value.checkInDate;
      console.log("Check-in date:", this.checkInDate);
      console.log("Check-out date:", this.checkOutDate);
      //-----------------------------------------------------

      if (this.total_days <= 0) {
        this.searchValidError = "please select valid dates";
        return;
      }

      let userSearchRequest = {
        start: new Date(this.searchForm.value.checkInDate).toUTCString(),
        end: new Date(this.searchForm.value.checkOutDate).toUTCString(),
      };
      // console.log("date boject", userSearchRequest);
      this.getCaterotyList(userSearchRequest);
    } else {
      this.searchValidError = "please select valid dates";
    }
  }

  handleMinus(type: any) {
    this.counterError = ''
    if (type === "adult") {
      if (this.adultCountNumber > 1) {
        this.totalPeople -= 1;
        this.adultCountNumber -= 1;
      } else {
        this.counterError = `minumum 1 person must be selected`;
      }
    }

    if (type === "child") {
      if (this.childrenCountNumber > 0) {
        this.totalPeople -= 1;
        this.childrenCountNumber -= 1;
      } else {
        this.counterError = `number cannot be less then 0`;
      }
    }
    setTimeout(() => {
      this.counterError = ''
    }, 3000);
  }

  handlePlus(type: any) {
    this.counterError = ''
    if (this.totalPeople < this.max_occupancy) {
      if (type === "adult") {
        this.adultCountNumber += 1;
        this.totalPeople += 1;
      }

      if (type === "child") {
        this.childrenCountNumber += 1;
        this.totalPeople += 1;
      }
    } else {
      this.counterError = `the maximum capicity is ${this.max_occupancy}`;
    }
    setTimeout(() => {
      this.counterError = ''
    }, 3000);
  }


  getCaterotyList(data) {
    console.log(data);
    // forkJoin(data).subscribe((res) => {
    //   console.log(res);
    //   this.onlineService.GetAvailableCategory(res).subscribe((response: any) => {
    //     console.log(data);
    //     console.log("blabla Response");
    //     console.log(response);
    //     if (response && response.data){
    //       this.getAvailableRoom = response.data;
    //     }
    //   })
    // })
    // this.getAvailableRoom = [];

    let req1 = this.createObservable(data, 1);
    let req2 = this.createObservable(data, 2);
    let req3 = this.createObservable(data, 3);
    let req4 = this.createObservable(data, 4);
    let req5 = this.createObservable(data, 5);
    let req6 = this.createObservable(data, 6);
    let req7 = this.createObservable(data, 16);
    let req8 = this.createObservable(data, 17);

    forkJoin([req1, req2, req3, req4, req5, req6, req7, req8]).subscribe((res: any) => {
      this.getAvailableRoom = res;
      console.log(res);
    })
    // this.onlineService.GetAvailableCategory(data).subscribe((response: any) => {
    //   console.log(data, ">>>>>>");
    //   if (response && response.data){
    //     this.getAvailableRoom = response.data;
    //   }
    // });
  }

  handleSelectionSection() {
    this.openSelectionSection = !this.openSelectionSection;
  }

  createObservable(data: any, id: any) {
    const requestData = { ...data, room_category_id: id };
    return this.onlineService.GetAvailableCategory(requestData);
  }

  selectedBookingId(roomDetails: any) {
    console.log("roomDetails", roomDetails);
    if (roomDetails.CategoryId && roomDetails.RoomId) {
      this.room_id = roomDetails.RoomId;
      this.room_category_id = roomDetails.CategoryId;
      this.room_title = roomDetails.RoomName;
      this.isCustomerBookingFormActive = true;
      this.max_occupancy = roomDetails.max_occupancy
    }
    this.selectedRoom = 1;
  }

  //for closing booking form
  CloseSubmitForm() {
    this.isCustomerBookingFormActive = false;
    this.adultCountNumber = 1;
    this.childrenCountNumber = 0;
    this.aadharFileInput.nativeElement.value = '';
    this.panFileInput.nativeElement.value = '';
    this.totalPeople = 1
    this.userInfoControl.get('AadharCardPhoto').enable();
    this.userInfoControl.get('PanCardPhoto').enable()
    // this.room_id = null;
    // this.room_category_id = null;
    // this.room_title = null;
    Object.keys(this.userInfoControl.controls).forEach(field => {
      const control = this.userInfoControl.get(field);
      control.reset()
    });
  };

  // Handle File Selection
  HandleDocumentUploads(event: any, type: string) {
    const file = this.aadharFileInput.nativeElement.files[0];
    const panFile = this.panFileInput.nativeElement.files[0];

    if (!file && type == 'AadharCardPhoto') {
      return this.userInfoControl.get('AadharCardPhoto').setErrors({ required: `aadhar card photo required` });
    }

    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        this.userInfoControl.get(type).setErrors({ invalidFileSize: `File size must be less than 5 MB` });
      } else if (!this.allowedFileTypes.includes(file.type)) {
        this.userInfoControl.get(type).setErrors({ invalidFileType: `please upload in jpeg, png, jpg, jpeg* format` });
      }
    }

    if (panFile) {
      if (panFile.size > 5 * 1024 * 1024) {
        this.userInfoControl.get(type).setErrors({ invalidFileSize: `File size must be less than 5 MB` });
      } else if (!this.allowedFileTypes.includes(panFile.type)) {
        this.userInfoControl.get(type).setErrors({ invalidFileType: `please upload in jpeg, png, jpg, jpeg* format` });
      }
    }

    if (type == 'AadharCardPhoto') {
      // this.disableAadharUpload = false;
      this.selectedAadharFile = file;
      console.log("FILE GOT", this.selectedAadharFile);

    } else if (panFile && type == 'PanCardPhoto') {
      // this.disablePanUpload = false
      this.selectedPanFile = panFile;
    }
  }

  cancelUploadPhoto(name: string, fileInput: HTMLInputElement) {
    if (name === 'aadhar') {
      this.disableAadharUpload = false;
      this.selectedAadharFile = null;
      if (this.aadharFileInput) {
        this.aadharFileInput.nativeElement.value = '';
      }
    }

    if (name === 'pan') {
      this.disablePanUpload = false
      this.selectedPanFile = null;
      if (this.panFileInput) {
        this.panFileInput.nativeElement.value = '';
      }
    }
  }

  uploadIdProof() {

  }

  closeMessageModel() {
    this.successMessageModal.hide();
  }
  // Submit form with file uploads
  submitForm() {

    console.log("form");

    if (this.userInfoControl.valid) {
      try {
        const aadharData = new FormData();
        const panData = new FormData();
        const headers = new HttpHeaders();
        headers.append('Accept', 'application/json');
        const options ={ headers };

        const uploadRequests: any[] = [];
        const isAadharUploaded = this.guestFiles.some(data => data.documentType == 2);
        const isPanUploaded = this.guestFiles.some(data => data.documentType == 3);

        if (this.selectedAadharFile && !isAadharUploaded) {
          aadharData.append('file', this.selectedAadharFile);
          const aadharUpload$ = this.http.post<ApiResponse>(`${this.baseURL}guest/public/add/image`, aadharData, options).pipe(
            map(response => response)
          );
          uploadRequests.push(aadharUpload$);
        } else if (!isAadharUploaded) {
          this.userInfoControl.get('AadharCardPhoto').setErrors({ required: 'Please upload Aadhaar card photo.' });
          return;
        }

        if (this.selectedPanFile && !isPanUploaded) {
          panData.append('file', this.selectedPanFile);
          const panUpload$ = this.http.post<ApiResponse>(`${this.baseURL}guest/public/add/image`, panData, options).pipe(
            map(response => response)
          );
          uploadRequests.push(panUpload$);
        }

        if (uploadRequests.length > 0) {
          forkJoin(uploadRequests).subscribe(
            (uploadResponses: any[]) => {
              uploadResponses.forEach((responseData, index) => {
                if (responseData.status === "success") {
                  let displayname = this.userInfoControl.value.name + responseData.data.originalName;
                  if (index === 0) {
                    if (!isAadharUploaded) {
                      return this.guestFiles.push({ ...responseData.data, documentType: 2, displayname }); // Aadhaar
                    } else if (!isPanUploaded) {
                     return this.guestFiles.push({ ...responseData.data, documentType: 3, displayname }); // PAN
                    }
                  } else if (index === 1) {
                    // if (isAadharUploaded) {
                      this.guestFiles.push({ ...responseData.data, documentType: 3, displayname }); // PAN
                    // }
                  }
                }
              });


              // Now construct the userInformation object with guestFiles
              let currentdate = new Date();
              this.userInformation = {
                check_in: this.checkInDate,
                check_out: this.searchForm.value.checkOutDate,
                room_id: '',
                room_category_id: this.room_category_id,
                total_days: this.total_days,
                adult: this.adultCountNumber,
                child: this.childrenCountNumber,
                room_title: this.room_title,
                isbillingguest: true,
                reservation_date: currentdate,
                booking_type: 0,
                customer_type: 3,
                customer_name: 'normal',
                reference_user_name: this.userInfoControl.value.referance_user,
                stay_type: 1,
                start: this.checkInDate,
                end: this.checkOutDate,
                guest_id: this.userInfoControl.value.old_guest_id,
                old_guest_id: this.userInfoControl.value.old_guest_id,
                name: this.userInfoControl.value.name,
                contact: this.userInfoControl.value.contact,
                email: this.userInfoControl.value.email,
                address: this.userInfoControl.value.address,
                birth_date: this.userInfoControl.value.birth_date,
                aadharcard_number: this.userInfoControl.value.aadharcard_number,
                pancard_number: this.userInfoControl.value.pancard_number,
                guestFiles: this.guestFiles
              };

              let data = {
                booking: this.userInformation
              };

              console.log("Data has formed", data);

              let headers = new HttpHeaders({
                'Content-Type': 'application/json',
                'Authorization': this.authorizationToken
              });

              let options = { headers };

              this.http.post<ApiResponse>(`${this.baseURL}bookingApprover/request`, data, options).subscribe((response) => {
                let resp = response;

                if (resp.status === "success") {
                  this.successMessageModal.show();
                  console.log("success", resp.data);
                }

              }, (error) => {
                this.showFormError = error.json().message;
                console.log(error);
                this.errorMessageModal.show();
              });
            }, (error) => {
              console.error('Error occurred during file upload:', error);
              this.showFormError = 'File upload failed. Please try again.';
              this.errorMessageModal.show();
            }
          );
        } else {
          let currentdate = new Date();
          this.userInformation = {
            check_in: this.checkInDate,
            check_out: this.searchForm.value.checkOutDate,
            room_id: '',
            room_category_id: this.room_category_id,
            total_days: this.total_days,
            adult: this.adultCountNumber,
            child: this.childrenCountNumber,
            room_title: this.room_title,
            isbillingguest: true,
            reservation_date: currentdate,
            booking_type: 0,
            customer_type: 3,
            customer_name: 'normal',
            reference_user_name: this.userInfoControl.value.referance_user,
            stay_type: 1,
            start: this.checkInDate,
            end: this.checkOutDate,
            guest_id: null,
            old_guest_id: this.userInfoControl.value.old_guest_id,
            name: this.userInfoControl.value.name,
            contact: this.userInfoControl.value.contact,
            email: this.userInfoControl.value.email,
            address: this.userInfoControl.value.address,
            birth_date: this.userInfoControl.value.birth_date,
            aadharcard_number: this.userInfoControl.value.aadharcard_number,
            pancard_number: this.userInfoControl.value.pancard_number,
            guestFiles: this.guestFiles // Here, guestFiles will now contain the uploaded file data
          };

          let data = {
            booking: this.userInformation
          };

          let headers = new HttpHeaders({
            ['Content-Type']: 'application/json',
            ['Authorization']: this.authorizationToken
          });

          let options = { headers };

          this.http.post<ApiResponse>(`${this.baseURL}bookingApprover/request`, data, options).subscribe((response) => {
            let resp = response;
            if (resp.status === "success") {
              this.successMessageModal.show();
              console.log("success", resp.data);
            }

          }, (error) => {
            this.showFormError = error.json().message;
            console.log(error);
            this.errorMessageModal.show();
          });
        }
      } catch (error) {
        console.log("Got an error", error);
      }
    } else {
      Object.keys(this.userInfoControl.controls).forEach(field => {
        const control = this.userInfoControl.get(field);
        control.markAsTouched();
      });
    }
  }
}