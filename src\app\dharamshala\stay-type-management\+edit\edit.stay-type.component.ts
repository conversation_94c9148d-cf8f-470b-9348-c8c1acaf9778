import { ViewEncapsulation } from '@angular/core';
import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, Input, Output, EventEmitter, OnDestroy, OnChanges } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'edit-stay-type',
    templateUrl: '../stay-type.action.html',
    styleUrls: ['../stay-type-style.scss'],
    encapsulation: ViewEncapsulation.None
})

export class EditStayTypeComponent implements OnInit, OnDestroy, OnChanges {
    menuIcon: any;
    public StayTypeForm: FormGroup;
    public pageType: string = "Edit";
    private sub: any;
    @Input() selectedStayType;
    @Output() hideEditEvent = new EventEmitter();
    @Output() editToList = new EventEmitter();
    constructor(
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        public translate: TranslateService
    ) { 
        translate.get('STAY_TYPE.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.initForm();
    }
    ngOnChanges(change: any) {        
        if (this.StayTypeForm) {
            this.StayTypeForm.patchValue(change.selectedStayType.currentValue);
            this.menuIcon = change.selectedStayType.currentValue.stay_type_icon;
        }
    }
    onIconSelect(icon: string): void {
        console.log('Icon selected:', icon);
        this.menuIcon = icon.replace('fa fa-', '');
        this.StayTypeForm.patchValue({
            stay_type_icon: this.menuIcon
        });
    }
    initForm() {
        this.StayTypeForm = this._fb.group({
            is_default: [false],
            stay_type_icon: [''],
            name: ['', Validators.required],
            status: [true, Validators.required],
            duration: ['', [Validators.required, CustomValidators.digits, CustomValidators.lte(24)]],
            charge: ['', [Validators.required, CustomValidators.number, CustomValidators.gte(0), CustomValidators.lte(100)]],
        })
        this.StayTypeForm.patchValue(this.selectedStayType);
        this.menuIcon = this.selectedStayType.stay_type_icon;
    }
    toggleChild(data: any = true) {
        this.editToList.emit(data);
    }
    hideComponent() {
        this.hideEditEvent.emit();
        this.StayTypeForm.reset();
        this.initializeStayTypeIcon();
    }
    initializeStayTypeIcon() {
        this.menuIcon = undefined;
    }
    saveStayType() {
        if (this.StayTypeForm.valid) {
            this.sub = this.DS.updateStayType(this.selectedStayType.id, this.StayTypeForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.toggleChild(res.data);
                        this.StayTypeForm.reset();
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.StayTypeForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({
                hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}