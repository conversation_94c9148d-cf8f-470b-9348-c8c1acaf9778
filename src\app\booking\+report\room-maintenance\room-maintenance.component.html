<section class="widget revenue-report">
    <header>
        <div class="row">
            <div class="col-sm-6">
                <h4>
                    <span class="red-header">
                        <i class="fa fa-clipboard"></i>&nbsp;&nbsp;
                        <span class="text-capitalize">
                            {{'ROOM_MAINT_REPORT.ROOM_MAIN' | translate:param}}
                        </span>
                        {{'ROOM_MAINT_REPORT.REP_MANAGE' | translate:param}}
                    </span>
                </h4>
            </div>
            <div class="col-sm-2 __download">
                <button class="btn btn-sm btn-primary pull-right" *ngIf="data && data.length > 0" (click)="printRecords()">
                    {{'ROOM_MAINT_REPORT.PRINT' | translate:param}}
                    <i class="fa fa-print"></i>
                </button>
            </div>
            <div class="float-sm-right text-right col-sm-4">
                <div class="row">
                </div>
                <div class="form-group __search">
                    <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="search()" placeholder="{{'ROOM_MAINT_REPORT.SEARCH' | translate:param}}">
                    <span class="form-group-addon">
                        <i class="fa fa-search"></i>
                    </span>
                    <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()">
                        <i class="fa fa-times"></i>
                    </span>
                </div>
            </div>
        </div>
    </header>
    <hr class="large-hr">
    <form [formGroup]="searchForm" (ngSubmit)="searchReports()">
        <!-- Filters -->
        <div class="row filter-row" style="margin-bottom: 15px;">
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'ROOM_MAINT_REPORT.ROOM_TYP' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                       <ng-select [items]="roomTypeList" [ngModel]="selectedFilterTypes.room_type_id" (change)="roomTypeChanged($event)" formControlName="room_type_id" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Room Type"></ng-select>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'ROOM_MAINT_REPORT.GUE_TYPE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <ng-select [items]="customerTypeList" [ngModel]="selectedFilterTypes.guest_id" (change)="guestTypeChanged($event)" formControlName="guest_id" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Guest Type"></ng-select>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'ROOM_MAINT_REPORT.GET_TYPE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                       <ng-select [items]="getTypeList" [ngModel]="selectedFilterTypes.get_type" formControlName="get_type" (change)="getTypeChanged($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Type"></ng-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row filter-row" style="margin-bottom: 15px;">
            <div class="col-sm-4"></div>
            <div class="col-sm-4"></div>
            <div class="col-sm-4">
                <button type="submit" class="btn btn-primary pull-right">{{'ROOM_MAINT_REPORT.SEARCH' | translate:param}}</button>
            </div>
        </div>
        <!-- <div class="row filter-row">
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600">
                            From Date :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime class="custom-width-datetime" [timepicker]="false" formControlName="fromDate"
                            [datepicker]="datepickerOpts"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600">
                            To Date :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime class="custom-width-datetime" [timepicker]="false" formControlName="toDate"
                            [datepicker]="datepickerOpts"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <button type="submit" class="btn btn-primary pull-right">Search</button>
            </div>
        </div> -->
        <!-- Amount Summary -->
        <div class="row" *ngIf="this.data" style="margin-top: 15px;">
            <div class="col-sm-3">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'ROOM_MAINT_REPORT.SUMM' | translate:param}}
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr>
                            <td>
                                {{'ROOM_MAINT_REPORT.TOTAL_CHILD' | translate:param}} :
                            </td>
                            <td>
                                <strong>
                                    {{totalChild}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                {{'ROOM_MAINT_REPORT.TOTAL_ADULT' | translate:param}} :
                            </td>
                            <td>
                                <strong>
                                    {{totalAdult}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                {{'ROOM_MAINT_REPORT.TOTAL_ROOM' | translate:param}} :
                            </td>
                            <td>
                                <strong>
                                    {{data ? data.length : 0}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </form>
    <div class="clearfix"></div>
    <div class="widget-body table-scroll">
        <div class="mt">

            <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
                <thead>
                    <tr>
                        <th>
                            <mfDefaultSorter by="id">#</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="room">{{'ROOM_MAINT_REPORT.ROOM_NO' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="room_type">{{'ROOM_MAINT_REPORT.ROOM_TYP' | translate:param}}</mfDefaultSorter>
                        </th>
                        <!-- class="no-sort text-center" -->
                        <th>
                            <mfDefaultSorter by="guest_type">{{'ROOM_MAINT_REPORT.GUE_TYPE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="guest">{{'ROOM_MAINT_REPORT.GUE_NAME' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter>{{'ROOM_MAINT_REPORT.STAY_DURA' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter [by]="paxCount">{{'ROOM_MAINT_REPORT.PAX' | translate:param}}</mfDefaultSorter>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let ds of mf.data; let i = index">
                        <td>{{i + 1}}</td>
                        <td><span class="fw-semi-bold">{{ds.room}}</span></td>
                        <td><span class="">{{ds.room_type}}</span></td>
                        <td><span class="">{{ds.guest_type}}</span></td>
                        <td><span class="">{{ds.guest | uppercase}}</span></td>
                        <td>
                            <span class="">
                                {{ds.start ? (ds.start | date:'d MMM yy') : '-'}} -
                                {{ds.end ? (ds.end | date:'d MMM yy') : '-'}}
                            </span>
                        </td>
                        <td><span class="fw-semi-bold">{{(ds.adult ? ds.adult : 0)}}(AD) {{(ds.child ? ds.child :
                                0)}}(CH)</span></td>
                    </tr>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                        <td colspan="100">
                            {{'ROOM_MAINT_REPORT.NO MATCHES' | translate:param}}
                        </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                        <td class="text-danger" colspan="100">
                            {{'ROOM_MAINT_REPORT.PERMISSION_DENIED' | translate:param}}
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="12">
                            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</section>