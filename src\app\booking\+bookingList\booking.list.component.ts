import { BookingService } from './../../shared/services/booking.service';
import { Component, OnInit } from '@angular/core';
import { AuthGuard } from "../../shared/guards/auth-guard.service";

import * as bookingData from './../data';
import * as moment from "moment";

@Component({
    selector: 'booking-list',
    templateUrl: './booking.list.component.html',
    styleUrls: ['./booking.list.component.scss']
})

export class BookingListComponent implements OnInit {
    data: any[] = [];
    originalData: any;
    searchQuery: string;
    showAdvance: boolean = false;
    private sub: any;
    public selectedBooking: any;
    public canViewRecords: boolean;
    public viewComp: boolean = false;
    public bookingType: any = [
        {
            id: '0',
            text: 'Single Booking'
        },
        {
            id: '1',
            text: 'Group Booking'
        }
    ]
    public sourceType: any = [
        {
            id: '0',
            text: 'On the spot'
        },
        {
            id: '1',
            text: 'Telephonic'
        }
    ]
    public paymentType: any = [
        {
            id: '0',
            text: 'Cash Payment'
        },
        {
            id: '1',
            text: 'Card Payment'
        }
    ]
    public proofTypeList: any = [
        {
            id: '0',
            text: 'Driving Licence'
        },
        {
            id: '1',
            text: 'Adhar Card'
        },
        {
            id: '2',
            text: 'Pan Card'
        },
        {
            id: '3',
            text: 'Election Card'
        }
    ]
    getCustomerTypeOptios: any = {
        width: '100%',
        placeholder: 'Customer Type',
        allowClear: true,
        minimumResultsForSearch: 15
    };
    getBookingStatusOptions: any = {
        width: '100%',
        placeholder: 'Booking Status',
        allowClear: true,
        minimumResultsForSearch: 15
    }
    getRoomNoOptions: any = {
        width: '100%',
        placeholder: 'Room No',
        allowClear: true,
        minimumResultsForSearch: 7
    }
    // http://bootstrap-datepicker.readthedocs.io/en/latest/options.html
    datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        todayHighlight: true,
        assumeNearbyYear: true,
        format: 'dd / mm / yyyy',
        icon: 'fa fa-calendar'
    }
    constructor(
        private auth: AuthGuard,
        private BS: BookingService
    ) { }
    public checkOutDate = (a: any) => {
        return a.detail[0].expected_check_out;
    }
    public checkInDate = (a: any) => {
        return a.detail[0].expected_check_in;
    }
    public bookingDate = (a: any) => {
        return a.booking_date;
    }    
    public uniqueBookingId = (a: any) => {
        return parseInt(a.unique_booking_id);
    }
    ngOnInit() {
        this.canViewRecords = true;
        this.sub = this.BS.getBookingList()
            .subscribe((res) => {
                if (res.status == "success") {
                    this.data = res.data;
                    this.originalData = res.data;
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            })
    }

    getCustomerType(){
        return bookingData.customerType;
    }
    getCustomerTypeCahnged(event){
        // console.log(event);
    }

    getBookingStatus(){
        return bookingData.bookingStatus;
    }
    getBookingStatusCahnged(event){
        // console.log(event);
    }

    getRoomNo(){
        return bookingData.roomNo;
    }
    getRoomNoChanged(event){
        // console.log(event)
    }
    viewBookingDetail(booking) {
        this.selectedBooking = booking;
        this.viewComp = true;
    }
    closeComp(){
        this.viewComp = false;
        this.selectedBooking = "";
    }
    search() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter( data => {
                let searchTarget = data.guest_name ? data.unique_booking_id ?
                    (<string>data.guest_name).concat(data.unique_booking_id) :
                    (<string>data.guest_name) : '';
                return (searchTarget.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else {
            this.initializeData();
        }
    }
    clearSearch() {
        console.log("Clearing Search");
        this.initializeData();
        this.searchQuery = undefined;
    }
    initializeData() {
        this.data = this.originalData;
    }
    ngOnDestroy(){
        if(this.sub){
            this.sub.unsubscribe();
        }
    }
}