import { Component, OnInit } from '@angular/core';
import { RoomsService } from './../../shared/services/room.service';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

@Component({
    selector: 'rooms',
    templateUrl: './rooms.component.html'
})
export class RoomsComponent implements OnInit {
    config: any;// New Change ****
    data: any[];
    originalData: any[];
    searchQuery: string;
    public selectedRoom: any;
    public canViewRecords: boolean;

    //services
    private sub : any;
    // hide and show child components
    hiddenAR: boolean = true;
    hiddenER: boolean = true;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****

    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private RS: RoomsService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

         // New Change ****
         this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
            });
    }

    ngOnInit() {
        this.canViewRecords = true;
        this.sub = this.RS.getAllRoomList()
        .subscribe((res) => {
            console.log('res')
            console.log(res)
            console.log('res')
            if(res.status == "success"){
                this.data = res.data.rooms;
                this.originalData = res.data.rooms;
            }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
     }
     changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
     findIndex(searchTerm, property, searchArray?: any[]) {
         searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }
    // add room componet
    showAR() {
        this.hiddenAR = !this.hiddenAR;
    }
    handleHiddenAR(event) {
        this.hiddenAR = event;
        if(this.canViewRecords && event.data){
            this.data.push(event.data);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }

    // edit room component
    showER(data) {
        this.hiddenER = !this.hiddenER;
        this.selectedRoom = data;
    }
    handleHiddenER(event) {
        this.hiddenER = event;
        if (this.canViewRecords && event.data) {
            this.data[this.findIndex(event.data.id, "id") - 1] = event.data;
            this.originalData[this.findIndex(event.data.id, "id",this.originalData) - 1] = event.data;            
        }
    }
    searchEvent() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter( data => {
                return ((<string>data.title).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    initializeData() {
        this.data = this.originalData;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}