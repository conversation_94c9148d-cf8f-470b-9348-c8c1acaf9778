import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { DharamshalaService } from "../../shared/services/dharamshala.service";
import { AuthGuard } from "../../shared/guards/auth-guard.service";
declare var Messenger: any;
import * as moment from "moment";
import { CurrencyPipe } from '@angular/common/src/pipes';
import { timestamp } from 'rxjs/operator/timestamp';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
@Component({
    selector: 'default-settings',
    templateUrl: './defaultSettings.component.html'
})

export class DefaultSettingsComponent implements OnInit {
    // API fields
    config: any;// New Change ****
    private sub: any;
    private getDataAPI: any;

    // Window fields
    earlyCheckIn: any;
    dharamshalaID: any;
    minuteStep: number = 5;

    // Form Fields
    defaultSetting: FormGroup;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        this.dharamshalaID = this.auth.getDharamshalaID();
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() {
        this.defaultSetting = this._fb.group({
            // check_in : ['',Validators.required],
            // check_out: ['',Validators.required]
            early_check_in_time: ['', Validators.required]
        });
        this.getData();
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    getData() {
        this.getDataAPI = this.DS.getEarlyCheckInLimit(this.dharamshalaID)
            .subscribe(res => {
                if (res.status == 'success') {
                    this.defaultSetting.controls['early_check_in_time'].patchValue(res.data.early_check_in_time ? res.data.early_check_in_time : moment().format('YYYY-MM-DD') + 'T00:00:00.000Z');
                }
            });
    }
    saveDefaultSettings() {
        if (this.defaultSetting.valid && this.dharamshalaID) {
            // let time = moment(this.defaultSetting.value.early_check_in_time).format();
            // let actualTime = time.substr(time.indexOf('T'), time.indexOf('+') - time.indexOf('T'));
            this.sub = this.DS.updateEarlyCheckInLimit(this.dharamshalaID, this.defaultSetting.value)
                .subscribe(res => {
                    if (res.status == 'success') {
                        this.defaultSetting.controls['early_check_in_time'].patchValue(res.data.early_check_in_time);
                    }
                });
        }
        else {
            Messenger().post({
                message: "Dharamshala not assigned!",
                type: 'error',
                showCloseButton: true
            })
        }
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }

    // generateTimeString(time: string) {
    //     let currDate = moment().format('YYYY-MM-DD');
    //     let timeZone = '+05:30';
    //     return (currDate + time + timeZone);
    // }
}