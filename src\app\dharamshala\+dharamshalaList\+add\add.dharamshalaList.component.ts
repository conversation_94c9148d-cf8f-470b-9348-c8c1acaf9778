import { <PERSON><PERSON>nent, OnInit, NgZone, OnDestroy, ViewChild } from '@angular/core';
import { FormControl, FormGroup, FormBuilder, FormArray, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { Select2OptionData } from 'ng2-select2';
import { FileUploader, FileUploaderOptions } from 'ng2-file-upload';
import { MapsAPILoader } from '@agm/core';
import { CustomValidators } from 'ng2-validation';

import { GoogleMapService } from './../../../shared/services/google-map.service';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { AuthGuard } from './../../../shared/guards/auth-guard.service';
import { _secretKey_auth } from './../../../shared/globals/config';
import { apiUrl } from './../../../api-env';
import { allowedFileTypes, allowedMimeTypes } from "../allowedFileTypes";
import { TranslateService } from '@ngx-translate/core';

declare var google: any;
declare var jQuery: any;
declare var moment: any;
declare var Messenger: any;
import * as CryptoJS from 'crypto-js';
const URL = apiUrl + 'dharamshala/add/image';
const URLDoc = apiUrl + 'dharamshala/add/doc';

@Component({
	selector: 'add-dharamshala',
	templateUrl: './add.dharamshalaList.component.html'
})
export class AddDharamshalaComponent implements OnInit {
	datepickerOpts = {
		autoclose: true,
		todayBtn: 'linked',
		todayHighlight: true,
		icon: 'fa fa-calendar',
		endDate: new Date(),
		format: 'dd/mm/yyyy'
	}
	pageName: string = "add";
	public uploader: FileUploader = new FileUploader({ 
		url: URL,
		allowedMimeType: allowedMimeTypes
	});
	public uploadedImages: any[] = [];
	public uploader2: FileUploader = new FileUploader({ 
		url: URLDoc,
		allowedFileType: allowedFileTypes
	});
	public uploadedDocs: any[] = [];

	select2Options: any = {
		width: '100%'
	};
	// --- why is this required? --- //
	check_in_options: any ={
		defaultTime: '08:00 AM',
        minuteStep: 5
	}
	check_out_options: any = {
		defaultTime: '08:00 PM',
        minuteStep: 5
	}
	initialData: any;
	lat: number = 19.0760098;
	lng: number = 72.8780646;
	zoom: number = 15;
	location_data = { coords: { lat: this.lat, lng: this.lng } };

	@ViewChild('externalinkTitle') externalinkTitle: any;
	@ViewChild('externallinkBody') externallinkBody: any;
	public city: any;
	public country: any = 103;
	public userCountry: any = 103;
	public company_addr: string;
	public markers: any[] = [];
	public externalLinks: any[] = [];
	public countryList: Array<Select2OptionData>;
	public citiesList: Array<Select2OptionData>;

	dharamshalaForm: FormGroup;

	//service variables
	private sub: any;
	private initData: any;
	
	constructor(
		private gmService: GoogleMapService,
		private mapsAPILoader: MapsAPILoader,
		private _zone: NgZone,
		private _fb: FormBuilder,
		private authGuard: AuthGuard,
		private DS: DharamshalaService,
		private router: Router,
		public translate: TranslateService
		
	) {
		let currentLang = localStorage.getItem('currentLang'); // New Change ****
		translate.setDefaultLang(currentLang);// New Change ****
		translate.get('DHARAMSHALA.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageName = res;
            //=> 'hello world'
		});
		this.getInitData();
	}
	
	ngOnInit() {
		// console.log("File uploader options",this.uploader.options);
		this.autocomplete();
		this.buildForm();
		this.initImageUploadEvents();
		this.initDocUploadEvents()
	}
	getInitData() {
		this.initData = this.DS.getInitData()
		.subscribe((res) => {
			if (res.status == "success") {
				this.initialData = res.data.roles;
				this.countryList = res.data.countries;
				this.citiesList = res.data.cities;
					// console.log("Initial Init Data : ",this.initialData);
					if (this.initData.length == 0) {
						Messenger().post({  hideAfter: 5,
							message: "No Admin level user found.",
							type: "info",
							showCloseButton: true
						});
					}
				}
			})
	}

	buildForm() {

		this.dharamshalaForm = this._fb.group({
			//  signing_authority: ['', Validators.required],
			name: ['', Validators.required],
			total_bed: ['', [Validators.required, CustomValidators.digits]],
			email: ['', [Validators.required, CustomValidators.email]],
			contact: ['', [Validators.required, CustomValidators.digits]],
			city: [''],
			city_name: [''],
			country: ['India', Validators.required],
			zip: ['', [Validators.required, CustomValidators.digits]],
			address: ['', Validators.required],
			lat: [0],
			lng: [0],
			uploadedImages: this._fb.array([]),
			external_link: this._fb.array([]),
			early_checkout_charges: [null, [Validators.required, CustomValidators.number, CustomValidators.lte(100)]],
			check_in: [new Date(),Validators.required],
			check_out: [new Date(),Validators.required],
			status: [true, Validators.required],
			user: this._fb.group({
				first_name: ['', [Validators.required]],
				last_name: ['', [Validators.required]],
				email: ['', [Validators.required]],
				dob: ['', [Validators.required]],
				country: ['', [Validators.required]],
				role_id: ['', Validators.required]
			}),
		})
		// console.log("this.this.dharamshalaForm : ",this.dharamshalaForm.controls['user'].get('first_name'));
	}

	getCordinates(data) {
		// console.log("Dragged to co-ordinates : ",data);
		this.lat = data.coords.lat;
		this.lng = data.coords.lng;
		this.gmService.getAddrFromLtLng(this.lat, this.lng).subscribe(res => {
			// console.log("Dragged to address name : ",res);
				if (res.status === "OK") {
					this.dharamshalaForm.controls['address'].patchValue(res.results[0].formatted_address);
					this.dharamshalaForm.controls['lat'].patchValue(this.lat);
					this.dharamshalaForm.controls['lng'].patchValue(this.lng);
				}
				else {
					Messenger().post({  hideAfter: 5,
						message: "Drag to a valid location!",
						type: "error",
						showCloseButton: true
					});
				}
			});
	}
	autocomplete() {
		this.mapsAPILoader.load().then(() => {
			const autocomplete = new google.maps.places.Autocomplete(
				document.getElementById('autocompleteInput') as HTMLInputElement,
				{ types: ['address'] }
			);
			
			autocomplete.addListener('place_changed', () => {
				this._zone.run(() => {
					const place = autocomplete.getPlace();
					if (place.geometry) {
						this.markers = [{
							lat: place.geometry.location.lat(),
							lng: place.geometry.location.lng(),
							label: place.name,
						}];
						this.lat = place.geometry.location.lat();
						this.lng = place.geometry.location.lng();
						this.dharamshalaForm.patchValue({
							lat: this.lat,
							lng: this.lng,
							address: place.formatted_address
						});
					}
				});
			});
		});
	}
    /**
     * when image has been uploaded but not saved, images will be list out to form control
     * @param {any} res : response - response from image upload api
     */
	addUploadedImages(res) {
		let control = <FormArray>this.dharamshalaForm.controls['uploadedImages'];
		control.push(
			this._fb.group({
				mimetype: [res.data.mimetype],
				extension: [res.data.extension],
				oldName: [res.data.oldName],
				originalName: [res.data.originalName],
				size: [res.data.size],
				uploaded: [res.data.uploaded],
				is_deleted: [res.data.is_deleted],
				is_doc: [res.data.is_doc]
			})
		);
	}
	fileOverCatch(event) {
		console.log("Image Status : ",event);
	}
	initImageUploadEvents() {
		this.uploader.onWhenAddingFileFailed = (item: any, filter: any, option: any) => {
			// console.log("item selected : ", item, filter, option);
			Messenger().post({  hideAfter: 5,
				message: item.type + ' format is not supported!',
				type: 'error',
				showCloseButton: true
			});
		};
		this.uploader.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
			//things to do on completion
			// console.log("Image uploaded On complete : ",response);
			if (response) {
				let res = JSON.parse(response);
				this.addUploadedImages(res);
			}
		};
		this.uploader.onBeforeUploadItem = (item: any) => {
			// image uploaded - add token of auth
			// console.log("Image uploaded before upload: ",item);			
			let token = this.authGuard.ud.session_id;
			let timestamp = (+ new Date()).toString();
			let generatedToken = CryptoJS.AES.encrypt(token,
				_secretKey_auth);
			this.uploader.authToken = generatedToken;
			// console.log("Uploader authToken : ",this.uploader.authToken);
		}
	}
	initDocUploadEvents() {
		this.uploader2.onWhenAddingFileFailed = (item: any, filter: any, option: any) => {
			// console.log("item selected : ", item, filter, option);
			Messenger().post({  hideAfter: 5,
				message: item.type + ' format is not supported!',
				type: 'error',
				showCloseButton: true
			});
		};
		this.uploader2.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
			//things to do on completion
			if (response) {
				let res = JSON.parse(response);
				this.addUploadedImages(res);
			}
		};
		this.uploader2.onBeforeUploadItem = (item: any) => {
			// image uploaded - add token of auth
			let token = this.authGuard.ud.session_id;
			let timestamp = (+ new Date()).toString();
			let generatedToken = CryptoJS.AES.encrypt(token,
				_secretKey_auth);
			this.uploader2.authToken = generatedToken;
		}
	}
	addExternalLink(title, body) {
		let control = <FormArray>this.dharamshalaForm.controls['external_link'];
		control.push(
			this._fb.group({
				title: [title],
				body: [body]
			}));
		let data = {
			title: title,
			body: body
		}
		// console.log("Eternal Links : ",this.dharamshalaForm.controls['external_link']);
		this.externalLinks.push(data);
		this.externalinkTitle.nativeElement.value = "";
		this.externallinkBody.nativeElement.value = "";
	}
	deleteExternalLink(index, ele) {
		let control = <FormArray>this.dharamshalaForm.controls['external_link'];
		control.removeAt(index);
		this.externalLinks.splice(index, 1);
	}
	getRoleList(): Select2OptionData[] {
		if (this.initialData) {
			return jQuery.map(this.initialData, function (obj) {
				return { id: obj.id, text: obj.name };
			})
		}
	}
	userRoleChanged(event) {
		let user = <FormGroup>this.dharamshalaForm.controls['user'];
		let role = <FormControl>user.controls['role_id'];
		setTimeout(function() {
			role.patchValue(event.value);
		}, 0);
	}
	countryChanged(event: any) {
		// console.log("event : ",event);
		this.dharamshalaForm.controls['country'].patchValue(event.value);
	}
	cityChanged(event: any) {
		this.dharamshalaForm.controls['city'].patchValue(event.value);
		this.dharamshalaForm.controls['city_name'].patchValue(this.citiesList[this.findIndex(parseInt(event.value), "id", this.citiesList)].text);
		// console.log("City Name : ",this.citiesList[this.findIndex(parseInt(event.value), "id", this.citiesList)].text);
		// console.log("FORM : ",this.dharamshalaForm.value);
	}
	userCountryChanged(event: any) {
		// console.log("user event : ",event);		
		let user = <FormGroup>this.dharamshalaForm.controls['user'];
		setTimeout(() => {
			user.controls['country'].patchValue(event.value);
		}, 0);
	}
	findIndex(searchTerm: any, property: any, targetArray: any[]) {
		for(let i = 0; i < targetArray.length; i++) {
			if(targetArray[i][property] === searchTerm) { return i; }
		}
		return -1;
	}
	authorityChanged(event) {
		// console.log("authority person changed : ",event);
		this.dharamshalaForm.controls['signing_authority'].patchValue(event.value);
	}
	saveDharamshala(dharamshalaForm: any) {
		// console.log("dharamshalaForm Form : ", this.dharamshalaForm);
		if (this.dharamshalaForm.valid) {
			this.sub = this.DS.savedharamshala(this.dharamshalaForm.value)
				.subscribe((res) => {
					if (res.status == "success") {
						this.dharamshalaForm.reset();
						this.router.navigate(['admin/dharamshala']);
					}
				}, (err) => {
					let errBody = JSON.parse(err._body);
					let errors = errBody.data;
					if (errors.length > 0) {
						console.log("errors : ",errors);
						errors.forEach(element => {
							if(element.fieldname == 'user.email') {
								let user = <FormGroup>this.dharamshalaForm.controls['user'];
								let email = user.controls['email'];
								email.setErrors({
									backend: element.error
								});
							}
							else {
								let control = this.dharamshalaForm.controls[element.fieldname];
								control.setErrors({
									backend: element.error
								});
							}
						});
					}
				});
			// console.log("this.dharamshalaForm.value : ",JSON.stringify(this.dharamshalaForm.value));
		} 
		else {
			Messenger().post({  hideAfter: 5,
				message: "Form can not be submitted",
				type: "error",
				showCloseButton: true
			});
		}
	}
	ngOnDestroy() {
		if (this.sub) {
			this.sub.unsubscribe();
		}
		if (this.initData) {
			this.initData.unsubscribe();
		}
	}
}