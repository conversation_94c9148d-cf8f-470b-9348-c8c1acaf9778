export class Menu {
    id: string;
    title: string;
    order?: number;
    routerLink?: string;
    routerParam?: string;
    icon: string;
    childIcon?: string;
    is_enabled: boolean;
    children?: Menu[];
}

export const menuList: Menu[] =
    [
        {
            "id": "1",
            "title": "Dashboard",
            "routerLink": "./",
            "routerParam": "",
            "icon": "dashboard",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": []
        },
        {
            "id": "2",
            "title": "Dharamshala",
            "routerLink": "dharamshala",
            "routerParam": "",
            "icon": "building-o",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": []
        },
        {
            "id": "22",
            "title": "Funds",
            "routerLink": "dharamshala/funds",
            "routerParam": "",
            "icon": "inr",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": []
        },
        {
            "id": "3",
            "title": "Amenities",
            "routerLink": "amenities",
            "routerParam": "",
            "icon": "s15",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": []
        },
        {
            "id": "4",
            "title": "Customer Type",
            "routerLink": "customer",
            "routerParam": "",
            "icon": "users",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": []
        },
        {
            "id": "5",
            "title": "User",
            "routerLink": "user",
            "routerParam": "",
            "icon": "user-circle-o",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": []
        },
        {
            "id": "19",
            "title": "Reference User",
            "routerLink": "reference",
            "routerParam": "",
            "icon": "user",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": []
        },
        {
            "id": "6",
            "title": "Customer List",
            "routerLink": "guest",
            "routerParam": "",
            "icon": "hotel",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": []
        },
        // {
        //     "id": "7",
        //     "title": "Bookings",
        //     "routerLink": "",
        //     "routerParam": "",
        //     "icon": "clipboard",
        //     "is_enabled": true,
        //     "childIcon": "angle-right",
        //     "children": [{
        //         "id": "8",
        //         "title": "Reservation",
        //         "routerLink": "reservation",
        //         "routerParam": "",
        //         "icon": "calendar",
        //         "is_enabled": true,
        //         "childIcon": "angle-right",
        //         "children": []
        //     }, {
        //         "id": "24",
        //         "title": "Booking History",
        //         "routerLink": "booking",
        //         "routerParam": "",
        //         "icon": "calendar",
        //         "is_enabled": true,
        //         "childIcon": "angle-right",
        //         "children": []
        //     }, {
        //         "id": "20",
        //         "title": "Cancellation Policy",
        //         "routerLink": "booking/policy",
        //         "routerParam": "",
        //         "icon": "calendar-times-o",
        //         "is_enabled": true,
        //         "childIcon": "angle-right",
        //         "children": []
        //     }, {
        //         "id": "21",
        //         "title": "Manage Extra Charges",
        //         "routerLink": "booking/extracharges",
        //         "routerParam": "",
        //         "icon": "inr",
        //         "is_enabled": true,
        //         "childIcon": "angle-right",
        //         "children": []
        //     }]
        // },
        // {
        //     "id": "9",
        //     "title": "Building",
        //     "routerLink": "building",
        //     "routerParam": "",
        //     "icon": "building",
        //     "is_enabled": true,
        //     "childIcon": "angle-right",
        //     "children": []
        // },
        // {
        //     "id": "10",
        //     "title": "Room Management",
        //     "routerLink": "",
        //     "routerParam": "",
        //     "icon": "sitemap",
        //     "is_enabled": true,
        //     "childIcon": "angle-right",
        //     "children": [
        //         {
        //             "id": "11",
        //             "title": "Room Category",
        //             "routerLink": "room/category",
        //             "routerParam": "",
        //             "icon": "sliders",
        //             "is_enabled": true,
        //             "childIcon": "angle-right",
        //             "children": []
        //         },
        //         {
        //             "id": "12",
        //             "title": "Rooms",
        //             "routerLink": "room",
        //             "routerParam": "",
        //             "icon": "table",
        //             "is_enabled": true,
        //             "childIcon": "angle-right",
        //             "children": []
        //         },
        //         {
        //             "id": "18",
        //             "title": "Rooms Booking Settings",
        //             "routerLink": "room/setting",
        //             "routerParam": "",
        //             "icon": "cog",
        //             "is_enabled": true,
        //             "childIcon": "angle-right",
        //             "children": []
        //         }
        //     ]
        // },
        {
            "id": "13",
            "title": "Menu Management",
            "routerLink": "",
            "routerParam": "",
            "icon": "tasks",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": [{
                "id": "14",
                "title": "Menu Group",
                "routerLink": "menu",
                "routerParam": "",
                "icon": "bars",
                "is_enabled": true,
                "childIcon": "angle-right",
                "children": []
            }]
        },
        {
            "id": "15",
            "title": "Role Management",
            "routerLink": "",
            "routerParam": "",
            "icon": "folder-o",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": [
                {
                    "id": "16",
                    "title": "Roles",
                    "routerLink": "role/user",
                    "routerParam": "",
                    "icon": "database",
                    "is_enabled": true,
                    "childIcon": "angle-right",
                    "children": []
                },
                {
                    "id": "17",
                    "title": "Roles Permission",
                    "routerLink": "role/permission",
                    "routerParam": "",
                    "icon": "gavel",
                    "is_enabled": true,
                    "childIcon": "angle-right",
                    "children": []
                }
            ]
        },
        {
            "id": "23",
            "title": "Agents",
            "routerLink": "dharamshala/agents",
            "routerParam": "",
            "icon": "user-secret",
            "is_enabled": true,
            "childIcon": "angle-right",
            "children": []
        }
    ]

