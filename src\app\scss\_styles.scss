.no-m-b {
  margin-bottom: 0px
}

table.small-footprint {
  td {
    padding: 0.2rem;
    vertical-align: middle;
  }
}

.simple-anchore-btn-group {
  a {
    position: relative;
    text-decoration: none;
    display: inline-block;
    color: #666;
    font-weight: bold;
    font-size: 13px;
    &:hover {
      color: #222
    }
  }
  a+a {
    margin-left: 10px;
    &::before {
      content: '|';
      position: absolute;
      left: 0;
      top: -1px;
      margin-left: -10px;
      vertical-align: top;
      color: #888;
      opacity: 0.7
    }
  }
}

.select2-container--default .select2-selection--single {
  border: 1px solid rgba(170, 170, 170, 0.51) !important;
  border-radius: 3px !important;
}

.select2-container .select2-selection--single {
  height: 33px !important;
}

.radio-horizontal {
  display: flex;
  align-items: center;
  padding: 10px 0; // justify-content: center
}

.abc-radio label {
  cursor: pointer
}

.abc-radio input[type="radio"]:focus+label::before {
  outline-color: transparent;
}

.panel-heading {
  p {
    margin: 0 0 0 10px;
    font-weight: 400;
  }
}

.abc-radio label::before {
  top: 3px;
}

.abc-radio label::after {
  top: 6px;
}

.__accessActions {
  margin-top: 10px;
}

.__checkbox {
  position: relative;
  vertical-align: middle;
  margin-right: 15px;
  input[type="checkbox"] {
    position: absolute;
    left: -9999px;
    &~label {
      height: 25px;
      width: 25px;
      cursor: pointer;
      text-align: center;
      line-height: 25px;
      color: #fff;
      vertical-align: middle;
      margin: 0;
      i {
        color: #fff
      }
      &.bg-success,
      &.bg-danger {
        i {
          animation: rotateX 300ms ease;
        }
      }
    }
  }
  &~p {
    margin: 0;
  }
}

@keyframes rotateX {
  from {
    transform: rotate(0deg)
  }
  to {
    transform: rotate(360deg)
  }
}

.__search {
  position: relative;
  .form-control {
    height: 32px;
    vertical-align: middle;
    padding-left: 32px;
    &:focus~.form-group-addon {
      color: #4D90FE
    }
  }
  .form-group-addon {
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
    line-height: 32px;
    width: 32px;
    transition: all ease 400ms;
  }
  .form-group-addon.right {
    right: 0;
    left: unset;
    cursor: pointer;
  }
}

.no-padding {
  padding: 0;
  input {
    margin: 0
  }
}

.dz-success-mark,
.dz-error-mark {
  display: inline-block;
  svg {
    width: 60%
  }
}

.toolbar-inputs {
  display: flex;
  width: 100%;
  margin-bottom: 10px;
  &>* {
    flex: 1;
    border-radius: 0;
  }
  input+input:not(:focus) {
    border-left-color: transparent
  }
  .large {
    flex-basis: 70%
  }
  .small {
    flex-basis: 30%;
  }
}

sebm-google-map {
  height: calc( 100% - 15px);
  margin: 15px .55rem;
}

.dropzone.dz-clickable,
.dropzone.dz-clickable .dz-message,
.dropzone.dz-clickable .dz-message * {
  cursor: pointer;
}

.dropzone {
  min-height: 150px;
  background: #fff;
  padding: 20px;
  background: #eee;
  box-shadow: 0 0 0px 5px #fff inset;
}

.dropzone {
  border: 2px dashed #ccc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-container {
  display: flex;
}

.image-container>div {
  flex: 1;
}

.image-container>div img {
  width: 100%;
}

.table-no-borders {
  margin: 0;
  tr {
    td,
    th {
      border-color: transparent !important
    }
  }
}

.vendor-container {
  height: 100px;
  border-radius: 5px;
  padding: 5px;
  background: #eee;
  border: 2px dashed rgba(0, 0, 0, .4);
  box-shadow: 0 0 0px 5px #fff inset;
  .vendor-added {
    display: inline-block;
    padding: 0px 7px 0px 7px;
    border-radius: 100px;
    margin: 3px;
    border: 1px solid rgba(0, 0, 0, 0.21);
    font-weight: 500;
    background: #fff;
    margin-right: 30px;
    position: relative;
    animation: pulse ease 320ms;
    transform-origin: center bottom;
    &>* {
      display: inline-block
    }
    input {
      -moz-appearance: none;
      -webkit-appearance: none;
      -ms-progress-appearance: none;
      border: none;
      background-color: transparent;
      pointer-events: none;
      display: table;
      text-align: center;
      text-transform: capitalize;
      width: auto;
      white-space: nowrap;
    }
    i {
      height: 20px;
      width: 20px;
      margin-left: 10px;
      text-align: center;
      line-height: 20px;
      font-size: 11px;
      background: #828282;
      border-radius: 100%;
      vertical-align: top;
      color: #fff;
      cursor: pointer;
      position: absolute;
      top: 1px;
      right: 0;
      margin-right: -25px;
    }
  }
}

@keyframes pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

button[type="submit"] {
  i {
    margin-right: 10px
  }
}

.form-control.ng-invalid.ng-touched:not(.ng-pristine) {
  border-color: red
}

.errMsg {
  font-size: 12px;
  font-weight: 500;
  color: red;
  display: block;
}

.capitalize {
  text-transform: capitalize
}

.text-center {
  text-align: center;
}

.__image_container {
  background: #efefef;
  border: 2px dashed rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 5px;
  box-shadow: 0 0 0px 5px #fff inset;
  height: auto;
  min-height: 100px;
  position: relative;
  .__images,
  .__docs {
    position: relative;
    display: inline-block;
    width: 150px;
    margin: 5px;
    img {
      width: 100%;
    }
    a {
      &:hover {
        text-decoration: none;
      }
    }
    a:not(.__doc):not(.__img) {
      display: inline-block;
      position: absolute;
      right: 0;
      top: 0;
      height: 20px;
      width: 20px;
      text-align: center;
      line-height: 20px;
      color: #fff;
      font-size: 12px;
      background: red;
      border-radius: 100%;
      margin: 5px;
      transform-origin: center;
      transition: all ease 230ms;
      z-index: 5;
      &:hover {
        font-size: 16px;
        background: transparent;
        color: red;
        transform: rotate(180deg)
      }
    }
  }
  .__docs {
    text-align: center;
    img {
      max-width: 65px;
    }
  }
  .__filename {
    position: relative;
    width: 100%;
    background: white;
    color: #484848;
    display: block;
    padding: 5px;
    font-size: 12px;
    text-align: center;
    margin-top: 5px;
    word-break: break-all;
  }
}

.__break-all {
  word-break: break-all;
}

.input-group .radio-horizontal {
  padding: 0px;
  label {
    margin-bottom: 0px;
  }
}

.floor_inputs {
  padding: 5px;
  border: 1px solid #dadada;
  &>* {
    flex: 1;
  }
  &+& {
    border-top: transparent
  }
}

.__fromBackend {
  position: absolute;
  bottom: 0;
  margin-bottom: -5px;
  white-space: nowrap;
}

.__fromBackend+.form-control {
  border-color: red;
  margin-bottom: 12px;
}

.errMsg.__fromBackend+.input-group {
  margin-bottom: 12px;
}

.errMsg.__fromBackend+select2 span.select2-container {
  margin-bottom: 12px;
  border: 1px solid red;
  border-radius: 5px;
}

.errMsg.__fromBackend+datetime .input-group {
  input,
  .input-group-addon {
    border-color: red;
  }
}

.float-sm-right.text-right {
  text-align: right
}

.app.nav-collapsed:not(.nav-static) .logo.hidden-sm-down {
  font-size: 10px;
  line-height: 1;
  word-wrap: break-word;
  padding: 14px 3px 0 3px;
  height: auto;
  margin: 1px 0;
}

.abc-checkbox label {
  user-select: none;
}

.errMsg.__fromBackend+datetime {
  display: block;
  margin-bottom: 12px;
  & .input-group.date {
    border: 1px solid red;
    border-radius: 5px;
    & .input-group-addon:not(:first-child) {
      border-left: 1px solid red;
      color: red;
    }
  }
}

.__search {
  margin-bottom: 0px;
  vertical-align: top
}

.table {
  tr.__selected {
    background: rgba(235, 156, 43, 0.1) !important;
  }
}

.angular2-contextmenu {
  .dropdown-menu {
    margin-left: 230px;
  }
}

input[type="radio"].is_default_radio,input[type="checkbox"].is_default_checkbox {
  position: absolute;
  left: -999%;
  &~label {
    position: relative;
    height: 25px;
    width: 25px;
    margin: 0 auto 0 10px;
    background: #eee;
    border-radius: 3px;
    border: 2px solid #ddd;
    cursor: pointer;
    transition: all ease 350ms;
  }
  &:checked~label {
    background: green;
    border-color: green;
    &::after {
      content: '✔';
      position: absolute;
      display: block;
      text-align: center;
      font-size: 20px;
      line-height: 1;
      color: #fff;
      height: 100%;
      width: 100%;
    }
  }
}


.reservations-wrapper {
  .countdown-label {
    font-size: 12px;
    color: #65584c;
    text-align: center;
    text-transform: uppercase;
    display: inline-block;
    letter-spacing: 2px;
  }

  #countdown {
    box-shadow: 0 1px 2px 0 rgba(1, 1, 1, 0.4);
    width: 240px;
    height: auto;
    text-align: center;
    background: #f1f1f1;
    border-radius: 5px;
    margin: auto;
  }

  #countdown #tiles {
    color: #fff;
    position: relative;
    z-index: 1;
    text-shadow: 1px 1px 0px #ccc;
    display: inline-block;
    font-family: Arial, sans-serif;
    text-align: center;
    padding: 0;
    border-radius: 5px 5px 0 0;
    font-size: 35px;
    font-weight: thin;
    display: block;

  }

  .color-full {
    background: #53bb74;
  }

  .color-half {
    background: #ebc85d;
  }

  .color-empty {
    background: #e5554e;
  }

  #countdown #tiles>span {
    width: 70px;
    max-width: 70px;
    padding: 18px 0;
    position: relative;
  }

  #countdown .labels {
    width: 100%;
    height: 25px;
    text-align: center;
    position: absolute;
    bottom: 8px;
  }

  #countdown .labels li {
    width: 102px;
    font: bold 15px 'Droid Sans', Arial, sans-serif;
    color: #f47321;
    text-shadow: 1px 1px 0px #000;
    text-align: center;
    text-transform: uppercase;
    display: inline-block;
  }
  .reservations-wrapper-text{
    text-align: center;
    font-weight: 700;
    margin-top: 5px;
    font-size: 18px;
    color: #e24c4c;
  }
}