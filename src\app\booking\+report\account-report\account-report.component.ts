import { <PERSON><PERSON><PERSON>, FormBuilder } from '@angular/forms';
import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import * as moment from "moment";
import { BookingService } from 'app/shared/services/booking.service';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subscription, Subject } from 'rxjs';
import { AuthGuard } from 'app/shared/guards/auth-guard.service';
import { paymentType } from "../../data";
import { SSL_OP_SSLEAY_080_CLIENT_DH_BUG } from 'constants';
import { ModalDirective } from 'ngx-bootstrap/modal';
declare var Messenger: any;

@Component({
    selector: 'account-report',
    templateUrl: 'account-report.component.html',
    styleUrls: ['account-report.component.scss']
})

export class accountReport implements OnInit, OnD<PERSON>roy {
    config: any;// New Change ****
    data: any[] = [];
    searchQuery: String;
    searchForm: FormGroup;
    canViewRecords: boolean;
    totalAmount: number = 0;
    totalPaymentAmount : number = 0;
    originalData: any[] = [];
    public datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        format: 'dd/mm/yyyy',
        todayHighlight: true,
        icon: 'fa fa-calendar',
    }
    isAdmin;
    public paymentType = paymentType;
    public usersList = [];
    public bookingTypeOptions = {
        width: '100%',
    };
    selectedFilterTypes = {
        payment_mode: '000000',
        user_id: '000000'
    }
    advanceAmt = 0;
    pendingAmt = 0;
    checkinAmt = 0;
    returnAmt = 0;
    agentAmt = 0;
    chequeAmt = 0;
    cashAmt = 0;
    bankAmt = 0;
    cardAmt = 0;
    agentAmtPay = 0;
    private csvSub: Subscription;
    private getReportDataSub: Subscription;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
  printBillService: Subscription;
  invoiceDataForView: any;
  totalAmaunt: any;
  @ViewChild("invoiceModal") invoiceModal: ModalDirective;
  @ViewChild("invoiceHtml") invoiceHtml: ElementRef;

    constructor(
        public translate: TranslateService,// New Change ****
        private BS: BookingService,
        private fb: FormBuilder,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
        private authGuard: AuthGuard,
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
        this.isAdmin = this.authGuard.isAdmin();
     }

    ngOnInit() {
        this.initForm()
        this.canViewRecords = true;
        this.paymentType = this.paymentType.map((obj) => {
            return { id: obj.id, text: obj.text.toUpperCase()}
        });
        this.paymentType.unshift({ "id": "000000", "text": "All-togather" });

        this.searchReports()
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    toDataChange(event) {
     this.searchForm.controls['toDate'].patchValue(event);
    }
    initForm() {
        this.searchForm = this.fb.group({
            toDate: [new Date()],
            fromDate: [new Date()]
        });
    }
    getSearchParams() {
        if (this.searchForm.valid) {
            let searchParams = JSON.parse(JSON.stringify(this.searchForm.value));
            searchParams.fromDate = moment(searchParams.fromDate).format('YYYY-MM-DD');
            searchParams.toDate = moment(searchParams.toDate).format('YYYY-MM-DD');
            return searchParams;
        }
        else {
            for (let field in this.searchForm.controls) {
                this.searchForm.controls[field].markAsDirty();
                this.searchForm.controls[field].markAsTouched();
            }
        }

    }
    searchReports() {
        let searchParams = this.getSearchParams();
        searchParams['payment_mode'] = this.selectedFilterTypes.payment_mode;
        searchParams['user_id'] = this.selectedFilterTypes.user_id;
        this.getReportData(searchParams);
    }
    paymentModeChanged(event) {
     this.selectedFilterTypes.payment_mode = event.id
    }
    userChanged(event){
        this.selectedFilterTypes.user_id = event.id
    }

    round(data: number) {
      if (!isNaN(Math.round(data))) {
          return Math.round(data);
      }
      else {
          return data;
      }
  }

  getTotalAmount(data: any) {
    let fundAmount = data.fund_amount ? data.fund_amount : 0;
    let total_amount = data.total_amount ? data.total_amount : 0;
    let cardSwipeCharges = data.cardswipecharges ? data.cardswipecharges : 0;
    let earlycheckincharge = data.earlycheckincharge ? data.earlycheckincharge : 0;
    let extra_pax_charges = data.extra_pax_charges ? data.extra_pax_charges : 0;
    return (total_amount + fundAmount + cardSwipeCharges + earlycheckincharge + extra_pax_charges);
}


    getReportData(searchParams) {
     // console.log(searchParams , "searchParams");

        this.originalData = []
        this.getReportDataSub = this.BS.getAccountReport(searchParams)
            .subscribe((res) => {

                if (res.status === 'success') {
                    // this.originalData = res.data.paymentRecords;
                    let a = res.data;
                    if (a.hasOwnProperty('agent')) {
                        let obj = {
                            name: 'agent',
                            checked: true,
                            data: []
                        }
                        obj.data = a.agent
                        this.originalData.push(obj)
                    }
                    if (a.hasOwnProperty('advance')) {
                        let obj = {
                            name: 'advance',
                            checked: true,
                            data: []
                        }
                        obj.data = a.advance
                        this.originalData.push(obj)
                    }
                    if (a.hasOwnProperty('checkin') || a.hasOwnProperty('checkout')) {
                        let obj = {
                            name: 'checkin/checkout',
                            checked: true,
                            data: []
                        };
                    
                        // Include a.checkin in obj.data if it exists
                        if (a.hasOwnProperty('checkin')) {
                            obj.data = obj.data.concat(a.checkin);
                        }
                    
                        // Include a.checkout in obj.data if it exists
                        if (a.hasOwnProperty('checkout')) {
                            obj.data = obj.data.concat(a.checkout);
                        }
                    
                        this.originalData.push(obj);
                    
                        // obj.data = a.checkin.filter((x) => x.booking_status == "checkout");
                        // this.originalData.push(obj);
                        // a.checkin.map((e:any) => {
                        //   if(e.booking_status == "checkout"){
                        // obj.data.push(e)
                        // this.originalData.push(obj)
                        //   }
                        // })
                        // console.log(this.originalData , "this.originalData");

                        // obj.data = a.checkin
                        // this.originalData.push(obj)
                    }
                    if (a.hasOwnProperty('pending')) {
                        let obj = {
                            name: 'pending',
                            checked: true,
                            data: []
                        }
                        obj.data = a.pending
                        this.originalData.push(obj)
                    }
                    // if (a.hasOwnProperty('return')) {
                    //     let obj = {
                    //         name: 'refund',
                    //         checked: true,
                    //         data: []
                    //     }
                    //     obj.data = a.return
                    //     this.originalData.push(obj)
                    // }
                    
                    // this.usersList = res.data.users.map((obj) => {
                    //     return { id: obj.user_id, text: obj.first_name.toUpperCase() + ' ' + obj.last_name.toUpperCase()}
                    // });
                    // this.usersList = this.sortArray(this.usersList)
                    // this.usersList.unshift({ "id": "000000", "text": "All-togather" });
                   
                    this.initializeData();
                   
                    this.updateAmountSummary();
                
                
                }
            });
    }
    search() {
        if (this.searchForm && this.data) {
            this.initializeData();
            if (this.data && this.searchQuery && this.searchQuery.trim() != '') {
                this.data = this.data.filter(data => {
                    let searchTarget = '';
                    Object.keys(data).forEach(key => {
                        searchTarget += data[key];
                    })
                    console.log('serach ', this.data )
                    return (searchTarget.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
                });
            }
            else {
                this.initializeData();
            }
        }
    }
    clearSearch() {
        this.initializeData();
        this.searchQuery = undefined;
    }
    onReset() {
        // console.log("on reset--------------------------------------")
        this.searchForm.controls['fromDate'].setValue(new Date());
        this.searchForm.controls['toDate'].setValue(new Date());
        this.selectedFilterTypes.payment_mode = '000000';
        this.selectedFilterTypes.user_id = '000000';
        this.data = [];
        this.originalData = [];
        this.initializeSummary()
        let searchParams = this.getSearchParams();
        this.getReportData(searchParams);
    }
    initializeData() {
        this.data = this.originalData;
    }

    initializeSummary() {
        this.advanceAmt = 0;
        this.pendingAmt = 0;
        this.checkinAmt = 0;
        this.returnAmt = 0;
        this.agentAmt = 0;
        this.totalAmount = 0;
        this.chequeAmt = 0;
        this.cashAmt = 0;
        this.bankAmt = 0;
        this.cardAmt = 0;
        this.agentAmtPay = 0;
        this.totalPaymentAmount = 0;
    }

    updateAmountSummary() {
       
        this.initializeSummary();
        this.data.map(resData => {
            //console.log(resData);
            resData.data.map(resData1 => {
                console.log(resData1.payment_mode , resData1)
                if(resData1.payment_mode === 'cash') {
                    resData1['payment_mode_name'] = 'CASH';
                    if(resData.name === 'agent') {
                        this.agentAmtPay =  this.agentAmtPay + resData1.payment_amount;
                    } else {
                        this.cashAmt = resData.name === 'refund' ? this.cashAmt - resData1.payment_amount : this.cashAmt + resData1.payment_amount;
                    }
                } else if(resData1.payment_mode === 'card') {
                    resData1['payment_mode_name'] = 'CARD';
                    if(resData.name === 'agent') {
                        this.agentAmtPay =  this.agentAmtPay + resData1.payment_amount + resData1.card_charge;
                    } else {
                        this.cardAmt = resData.name === 'refund' ? this.cardAmt - resData1.payment_amount - resData1.card_charge : this.cardAmt + resData1.payment_amount + resData1.card_charge;
                    }
                } else if(resData1.payment_mode === 'cheque') {
                    resData1['payment_mode_name'] = 'CHEQUE';
                    if(resData.name === 'agent') {
                        this.agentAmtPay =  this.agentAmtPay + resData1.payment_amount;
                    } else {
                        this.chequeAmt = resData.name === 'refund' ? this.chequeAmt - resData1.payment_amount : this.chequeAmt + resData1.payment_amount;
                    }
                } else if(resData1.payment_mode === 'bank') {
                    resData1['payment_mode_name'] = 'BANK';
                    if(resData.name === 'agent') {
                        this.agentAmtPay =  this.agentAmtPay + resData1.payment_amount;
                    } else {
                       this.bankAmt = resData.name === 'refund' ? this.bankAmt - resData1.payment_amount : this.bankAmt + resData1.payment_amount;
                    }
                }
                this.totalPaymentAmount = this.cashAmt +  this.cardAmt + this.chequeAmt + this.bankAmt + this.agentAmtPay;
                console.log(this.totalPaymentAmount ,1234);

                if(resData.name === 'pending'){
                    this.pendingAmt = this.pendingAmt +  resData1.payment_amount + resData1.card_charge;
                } else if (resData.name === 'advance'){
                    this.advanceAmt = this.advanceAmt +  resData1.payment_amount + resData1.card_charge;
                } else if (resData.name === 'checkin/checkout'){
                    this.checkinAmt = this.checkinAmt +  resData1.payment_amount + resData1.card_charge;
                } else if (resData.name === 'refund'){
                    this.returnAmt = this.returnAmt +  resData1.return_amount + resData1.card_charge;
                } else if (resData.name === 'agent'){
                    this.agentAmt = this.agentAmt +  resData1.payment_amount + resData1.card_charge;
                }
                this.totalAmount =  this.pendingAmt +  this.advanceAmt + this.checkinAmt + this.agentAmt ;

            })
        })
    }

    printRecords() {
      let searchParams = this.getSearchParams();
      this.csvSub = this.BS.getAccountCsvReport(searchParams).subscribe(
        (res) => { 
            debugger;
          let parsedResponse = res;
          let blob = new Blob([parsedResponse], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
          let currentDate = new Date();
          let options = {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
            hour12: false,
            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          };
          let formattedDate = currentDate.toLocaleString(undefined, options);
          let fileName = `AccountReport_${formattedDate}.xlsx`;
          let url = window.URL.createObjectURL(blob);
          if (navigator.msSaveOrOpenBlob) {
            navigator.msSaveBlob(blob, fileName);
          } else {
            let a = document.createElement("a");
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }
          window.URL.revokeObjectURL(url);
        },
        (err) => console.log("error : ", err)
      );
    }

    showNotification(message: string, type: string, showCloseButton: boolean = true, hideAfter: number = 3) {
        Messenger().post({
          type: type,
          message: message,
          hideAfter: hideAfter,
          showCloseButton: showCloseButton,
        });
      }
    sortArray(array) {
        array.sort(function (x, y) {
            let a = x.text.toUpperCase(),
                b = y.text.toUpperCase();
            return a == b ? 0 : a > b ? 1 : -1;
        });
        return array
    }
    ngOnDestroy(): void {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if (this.getReportDataSub)
            this.getReportDataSub.unsubscribe();
        if (this.csvSub)
            this.csvSub.unsubscribe();
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }

    viewInvoice(item: any) {
      this.printBillService = this.BS.getBookingBillDetails(
        item
      ).subscribe((res) => {
        let data = res.data;
        let adult = 0;
        let child = 0;
        let hasEarlyCheckInCharge = 0;
        for (let i = 0; i < data.bookingRoom.length; i++) {
          adult += data.bookingRoom[i].adult;
          child += data.bookingRoom[i].child;
          for (let j = 0; j < data.bookingRoomCharge.length; j++) {
            if (
              data.bookingRoom[i].id == data.bookingRoomCharge[j].booking_room_id
            ) {
              if (data.bookingRoom[i].early_checkin_charge) {
                hasEarlyCheckInCharge += data.bookingRoom[i].early_checkin_charge;
              }
              data.bookingRoom[i]["bookingCharges"] = data.bookingRoomCharge[j];
              break;
            }
          }
        }
        data["booking_details"] = data;
        data["early_check_in_charges"] = hasEarlyCheckInCharge;
        data["adult"] = adult;
        data["child"] = child;
        data["action_type"] = "invoice";
        for (let i = 0; i < data.fundsList.length; i++) {
          data.fundsList[i]["amount"] = 0;
          if (data.bookingFund.length > 0) {
            for (let j = 0; j < data.bookingFund.length; j++) {
              if (data.fundsList[i].id == data.bookingFund[j]["fund_id"]) {
                data.fundsList[i]["amount"] += data.bookingFund[j]["amount"];
              }
            }
            if (data.fundsList[i]["id"] == data.defaultFund.fund_id) {
              data.fundsList[i]["amount"] += data.paidAmount - data.fundAmount;
            }
          } else if (data.fundsList[i].id == data.defaultFund.fund_id) {
            data.fundsList[i]["amount"] = data.paidAmount;
          }
        }
        console.log(data,'data')
        this.invoiceDataForView = data;
        this.invoiceModal.show();
        this.totalAmaunt = this.invoiceDataForView.fundsList[0].amount;
      });
    }

    getHtml(bookingId: number) {
      let data = {
        data: this.invoiceHtml.nativeElement.innerHTML,
      };
      this.BS.saveHtmltoPDF(bookingId, data).subscribe((res) => {
        if (res) {
          //new change by kajal
          var blob = new Blob([res], { type: "application/pdf" });
          const blobUrl = URL.createObjectURL(blob);
          const iframe = document.createElement("iframe");
          iframe.style.display = "none";
          iframe.src = blobUrl;
          document.body.appendChild(iframe);
          iframe.contentWindow.print();
        }
      });
    }
}
