import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, Validators, FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Select2OptionData } from 'ng2-select2';
import { RoomsService } from './../../../shared/services/room.service';
// import * as data from '../../data';
declare var Messenger: any;
@Component({
    selector: 'edit-room',
    templateUrl: '../rooms.actions.component.html'
})

export class EditRoomComponent implements OnInit, OnDestroy {
    public pageName: string = "Edit";
    select2Options: any = {
        width: '100%'
    };
    roomForm: FormGroup;
    //services
    private sub: any;
    private floor: any;
    private getData: any;
    private floorList: any;
    // Input and outputs
    @Input() getHiddenER;
    @Input() selectedRoom;
    @Output() sendHiddenER = new EventEmitter();
    public roomCat: any[];
    public wing: any[];
    public selectedFloorId: any;
    public selectedBuildingId: any;
    public selectedRoomCategoryId: any;
    private categoryListCache: any[] = null;
    private wingListCache: any[] = null;
    private floorListCache: any[] = null;
    selectedFloor: any = null;

    constructor(
        private _fb: FormBuilder,
        private RS: RoomsService
    ) {}

    ngOnInit() {
        this.buildForm();
        this.loadRoomCategories();
        console.log("this.selectedRoom : ", this.selectedRoom);
        this.setBuilding().then(res => {
            // Load floors for the selected building
            if (this.selectedRoom && this.selectedRoom.building_id) {
                this.loadFloorsForBuilding(this.selectedRoom.building_id);
                // Set initial floor selection
                if (this.selectedRoom.floor_id) {
                    this.selectedFloor = { id: this.selectedRoom.floor_id, text: this.selectedRoom.floor_name };
                }
            }
        });
        this.roomForm.patchValue(this.selectedRoom);
    }

    loadRoomCategories() {
        if (this.categoryListCache) {
            this.roomCat = this.categoryListCache;
            return;
        }

        this.getData = this.RS.getRoomCatandWingData()
            .subscribe((res) => {
                if (res.status == "success") {
                    if (res.data.roomcategories) {
                        this.roomCat = res.data.roomcategories;
                        this.categoryListCache = this.roomCat;
                    } else {
                        this.roomCat = [];
                        this.categoryListCache = [];
                        Messenger().post({  hideAfter: 5,
                            message: "No Room Categories found.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                    if (res.data.buildings) {
                        this.wing = res.data.buildings;
                        this.wingListCache = this.wing;
                    } else {
                        this.wing = [];
                        this.wingListCache = [];
                        Messenger().post({  hideAfter: 5,
                            message: "No Buildings found.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                }
            });
    }

    setBuilding() {
        return new Promise<any>((resolve,reject) => {
            setTimeout(() => {
                this.selectedBuildingId = this.selectedRoom['building_id'];
                this.selectedRoomCategoryId = this.selectedRoom['room_category_id'];
                resolve(true);
            }, 200);
        })
    }
    buildForm() {
        this.roomForm = this._fb.group({
            title: ['', Validators.required],
            room_category_id: ['', [Validators.required, CustomValidators.digits]],
            door_id: ['', []],
            building_id: ['', [Validators.required, CustomValidators.digits]],
            floor_id: ['',[Validators.required, CustomValidators.digits]],
            default_bed: ['0', [Validators.required, CustomValidators.digits]],
            max_bed: ['0', [Validators.required, CustomValidators.digits]],
            status: ['', Validators.required]
        })
    }
    ngOnDestroy() {
        if (this.getData) {
            this.getData.unsubscribe();
        }
        if (this.floor) {
            this.floor.unsubscribe();
        }
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
    getCategorylist(): any[] {
        console.log("this.roomCat : ",this.roomCat);
        return jQuery.map(this.roomCat, function (obj) {
            return { id: obj.id, text: obj.name };
        })
    }
    getWingList(): any[] {
        if (this.wingListCache) {
            return jQuery.map(this.wingListCache, function (obj) {
                return { id: obj.id, text: obj.name };
            });
        }
        return [];
    }
    getFloorList(): any[] {
        if (this.floorListCache) {
            return jQuery.map(this.floorListCache, function (obj) {
                return this.floorListCache.push({ id: obj.id, text: obj.name });
            });
        }
        return [];
    }
    categoryChanged(event) {
        console.log("Category changed", event);
        this.roomForm.controls['room_category_id'].patchValue(event.id);
    }
    wingChanged(event) {
        console.log("Wing changed", event);
        if (event) {
            this.roomForm.controls['building_id'].patchValue(event.id);
            this.loadFloorsForBuilding(event.id);
            // Reset floor selection when building changes
            this.selectedFloor = null;
            this.roomForm.controls['floor_id'].patchValue(null);
        }
    }
    floorChanged(event) {
        console.log("Floor changed", event);
        if (event) {
            this.selectedFloor = event;
            this.roomForm.controls['floor_id'].patchValue(event.id);
        }
    }
    loadFloorsForBuilding(buildingId: number) {
        this.floor = this.RS.getBuildingFloors(buildingId).subscribe((res) => {
            if (res.status === "success") {
                this.floorList = res.data;
                this.floorListCache = res.data;
                // Set initial floor selection after loading floors
                if (this.selectedRoom && this.selectedRoom.floor_id) {
                    this.selectedFloor = { id: this.selectedRoom.floor_id, text: this.selectedRoom.floor_name };
                }
            }
        });
    }
    // save
    saveRoom() {
        if (this.roomForm.valid) {
            if(this.roomForm.value && this.roomForm.value.door_id == ''){
                this.roomForm.value.door_id = null;
            }
            console.log("Form : ",this.roomForm.value);
            this.sub = this.RS.updateRoomCatandWingData(this.selectedRoom.id, this.roomForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        Messenger().post({  hideAfter: 5,
                            message: res.message,
                            type: res.status,
                            showCloseButton: true
                        });
                        this.toggleChild(res.data);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.roomForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    // toggle to mains
    toggleChild(data) {
        this.getHiddenER = !this.getHiddenER;
        let result;
        if (data) {
            result = { 'getHiddenER': this.getHiddenER, 'data': data }
        } else {
            result = { 'getHiddenER': this.getHiddenER }
        }
        this.sendHiddenER.emit(result);
    }
}