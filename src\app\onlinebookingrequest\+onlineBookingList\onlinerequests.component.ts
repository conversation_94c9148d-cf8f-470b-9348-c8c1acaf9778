import { Component, OnInit, ViewChild } from '@angular/core';
import { BookingService } from '../../shared/services/booking.service';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
import { ModalDirective } from 'ngx-bootstrap/modal';

@Component({
    selector: 'rooms',
    styleUrls: ['./onlinebookingrequest.style.scss'],
    templateUrl: './onlineRequestRooms.component.html'
})
export class OnlineBookingRequestComponent implements OnInit {
    config: any;// New Change ****
    data: any[];
    originalData: any[];
    searchQuery: string;
    public selectedBooking: any;
    public canViewRecords: boolean;
    cancelReason: string;
    cancelId: number;
    cancelErrorMessage: any;
    //services
    private sub: any;
    // hide and show child components
    hiddenAR: boolean = true;
    isLoading: boolean = true
    cancelButtenLoader: boolean = false;
    acceptBookingData: any;
    acceptErrorMessage: any;
    acceptButtonLoader: any;
    transferRoomId: number
    customerTypeId: any
    updateButtonStatus: boolean = false;
    showReferenceUserField: boolean = true;
    reference_user_name: string;
    successModalStyle: string = '';
    acceptBookingStyle: string = '';
    cancleBookingStyle: string = '';
    imageUrls: string[] = [];

    showReferenceInput: boolean;
    referanceInput: string;


    customerList: any = [
        {
            id: 1,
            customer_name: 'Satsangi'
        },
        {
            id: 3,
            customer_name: 'Normal'
        }
    ]
    roomsList: any
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    @ViewChild('cancelBookingRequest') public cancelBookingRequest: ModalDirective;
    @ViewChild('viewDetailsModal') public viewDetailsModal: ModalDirective;
    @ViewChild('acceptBookingModal') public acceptBookingModal: ModalDirective;
    @ViewChild('viewDocumentModal') public viewDocumentModal: ModalDirective;
    @ViewChild('successModal') public successModal: ModalDirective;

    constructor(public translate: TranslateService, private auth: AuthGuard, private services: BookingService, config: AppConfig, private TS: TranslateEventService) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    getOnlineBookingData() {
        try {
            this.services.getOnlineBookingList()
                .subscribe((res) => {
                    console.log("Response", res)
                    if (res.status == "success") {
                        this.data = res.data;
                        this.isLoading = false
                        console.log("data", this.data);

                        // this.originalData = res.data.rooms;
                    }
                }, error => {
                    if (error.status !== 'success') {
                        this.canViewRecords = false;
                        this.isLoading = false
                    }
                });
        } catch (error) {
            console.log('got error in fetching data', error);

            this.isLoading = false
        } finally {
            // this.isLoading = false
        }
    }
    ngOnInit() {
        this.canViewRecords = true;
        this.getOnlineBookingData();
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    //new functionalities added here
    ReturnDate(date: any) {
        const newDate = new Date(date);
        return newDate.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    }

    //cancel the booking
    cancelOnlineBookingRequest() {
        if (!this.cancelReason) {
            return this.cancelErrorMessage = "Please Add Reason for Cancellation"
        }
        this.cancelButtenLoader = true
        this.services.cancelOnlineBooking(this.cancelId, this.cancelReason).subscribe(
            (response) => {

                if (response.status === "success") {
                    this.cancleBookingStyle = 'alert alert-success'
                    this.cancelErrorMessage = response.message;
                    this.cancelButtenLoader = false
                    // console.log("Cancellation successful", response);
                    setTimeout(() => {
                        this.cancelBookingRequest.hide();
                        this.viewDetailsModal.hide();
                        this.cancelErrorMessage = '';
                        this.cancelReason = '';
                    }, 3000);
                    return this.getOnlineBookingData()
                }

            }, (error) => {
                console.log('error in cancelling booking');
                if (error) {
                    this.cancleBookingStyle = 'alert alert-danger'
                    this.cancelErrorMessage = error.message;
                    this.cancelButtenLoader = false
                    setTimeout(() => {
                        this.cancelBookingRequest.hide();
                        this.cancelErrorMessage = '';
                        this.cancelReason = '';
                    }, 3000);
                    return this.getOnlineBookingData()
                }

                this.cancelButtenLoader = false
            }
        );
    }
    closeAcceptBooking(){
        this.acceptBookingModal.hide()
        this.viewDetailsModal.hide()
    }
    cancleRejectBooking() {
        this.cancelBookingRequest.hide()
        this.viewDetailsModal.hide()
    }

    canclemodal(id: any) {
        this.cancelId = id
        console.log('Canceling booking with id:', this.cancelId);
        this.cancelBookingRequest.show();
    }

    acceptBookingRequest() {
        this.acceptButtonLoader = true;
        this.services.approveOnlineBooking(this.acceptBookingData).subscribe((response) => {
            if (response.status === "success") {
                this.acceptBookingStyle = 'alert alert-success'
                this.acceptErrorMessage = response.message;
                this.acceptButtonLoader = false
                // console.log("acceptlation successful", response);
                setTimeout(() => {
                    this.acceptBookingModal.hide();
                    this.viewDetailsModal.hide();
                    this.acceptErrorMessage = ''
                }, 2800);
                return this.getOnlineBookingData()
            }

        }, (error) => {
            console.log('error in acceptling booking', error);
            if (error) {
                this.acceptBookingStyle = 'alert alert-danger'
                this.acceptErrorMessage = error.message;
                this.acceptButtonLoader = false
                setTimeout(() => {
                    this.acceptBookingModal.hide();
                    this.viewDetailsModal.hide();
                    this.acceptErrorMessage = ''
                }, 3000);
                return this.getOnlineBookingData()
            }
        }
        )
    }

    acceptmodal(booking: any) {
        console.log(booking, "booking");

        this.acceptBookingData = booking;
        this.acceptBookingModal.show()
    }
    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }
    goback() {
        this.viewDetailsModal.hide();
        this.selectedBooking = null;
        this.transferRoomId = null;
        this.customerTypeId = null;
        this.showReferenceUserField = true;
    }

    getRoomslist(data: any) {

        let modifyData = {
            start_date: data.start,
            end_date: data.end,
            room_category_id: data.roomCategoryDetails.id
        }

        console.log(modifyData);

        this.services.getAvailableRooms(modifyData).subscribe((response) => {
            if (response.status === "success") {
                this.roomsList = response.data;
                console.log(this.roomsList, "got rooms list");
            }
        }, (error) => {
            console.log("got error", error);
        })
    }

    // set room id to save to the database

    onRoomChange(event: any): void {
        const selectedId = event.target.value;
        this.transferRoomId = selectedId ? Number(selectedId) : null;
        console.log('Selected room ID:', this.transferRoomId);
    }
    changeCustomerType(event: any) {
        this.customerTypeId = event.target.value;
        if(event.target.value == 1) {
            this.showReferenceUserField = false;
        }
        console.log(this.customerTypeId, "customerTypeId");
    }


    timeoutMethod() {
        setTimeout(() => {
            this.selectedBooking = null;
            this.roomsList = null;
            this.customerTypeId = null;
            this.transferRoomId = null;
            this.acceptErrorMessage = ''
            this.successModal.hide()
            this.getOnlineBookingData();
            this,this.viewDetailsModal.hide();
        }, 1500);
    }
    updateRoomDetails(data: any) {
        let formatData = {
            online_booking_id: data.id ,
            new_room_id: this.transferRoomId ? this.transferRoomId : null,
            new_room_title: this.transferRoomId && this.roomsList.find(room => room.id === this.transferRoomId).title,
            customer_type: this.customerTypeId ? this.customerTypeId : null,
            reference_user_name: this.reference_user_name ? this.reference_user_name : '',
            room_category_id: this.selectedBooking.room_category_id,
            total_amount: this.selectedBooking.total_amount,
            total_days: this.selectedBooking.total_days,
            selectedBooking: this.selectedBooking
        }

        console.log(formatData, "data fromatted");
        this.acceptButtonLoader = true
        this.services.transferBookingRoom(formatData).subscribe((response) => {
            if (response.status === "success") {
                this.acceptButtonLoader = false
                this.acceptErrorMessage = response.message
                console.log(response, "got response");
                this.successModalStyle = 'alert alert-success'
                this.successModal.show()
                this.timeoutMethod();
            } else {
                this.acceptErrorMessage = response.message
                this.acceptButtonLoader = false
                console.log("error response", response);
                this.successModalStyle = 'alert alert-danger'
                this.successModal.show()
                this.timeoutMethod();
            }
            this.showReferenceUserField = true;
        }, (error) => {
            this.acceptErrorMessage = error.message
            this.acceptButtonLoader = false
            console.log(error, "got error");
            this.successModalStyle = 'alert alert-danger'
            this.successModal.show()
            this.timeoutMethod();
        })


    }

    checkReferance() {
        if (this.customerTypeId == 1 && !this.selectedBooking.referance_user_name) {
            this.showReferenceInput = true
        }
    }
    // view details of the booking
    showER(data: any) {
        console.log(data,"dataaa on view");
        
        this.getRoomslist(data);
        this.selectedBooking = data;
        this.viewDetailsModal.show();
    }

    viewDocumentProof(img: any) {
        for (const url of img) {
            this.imageUrls.push(url)
        }
        this.viewDocumentModal.show();
    }

    closeDocumentModal() {
        this.viewDocumentModal.hide();
        this.viewDetailsModal.hide();
        this.imageUrls = [];
        setTimeout(()=>{
            this.viewDetailsModal.show();
        },500)
    }

    searchEvent() {
        this.initializeData();
        if (this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter(data => {
                return ((<string>data.title).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    initializeData() {
        this.data = this.originalData;
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}