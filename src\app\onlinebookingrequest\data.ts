import { Select2OptionData } from 'ng2-select2/ng2-select2';
export const rooms = [
	{
		'id': 1,
		'name': 'royal',
		'maxStay': 40,
		'totalRooms': 20,
		'onlineQuota': 5,
		'releasebefore': 2,
		'description': 'Lorem ipsum this is dummy text to show description of room.',
		'amenities': [
			{
				'type': 'television',
				'charge': null
			},
			{
				'type': 'room service',
				'charge': 300
			}
		],
		'images': [],
		'tariffBy': [
			{
				'customerType': 'royal',
				'valueinPer': 10,
				'valueinAmt': null
			},
			{
				'customerType': 'exclusive',
				'valueinPer': 12,
				'valueinAmt': null
			},
			{
				'customerType': 'general',
				'valueinPer': null,
				'valueinAmt': 150
			},
			{
				'customerType': 'hari bhakt',
				'valueinPer': null,
				'valueinAmt': null
			}
		],
		'releaseQuotaBefore': 2,
		'status': false
	},
	{
		'id': 2,
		'name': 'exclusive',
		'maxStay': 60,
		'totalRooms': 30,
		'onlineQuota': 15,
		'releasebefore': 2,
		'description': 'Lorem ipsum this is dummy text to show description of room.',
		'amenities': [
			{
				'type': 'television',
				'charge': null
			},
			{
				'type': 'room service',
				'charge': 300
			}
		],
		'images': [],
		'tariffBy': [
			{
				'customerType': 'royal',
				'valueinPer': 10,
				'valueinAmt': null
			},
			{
				'customerType': 'exclusive',
				'valueinPer': 12,
				'valueinAmt': null
			},
			{
				'customerType': 'general',
				'valueinPer': 150,
				'valueinAmt': null
			},
			{
				'customerType': 'hari bhakt',
				'valueinPer': null,
				'valueinAmt': null
			}
		],
		'releaseQuotaBefore': 2,
		'status': true
	}
]

export const roomList = [
	{
		'id': 1,
		'title': 'A1',
		'category': 'Royal',
		'wing': 'Wing-A',
		'phone': '123456652',
		'status': true
	},
	{
		'id': 2,
		'title': 'B1',
		'category': 'Exclusive',
		'wing': 'Wing-B',
		'phone': '54212452',
		'status': false
	},
	{
		'id': 3,
		'title': 'C1',
		'category': 'Standard',
		'wing': 'Wing-C',
		'phone': '1234252',
		'status': true
	},
	{
		'id': 4,
		'title': 'D1',
		'category': 'Royal',
		'wing': 'Wing-D',
		'phone': '111223324',
		'status': true
	}
]

export const select2DefaultData: Select2OptionData[] = [{
	id: '1',
	text: 'Large Magellanic Cloud'
},
{
	id: '2',
	text: 'Andromeda Galaxy'
},
{
	id: '3',
	text: 'Sextans A'
}];
export const reservationDetails = [
	{
		'id': 1,
		'roomType': 'Royal',
		'rooms': [
			{
				'id': 1,
				'name': 'A1',
				'person': 4,
				'bookings': [
					{
						'id': 1,
						'customerName': 'Mr. John Dow',
						'start': {
							'date': 1,
							'month': 4,
							'year': 2017
						},
						'end': {
							'date': 6,
							'month': 4,
							'year': 2017
						},
						'status': 'Checked In'
					}
				]
			},
			{
				'id': 2,
				'name': 'A2',
				'person': 4,
				'bookings': [
					{
						'id': 1,
						'customerName': 'Miss. Sansa Stark',
						'start': {
							'date': 27,
							'month': 4,
							'year': 2017
						},
						'end': {
							'date': 12,
							'month': 5,
							'year': 2017
						},
						'status': 'Checked In'
					}
				]
			}
		]
	}
]



export const sample = {
	"room_settings": [
		{
			"room": "A1",
			"settings": [
				{
					"date": "2017-05-31T18:30:00.000Z",
					"oq": 55

				},
				{
					"date": "2017-06-01T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-02T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-03T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-04T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-05T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-06T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-07T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-08T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-09T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-10T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-11T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-12T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-13T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-14T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-15T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-16T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-17T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-18T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-19T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-20T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-21T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-22T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-23T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-24T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-25T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-26T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-27T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-28T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-29T18:30:00.000Z",
					"oq": 15

				}
			]
		},
		{
			"room": "A2",
			"settings": [
				{
					"date": "2017-05-31T18:30:00.000Z",
					"oq": 25

				},
				{
					"date": "2017-06-01T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-02T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-03T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-04T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-05T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-06T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-07T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-08T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-09T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-10T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-11T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-12T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-13T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-14T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-15T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-16T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-17T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-18T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-19T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-20T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-21T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-22T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-23T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-24T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-25T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-26T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-27T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-28T18:30:00.000Z",
					"oq": 15

				},
				{
					"date": "2017-06-29T18:30:00.000Z",
					"oq": 15

				}
			]
		}
	]
}