import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()
export class AmenitiesService {

    constructor(private chttp: CommonHttpService) {  }

    getAllAmenities(){
        return this.chttp.get('amenity/list');
    }
    getOneAmenity(id){
        return this.chttp.get(`amenity/${id}`);
    }
    saveAmenities(data){
        return this.chttp.post('amenity/add', data, true)
    } 
    updateAmenity(id,data){
        return this.chttp.post(`amenity/edit/${id}`, data, true)
    }  
}