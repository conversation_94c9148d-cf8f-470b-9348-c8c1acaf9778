import { Component, OnInit, Input, OnChanges, Output, EventEmitter, OnDestroy } from '@angular/core';
import { BookingService } from './../../../shared/services/booking.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { _secretKey } from '../../../shared/globals/config';
import { CustomValidators } from 'ng2-validation';
import * as CryptoJS from 'crypto-js';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'edit-cancelation-policy',
    templateUrl: '../cancelationPlicy.action.component.html'
})

export class EditCancelationPolicyComponent implements OnInit, OnDestroy, OnChanges {
    policyForm: FormGroup;
    pageType: string = "Edit";
    private _secretKey = _secretKey;
    // service
    private sub: any;

    @Input() selectedPolicy;
    @Output() sendEdited = new EventEmitter();
    @Output() closeComp = new EventEmitter();
    constructor(
        private _fb: FormBuilder,
        private BS: BookingService,
        public translate: TranslateService
    ) { 
        translate.get('CANCELLATION POLICY.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.buildForm(this.selectedPolicy);
    }
    ngOnChanges(changes: any) {
        if (changes) {
            this.buildForm(this.selectedPolicy);
        }
    }
    buildForm(data) {
        this.policyForm = this._fb.group({
            name: ['', [Validators.required]],
            status: ['', Validators.required],
            passcode: ['', [Validators.required]],
            discount: ['0', [Validators.required, CustomValidators.number]],
            day_before: ['', [Validators.required, CustomValidators.digits]],
        })
        if (data) {
            this.policyForm.patchValue(data);
            if (data.passcode) {
                let bytes = CryptoJS.AES.decrypt(data.passcode, this._secretKey);
                let passcode = bytes.toString(CryptoJS.enc.Utf8);
                this.policyForm.get('passcode').patchValue(passcode);
            }
        }
    }
    closeThisComp() {
        this.closeComp.emit(true);
    }
    savePolicy() {
        if (this.policyForm.valid) {
            let data = JSON.parse(JSON.stringify(this.policyForm.value));
            data.passcode = CryptoJS.AES.encrypt(data.passcode, this._secretKey).toString();
            this.sub = this.BS.updatePolicy(this.selectedPolicy.id, data)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.policyForm.reset();
                        this.sendEdited.emit(res.data);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.policyForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({
                hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}