import { NgSelectModule } from '@ng-select/ng-select';
import { NgModule, Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { AddRoomMaintenanceReasonComponent } from './+add/add.roomMaintenanceReason.component';
import { EditRoomMaintenanceReasonComponent } from './+edit/edit.roomMaintenanceReason.component';
import { RoomMaintenanceReasonComponent } from './roomMaintenanceReason.component';
import { RoomMaintenanceReasonService } from 'app/shared/services/roomMaintenanceReason.service';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

// all routes related to this Role module.
export const routes = [
  { path: '', component: RoomMaintenanceReasonComponent, pathMatch: 'full' }
]

@NgModule({
  imports: [
    FormsModule,
    CommonModule,
    NgSelectModule,
    TooltipModule.forRoot(),
    DataTableModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    TranslateModule.forRoot({
      loader:{ 
          provide: TranslateLoader, 
          useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
          deps: [HttpClient] 
      }})
  ],
  exports: [],
  declarations: [
    AddRoomMaintenanceReasonComponent,
    EditRoomMaintenanceReasonComponent,
    RoomMaintenanceReasonComponent
  ],
  providers: [RoomMaintenanceReasonService],
})
export class RoomMaintenanceReasonModule { }
