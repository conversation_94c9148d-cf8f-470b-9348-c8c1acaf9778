import { AddExpenseComponent } from './+add/add.expenses.component';
import { DataTableModule } from 'angular2-datatable';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { ExpensesComponent } from './expenses.component';
import { ExpensesService } from 'app/shared/services/expenses.service';
import { EditExpenseComponent } from './edit/edit.expenses.component';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
export const routes: Routes = [
    { path: '', component: ExpensesComponent, pathMatch: 'full'}
]
@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        DataTableModule,
        ReactiveFormsModule,
        RouterModule.forChild(routes),
        TranslateModule.forRoot({
            loader:{ 
                provide: TranslateLoader, 
                useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
                deps: [HttpClient] 
            }})
    ],
    exports: [],
    declarations: [
        ExpensesComponent,
        AddExpenseComponent,
        EditExpenseComponent,
    ],
    providers: [
        ExpensesService
    ],
})
export class ExpensesModule { }