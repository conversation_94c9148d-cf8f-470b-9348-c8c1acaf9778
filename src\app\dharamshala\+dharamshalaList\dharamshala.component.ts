import { DharamshalaService } from './../../shared/services/dharamshala.service';
import { Component, OnInit } from '@angular/core';
import { DharamShala } from './../data';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

@Component({
    selector: 'dharamshala-list',
    templateUrl: './dharamshala.component.html'
})
export class DharamshalaListComponent implements OnInit {
    // service variables
    config: any;// New Change **** 
    data: any[];
    private sub : any;
    originalData: any[];
    searchQuery: string;
    public canViewRecords: boolean;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private DS : DharamshalaService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
		translate.setDefaultLang(currentLang);// New Change ****
    }

    ngOnInit() {
        // console.log("this.auth.roleAccessPermission('dharamshala','list') : ", this.auth.roleAccessPermission('dharamshala', 'list'));
        this.canViewRecords = true;
        this.sub = this.DS.listAllDharamshalas()
        .subscribe((res)=> {
            if(res.status == "success"){
                this.data = res.data;
                this.originalData = res.data;
                // console.log("Dharamshala List : ",this.data);
            }else{
                // console.log(res)
            }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
     }
     changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    searchEvent() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter( data => {
                return ((<string>data.name).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    
    initializeData() {
        this.data = this.originalData;
    }

    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        
        if(this.sub) {
            this.sub.unsubscribe();
        }
        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}