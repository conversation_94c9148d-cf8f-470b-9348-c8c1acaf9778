import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
import { ModalDirective } from 'ngx-bootstrap/modal';
import { PaymentRequestService } from 'app/shared/services/payment.service';
import { BookingService } from 'app/shared/services/booking.service';

@Component({
    selector: 'payment-request',
    styleUrls: ['./onlinepaymentrequest.style.scss'],
    templateUrl: './onlinepaymentrequest.component.html',
})
export class OnlinePaymentRequestComponent implements OnInit {
    config: any;// New Change ****
    paymentsData: any[];
    originalPaymentsData: any[];
    searchQuery: string;
    public selectedPayment: any;
    public canViewRecords: boolean;
    cancelReason: string;
    cancelId: number;
    PhoneErrorMsg: boolean = false
    private sub: any;
    hiddenAR: boolean = true;
    isLoading: boolean = true;
    cancelPaymentMsg: string;
    cancelButtonLoader: boolean = false
    acceptPaymentData: any;
    PaymentButtonLoader: boolean = false
    roomsList: any
    acceptButtonLoader: any;
    transferRoomId: number;
    successMessageStyle: string = '';
    cancelPaymentMsgStyle: string = '';
    // viewPaymentDetail: boolean = true;
    imageUrls: string[] = [];
    acceptErrorMessage: any
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    @ViewChild('cancelPaymentRequest') public cancelPaymentRequest: ModalDirective;
    @ViewChild('viewPaymentRequest') public viewPaymentRequest: ModalDirective;
    @ViewChild('acceptPaymentModal') public acceptPaymentModal: ModalDirective;
    @ViewChild('viewDocumentModal') public viewDocumentModal: ModalDirective;
    @ViewChild('successModal') public successModal: ModalDirective;

    constructor(public translate: TranslateService, private auth: AuthGuard, private services: PaymentRequestService, private  BookingService: BookingService, config: AppConfig, private TS: TranslateEventService) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    getPaymentData() {
        try {
            this.isLoading = true;
            this.services.getOnlinePaymentList()
                .subscribe((res) => {
                    console.log("Response", res)
                    if (res.status == "success") {
                        this.paymentsData = res.data;
                        // console.log("payment data", this.paymentsData);
                        // this.originalPaymentsData = res.data.rooms;
                        this.isLoading = false
                        this.canViewRecords = true;
                    }
                }, (error) => {
                    if (error.status !== 'success') {
                        this.canViewRecords = false;
                        this.isLoading = false
                    }
                });
        } catch (error) {
            this.canViewRecords = false
            this.isLoading = false
            console.log("got error fetching data", error);
        }
    }
    ngOnInit() {
        this.canViewRecords = true;
        this.getPaymentData();
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    //cancel the payment request
    cancelOnlinePaymentRequest() {
        if (!this.cancelReason) {
            this.cancelPaymentMsgStyle = 'alert alert-danger text-capitalize';
            return this.cancelPaymentMsg = "Please Add Reason for Cancellation";
        }
        this.cancelButtonLoader = true
        this.services.cancelOnlinePayment(this.cancelId, this.cancelReason).subscribe((response) => {
            if (response.status === "success") {
                //set success message and remove loder
                this.cancelPaymentMsgStyle = 'alert alert-success text-capitalize';
                this.cancelPaymentMsg = response.message;
                this.cancelButtonLoader = false

                //fetch new data again
                this.getPaymentData();
                setTimeout(() => {
                    this.cancelPaymentRequest.hide();
                    this.viewPaymentRequest.hide()
                    this.cancelPaymentMsg = '';
                    this.cancelReason = ''
                }, 3000);
            }
        }, (error) => {
            console.log('error in cancelling booking', error);
            if (error) {
                this.cancelPaymentMsgStyle = 'alert alert-danger text-capitalize';
                this.cancelPaymentMsg = error.message;
                this.cancelButtonLoader = false
                setTimeout(() => {
                    this.cancelPaymentRequest.hide();
                    this.cancelPaymentMsg = '';
                    this.cancelReason = '';
                }, 3000);
                return this.getPaymentData()
            }
            this.cancelButtonLoader = false
        }
        );
    }

    closeRejectPayment() {
        this.cancelPaymentRequest.hide()
        this.viewPaymentRequest.hide()
    }

    closeAcceptPayment() {
        this.acceptPaymentModal.hide()
        this.viewPaymentRequest.hide()
    }

    viewDocumentProof(img: any) {
        for (const url of img) {
            this.imageUrls.push(url)
        }
        this.viewDocumentModal.show();
    }
    
    closeDocumentModal() {
        this.viewDocumentModal.hide();
        this.viewPaymentRequest.hide();
        this.imageUrls = [];
        setTimeout(()=>{
            this.viewPaymentRequest.show();
        }, 500)
    }
    openCancelModal(id: any) {
        this.cancelId = id
        console.log('id', id);
        // this.viewPaymentRequest.hide();
        this.cancelPaymentRequest.show();
    }

    acceptmodal(payment: any) {
        this.selectedPayment = payment;
        this.acceptPaymentModal.show()
    }

    //api rquest to approve the payment request of user
    approvePaymentRequest() {

        this.PaymentButtonLoader = true
        console.log("approve data", this.selectedPayment);

        this.services.approveOnlinePayment(this.selectedPayment).subscribe((response) => {
            if (response && response.status === 'success')
                this.successMessageStyle = 'alert alert-success text-capitalize';
                this.acceptErrorMessage = "payment Accapted Now Reserving Room"
                this.services.createBooking(response.data).subscribe((data) => {
                    if (data.status === "success") {
                        this.successMessageStyle = 'alert alert-success text-capitalize';
                        this.acceptErrorMessage = data.message;

                        setTimeout(() => {
                            this.PaymentButtonLoader = false
                            this.getPaymentData();
                            this.selectedPayment = null
                            this.viewPaymentRequest.hide();
                            this.acceptPaymentModal.hide();
                            this.acceptErrorMessage = ''
                        }, 1500);
                    }

                }, (error) => {
                    this.successMessageStyle = 'alert alert-danger text-capitalize';
                    this.acceptErrorMessage = error.message;
                    this.PaymentButtonLoader = false
                    console.log('got error in saving the data in booking rooms', error);
                })
        }, (error) => {
            this.PaymentButtonLoader = false
            this.successMessageStyle = 'alert alert-danger text-capitalize';
            this.acceptErrorMessage = "Failed To Approve The Payment"
            console.log('Approval failed', error);
        })
    }

    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.paymentsData;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }
    goback() {
        this.viewPaymentRequest.hide();
        this.transferRoomId = null
    }
    showER(data: any) {
        this.getRoomslist(data);
        this.selectedPayment = data
        this.viewPaymentRequest.show();
    }
    // edit payment component
    // showPaymentDetails(id: number) {
    //     this.services.viewOnlinePayment(id).subscribe((response) => {
    //         this.selectedPayment = response;
    //         console.log('response received', response);
    //     }, (error) => {
    //         console.log('error received', error);
    //     })
    //     this.viewPaymentDetail = !this.viewPaymentDetail;
    // }

    // searchEvent() {
    //     this.initializeData();
    //     if (this.searchQuery && this.searchQuery.trim() != '') {
    //         this.paymentsData = this.paymentsData.filter(data => {
    //             return ((<string>data.title).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
    //         });
    //     }
    //     else
    //         this.initializeData();
    // }
    initializeData() {
        this.paymentsData = this.originalPaymentsData;
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }

    getRoomslist(data: any) {
        console.log(data, 'search room data')
        let modifyData = {
            start_date: data.room_detail.start,
            end_date: data.room_detail.end,
            room_category_id: data.room_detail.room_category_id
        }

        console.log(modifyData);

        this.BookingService.getAvailableRooms(modifyData).subscribe((response) => {
            if (response.status === "success") {
                this.roomsList = response.data;
                console.log(this.roomsList, "got rooms list");
            }
        }, (error) => {
            console.log("got error", error);
        })
    }

    onRoomChange(event: any): void {
        const selectedId = event.target.value;
        this.transferRoomId = selectedId ? Number(selectedId) : null;
        console.log('Selected room ID:', this.transferRoomId);
    }

    timeoutMethod() {
        setTimeout(() => {
            this.selectedPayment = null;
            this.roomsList = null;
            this.transferRoomId = null;
            this.acceptErrorMessage = ''
            this.successModal.hide()
            this.getPaymentData();
            this,this.viewPaymentRequest.hide();
        }, 1500);
    }

    updateRoomDetails(data: any) {
        let formatData = {
            online_booking_id: data.online_booking_rooms_id ,
            new_room_id: this.transferRoomId ? this.transferRoomId : null,
            new_room_title: this.transferRoomId && this.roomsList.find(room => room.id === this.transferRoomId).title,
            customer_type: null,
            room_category_id: this.selectedPayment.room_detail.room_category_id,
            total_amount: this.selectedPayment.room_detail.total_amount,
            total_days: this.selectedPayment.room_detail.total_days,
            selectedPayment: this.selectedPayment
        }

        console.log(formatData, "data fromatted");
        this.acceptButtonLoader = true
        this.BookingService.transferBookingRoom(formatData).subscribe((response) => {
            if (response.status === "success") {
                this.acceptButtonLoader = false
                this.acceptErrorMessage = response.message
                console.log(response, "got response");
                this.successMessageStyle = 'alert alert-success text-capitalize';
                this.successModal.show()
                this.timeoutMethod();
            } else {
                this.acceptErrorMessage = response.message
                this.acceptButtonLoader = false
                console.log("error response", response);
                this.successMessageStyle = 'alert alert-danger text-capitalize';
                this.successModal.show()
                this.timeoutMethod();
            }
        }, (error) => {
            this.acceptErrorMessage = error.message
            this.acceptButtonLoader = false
            console.log(error, "got error");
            this.successMessageStyle = 'alert alert-danger text-capitalize';
            this.successModal.show()
            this.timeoutMethod();
        })
    }
}