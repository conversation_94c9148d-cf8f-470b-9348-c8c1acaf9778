import { Routes } from "@angular/router";
import { ForgotPasswordComponent } from "./setPassword/forgotPassword.component";
import { SetPasswordComponent } from "./setPassword/setPassword.component";
import { ErrorComponent } from "./error/error.component";
import { AuthGuard } from "./shared/guards/auth-guard.service";
import { OnlineBookingComponent } from "./OnlineBooking/onlinebooking.component";
import { OnlinePaymentComponent } from "./onlinepayment/onlinepayment.component";



export const ROUTES: Routes = [
  {
    path: "",
    redirectTo: "login",
    pathMatch: "full",
  },
  {
    path: "onlinebooking",
    component: OnlineBookingComponent,
    pathMatch: "full",
  },
  {
    path: "onlinepayment",
    component: OnlinePaymentComponent,
    pathMatch: "full",
  },
  {
    path: "admin",
    loadChildren:  ()=> import("./layout/layout.module").then((module)=> module.LayoutModule),
    canActivate: [AuthGuard],
  },
  {
    path: "login",
    loadChildren: ()=> import("./login/login.module").then((module)=> module.LoginModule),
  },
  {
    path: "setpassword/:id",
    component: SetPasswordComponent,
  },
  {
    path: "resetpassword",
    component: ForgotPasswordComponent,
  },
  {
    path: "resetpassword/:id",
    component: SetPasswordComponent,
  },
  {
    path: "error",
    component: ErrorComponent,
  },
  {
    path: "**",
    component: ErrorComponent,
  },

];
