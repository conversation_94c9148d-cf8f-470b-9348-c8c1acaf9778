import { Injectable, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@angular/core'; // <-- Added ErrorHandler
import { HttpClient, HttpResponse, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs/Rx';
import { apiUrl } from '../api-env';

@Injectable()
export class LoggingService implements ErrorHandler { // <-- Implemented ErrorHandler
    private serverLogUrl = apiUrl + 'api/log';
    // Use a single endpoint for different log types

    constructor(private http: HttpClient) {}

    // Added to support Angular global error handling
    handleError(error: any): void {
        this.error(error); // <-- Log error using existing logic
    }

    log(message: any) {
        // Custom logic to send the log message to the server or file
        // For simplicity, we'll just log it to the console for now
        console.error(message, "hi1");
        return this.sendLogToServer(message);
        //console.log(message);
    }

    error(message: any) {
        // Custom logic to send the error message to the server or file
        // For simplicity, we'll just log it to the console for now
        console.error(message);
        return this.sendLogToServer(message);
        //console.error(message);
    }

    logHttpRequest(url: string): Observable<HttpResponse<any>> {
        const logData = {
            type: 'http-request',
            message: { url }, // Include the details in a message property
        };

        // Log the data to the console.
        console.log('HTTP Request:', logData);

        // Send the log to the server for centralized logging.
        return this.sendLogToServer(logData);
    }

    logHttpError(url: string): Observable<HttpResponse<any>> {
        const logData = {
            type: 'http-error',
            message: { url }, // Include the details in a message property
        };

        // Log the data to the console.
        console.error('HTTP Error:', logData);

        // Send the log to the server for centralized logging.
        return this.sendLogToServer(logData);
    }

    private sendLogToServer(logData: any): Observable<HttpResponse<any>> {
      // You can include the type in the log data to distinguish different log types
          
      const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
      const options = { headers };
    
      // 🔧 Fix: Safely stringify logData to avoid circular reference issues
      const getCircularReplacer = () => {
        const seen = new WeakSet();
        return (key: string, value: any) => {
          if (typeof value === 'object' && value !== null) {
            if (seen.has(value)) {
              return '[Circular]';
            }
            seen.add(value);
          }
      
          // 🛠 Fix for Date-like objects throwing toISOString errors
          if (value instanceof Date) {
            try {
              return value.toISOString();
            } catch {
              return '[Invalid Date]';
            }
          }
      
          // 🛡️ Fix for objects with custom toJSON that throws
          if (value && typeof value.toJSON === 'function') {
            try {
              return value.toJSON();
            } catch {
              return '[Invalid toJSON]';
            }
          }
          return value;
        };
      };
    
      const body = logData
    
      return this.http.post<HttpResponse<any>>(this.serverLogUrl, body, options);
    }
    
}