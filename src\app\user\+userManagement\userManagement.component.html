<!--
  ***** <add-user></add-user>
  ********************************
  [data] - will get roles from parent component / Type : Input,
  [gethiddenAdduser] - will get a variable which represents whether the <add-user> component hidden or not / Type : Input, 
  (toggleHiddenAddUser) - this event is for parent whenever child component( <add-user> ) will change hiddenAddUser variable value to hide and show it self / Type : Output.
  ********************************
 -->

<add-user *ngIf="!hiddenAddUser" [data]="userRolesData" [countryList]="countryList"
 [gethiddenAdduser]="hiddenAddUser" (toggleHiddenAddUser)="hiddenAddUserToggled($event)">
</add-user>

<!--
  ***** <edit-user></edit-user>
  ********************************
  same as above eith 
  [roleList] will have roles data.
  ********************************
 -->

<edit-user *ngIf="!hiddenEditUser" [data]="selectedUser" [rolesList]="userRolesData" [countryList]="countryList" [searchQuery]="searchQuery"
[gethiddenEditUser]="hiddenEditUser" (toggleHiddenEditUser)="hiddenEditUserToggle($event)" ></edit-user>

<section class="widget" *ngIf="hiddenAddUser && hiddenEditUser" >
  <header>
    <h4><span class="" style="color: red;"><i class="fa fa-user-circle"></i>&nbsp;&nbsp;{{ 'USER.USER_MANAGEMENT' | translate:param }}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right text-right col-sm-6">

    <button *ngIf="!isSuperAdmin && auth.roleAccessPermission('user','add')" (click)="toggleAddUser()" class="display-inline-block btn btn-sm btn-inverse" tooltip="{{ 'USER.ADD_NEW_USER' | translate:param }}" placement="top"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{ 'USER.ADD' | translate:param }}</button>
    <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" #search (keyup)="canViewRecords ? searchEvent() : null" [(ngModel)]="searchQuery" placeholder="{{ 'USER.SEARCH' | translate:param }}">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body table-scroll ">
    <div class="mt">
        <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
        <tr>
          <th>
            <mfDefaultSorter by="id">#</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="name">{{ 'USER.NAME' | translate:param }}</mfDefaultSorter>
          </th>
          <th *ngIf="isSuperAdmin" class="no-sort  ">
            <mfDefaultSorter by="info">Dharamshala</mfDefaultSorter>
          </th>
          <th class="no-sort  ">
            <mfDefaultSorter by="info">{{ 'USER.EMAIL' | translate:param }}</mfDefaultSorter>
          </th>
          <th class=" ">
            <mfDefaultSorter by="description">{{ 'USER.ROLE' | translate:param }}</mfDefaultSorter>
          </th>
          <th class=" text-center">
            <mfDefaultSorter by="date">{{ 'USER.STATUS' | translate:param }}</mfDefaultSorter>
          </th>
          <th *ngIf="auth.roleAccessPermission('user','edit')" class="no-sort text-center">
            <mfDefaultSorter by="status">{{ 'USER.ACTION' | translate:param }}</mfDefaultSorter>
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let person of mf.data">
          <td>{{findIndex(person.id)}}</td>
          <td><span class="uppercase fw-semi-bold">{{person.first_name}}&nbsp;{{person.last_name}}</span></td>
          <td *ngIf="isSuperAdmin">{{person.dharamshala_name ? person.dharamshala_name : '-'}}</td>
          <td class="">
           
               {{person.email}}
           
          </td>
          <td class=" capitalized">
            {{person.role_name}}
            </td>
          <td class=" text-center">
            <span class="text-success" *ngIf="person.status">{{ 'USER.ACTIVE' | translate:param }}</span>
            <span class="text-danger" *ngIf="!person.status">{{ 'USER.INACTIVE' | translate:param }}</span>
          </td>
          <td *ngIf="auth.roleAccessPermission('user','edit')" class="width-100 text-center">
            <button (click)="editUserData(person)" class="btn btn-xs btn-default" tooltip="{{ 'USER.EDIT_NEW_USER' | translate:param }}" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{ 'USER.EDIT' | translate:param }}</button>
          </td>
        </tr>
        <tr *ngIf="canViewRecords && mf.data.length === 0">
          <td colspan="100">
            {{ 'USER.NO MATCHES' | translate:param }}
          </td>
        </tr>
        <tr *ngIf="!canViewRecords">
          <td class="text-danger" colspan="100">
            {{ 'USER.PERMISSION_DENIED' | translate:param }}
          </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="12">
            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
          </td>
        </tr>
        </tfoot>
      </table>
  </div>
  </div>
</section>