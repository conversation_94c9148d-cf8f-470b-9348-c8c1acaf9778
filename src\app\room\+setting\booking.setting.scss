$height: 55px;
$border-color: #dadada;
body.modal-open {
  overflow: scroll;
}

.pagination {
  display: table;
  margin: 0 0 0 auto;
  &._2{
    margin: 0 auto 0 0 !important;
  }
}

.pagination a {
  color: #333;
  background: #fff;
  input[type="checkbox"] {
    position: absolute;
    left: -999%;
    &~label {
      margin-bottom: 0px;
      min-width: 200px;
      margin-left: -14px;
      margin-right: -14px;
      cursor: pointer;
    }
    &:checked~label {
      background: #fff;
      color: #222
    }
    &:not(:checked)~label {
      background: #f5f5f5;
      color: #999999
    }
  }
}

.custome_table {
  width: 100%;
  margin: 20px auto 0;
  td {
    padding: 2px 0;
    text-align: center;
    color: #075ac7;
    background: #fff;
    border: 1px solid $border-color;
    &.disabled {
      color: #999;
    }
    .verticle_text {
      align-content: stretch;
      writing-mode: vertical-rl;
      transform: rotate(180deg);
      font-weight: bold;
      color: #666;
      text-transform: uppercase;
      font-size: 12px;
      letter-spacing: 1.12px;
      margin: 0 auto;
    }
    .month_name,
    .day_name {
      font-weight: bold;
      font-size: 10px;
      text-transform: uppercase;
    }
    &>.dates {
      display: flex;
      width: 50px;
      padding: 2px;
      font-weight: bold;
      color: #666;
      font-size: 12px;
      &>span {
        flex: 1
      }
      .centered {
        line-height: 2;
        writing-mode: vertical-lr;
        transform: rotate(180deg);
        &+div {
          border-left: 1px solid #dadada;
        }
      }
    }
    &.__settings {
      input {
        -webkit-appearance: none;
        -moz-appearance: none;
        border: none;
        width: 25px;
        text-align: center;
        outline: none;
        outline-color: transparent;
        &:focus,
        &:active {
          outline: none;
          outline-color: transparent
        }
      } // .__inputs {
      //   input[type="text"]:first-child:not(:focus) {
      //     background: rgba(238, 238, 238, 0) // background: rgba(48, 255, 116, 0.23)
      //   }
      //   input[type="text"]:last-child:not(:focus) {
      //     background: rgba(239, 239, 239, 0) // background: rgba(255, 0, 0, 0.23);
      //   }
      // }
    }
  }
}

.bold-fonts {
  font-weight: bold;
  text-transform: uppercase;
  text-align: center;
  padding: 5px;
  line-height: 1;
  color: #333;
  font-size: 12px;
}

.child-flexed {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  &>span {
    flex: 1;
    line-height: 2.5;
    font-size: 11px;
    padding: 0 3px;
  }
}

.date-list {
  span {
    display: block
  }
}

input[type=number] {
  -moz-appearance: textfield;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

// .ng-dirty.ng-invalid.ng-touched{
//   border-color: red
// }
