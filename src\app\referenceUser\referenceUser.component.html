<div class="row">
  <div class="" [ngClass]="{'col-md-12': (addRUHidden && editRUHidden), 'col-md-7' : (!addRUHidden || !editRUHidden)}">
    <section class="widget">
      <header>
        <h4><span class="" style="color: red;"><i class="fa fa-user"></i>&nbsp;&nbsp;{{'REF_USER.REF_USER_MAN' | translate:param}}</span></h4>
      </header>
      <hr class="large-hr">
      <div class="float-sm-right text-right col-sm-12">
        <button
          *ngIf="auth.roleAccessPermission('referenceuser','add')"
          (click)="showAddRU()" 
          [disabled]="!addRUHidden || !editRUHidden" 
          class="display-inline-block btn btn-sm btn-inverse" 
          tooltip="{{'REF_USER.ADD_TOOLTIP' | translate:param}}" 
          placement="top">
          <i class="fa fa-plus"></i>&nbsp;&nbsp;{{'REF_USER.ADD' | translate:param}}
        </button>
        <div class="form-group display-inline-block __search">
          <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'REF_USER.SEARCH' | translate:param}}">
          <span class="form-group-addon"><i class="fa fa-search"></i></span>
          <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
        </div>
      </div>
      <div class="clearfix"></div>
      <div class="widget-body table-scroll">
        <div class="mt">
          <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10" >
            <thead>
              <tr>
                <th>
                  <mfDefaultSorter by="id">#</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="name">{{'REF_USER.NAME' | translate:param}}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="contact">{{'REF_USER.CONTAC_NO' | translate:param}}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="contact">{{'REF_USER.CUST_TYPE' | translate:param}}</mfDefaultSorter>
                </th>
                <th *ngIf="auth.roleAccessPermission('referenceuser','edit')" class="no-sort text-center">
                  <mfDefaultSorter by="status">{{'REF_USER.ACTION' | translate:param}}</mfDefaultSorter>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let ds of mf.data; let i =index" [ngClass]="{'__selected': (ds.id == selectedRU?.id)}">
                <td>{{findIndex(ds.id,"id")}}</td>
                <td><span class="uppercase fw-semi-bold">{{ds.name}}</span></td>
                <td><span class="">{{ds.contact}}</span></td>
                <td><span class="">{{ds.customer_type_ids ? getCustomerTypeString(ds) : '-' }}</span></td>
                <td *ngIf="auth.roleAccessPermission('referenceuser','edit')" class="width-100 text-center">
                  <button (click)="showEditRU(ds)" class="btn btn-xs btn-default" tooltip="Edit Reference User" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;Edit</button>
                </td>
              </tr>
              <tr *ngIf="canViewRecords && mf.data.length === 0">
                <td colspan="100">
                  {{'REF_USER.NO_MATCHES' | translate:param}}
                </td>
              </tr>
              <tr *ngIf="!canViewRecords">
                <td class="text-danger" colspan="100">
                  {{'REF_USER.PERMISSION_DENIED' | translate:param}}
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="12">
                  <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </section>
  </div>

    <add-reference-user class="col-md-5" *ngIf="!addRUHidden"
     [getHiddenaddRU]="addRUHidden" [customerTypeList]="customerTypeList" (sendHiddenaddRU)="handleHiddenaddRU($event)"
     (listSavedData)="addDateTolist($event)"></add-reference-user>
    
    <edit-reference-user class="col-md-5" *ngIf="!editRUHidden" [data]="selectedRU"
    [getHiddeneditRU]="editRUHidden" [customerTypeList]="customerTypeList" (sendHiddeneditRU)="handleHiddeneditRU($event)"
    (listeditedData)="updateTolist($event)" ></edit-reference-user>

</div>
