import { ForgotPasswordComponent } from './setPassword/forgotPassword.component';
import { SetPasswordComponent } from './setPassword/setPassword.component';
import { AuthGuard } from './shared/guards/auth-guard.service';
import { NgModule, ApplicationRef, ErrorHandler } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LoggingService } from './logging/logging.service';
import { ConsoleService } from './console/console.service';
import { ErrorLoggingService } from './errorlogging/errorlogging';
import { RouterModule } from '@angular/router';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { removeNgStyles, createNewHosts, createInputTransfer } from '@angularclass/hmr';
import { CommonModule } from '@angular/common';

import { CommonHttpService } from './shared/services/common-http/common-http.service';
import { DropzoneDirective } from './dharamshala/dropZone.directive';
/*
 * Platform and Environment providers/directives/pipes
 */
import { ENV_PROVIDERS } from './environment';
import { ROUTES } from './app.routes';
// App is our top level component
import { App } from './app.component';
import { APP_RESOLVER_PROVIDERS } from './app.resolver';
import { AppState, InteralStateType } from './app.service';
import { AppConfig } from './app.config';
import { ErrorComponent } from './error/error.component';
import 'messenger/build/js/messenger.js';
import { ModalDirective } from './shared/directive/modal/modal.directive';
import { CardComponent } from './reservation/card/card.component';
import { ReservationServices } from './shared/services/reservation.services';
import { OnlineBooking } from "./OnlineBooking/onlineBooking.service";
import { OnlineBookingComponent } from './OnlineBooking/onlinebooking.component';
import { OnlinePaymentComponent } from './onlinepayment/onlinepayment.component';
import { ModalModule } from 'ngx-bootstrap/modal';
import { NgSelectModule } from '@ng-select/ng-select';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Application wide providers
const APP_PROVIDERS = [
  ...APP_RESOLVER_PROVIDERS,
  AppState,
  AppConfig
];

type StoreType = {
  state: InteralStateType,
  restoreInputValues: () => void,
  disposeOldHosts: () => void
};

/**
 * `AppModule` is the main entry point into Angular2's bootstraping process
 */
@NgModule({
  bootstrap: [ App ],
  declarations: [
    App,
    ErrorComponent,
    OnlineBookingComponent,
    OnlinePaymentComponent,
    SetPasswordComponent,
    ForgotPasswordComponent,
    ModalDirective, 
    CardComponent
  ],
  imports: [ // import Angular's modules
    BrowserModule,
    BrowserAnimationsModule,
    CommonModule,
    NgSelectModule,
    ModalModule.forRoot(), 
    BsDatepickerModule.forRoot(),
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
        deps: [HttpClient]
      }
    }),
    RouterModule.forRoot(ROUTES, { useHash: true })
  ],
  exports: [ ModalDirective ],
  entryComponents: [CardComponent],
  providers: [ // expose our Services and Providers into Angular's dependency injection
    ENV_PROVIDERS,
    APP_PROVIDERS,
    CommonHttpService,
    AuthGuard,
    { provide: ErrorHandler, useClass: LoggingService },
    ReservationServices,
    OnlineBooking,
    LoggingService,
    ErrorLoggingService,
    ConsoleService,
  ]
})
export class AppModule {
  constructor(public appRef: ApplicationRef, public appState: AppState) {}

  hmrOnInit(store: StoreType) {
    if (!store || !store.state) return;
    console.log('HMR store', JSON.stringify(store, null, 2));
    // set state
    this.appState._state = store.state;
    // set input values
    if ('restoreInputValues' in store) {
      let restoreInputValues = store.restoreInputValues;
      setTimeout(restoreInputValues);
    }

    this.appRef.tick();
    delete store.state;
    delete store.restoreInputValues;
  }

  hmrOnDestroy(store: StoreType) {
    const cmpLocation = this.appRef.components.map(cmp => cmp.location.nativeElement);
    // save state
    const state = this.appState._state;
    store.state = state;
    // recreate root elements
    store.disposeOldHosts = createNewHosts(cmpLocation);
    // save input values
    store.restoreInputValues  = createInputTransfer();
    // remove styles
    removeNgStyles();
  }

  hmrAfterDestroy(store: StoreType) {
    // display new elements
    store.disposeOldHosts();
    delete store.disposeOldHosts;
  }

}

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http);
}

