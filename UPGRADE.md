# Angular 6 to Angular 10 Upgrade Documentation

## Overview
This document outlines the process, changes, and challenges faced during the upgrade of our Dharamshala Web application from Angular 6 to Angular 10. The upgrade was necessary to leverage newer features, improve performance, and maintain better security standards.

## Major Updates

### 1. Core Package Updates
- Updated Angular core packages from 6.x to 10.2.5:
  ```json
  "@angular/cdk": "^10.2.7",
  "@angular/common": "^10.2.5",
  "@angular/compiler": "^10.2.5",
  "@angular/core": "^10.2.5",
  "@angular/forms": "^10.2.5",
  "@angular/platform-browser": "^10.2.5",
  "@angular/platform-browser-dynamic": "^10.2.5",
  "@angular/platform-server": "^10.2.5",
  "@angular/router": "^10.2.5"
  ```

### 2. RxJS Updates
- Updated RxJS from version 5.x to 6.6.7
- Added RxJS compatibility package to handle legacy code:
  ```json
  "rxjs": "^6.6.7",
  "rxjs-compat": "^6.6.7"
  ```

### 3. TypeScript Configuration Updates
- Updated TypeScript version to 4.0.8
- Modified tsconfig.json to support newer module formats and ES2015+ features
- Updated lib array to include newer JavaScript features:
  ```json
  "lib": [
    "dom",
    "es2015",
    "es2016",
    "es2017",
    "es2018",
    "esnext"
  ]
  ```

### 4. Webpack Configuration Updates
- Updated webpack configuration to support Angular 10
- Modified module resolution and compilation settings
- Updated loaders and plugins to their latest compatible versions

## Major Issues Faced

### 1. Module Import Issues
- **Problem**: Breaking changes in module import syntax
- **Solution**: Updated import statements to use new syntax and added rxjs-compat for backward compatibility
- **Example Error**:
  ```
  error TS2307: Cannot find module '@angular/platform-browser-dynamic'
  ```

### 2. RxJS Migration
- **Problem**: RxJS 6 introduced breaking changes in import paths and operators
- **Solution**: 
  - Added rxjs-compat package
  - Updated import statements to use pipeable operators
  - Replaced deprecated RxJS features with new alternatives

### 3. Template Syntax Changes
- **Problem**: Some template syntax features were deprecated
- **Solution**: Updated template syntax to use newer Angular 10 conventions
- **Example**: Updated template reference variables and structural directives

### 4. Webpack Build Issues
- **Problem**: Incompatibility with older webpack configuration
- **Solution**: 
  - Updated webpack configuration
  - Modified loader configurations
  - Updated compilation and build settings

## Packages Still Needing Updates

1. **Angular Material Dependencies**
   - Current: Using custom implementation
   - Recommended: Upgrade to @angular/material 10.x

2. **Third-Party Libraries**
   - `angular2-datatable`: Needs replacement with newer alternative
   - `angular2-google-maps`: Should be updated to @agm/core
   - `angular2-text-mask`: Consider updating to newer text mask alternatives

3. **Development Dependencies**
   - Some dev dependencies need updates:
     ```json
     "awesome-typescript-loader": "^5.2.1",
     "webpack-dev-middleware": "1.6.1"
     ```

## Known Issues and Workarounds

1. **HMR (Hot Module Replacement)**
   - Current implementation using @angularclass/hmr needs updates
   - Temporary workaround: Using manual page refresh during development

2. **AOT Compilation**
   - Some components may face AOT compilation issues
   - Workaround: Using JIT compilation for affected components

## Recommendations

1. **Package Updates**
   - Regularly update remaining outdated packages
   - Follow Angular update guide for minor version updates

2. **Code Modernization**
   - Replace deprecated services and methods
   - Adopt Angular 10 best practices
   - Implement lazy loading for better performance

3. **Testing**
   - Update test configurations
   - Add more unit and integration tests
   - Implement e2e tests using latest Protractor version

## Future Considerations

1. **Angular 11+ Upgrade Path**
   - Plan for future upgrades
   - Keep dependencies updated
   - Monitor Angular release schedule

2. **Performance Optimization**
   - Implement lazy loading
   - Use Angular CLI for better build optimization
   - Consider server-side rendering (Angular Universal)

## Resources

- [Official Angular Update Guide](https://update.angular.io/)
- [RxJS Migration Guide](https://rxjs.dev/guide/v6/migration)
- [Angular CLI Documentation](https://angular.io/cli)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/) 