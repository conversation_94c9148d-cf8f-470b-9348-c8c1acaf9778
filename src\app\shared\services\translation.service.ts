import { Injectable } from '@angular/core';
import { Subject } from 'rxjs-compat';

@Injectable()
export class TranslateEventService {
    public langChangeSubject = new Subject();
    public $langChangeSubject = this.langChangeSubject.asObservable();

    constructor() { }

    emitLangChange(data: 'en' | 'hi') {
        this.langChangeSubject.next(data);
        this.setCurrentLanguage(data);
    }

    getCurrentLang() {
        let currentLang = localStorage.getItem('currentLang');
        if (currentLang)
            return currentLang;
        else {
            this.setCurrentLanguage('en');
            return 'en';
        }
    }

    setCurrentLanguage(lang: 'en' | 'hi') {
        if (lang)
            localStorage.setItem('currentLang', lang);
    }
}