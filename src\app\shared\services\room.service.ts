import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()

export class RoomsService {
    constructor(
        private chttp: CommonHttpService
    ) { }

    getRoomCatandWingData(){
       return this.chttp.get(`room/add`);
    }
    saveRoomCatandWingData(data){
       return this.chttp.post(`room/add`, data, true);
    }
    getAllRoomList(){
        return this.chttp.get(`room/list`);
    }
    getBuildingFloors(id) {
        return this.chttp.get(`room/add/floor/${id}`);
    }
    updateRoomCatandWingData(id,data){
        return this.chttp.post(`room/edit/${id}`, data, true);
    }
}