name: stage-cd-pipeline

on:
  workflow_run:
    workflows: 
      - stage-ci-pipeline
    types:
      - completed

permissions:
  contents: write
  actions: write


jobs:
  deploy:
    runs-on: self-hosted-dhaharmshala
    defaults:
      run:
        working-directory: /home/<USER>/server-deployment
    steps:
      - name: Pull Updated Docker Image
        run: docker compose pull
      - name: Deploy Docker Container
        run: docker compose up --force-recreate -d
