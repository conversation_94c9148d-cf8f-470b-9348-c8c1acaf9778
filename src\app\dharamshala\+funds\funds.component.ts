import { DharamshalaService } from './../../shared/services/dharamshala.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import * as _ from "lodash";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

import { AuthGuard } from "../../shared/guards/auth-guard.service";
declare var Messenger: any;

@Component({
    selector: 'funds',
    templateUrl: './funds.component.html'
})

export class FundsComponent implements OnInit {
    config: any;// New Change ****
    private sub: any;
    public data: any[];
    public prevVal: any;
    searchQuery: string;
    public selectedFund: any;
    public originalData: any[];
    public canViewRecords: any;
    private changeDefaultSer: any;
    public hideAdd: boolean = true;
    public hideEdit: boolean = true;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private DS: DharamshalaService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        this.canViewRecords = true;
        this.sub = this.DS.getAllFunds()
            .subscribe((res) => {
                if (res.status == "success") {
                    this.data = res.data.funds;
                    // console.log("Funds Response : ",res);
                    if (res.data.fund_id > -1) {
                        // console.log("Fund_id found : ",res.data.fund_id);
                        this.data.forEach(element => {
                            element.is_default = false;
                        });
                        this.data[this.findIndex(res.data.fund_id, "id")].is_default = true;
                    }
                    // --- Creating a Clone of this.data to enable search functionatlity --- // 
                    this.originalData = this.data;
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
            // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() { }
    
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    showAdd() {
        this.hideAdd = false;
        this.hideEdit = true;
    }
    addTolistEvent(event) {
        // --- add only when basic list viewing permission is available --- //
        if (this.canViewRecords) {
            this.originalData.push(event);
            this.initializeData();
        }
    }
    handleAdd(event) {
        this.hideAdd = true;
    }
    showEdit(fund) {
        this.selectedFund = fund;
        this.hideAdd = true;
        this.hideEdit = false;
    }
    editTolistEvent(event) {
        // --- edit only when basic list viewing permission is available --- //
        if (this.canViewRecords) {
            // --- There is a need to hide edit here --- //
            this.hideEdit = true;
            // console.log('this.originalData :::::::::::::::: ',this.originalData);
            this.originalData[this.findIndex(event.fund.id, "id", this.originalData)] = event.fund;
            this.data[this.findIndex(event.fund.id, "id", this.data)] = event.fund;
            if (event.fund_id) {
                _.map(this.originalData, original => { original.is_default = false; });
                _.map(this.data, data => { data.is_default = false; });
                this.originalData[this.findIndex(event.fund_id, "id", this.originalData)].is_default = true;
                this.data[this.findIndex(event.fund_id, "id", this.data)].is_default = true;
            }
            // else {
            //     this.originalData[this.findIndex(event.fund.id, "id", this.originalData)].is_default = true;
            //     this.data[this.findIndex(event.fund.id, "id", this.data)].is_default = true;
            // }
        }
    }
    handleEdit(event) {
        this.hideEdit = true;
    }
    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] == searchTerm) { return (i); }
        }
        return -1;
    }
    changeDefault(fund) {
        this.originalData.forEach((element, index) => {
            if (element.is_default == true) {
                this.prevVal = index;
                element.is_default = false;
            }
        });
        // --- Uncheck only if index is available in modified Funds-list(this.data) ---//
        if (this.prevVal > -1 && this.findIndex(this.originalData[this.prevVal].id, "id", this.data) > -1) {
            this.data[this.findIndex(this.originalData[this.prevVal].id, "id", this.data)].is_default = false;
        }
        fund.is_default = true;
        this.originalData[this.findIndex(fund.id, "id", this.originalData)].is_default = true;
        if (fund.status) {
            this.changeDefaultSer = this.DS.updateDefaultFund(fund.id, fund)
                .subscribe((res) => {
                    if (res.status != "success") {
                        this.revertToPrevDefaultFund(fund);
                    }
                }, (err) => {
                    this.revertToPrevDefaultFund(fund);
                })
        } else {
            Messenger().post({
                hideAfter: 3,
                message: "In-active fund cannot be marked as default!",
                type: 'error',
                showCloseButton: true
            });
            setTimeout(() => {
                this.revertToPrevDefaultFund(fund);
            }, 100);
        }
    }
    revertToPrevDefaultFund(fund: any) {
        // console.log("Fund : ", fund, this.originalData);
        fund.is_default = false;
        // --- Uncheck only if index is available in modified Funds-list(this.data) ---//
        if (this.prevVal > -1 && this.findIndex(this.originalData[this.prevVal].id, "id", this.data) > -1) {
            this.data[this.findIndex(this.originalData[this.prevVal].id, "id", this.data)].is_default = true;
        }
        this.originalData[this.findIndex(fund.id, "id", this.originalData)].is_default = false;
        this.originalData[this.prevVal].is_default = true;
    }
    searchEvent() {
        this.initializeData();
        if (this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter(data => {
                return ((<string>data.name).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    initializeData() {
        this.data = this.originalData;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        if (this.sub) {
            this.sub.unsubscribe()
        }
        if (this.changeDefaultSer) {
            this.changeDefaultSer.unsubscribe();
        }

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}