<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-calendar-times-o"></i>&nbsp;&nbsp;{{pageType}} {{ 'CANCELLATION POLICY.ADD_PAGE.CANCELLATION_POLICY' | translate:param }}</span></h4>
  </header>
  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="policyForm" (ngSubmit)="savePolicy()">

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'CANCELLATION POLICY.ADD_PAGE.NAME' | translate:param }}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="policyForm.controls.name.errors?.backend">{{policyForm.controls.name.errors?.backend}}</span>
              <input type="text" class="form-control" formControlName="name" placeholder="">
              <span class="errMsg" *ngIf="!policyForm.controls.name.valid && !policyForm.controls.name.pristine">
                <span [hidden]="!policyForm.controls.name.errors.required">{{ 'CANCELLATION POLICY.ADD_PAGE.VALIDATION_MSG.NAME_REQUIRED' | translate:param }}</span>
              </span>
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'CANCELLATION POLICY.ADD_PAGE.DAY_BEFORE' | translate:param }}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="policyForm.controls.day_before.errors?.backend">{{policyForm.controls.day_before.errors?.backend}}</span>
              <input type="text" class="form-control" formControlName="day_before" placeholder="">
              <span class="errMsg" *ngIf="!policyForm.controls.day_before.valid && !policyForm.controls.day_before.pristine">
                <span [hidden]="!policyForm.controls.day_before.errors.required">{{ 'CANCELLATION POLICY.ADD_PAGE.VALIDATION_MSG.DAY_BEFORE_REQUIRED' | translate:param }}</span>
                <span [hidden]="!policyForm.controls.day_before.errors.digits">{{ 'CANCELLATION POLICY.ADD_PAGE.VALIDATION_MSG.ONLY_DIGIT_REQUIRED' | translate:param }}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right text-danger">{{ 'CANCELLATION POLICY.ADD_PAGE.PASSCODE' | translate:param }}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="policyForm.controls.passcode.errors?.backend">{{policyForm.controls.passcode.errors?.backend}}</span>
              <input type="text" class="form-control" formControlName="passcode" placeholder="">
              <span class="errMsg" *ngIf="!policyForm.controls.passcode.valid && !policyForm.controls.passcode.pristine">
                <span [hidden]="!policyForm.controls.passcode.errors.required">{{ 'CANCELLATION POLICY.ADD_PAGE.VALIDATION_MSG.PASSWORD_REQUIRED' | translate:param }}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'CANCELLATION POLICY.ADD_PAGE.CHARGE' | translate:param }}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="policyForm.controls.discount.errors?.backend">{{policyForm.controls.discount.errors?.backend}}</span>
              <div class="input-group" [ngClass]="{'has-error': (!policyForm.controls.discount.valid && !policyForm.controls.discount.pristine) || (policyForm.controls.discount.errors?.backend)}">
                <span class="input-group-addon"><i class="fa fa-percent"></i></span>
                <input type="text" class="form-control" formControlName="discount" placeholder="">
              </div>
              <span class="errMsg" *ngIf="!policyForm.controls.discount.valid && !policyForm.controls.discount.pristine">
                <span [hidden]="!policyForm.controls.discount.errors.required">{{ 'CANCELLATION POLICY.ADD_PAGE.VALIDATION_MSG.DISCOUNT_REQUIRED' | translate:param }}</span>
                <span [hidden]="!policyForm.controls.discount.errors.number">{{ 'CANCELLATION POLICY.ADD_PAGE.VALIDATION_MSG.ONLY_DIGIT' | translate:param }} </span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{ 'CANCELLATION POLICY.ADD_PAGE.STATUS' | translate:param }}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio-1" [value]="true">
                  <label for="radio-1">
                    {{ 'CANCELLATION POLICY.ADD_PAGE.ACTIVE' | translate:param }}
                  </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio-2" [value]="false">
                  <label for="radio-2">
                    {{ 'CANCELLATION POLICY.ADD_PAGE.INACTIVE' | translate:param }}
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" [disabled]="!policyForm.valid" class="btn btn-sm btn-inverse capitalized"><i
                    class="fa fa-check"></i>{{ 'CANCELLATION POLICY.ADD_PAGE.SAVE' | translate:param }}</button>
                <button type="button" (click)="closeThisComp()" class="btn btn-sm btn-secondary">{{ 'CANCELLATION POLICY.ADD_PAGE.CLOSE' | translate:param }}</button>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>