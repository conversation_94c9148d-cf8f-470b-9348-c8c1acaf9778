<div class="row">
  <div class="" [ngClass]="{'col-md-12': (hiddenAdd && hiddenEdit),'col-md-6': (!hiddenAdd || !hiddenEdit)}">
    <section class="widget">
      <header>
        <h4><span class="" style="color: red;"><i class="fa fa-calendar-times-o"></i>&nbsp;&nbsp;{{ 'CANCELLATION POLICY.CANCELLATION POLICY MANAGEMENT' | translate:param }}</span></h4>
      </header>
      <hr class="large-hr">
      <div class="float-sm-right text-right col-sm-12">
        <button *ngIf="auth.roleAccessPermission('policy','add')" class="display-inline-block btn btn-sm btn-inverse"
          (click)="showAdd()" [disabled]="!hiddenAdd || !hiddenEdit" tooltip="{{ 'CANCELLATION POLICY.ADD_NEW_CANCELLATION_POLICY' | translate:param }}" placement="top">
          <i class="fa fa-plus"></i>&nbsp;&nbsp;{{ 'CANCELLATION POLICY.ADD' | translate:param }}
        </button>
        <div class="form-group display-inline-block __search">
          <input type="text" class="form-control" placeholder="{{ 'CANCELLATION POLICY.SEARCH' | translate:param }}" (keyup)="canViewRecords ? search() : null"
            [(ngModel)]="searchQuery">
          <span class="form-group-addon"><i class="fa fa-search"></i></span>
          <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
        </div>
      </div>
      <div class="clearfix"></div>
      <div class="widget-body table-scroll">
        <div class="mt">

          <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
            <thead>
              <tr>
                <th>
                  <mfDefaultSorter by="id">#</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="name">{{ 'CANCELLATION POLICY.NAME' | translate:param }}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="day_before">{{ 'CANCELLATION POLICY.DAYS BEFORE' | translate:param }}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="charge">{{ 'CANCELLATION POLICY.CHARGE' | translate:param }}</mfDefaultSorter>
                </th>
                <th class="text-center">
                  <mfDefaultSorter>{{ 'CANCELLATION POLICY.REFERENCE_REQUIRED' | translate:param }}</mfDefaultSorter>
                </th>
                <th class="text-center">
                  <mfDefaultSorter>{{ 'CANCELLATION POLICY.COMMENT_REQUIRED' | translate:param }}</mfDefaultSorter>
                </th>
                <th class="no-sort text-center">
                  <mfDefaultSorter by="status">{{ 'CANCELLATION POLICY.STATUS' | translate:param }}</mfDefaultSorter>
                </th>
                <th *ngIf="auth.roleAccessPermission('policy','edit')" class="no-sort text-center">
                  <mfDefaultSorter by="status">{{ 'CANCELLATION POLICY.ACTION' | translate:param }}</mfDefaultSorter>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let ds of mf.data; let i =index" [ngClass]="{'__selected': (ds.id == selectedPolicy?.id)}">
                <td>{{findIndex(ds.id,"id")}}</td>
                <td>{{ds.name}}</td>
                <td><span class="fw-semi-bold">{{ds.day_before}}</span></td>
                <td><span class="">{{ds.discount}}</span></td>
                <td class="text-center">
                  <div tooltip="{{ 'CANCELLATION POLICY.CHECK_IF_REFERENCE_IS_REQUIRED_WHILE_CANCELLATION' | translate:param }}" placement="bottom" style="position: relative">
                    <input type="checkbox" id="{{ds.id}}_reference_required" class="is_default_checkbox" name="reference_required"
                      (change)="updateCancellationReference(ds)" [checked]="ds.is_reference_required">
                    <label for="{{ds.id}}_reference_required"></label>
                  </div>
                </td>
                <td class="text-center">
                  <div tooltip="{{ 'CANCELLATION POLICY.CHECK_IF_NOTE_IS_REQUIRED_WHILE_CANCELLATION' | translate:param }}" placement="bottom" style="position: relative">
                    <input type="checkbox" id="{{ds.id}}_note_required" class="is_default_checkbox" name="reference_required"
                      (change)="updateCancellationNote(ds)" [checked]="ds.is_note_required">
                    <label for="{{ds.id}}_note_required"></label>
                  </div>
                </td>
                <td class="text-center">
                  <span class="text-success" *ngIf="ds.status">{{ 'CANCELLATION POLICY.ACTIVE' | translate:param }}</span>
                  <span class="text-danger" *ngIf="!ds.status">{{ 'CANCELLATION POLICY.INACTIVE' | translate:param }}</span>
                </td>
                <td *ngIf="auth.roleAccessPermission('policy','edit')" class="width-100 text-center">
                  <button (click)="showEdit(ds)" class="btn btn-xs btn-default" tooltip="{{ 'CANCELLATION POLICY.EDIT_CANCELLATION_POLICY' | translate:param }}"
                    placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{ 'CANCELLATION POLICY.EDIT' | translate:param }}</button>
                </td>
              </tr>
              <tr *ngIf="canViewRecords && mf.data.length === 0">
                <td colspan="100">
                  {{ 'CANCELLATION POLICY.NO MATCHES' | translate:param }}
                </td>
              </tr>
              <tr *ngIf="!canViewRecords">
                <td class="text-danger" colspan="100">
                  {{ 'CANCELLATION POLICY.PERMISSION_DENIED' | translate:param }}
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="12">
                  <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </section>
  </div>
  <div class="col-md-6">
    <add-cancelation-policy *ngIf="!hiddenAdd" (sendaddedPolicy)="addtoList($event) " (closeComp)="closeAdd($event)"></add-cancelation-policy>

    <edit-cancelation-policy *ngIf="!hiddenEdit" [selectedPolicy]="selectedPolicy" (sendEdited)="addupdatedtoList($event)"
      (closeComp)="closeEdit($event)"></edit-cancelation-policy>
  </div>
</div>