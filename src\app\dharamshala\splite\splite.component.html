<div class="row">
    <div class="col-sm">
        <section class="widget">
            <header>
                <h4><span class="" style="color: red;"><i class="fa fa-inr"></i>&nbsp;&nbsp;Split Amount</span></h4>
            </header>
            <hr class="large-hr">
            <div class="clearfix"></div>
            <div class="widget-body">
                <div class="mt">
                    <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
                        <thead>
                            <tr>
                                <th>
                                    <mfDefaultSorter by="id">#</mfDefaultSorter>
                                </th>
                                <th>
                                    <mfDefaultSorter by="id">Maxamount</mfDefaultSorter>
                                </th>
                                <th>
                                    <mfDefaultSorter by="id">Minamount</mfDefaultSorter>
                                </th>
                                <th>
                                    <mfDefaultSorter by="id">Action</mfDefaultSorter>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of mf.data">
                                <td>{{item.id}}</td>

                                <td>{{item.taxable_amount}}</td>

                                <td>{{item.split_receipt_base_amount}}</td>
                                <td>
                                    <div style="position: relative">
                                        <button type="button" (click)="editSpliteData()" class="btn btn-xs btn-default" tooltip="{{ 'edit splite amount' }}" placement="top" ><i class="fa fa-pencil"></i>&nbsp;&nbsp;Edit</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>
    <!-- <splite-edit *ngIf="isavelebal" [mydata]="data"></splite-edit> -->
</div>