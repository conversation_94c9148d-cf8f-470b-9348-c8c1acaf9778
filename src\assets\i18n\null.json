{"DASHBOARD": {"HELLO": "<PERSON>"}, "AMENITIES": {"AMENITIES_MANAGEMENT": "EXTRA SERVICE REPORT", "NAME": "Name", "CHARGE": "Charge", "STATUS": "Status", "ACTION": "Action", "ADD": "Add", "SEARCH": "Search", "ACTIVE": "Active", "EDIT": "Edit", "INACTIVE": "Inactive", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "AMENITIES": "Amenities", "NAME": "Name*", "CHARGE": "Charge*", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CANCEL": "Cancel", "BREAD_CRUMB_AMENITIES": "Amenities", "VALID_MSG": {"NAME_REQ": "Amenity Name is required", "CHARGE_REQ": "Amenity charge is required", "ONLY_DIGIT_REQ": "Only Digits allowed here."}}, "NO MATCHES": "No matches", "ADD NEW AMENITY": "Add new Amenity", "EDIT_AMENITY_DETAILS": "Edit Amenity details"}, "CANCELLATION POLICY": {"CANCELLATION POLICY MANAGEMENT": "Cancellation  Settings", "DAYS BEFORE": "Days before", "NAME": "NAME", "ADD": "Add", "ADD_NEW_CANCELLATION_POLICY": "Add new cancellation policy", "REFERENCE_REQUIRED": "Reference required", "COMMENT_REQUIRED": "COMMENT REQUIRED", "CHECK_IF_REFERENCE_IS_REQUIRED_WHILE_CANCELLATION": "Check if Reference is required while cancellation", "CHECK_IF_NOTE_IS_REQUIRED_WHILE_CANCELLATION": "Check if Note is required while cancellation", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "CHARGE": "Charge", "STATUS": "Status", "ACTION": "Action", "SEARCH": "Search", "ACTIVE": "Active", "EDIT": "Edit", "EDIT_CANCELLATION_POLICY": "Edit cancellation policy", "INACTIVE": "Inactive", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "CANCELLATION_POLICY": "Cancellation Policy", "DAY_BEFORE": "Days before*", "PASSCODE": "Passcode*", "NAME": "Name*", "CHARGE": "Charge*", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CANCEL": "Cancel", "CLOSE": "Close", "VALIDATION_MSG": {"NAME_REQUIRED": "Name is required", "DAY_BEFORE_REQUIRED": "Day Before is required", "ONLY_DIGIT_REQUIRED": "Only digits allowed here.", "PASSWORD_REQUIRED": "Passcode is required", "DISCOUNT_REQUIRED": "Discount is required", "ONLY_DIGIT": "Only Digits allowed here."}}}, "MANAGE_EXTRA_CHARGES": {"MANAGE_EXT_CHARGE": "Manage Extra Charges", "NAME": "Name", "ADD": "Add", "CHARGE": "Charge", "STATUS": "Status", "ACTION": "Action", "SEARCH": "Search", "ACTIVE": "Active", "EDIT": "Edit", "INACTIVE": "Inactive", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "ADD_NEW_EXT_CHARGE": "Add new extra charges", "EDIT_EXTRA_CHARGES": "Edit extra charges", "ADD_PAGE": {"PAGETYPE": "Add", "EXTRA_CHARGES": "Extra Charges", "EDIT_PAGE_TYPE": "Edit", "NAME": "Name*", "CHARGE": "Charge*", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CLOSE": "Close", "VALID_MSG": {"NAME_REQ": "Name is required", "CHARGE_REQ": "Charge is required", "ONLY_DIGIT_REQ": "Only Digits allowed here."}}}, "EXPENSES": {"EXPENSES_MANAGEMENT": "Expense Report", "ADD": "Add", "SEARCH": "Search", "NOTE": "NOTE", "DATE": "DATE", "AMOUNT": "AMOUNT", "NO MATCHES": "No matches", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "EXPENSES": "Expenses", "AMOUNT": "Amount*", "NOTE": "Note*", "SAVE": "Save", "CLOSE": "Close", "VALID_MSG": {"AMT_REQ": "Amount is required", "NOT_REQ": "Note is required"}}}, "USER": {"USER_MANAGEMENT": "User Management", "NAME": "NAME", "EMAIL": "EMAIL", "ROLE": "ROLE", "STATUS": "Status", "ACTIVE": "Active", "EDIT_NEW_USER": "Edit User details", "INACTIVE": "Inactive", "ACTION": "Action", "EDIT": "Edit", "ADD": "Add", "ADD_NEW_USER": "Add new User", "NO MATCHES": "No matches", "SEARCH": "Search", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "USER_MANAGEMENT": "User Management", "USER": "User", "FIRST_NAME": "First Name*", "LAST_NAME": "Last Name*", "USER_ROLE": "User Role*", "EMAILID": "Email Id*", "MOBILE_NO": "Mobile No.", "DOB": "Date of Birth*", "COUNTRY": "Country*", "CITY": "City", "ZIP_POSTAL": "Zip/Postal Code", "ADDRESS": "Address", "GENDER": "Gender*", "MALE": "Male", "FEMALE": "Female", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CANCEL": "Cancel", "VALID_MSG": {"FNAME_REQ": "First name is required", "LNAME_REQ": "Last name is required", "EMAIL_REQ": "Email Id is required", "VALID_EMAIL": "Email Id is not valid", "VALID_MOBILE_NUM": "Mobile Number is Not Valid", "COUNTRY_REQ": "Country is required"}}}, "ROOM": {"ROOM_CAT": {"ROOM_CAT_MANAGE": "Room Category", "NAME": "NAME", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "ACTION": "Action", "EDIT": "Edit", "ADD": "Add", "NO MATCHES": "No matches", "SEARCH": "Search", "ADD_NEW_CAT": "Add new room category", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "MAX_PER": "MAX. PERSON", "EDIT_NEW_CAT": "Edit room category", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "ROOM_CATE": "Room Category", "ROOM_CAT_MANAGE": "Room Management", "NAME": "Name*", "CHARGE": "Charge*", "TOTAL_ROOM": "Total Rooms*", "DEF_ONLINE_QUO": "Default Online Quota*", "STD_OCCU": "Standard Occupancy*", "EXT_ADU_CHA": "Extra Adult Charges*", "EXT_CHIL_CHA": "Extra Child Charges*", "MAX_OCCU": "Max Occupancy*", "DESC": "Description", "AMENITIES": "Amenities", "IMAGES": "Images", "TARRIF_BY": "Discount in Tariff By", "CUSTOMER": "Customer", "IN_PERCE": "In Percentage (%)", "CHARGE_RS": "Charges (Rs)", "IN_AMT": " in Amount (", "EXT_BAF_CHAR": "Extra Bed Charges", "EXT_ADU_CHAR": "Extra Adult Charges", "EXT_CHIL_CHAR": "Extra Child Charges", "REL_QUOT_BEFORE": "Release Quota Before*", "STATUS": "Status", "FILE_NAME": "Name", "FILE_UPLOAD": "Upload", "FILE_REMOVE": "Remove", "QUEUE_PROGRESS": "Queue progress:", "UPLOAD_ALL": "Upload all", "CANCEL_ALL": "Cancel all", "REMOVE_ALL": "Remove all", "FILE_SIZE": "Size", "PROGRESS": "Progress", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CANCEL": "Cancel", "VALID_MSG": {"ROOM_CAT_REQ": "Room Category Name is required", "ROOM_CAT_CHARGE": "Room Category Charge is required", "ROOM_NUM_ALLOW": "Only numbers allowed here.", "ROOM_TOT_REQ": "Total Rooms are required", "ROOM_FIX_NUM_ALLO": "Only Fixed numbers allowed here.", "ROOM_DEF_QUO_REQ": "Please enter Default online quota", "ROOM_STD_ADUL_REQ": "Please enter Stadard Adult Occupancy", "ROOM_ADU_OCCU": "Please enter Max Adult Occupancy", "ROOM_MAX_CHIL_OCCU": "Please enter Max Child Occupancy", "ROOM_MAX_REQ": "Please enter Max Occupancy", "ROOM_DESC_REQ": "Please enter Description", "ROOM_REL_QUO_BEFORE": "Please enter Release Quota Before"}}}, "ROOM_MANAGE": {"ROOM_MANAGEMENT": "Rooms", "NAME": "NAME", "DOORID": "DOOR ID", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "ACTION": "Action", "EDIT": "Edit", "ADD": "Add", "NO MATCHES": "No matches", "CATEGORY": "CATEGORY", "SEARCH": "Search", "ADD_NEW_ROOM": "Add new room", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "EDIT_NEW_CAT": "Edit room details", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "ROOM": "Room", "ROOM_MANAGE": "Room Management", "NO_TITLE": "No/Title*", "DOOR_ID": "Door Id", "CATEGORY": "CATEGORY*", "WING_BUILD": "Wing/Building Name*", "STATUS": "Status", "FLOOR": "Floor", "DEF_BED": "Default Bed*", "MAX_BED": "Max Bed*", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CANCEL": "Cancel", "VALID_MSG": {"TIT_REQ": "Room No/Title is required", "DEF_BED_REQ": "Please Enter De<PERSON> beds", "NUM_ALLOW": "Only number allowed", "MAX_BED_REQ": "Please enter Max bed", "ONLY_NUM_ALLOW": "Only number allowed"}}}}, "BUILDING": {"BUILDING_MANAGEMENT": "Building floor settings", "NAME": "NAME", "NO_OF_FLOOR": "NUMBER OF FLOORS", "STATUS": "Status", "ACTIVE": "Active", "ADD_NEW_BUILD": "Add new building", "EDIT_BUILD_DETAILS": "Edit building details", "INACTIVE": "Inactive", "ACTION": "Action", "EDIT": "Edit", "ADD": "Add", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "SEARCH": "Search", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "BUILDING": "Building", "NAME": "Name*", "ACTIVE": "Active", "INACTIVE": "Inactive", "CANCEL": "Cancel", "SAVE": "Save", "STATUS": "Status", "ADD_FLOOR": "Add Floors*", "VALID_MSG": {"BUILD_REQ": "Buildng Name is required"}}}, "CUSTOMER_TYPE": {"CUST_TYPE_MANAGE": "Customer Type Settings", "CUST_TYPE": "CUSTOMER TYPE", "DIS": "DISCOUNT", "STATUS": "STATUS", "DEFAULT": "DEFAULT", "DEF_TOOLTIP": "Check to make this your default customer", "BAKHI_CHECK": "BAKHI CHECKOUT", "BAKHI_TOOLTIP": "Check if customer can checkout without full payment", "REF_REQ": "Reference required", "REF_REQ_TOOLTIP": "Check if reference is required for this customer", "COMME_REQ": "COMMENT REQUIRED", "COMME_REQ_TOOLTIP": "Check if text comment is required for this customer", "ACTIVE": "Active", "INACTIVE": "Inactive", "ACTION": "Action", "EDIT": "Edit", "ADD": "Add", "ADD_NEW_CUST_TYPE": "Add new Customer Type", "EDIT_CUST_TYPE": "Edit Customer Type", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "SEARCH": "Search", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "CUST_TYPE": "Customer Type", "TYPE_NAME": "Type Name*", "DISC_TYPE": "Discount Type*", "DISCOUNT": "Discount*", "AMOUNT": "Amount", "PERCE": "Percentage", "NONE": "None", "EXT_BAD_CHAR": "Extra Bed Charges*", "ACTIVE": "Active", "INACTIVE": "Inactive", "STATUS": "STATUS", "SAVE": "Save", "CANCEL": "Cancel", "VALID_MSG": {"CUST_NAME_REQ": "Customer Type Name is required", "ONLY_DIGIT_REQUIRED": "Only digits allowed.", "INVALID_VAL": "invalid value entered.", "EXT_BAD_CHAR_REQ": "Extra Bed charges is required", "CURR_REQ": "Add currency in digit."}}}, "FUNDS": {"FUNDS_MANAGE": "Donation", "ADD": "Add", "ADD_NEW_FOUND": "Add new Fund", "FUNT_TIT": "Fund Title", "DEFAULT": "<PERSON><PERSON><PERSON>", "SEARCH": "Search", "ACTIVE": "Active", "INACTIVE": "Inactive", "ACTION": "Action", "STATUS": "STATUS", "EDIT": "Edit", "NO MATCHES": "No matches", "EDIT_FUND_DETA": "Edit Fund details", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "FUND_TYPE": "Fund Type", "NAME": "Name*", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CANCEL": "Cancel", "VALID_MSG": {"NAME_REQ": "Name is required"}}}, "AGENT_LOCATION": {"AGENT_LOC_MANAGE": "Agent Location", "STATUS": "STATUS", "ACTIVE": "Active", "INACTIVE": "Inactive", "ACTION": "Action", "EDIT": "Edit", "ADD": "Add", "ADD_NEW_AGEN": "Add new Agent", "PLACE": "PLACE", "STATE": "STATE", "EDIT_AGEN_DETAIL": "Edit Agent details", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "SEARCH": "Search", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "AGENT_LOCATION": "Agent Location", "PLACE": "Place*", "STATE": "State*", "ACTIVE": "Active", "INACTIVE": "Inactive", "STATUS": "Status", "SAVE": "Save", "CANCEL": "Cancel", "VALID_MSG": {"PLACE_REQ": "Place is required", "STATE_REQ": "State is required"}}}, "AGENT": {"AGENT_MANAGE": "Agents", "ADD": "Add", "SEARCH": "Search", "ADD_NEW_AGEN": "Add new Agent", "NAME": "NAME", "LOCATION": "LOCATION", "STATUS": "STATUS", "ACTION": "Action", "EDIT": "Edit", "ACTIVE": "Active", "EDIT_AGET_DETA": "Edit Agent details", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "AGEN_TYPE": "Agent Type", "NAME": "Name*", "LOCATION": "Location*", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CANCEL": "Cancel", "VALID_MSG": {"NAME_REQ": "Name is required", "LOCATION_REQ": "location is required"}}}, "STAY_TYPE": {"STAY_TYPE_MANA": "Stay Type", "ADD": "Add", "ADD_STA_TYPE": "Add new Stay Type", "SEARCH": "Search", "NAME": "NAME", "DURATION": "Duration", "CHARGE": "Charge", "DEFAULT": "<PERSON><PERSON><PERSON>", "HRS": "hrs", "STATUS": "STATUS", "ACTION": "Action", "ACTIVE": "Active", "INACTIVE": "Inactive", "EDIT": "Edit", "ICON": "ICON", "EDIT_STAY_TYPE": "Edit Stay Type details", "NO MATCHES": "No matches", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "STAY_TYPE": "Stay Type", "NAME": "Name*", "DURATION": "Duration*", "CHARGE": "Charge*", "IN_HOURS": "in hours", "CHARGE_IN_PER": "in percentage", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "ICON": "Icon", "SAVE": "Save", "CANCEL": "Cancel", "IN_PERCE": "in Percentage", "TOGGEL_DROP": "Toggle Dropdown", "VALID_MSG": {"NAME_REQ": "Name is required", "DURA_REQ": "Duration is required", "ONLY_DIGIT_REQ": "Only digits are allowed.", "POSITIVE_NUM_REQ": "Should be a positive number between 0 to 24.", "CHARGE_REQ": "charge is required", "ONLY_DIGIT_REQUIRED": "Only digits are allowed.", "POSITIVE_REQ": "Should be a positive number between 0 to 100."}}}, "DHARAMSHALA": {"DHARAM_MANAGE": "Dharamshala Management", "ADD_NEW_DHARAM": "Add new Dharamshala", "ADD": "Add", "SEARCH": "Search", "DHARAM_NAME": "Dharamshala Name", "SIGN_AUTHO": "Signing Authority", "ADMIN_EMAIL": "<PERSON><PERSON>", "STATUS": "Status", "ACTION": "Action", "ACTIVE": "Active", "INACTIVE": "Inactive", "EDIT_NEW_TOOLTIP": "Edit Dharamshala details", "VIEW_NEW_TOOLTIP": "View Dharamshala details", "EDIT": "Edit", "VIEW": "View", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "DHARAMSHALA": "Dharamshala", "NAME": "Dharamshala Name*", "TAT_BAD": "Total Bed*", "EMAILID": "Email*", "CONC_NO": "Contact No*", "ZIP_POST": "Zip/Postal Code*", "CITY": "City", "COUNTRY": "Country*", "ADDRESS": "Address*", "LOCATION": "Location", "IMAGES": "Images", "FILE_NAME": "Name", "FILE_PROG": "Progress", "FILE_UPLOAD": "Upload", "FILE_REMOVE": "Remove from list", "UPLOAD_ALL": "Upload all", "CANCEL_ALL": "Cancel all", "REMOVE_ALL": "Remove all", "DOC": "Documents", "EXT_LINKS": "External Links", "CHECK_IN": "Check In", "CHECK_OUT": "Check Out", "STATUS": "Status", "ACTIVE": "Active", "INACTIVE": "Inactive", "EAR_CHE_CHARGE": "Early Check-out Cancellation Charges*", "DHARAM_AD_DETA": "Dharamshala Admin Details:", "FNAME": "First Name*", "LAS_NAME": "Last Name*", "EMAIL_ID": "Email Id*", "DOB": "Date of Birth*", "SAVE": "Save", "CANCEL": "Cancel", "TITLE": "Title", "LINK": "Link", "USER_ROLE": "User Role*", "QUEUE_PROGRESS": "Queue progress:", "VALID_MSG": {"NAME_REQ": "Dharamshala Name is required", "TOT_BAD_REQ": "Please  Enter total beds.", "ONLY_DIGIT_REQ": "Only Digits allowed.", "EMAIL_REQ": "Email Id is required", "VALID_EMAIL": "Email Id is not valid", "CON_REQ": "Contact number is required", "VALID_CONT": "Please enter valid contact number", "ZIP_POS_REQ": "Zip/postal code is required", "CITY_REQ": "City is required", "COUNTRY_REQ": "Country is required", "ADDRE_REQ": "Address is required", "EAR_CHE_CHARGE_REQ": "Early check-out cancellation charges are required.", "INVALID_VAL": "invalid value entered.", "FIR_NAME_REQ": "First Name is required", "LAST_NAME_REQ": "Last Name is required", "USER_ROLE_REQ": "User Role is required"}}, "VIEW_PAGE": {"DHARAM_DETAIL": "Dharamshala Details", "DHARAMSHALA": "Dharamshala", "BACK": "Back", "EMAIL": "Email Id.", "CONC_NO": "Contact No.", "ADDRESS": "Address", "ZIP_POSTAL": "Zip/Postal Code", "CITY": "City", "COUNTRY": "Country", "IMAGES": "Images", "NO_IMAGES": "No images", "DOC": "Documents", "NO_DOC": "No documents", "EXT_LINKS": "External Links", "NO_LINK": "No Links", "EDIT": "Edit", "NAME": "Name"}}, "REF_USER": {"REF_USER_MAN": "Reference Form Setting", "ADD": "Add Reference", "ADD_TOOLTIP": "Add new Reference User", "SEARCH": "Search", "NAME": "Name", "CONTAC_NO": "Contact No.", "CUST_TYPE": "Customer Types", "ACTION": "Action", "EDIT": "Edit", "EDIT_TOOLTIP": "Edit Reference User", "NO_MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "REF_USER": "Reference User", "PER_NAME": "Reference Person Name", "CUST_TYPE": "Customer Type*", "SAVE": "Save", "CLOSE": "Close", "CONT_NO": "Contact No.*", "VALID_MSG": {"PER_REQ": "Reference Person's Name is required", "CUST_TYPE_REQ": "Customer type is required.", "CONT_REQ": "Contact number is required", "ONLY_DIGIT_REQ": "Only Digits allowed here."}}}, "RESERVATIONS": {"REM_AMOUNT": "Remaining Payable Amount", "RESERVATIONS": "Reservations", "ROOM_MAIN_REASON": "Room Maintenance Reason", "TODAY": "Today", "ROOM": "Room", "LOADING": "Loading", "CATE": "category...", "PREV": "Previous", "PREV_DAY": "Previous day", "NEX_DAY": "Next day", "NEXT": "Next", "PREV_YEAR": "Previous year", "PREV_MONTH": "Previous month", "NEX_MONTH": "Next month", "NEXT_YEAR": "Next year", "OVER_BOOK": "Over Booking", "CONF_OVE_BOOK": "Confirm Over-booking", "DEL_OVE_BOOK": "Delete Over-booking", "CLICK_VIEW": "click to view", "UPLOAD": "Upload", "REMOVE": "Remove", "BEDS_RESEV": "Beds Reserved", "UNIQ_BOOK_ID": "Unique Booking ID", "CHECK_IN_DATE": "Check In Date", "CHECK_OUT_DATE": "Check Out Date", "REG_ON": "Registration On", "CURR_STATUS": "Current Status", "BOOK_TYPE": "Booking Type", "TOT_PER": "Total Person", "CUST_TYPE": "Customer Type", "ROOM_CAT": "Room Category", "STAY_TYPE": "Stay Type", "EXPEC_CHECK_IN": "Expected Check-in", "ACHECK_IN": "Check-in", "PAY_HIST": "Payment History", "AGEN_NAME": "Agent Name", "AGEN_RECEIP_NO": "Agent Receipt No.", "AGENT_LOCATION": "Agent Location", "TOT_AMT": "Total Amount", "CHARGE_APPLIED": "charges applied", "TOT_CARD_SWI": "Total Card Swipe Charges", "EXT-GUE_CHAR": "Extra guest charge", "FROM": "from", "EARLY_CHECK_IN": "Early Check-In Charges in", "CAN_CHARGE": "Cancellation Charges", "CUST_DIS": "Customer Discount", "REF_NAME": "Referal Name", "REF_DIS": "Referal Discount", "BY": "by", "WITH": "with", "PAY": "Payments", "ON": "on", "CARD_CHARGE": "Card Charges.", "RET_AMT": "Return Amount.", "FUNDS_ADDED": "Funds added in", "CUST_DISC": "Custom Discount", "RETD_AMT": "Returned Amount", "PEND_PAY": "Pending Payment", "REM_PAY": "Remaining Payments", "NET_AMT": "Net Amount", "CAR_SWIP_CHAR": "card swipe charges", "EXT_GUE_CHAR": "Extra Guest Charge", "PRE_CAN_CHAR": "Previous Cancellation Charges", "PAY_AMT_CAN": "Payable Amount after Cancellation", "EA_CHEK_CHAR": "Early check-in charges", "WITH_RET_AMT": "with return amount", "RETED_AMT": "Returned amount", "FUNDS": "Funds", "CUST_DISCS": "Custom Discounts", "PAY_MODE": "Payment Mode :", "RECE_NUM": "Reciept Number", "CARD_SWI": "Card Swipe Charges", "BANK_NAME": "Bank Name", "CHECK_NO": "Cheque No.", "AMOUNT": "Amount", "RET_AMT_DOT": "Return Amount", "TOT_PAY": "Total Payable", "CUST_DISC_PLACE": "custom discount", "PAVATI_NO": "Pavati No.*", "PAVATI_NO_PLACE": "pavati no.", "REF": "Reference", "NOTE": "Note", "PAN_NO": "PAN Card No", "PAN_NO_PLACE": "pan card no.", "CUST_DETAILS": "Customer Details", "BOOK_FROM": "Booking From", "IMME_RETU": "Immediate Return Amount", "REF_AMT": "Refund Amount:", "ADV_PREV": "This is the advance/previous payment taken. As a result of the new", "GUE_TYPE": "Guest Type discount", "REF_AMT_NEED": "this amount needs to be refunded", "BILL_NO": "Bill No", "MR": "Mr", "DATE": "Date", "ADDRESS": "Address", "TOT_PAX": "Total Pax", "ARRIVE": "Arrival", "DEPARTURE": "Departure", "SR_NO": "Sr No", "BOOK_DATE": "Booking Date", "BHET_FU_RE": "Bhet fund received", "ADD": "Add", "SAVE": "Save", "NAME": "Name", "CONT": "Contact", "EMAIL": "Email", "ROOM_NO": "Room No", "BOOK_TO": "Booking To", "ACTION": "Action", "COMM_ROOM_BOOK": "Common Room Bookings", "CLICK_HERE": "Click here to add a new", "TOT_BED": "Total Beds", "RES_BED": "Reserved Beds", "TOT_BEDS": "Total Bed", "STATUS": "Status", "FULL_NAME": "Full Name", "CONC_NO": "Contact No", "SAVE_DOC": "Save Documents", "CHECK_IN": "Check In", "PROF_TYPE": "Proof Type", "FILE_UPLOAD": "Upload", "FILE_REMOVE": "Remove", "DETAIL_FOR": "Details for", "PERS_INFO": "Personal Info", "TYPE": "Type*", "ADULT": "Adult", "CHILD": "Child", "AGE": "age*", "GENDER": "Gender*", "MALE": "Male", "FEMALE": "Female", "RES_INFO": "Residental Info", "CITY": "City", "COUNTRY": "Country", "DOC": "Documents", "NUM": "Number", "GUEST_NAME": "Guest Name", "GUEST_CONT": "Guest Contact", "GUE_EMAIL": "Guest Email", "SEARCH_AVAIL": "Search Availibility", "SET_AS_MAIN": "Set as Main", "ADD_NEW": "Add New", "SPLIT_BOOK": "Make Split Booking", "OVER_BOOKING": "Add New Over-Booking Note", "CLOSE": "Close", "RESERVE": "Reserve", "UNDER_MAIN": "Mark As Under Maintenance", "CLEAR": "Clear", "OVER_NEW_BOOKING": "Add New Over-Booking", "ADD_EXT_BOOK": "Add Extra Booking", "VIEW": "View", "CHECKIN": "CheckIn", "CHECKOUT": "CheckOut", "ROOM_CHECK": "Room CheckOut", "CHA_GUE_TYPE": "Change Guest Type", "MARK_ROOM_ACTI": "Mark <PERSON> Active", "PRINT_BILL": "Print Bill", "UPDA_PAY": "Update Payment", "ADD_VIEW_NOTE": "Add/View Note", "EDIT_GUES": "Edit Guest", "ADD_GUES": "Add Guest", "SPLIT_BOO": "Split Booking", "NO_SHOW": "No Show", "CANCEL": "Cancel", "ADD_NEW_BOOKS": "Add New Booking", "VIEW_BOOKI": "View Bookings", "ADD_EDIT_GUE": "Add/Edit Guest", "RES_ON": "Reservation On", "REF_": "Ref", "CHECK_OUT": "Check Out", "AGENT": "Agent", "GUEST_INFO": "Guest Information", "IS_BILL_GUE": "Is Billing Guest?", "CON_NUM": "Contact number", "PAYM": "Payment", "SOURCE": "Source", "ADV_PAY": "Advance Payment", "RESER_CHECK_IN": "Reserve & Check In", "RENT": "Rent", "ROOM_RENT": "Room Rent", "SUB_TOT": "Subtotal", "BOOKING": "Booking", "GUE_IN_ROOM": "Guest in room", "GUE_DETA": "Guest Details", "ADD_AN_ADU": "Add an adult/child!", "TOTAL": "Total", "BOOK_CANCEL": "Booking Cancellation", "SEL_POLICY": "Select Cancellation Policy", "REF_TYPE": "Refernce Type", "PASSCODE": "Passcode", "TOT_BOOK_CHARGE": "Total Booking Charges", "APP_CAN_CHAR": "Applied cancellation charges", "TOT_REF_AMT": "Total Refundable Amount", "TOT_PAY_AMT": "Total Payable Amount", "SUB": "Subject", "RECE_SIGN": "Receiver's Signature", "PANNO": "PAN NO", "GEN_PDF": "Generate PDF", "SEL_GUE_TYP": "Selected Guest Type", "CONF_DEL_GUE": "Are you sure you want to delete this guest", "ROOM_DEL_GUES": " Room details will be lost as it is the last guest in room", "ARE_U_SURE_GUES": "Are you sure you want to checkout guest from this room", "CONTINUE": "Continue", "ALERT": "<PERSON><PERSON>", "ADV_PREV_PAY": "This is the advance/previous payment taken. As a result of the", "CHANGE_GUE_TYPE": "change in Guest Type discount", "OVER_BOKK_CANCE": "Over-Booking cancellation", "TXT_MSG": "text message", "ENT_TXT_MSG": "enter a text message", "AGE_REC_NO": "agent receipt no", "VALID_MSG": {"CANCEL": "Cancel", "PAVATI_REQ": "<PERSON><PERSON><PERSON> number is required", "REF_REQ": "Reference user is required", "NOT_REQ": "Note is required", "PAN_REQ": "PAN Card No. is required", "NAME_REQ": "Name is required", "NAME_LEAVE": "Cannot leave name empty.", "INV_NAME": "Invalid name", "CON_REQ": "Contact is required", "EMAIL_REQ": "Email Id is required", "VALID_EMAIL": "Email Id is not valid", "GUEST_TYPE_REQ": "Guest type is required", "DOB_REQ": "DOB is required", "ONLY_DIGIT_REQ": "Only digits are allowed", "CONF": "Confirm", "OK": "OK", "AGE_REQ": "Age is required", "INV_AGE": "Invalid age", "GEN_REQ": "Gender is required", "ADDRE_REQ": "Address is required", "CITY_REQ": "City is required", "COUNTRY_REQ": "Country is required", "ZIP_REQ": "Zip code is required", "SET_IN_ALL": "Set in All", "ID_NAME": "name is required", "NUM_REQ": "number is required", "PROOF_REQ": "ID proof is required", "CHECK_IN_REQ": "Expected Check-in is required", "CHECK_OUT_REQ": "Expected Check-out is required", "AGEN_REC_REQ": "Agent receipt is required", "INVALID_AMT": "invalid amount entered.", "INVALID_ADVANCE_AMT": "We only accept 1 day payment as advance", "ADU_CHIL_REQ": "Adult or child is required", "IN_VAL_DOB": "Invalid date of birth", "REF_IS_REQ": "Reference is required", "PASSWORD_REQUIRED": "Passcode is required"}}, "ROOM_BOOKING_SETTING": {"ROOM_BOOK_SETI": "Online Booking Settings", "ROOM": "Room", "TYPE": "Type", "OQ": "OQ", "PQ": "OQ", "PERMISSION_DENIED": "You currently don't have the permission to view the content"}, "REPORT": {"DISCOUNT_APPROVAL": "Discount Approval", "SEARCH_REPORT": "Type to search...", "REP_MANAGE": "Report", "REP_RESERVATION": "Reservation", "REP_BOOKING": "Booking", "REP_CHECKOUT": "Checkout", "REP_CHECKIN": "Checkin", "SEARCH": "Search", "ROOM_TYP": "Room Type", "GUE_TYPE": "Guest Type", "REF_TYPE": "Reference Person", "AGENT_LOCATION": "Agent Location", "BOOK_STAT": "Booking Status", "F_DATE": "From Date", "T_DATE": "To Date", "F_CHECKING_DATE": "From Checking Date", "T_CHECKING_DATE": "To Checking Date", "SUMM": "Summary", "TOT_AMT": "Total Amount", "AMOUNT": "Room Charge", "TOT_DIS": "Total Discount", "TOT_AMT_REC": "Total Amount Received", "TOT_PEN_AMT": "Total Pending Amount", "TOT_REF_AMT": "Total Refund Amount", "TOT_AVAI_ROOM": "Total Available Rooms", "TOT_REC": "Total Records", "RESET": "Reset", "SEARCH_PARAM": "Search Parameters", "BALAN_AMT": "Pending Amount", "REF": "Reference", "BOOK_DATE": "Booking Date", "CUSTOMER": "Customer Type", "ROOM_NO": "Room No", "CANCE_BY": "Cancelled By", "RES_ID": "Res id", "AGEN_RECEIP_NO": "Booking Centre Receipt No", "GUEST_NAME": "Guest Name", "CHE_IN": "Check-in Date", "REF_NAME": "Ref. Name", "STAY_DURA": "Day to Day", "EXPEC_CHECK_IN": "Expected Check-in", "EXPEC_CHECK_OUT": "Expected Check-out", "PAX": "Pax", "BILL_N": "Bill no", "DISCOUNT": "Discount", "DIS_AMOUNT": "Disc. Amt", "ROOM_CHARGE_DISCOUNT": "Room Charge After Disc.", "TOTAL_DISCOUNT": "Total Disc.", "AMOUNT_RECEIVED": "Amount Received", "AGENT_AMT_RECEIVED": "Booking Centre Received", "UPDAT_PAY": "Updated Payments", "REF_AMT": "Refund Amount", "REP_PENDING": "Pending Payments", "REP_DOWNLOAD": "Download Report", "PAVATI_NO": "Pavati No", "ADD_PROOF": "Address Proof", "ACTION": "Details", "VIEW_DOC": "View Document", "NO_DOC": "No Document", "IN_VOICE": "In-voice", "DETAILS": "Details", "PAY": "Pay", "PI": "<PERSON><PERSON><PERSON>", "STATUS": "Report Status", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "REF-NAME": "Reference Name", "NOTE": "Note", "COUNT": "Count", "STAY_PERI": "Stay Period", "CLOSE": "Close", "PRINT": "Print", "BILL_NO": "Receipt No", "DATE": "Date", "NAME": "Name", "SR_NO": "Sr.No.", "SUB": "Subject", "AMT": "Amount", "TOTAL": "Total", "RECE_SIGN": "Receiver's Signature", "OK": "OK", "GEN_PDF": "Generate PDF", "VIEW_DOC_PROOF": "View document proof", "NO_DOC_PROOF": "No document proof available", "VIEW_IN_VOICE": "View In-voice", "VIEW_DE": "View Details", "UP_PAY": "Update Pending Payments", "POLI_INQ": "Police Inquiry", "GUE_LIST": "Guest List", "GUE_TYP_LIST": "Guest Type List", "PAN_NO": "PAN NO", "booking": "booking", "checkout": "checkout", "checkin": "checkin", "cancelled": "Room Cancellation", "pending-payments": "pending-payments", "revenue": "revenue", "agents": "agents", "guest": "guest", "pending-payment-collections": "pending-payment-collections", "category-wise-availability": "category-wise-availability", "room-maintenance": "room-maintenance", "reservation": "Reservation"}, "CATEG_REPORT": {"REP_MAN": "Report", "DOWN": "download", "SEARCH": "Search", "ROOM_TYP": "Room Type", "F_DATE": "From Date", "T_DATE": "To Date", "SUMM": "Summary", "TOTAL_ROOM": "Total Rooms", "OCCU": "Occupied", "AVAI": "Available", "TOT": "Total", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records."}, "PEN_PAYM_REPORT": {"PEN_PAY_COLL": "Pending Payment Collections", "REP_MAN": "Report", "DOWN": "download", "SEARCH": "Search", "F_DATE": "From Date", "T_DATE": "To Date", "SUMM": "Summary", "TOT_AMT": "Total Amount", "NAME": "Name", "UNIQ_BOOK_ID": "Booking ID", "CHECK_DATE": "Checkout Date", "PAY_DATE": "Payment Date", "PAVATI_NO": "Pavati No", "AMOUNT": "Amount", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records."}, "REM_PAY_PEP": {"REM_AMOUNT": "Remaining Payable Amount", "PAY_MODE": "Payment Mode :", "BOOK_FROM": "Booking From", "REM_PAY": "Remaining Payments", "NET_AMT": "Net Amount", "WITH": "with", "CAR_SWIP_CHAR": "card swipe charges", "EXT_GUE_CHAR": "Extra Guest Charge", "PRE_CAN_CHAR": "Previous Cancellation Charges", "CAN_CHARGE": "Cancellation Charges", "PAY_AMT_CAN": "Payable Amount after Cancellation", "EA_CHEK_CHAR": "Early check-in charges", "CUST_DISCS": "Custom Discounts", "DIS_ON": "discount on", "RETED_AMT": "Returned amount", "FUNDS": "Funds", "RET_AMT_DOT": "Return Amount", "CARD_SWI": "Card Swipe Charges", "TOT_PAY": "Total Payable", "CUST_DISC": "Custom Discount", "PAVATI_NO": "Pavati No", "PAVATI_REQ": "<PERSON><PERSON><PERSON> number is required", "REF": "Reference", "REF_REQ": "Reference user is required", "NOTE": "Note", "NOT_REQ": "Note is required", "UPDA_PAY": "Update Payment", "CANCEL": "Cancel", "CUST_DETAILS": "Customer Details", "TO": "to", "RECE_NUM": "Reciept Number", "BANK_NAME": "Bank Name", "CHECK_NO": "Cheque No.", "AMOUNT": "Amount", "CUST_DISC_PLACE": "custom discount", "PAVATI_NO_PLACE": "pavati no.", "TXT_MSG": "text message"}, "ROOM_MAINT_REPORT": {"ROOM_MAIN": "Extra Bed", "REP_MANAGE": "Report", "PRINT": "Print", "SEARCH": "Search", "ROOM_TYP": "Room Type", "GUE_TYPE": "Guest Type", "GET_TYPE": "Get Type", "SUMM": "Summary", "TOTAL_ADULT": "Total Adult", "TOTAL_CHILD": "Total Child", "TOTAL_ROOM": "Total Rooms", "ROOM_NO": "Room No", "GUE_NAME": "Guest Name", "STAY_DURA": "Stay Duration", "PAX": "Pax", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records."}, "VIEW_BOOKI_DETAI": {"VIEW_BOOK": "View Booking Details", "BACK": "Back", "PRINT": "Print", "BOOK_DE": "Booking Details", "RES_ID": "Reservation Id", "STAY_TYPE": "Stay type", "BOOK_TYPE": "Booking Type", "BOOK_STAT": "Booking Status", "BOOK_DATE": "Booking Date", "RATE": "Rate", "EX_AD_GU_CHA": "Extra Added Guest Charges", "CAR_SWIP_CHAR": "Card swipe charges", "EA_CHECK_CHARG": "Early checkin charges", "FUND_AMT": "Fund amount", "DISCOUNT": "Discount", "PAID": "Paid", "BALAN_AMT": "Pending Amount", "REF_AMT": "Refund Amount", "EXP_CHECK_IN": "Expected Check-In", "EXP_CHECK_OUT": "Expected Check-Out", "ACTU_CHECK_IN": "Actual Check In", "ACTU_CHECK_OUT": "Actual Check out", "BILL_GUE_DETAI": "Billing Guest Details", "GUEST_NAME": "Guest Name", "GUE_TYPE": "Guest Type", "CONT": "Contact No", "EMAIL": "Email", "ADDRESS": "Address", "CITY": "City", "ZIP": "Zip", "ROOM_NO": "Room No", "ROOM_TYP": "Room Type", "TOT_ADU": "Total Adult", "TOT_CHIL": "Total Child", "DETAILS": "Details", "CHECK_IN": "Check In", "CHECK_OUT": "Check Out", "NOTES": "Notes", "PAY_DETA": "Payment Details", "PAY_MODE": "Payment Mode", "DATE": "Date", "BANK_NAME": "Bank Name", "BANK_CHECK_NO": "Bank Check No", "AMOUNT": "Amount", "TOT_PEN_AMT": "Total Pending Amount", "NO MATCHES": "No matches", "PRI_GUE_PHO_ID": "Primary Guest Photo ID", "GUE_DETA": "Guest Details", "MATU": "Maturity", "STAY_DURA": "Stay Duration", "RES_ID_D": "Res ID", "REF_NAME": "Ref. Name", "ADD_PROOF": "Address Proof", "CLOSE": "Close", "VIEW_DOC_PROOF": "View document proof", "VIEW_DOC": "View Document", "NO_DOC": "No Document", "NO_DOC_PROOF": "No document proof available"}, "GUEST_LIST": {"CUST_LIST_MANG": "Customer List Management", "SEARCH": "Search", "FULL_NAME": "Full Name", "CONT": "Contact", "EMAILID": "Email", "ACTION": "Action", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view the content", "CUST_DETAILS": "Customer Details", "NAME": "Name", "GENDER": "Gender", "CONC_NO": "Contact No", "ADDRESS": "Address", "ZIP_POSTAL": "Zip/Postal Code", "CITY": "City", "VIEW_CUST_DETAI": "View customer details", "VIEW": "View"}, "ROLE_PERMI": {"ROLE_PERMI_MANAGE": "Role Permission Management", "ROLE_MANAG": "Role Management", "ROLE_PER": "Role Permission", "USER_ROLE": "User Role", "ACCE_CONT": "Access Controllers", "ACC_ACT": "Access Actions", "ROLE_FOUND": "No Roles Found", "PERMISSION_DENIED": "You currently don't have the permission to view the role permissions."}, "USER_ROLE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "ROLE_MANAGE": "Roles Management", "ROLES": "Roles", "ADD_NEW_USER_ROLE": "Add new user role", "ADD_ROLE": "Add Role", "SEARCH": "Search", "ROLE_NAME": "Role Name", "MENU_GRP": "Menu Group", "STATUS": "Status", "ACTION": "Action", "ACTIVE": "Active", "INACTIVE": "Inactive", "EDIT": "Edit", "EDIT_USER_ROLE": "Edit user role", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view the content", "ROLE_MANAG": "Role Management", "ROLE_NAME_REQ": "Role Name is required", "DESC": "Description", "SAVE": "Save", "CANCEL": "Cancel"}, "CHANGE_PASSW": {"CHANGE_PA": "Change Password", "OLD_PASS": "Old Password", "CURR_PASS_REQ": "Your Current Password is required", "NEW_PASS": "New Password", "NEW_PASS_REQ": "New Password is required", "NEW_PASS_SHORT": "New Password too short", "CONF_NEW_PASS": "Confirm New Password", "CONF_PASS": "Confirm your Password", "PASS_NO_MATCH": "Password does not match", "SAVE": "Save", "CANCEL": "Cancel"}, "DEF_SETTING": {"DEF_SET": "<PERSON><PERSON><PERSON>", "EAR_CHECK_LIM": "Early check-in time limit", "SAVE": "Save", "CANCEL": "Cancel"}, "PROFILE": {"MY_PROF": "My Profile", "FIRST_NAME": "First Name", "FNAME_REQ": "First name is required", "LAST_NAME": "Last Name", "LNAME_REQ": "Last name is required", "EMAILID": "Email <PERSON>d", "CONC_NO": "Contact No", "MOB_NUM_REQ": "Mobile number is required", "DOB": "Date of Birth", "DOB_REQ": "Date of Birth is required", "ADDRESS": "Address", "ADDRE_REQ": "Address is required", "CITY": "City", "CITY_REQ": "City is required", "COUNTRY": "Country", "COUNTRY_REQ": "Country is required", "ZIP_POSTAL": "Zip/Postal Code", "ZIP_POS_REQ": "Zip/postal code is required", "GENDER": "Gender", "SAVE": "Save", "CANCEL": "Cancel"}, "SHIFT_IN": {"SHIT_IN": "Dharamshala - Shift in", "SHIFT_FRO_USER": "Shift in from user", "USER_REQ": "User is required", "PETI_CASH": "Petty Cash", "CURR_SHIFT": "You are currently in a shift", "SHITF_IN": "Dharamshala - Shift in", "ALERT": "<PERSON><PERSON>", "CONF_ACC_PETTY": "You are about to create a new Petty Account! Are you sure you want to proceed further", "PET_RUPE": "petty cash in rupees", "YES": "Yes", "NO": "No"}, "SHIFT_OUT": {"SHIFTT_OUT": "Shift Out", "PREV_SHIF_AMT": "Previous Shift Amount", "DAY_INC": "Day income", "DAY_EXP": "Day expenses", "BAL_PAT_CASH": "Balance Petty cash", "SHIFT-OUT": "Shift-out"}, "LOGIN": {"DHARAMSHALA": "Dharamshala", "LOGIN_ACC": "Log in your Account", "EMAIL_REQ": "Email is Required", "PASS_REQ": "Password is Required", "REM_ME": "Remember me", "LOGINN": "<PERSON><PERSON>", "FORG_PASS": "Forget Password"}, "MENU_MANAGE": {"MENU_GROU_MANG": "Menu Group Management", "ADD": "Add", "ADD_NEW_GROUP": "Add new menu group", "MENU_GRP": "Menu Group", "ACTION": "Action", "ACTIVE": "Active", "INACTIVE": "Inactive", "EDIT": "Edit", "EDIT_NEW_GRP": "Edit menu group", "MANGE_MEN_GRP": "Manage menu group", "MANG_MENU": "<PERSON><PERSON>", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view the content", "PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "MENU_MANAGE": "Menu Management", "NAME": "Name*", "GROP_NAME": "Group name is required", "STATUS": "Status", "SAVE": "Save", "CANCEL": "Cancel", "AUTHO_MENU_LIS": "Authorised Menu List", "ADD_NEW_MENU": "Add new menu item", "NO_MENU": "No Menu", "MENU_REQ": "<PERSON>u name is required", "PARENT": "Parent", "PARE_REQ": "Parent is required", "INVA_URL": "Invalid URL", "TOGGEL_DROP": "Toggle Dropdown", "SAV_MENU": "<PERSON> <PERSON><PERSON>", "ADD_NEW_MENU_ITE": "Add menu item", "EDIT_MENU_ITEM": "Edit menu item"}, "NAVBAR": {"TUR_ON_OFF": "Turn on/off", "SIDEBAR": "sidebar", "COLLAPSING": "collapsing", "MY_PROF": "My Profile", "DEF_SET": "<PERSON><PERSON><PERSON>", "CHANGE_PA": "Change Password", "SHIFTT_OUT": "Shift Out", "LOG_OUT": "Log Out", "HELLO": "Hello", "PROF": "My Profile"}, "NOTIFICATION": {"YUO_HAVE": "You have 13 notifications", "NOTIFI": "Notifications", "MESSAGES": "Messages", "PROG": "Progress", "NEW_JUST_SIGN": "1 new user just signed up! Check out", "MONICA": "<PERSON>", "S_ACCO": "s account", "TWO_MIN_AGO": "2 mins ago", "PRE_ALPHA": "2.1.0-pre-alpha just released", "FIVE_AGO": "5h ago", "SERVER_LOAD_LIMIT": "Server load limited", "SEVEN_AGO": "7h ago", "USER": "User", "JEFF": "<PERSON>", "REGD": "registered", "ALLOW": "Allow", "DENY": "<PERSON><PERSON>", "INST": "Instructions for changing your <PERSON>vat<PERSON> Account password. Please", "CHECK_ACC": "check your account", "SEC_PAGE": "security page", "NEW": "New", "FACEBOOK_LIKE_REC": "facebook likes received", "DARK_MATTE_DETEC": "Dark matter detected", "SYNC_AT": "Synced at"}, "SIDEBAR": {"DHARAMSHALA": "Dharamshala"}, "SET_PASSWORD": {"PASS_REQ": "New Password is required", "CONF_PASS_REQ": "Confirm your Password", "PASS_NO_MATCH": "Password does not match", "PASSS_REQ": "Password is required", "NEW_PASS": "New Password", "RE_TYP": "Retype Password", "SET_PASS": "Set Password"}, "FORGOT_PASS": {"EMAIL_ADD": "Email Address", "EMAIL_REQ": "Email is Required", "EMAIL_VALID": "Email is not valid", "SEND_LINK": "Send Link"}, "DIS_REF_USER": {"REF_USER_MAN": "Discount Approval User", "ADD": "Add User", "ADD_TOOLTIP": "Add new Discount Approval User", "SEARCH": "Search", "USER": "User", "REFERENCE": "Reference Persons", "GUEST_PER": "Guest Change Permission", "REFERENCE_PER": "Reference Change Permission", "ACTION": "Action", "EDIT": "Edit", "EDIT_TOOLTIP": "Edit Reference User", "NO_MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "REF_USER": "Discount Reference Name", "PER_NAME": "Select Reference Name", "CUST_TYPE": "Select Discount Approval User", "SAVE": "Save", "CLOSE": "Close", "CONT_NO": "Contact No.*", "GUEST_TYPE_CHANGE": "Guest type Permission", "REF_CHANGE": "Reference Change Permission", "VALID_MSG": {"PER_REQ": "Reference Person's Name is required", "CUST_TYPE_REQ": "Customer type is required.", "CONT_REQ": "Contact number is required", "ONLY_DIGIT_REQ": "Only Digits allowed here."}}}, "COLL_BREAKUP_REPORT": {"DATE": "Date", "COLL_BREAK_UP": "Collections Break Up", "PRINT": "Print", "REP_MAN": "Report", "DOWN": "download", "SEARCH_TXT": "Type to Search", "SEARCH": "Search", "RESET": "Reset", "F_DATE": "From Date", "T_DATE": "To Date", "USER_NAME": "User Name", "SUMM": "Payment Summary", "SUMM_MODE": "Payment Mode Summary", "AGENT_PAY": "Agent Payment", "CASH_PAY": "Cash Payment", "CARD_PAY": "Card Payment", "CHQ_PAY": "Cheque Payment", "BANK_PAY": "Bank Payment", "TOT_AMT": "Total Amount", "ADV_PAY": "Advance Payment", "PEN_PAY": "Pending Payment", "REF_AMT": "Refund Amount", "BHT": "<PERSON><PERSON><PERSON>", "CHECKOUt_PAY": "Checkin/Checkout Payment", "NAME": "Title", "ROOM_TYPE": "Room Type", "ROOM_No": "Room No", "GUEST_NAME": "Guest Name", "GUEST_TYPE": "Guest Type", "PAY_MODE": "Payment Mode", "AMOUNT": "Amount", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records."}, "ROOM_MAINTENANCE_REASON": {"ROOM_MAIN": "Room Maintenance Reason", "REP_MANAGE": "Report", "PRINT": "Print", "SEARCH": "Search", "EDIT": "Edit", "ADD": "Add", "ADD_NEW_ROOM": "Add new room", "TITLE": "Title", "DES": "Description", "STATUS": "Status", "SUMM": "Summary", "TOTAL_ROOM": "Total Rooms", "ROOM_NO": "Room No", "GUE_NAME": "Guest Name", "STAY_DURA": "Stay Duration", "PAX": "Pax", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records.", "ADD_PAGE": {"PAGETYPE": "Add", "EDIT_PAGE_TYPE": "Edit", "ROOM": "Room Maintenance Reason", "NO_TITLE": "Title*", "STATUS": "Status", "DESC": "Description", "ACTIVE": "Active", "INACTIVE": "Inactive", "SAVE": "Save", "CANCEL": "Cancel", "VALID_MSG": {"TIT_REQ": "Room Title is required", "DEF_BED_REQ": "Please Enter De<PERSON> beds", "NUM_ALLOW": "Only number allowed", "MAX_BED_REQ": "Please enter Max bed", "ONLY_NUM_ALLOW": "Only number allowed"}}}, "ROOM_MAINT_REASON_REPORT": {"ROOM_MAIN": "Room Maintenance", "REP_MANAGE": "Report", "PRINT": "Print", "SEARCH": "Search", "ROOM_TYP": "Room Type", "SUMM": "Summary", "TOTAL_ROOM": "Total Rooms", "ROOM_NO": "Room No", "TITLE": "Title", "REASON": "Reason", "START_DATE": "Start Date", "END_DATE": "End Date", "RES_ID": "Res id", "NO MATCHES": "No matches", "PERMISSION_DENIED": "You currently don't have the permission to view these records."}}