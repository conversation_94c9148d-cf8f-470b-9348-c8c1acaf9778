import { Injectable } from "@angular/core";
import { trim } from "jquery";
import { catchError, map } from 'rxjs/operators';
import { CommonHttpService } from "./common-http/common-http.service";

@Injectable()
export class ReservationServices {

    constructor(private http: CommonHttpService) {
    }

    resetFlagForCheckIn() {
        return this.http.get(`guest/resetFlagForCheckIn`, false);
    }



    getCheckInData(payload) {
        return this.http.post('guest/CheckInPreProcess', payload).pipe(
            map((response) => {
                return response;
            }),
            catchError(err => this.handleError('Error ::: ', err)));
    }

    verifyProcess(payload) {
        return this.http.post('guest/verifyProcess', payload).pipe(
            map((response) => {
                return response;
            }),
            catchError(err => this.handleError('Error ::: ', err)));
    }

    SendonlineBookingUrl(payload: any) {
        return this.http.post('bookingApprover/sendsms', payload)
    }

    getSplitBookingPreProcess(payload) {
        return this.http.post('guest/SplitBookingPreProcess', payload).pipe(
            map((response) => {
                return response;
            }),
            catchError(err => this.handleError('Error ::: ', err)));
    }

    getTransferRoomBookingPreProcess(payload) {
        return this.http.post('guest/TransferRoomBookingPreProcess', payload).pipe(
            map((response) => {
                return response;
            }),
            catchError(err => this.handleError('Error ::: ', err)));
    }

    GetDoorId(data: any) {
        console.log("inside Doorid");
        return this.http.post('room/view', data);
    }

    getCheckoutPreProcess(payload) {
        return this.http.post('guest/CheckoutPreProcess', payload).pipe(
            map((response) => {
                return response;
            }),
            catchError(err => this.handleError('Error ::: ', err)));
    }

    getCleanRoomPreProcess(payload) {
        return this.http.post('guest/CleanRoomPreProcess', payload).pipe(
            map((response) => {
                return response;
            }),
            catchError(err => this.handleError('Error ::: ', err)));
    }

    public handleError(errFrom: string, error: any) {
        if (error.status === 500) {
            console.log('ERROR : 500 : Interal Server Error : ' + error)
        } else if (error.status === 400) {
            console.log('ERROR : 400 : Bad Request : ' + error)
        }
        else if (error.status === 404) {
            console.log('ERROR : 404 : Not Found : ' + error)
        } else if (error.status === 401) {
            console.log('ERROR : 401 : Unauthorized : ' + error)
        } else if (error.status === 0 || error.status === undefined) {
            console.log('ERROR : Undefined : API failed to connect : ' + error)
        } else {
            console.log('ERROR : Unknown : Oops! Something is wrong : ' + error)
        }

        return (error);
    }
}
