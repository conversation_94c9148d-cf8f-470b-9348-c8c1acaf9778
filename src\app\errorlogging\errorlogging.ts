import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs/Rx';
import { apiUrl } from '../api-env'
@Injectable()
export class ErrorLoggingService {
  private serverLogUrl = apiUrl + 'api/log';
  // Use a single endpoint for different log types

  constructor(private http: HttpClient) { }

  log(logData: { type: string; message: any }): Observable<HttpResponse<any>> {
    // Log the data to the console.
    console.log('Log occurred:', logData);

    // Send the log to the server for centralized logging.
    return this.sendLogToServer(logData);
  }

  private sendLogToServer(logData: { type: string; message: any }): Observable<HttpResponse<any>> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    const options = { headers };

    const body = JSON.stringify(logData);

    return this.http.post<HttpResponse<any>>(this.serverLogUrl, body, options);
  }
}
