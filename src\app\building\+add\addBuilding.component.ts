import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, FormArray, Validators } from '@angular/forms';
import { Select2OptionData } from 'ng2-select2';
import { BuildingService } from './../../shared/services/building.service';
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;
@Component({
    selector: 'add-building',
    templateUrl: '../building.actions.component.html'
})

export class AddBuildingComponent implements OnInit {
    pageType: string = "Add";

    buildingForm: FormGroup
    select2Options: any = {
        "width": "100%"
    }
    private sub: any;

    @Input() dharamshalaList: any[];
    @Input() gethiddenAddBuilding: boolean;
    @Output() sendBuildingEvent = new EventEmitter();

    constructor(
        private _fb: FormBuilder,
        private BS: BuildingService,
        public translate: TranslateService,
    ) { 
        translate.get('BUILDING.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.buildForm();
    }
    buildForm() {
        this.buildingForm = this._fb.group({
            // dharamshala_id: [((this.dharamshalaList.length > 0) ? this.dharamshalaList[0].id : ''), Validators.required],
            name: ['', Validators.required],
            status: [true, Validators.required],
            floors: this._fb.array([])
        })
        this.addFloors();
    }
    getDharamshalaList(): Select2OptionData[] {
        if (this.dharamshalaList.length > 0) {
            return jQuery.map(this.dharamshalaList, function (obj) {
                return { id: obj.id, text: obj.name };
            })
        }
    }
    // dharamshalaChanges(event) {
    //     this.buildingForm.controls['dharamshala_id'].patchValue(event.value);
    // }
    addFloors() {
        let control = <FormArray>this.buildingForm.controls['floors'];
        control.push(this._fb.group({
            name: ['', Validators.required],
            status: [true, Validators.required]
        }));
    }
    deleteFloor(index) {
        let control = <FormArray>this.buildingForm.controls['floors'];
        control.removeAt(index)
    }

    saveBuilding() {
        if (this.buildingForm.valid) {
            this.sub = this.BS.saveBuilding(this.buildingForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.buildingForm.reset();
                        this.toggleChild(res.data);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.buildingForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        }else{
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    toggleChild(data) {
        let result;
        this.gethiddenAddBuilding = !this.gethiddenAddBuilding;
        if (data) {
            result = { gethiddenAddBuilding: this.gethiddenAddBuilding, data: data }
        } else {
            result = { gethiddenAddBuilding: this.gethiddenAddBuilding }
        }
        this.sendBuildingEvent.emit(result);
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}