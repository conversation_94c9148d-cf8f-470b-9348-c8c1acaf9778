.iconpicker-items {
    a {
      margin: 3px;
      height: 30px;
      width: 30px;
      text-align: center;
      display: inline-block;
      font-size: 20px;
      color: #333;
      i {
        line-height: 10px;
      }
      &.iconpicker-selected {
        color: #fff !important;
        border-radius: 5px;
      }
    }
  }
  .icp-dd ~ .dropdown-menu{
    border: none;
    box-shadow: none;
    background: transparent;
  }
  .popover-content {
    padding: 5px;
    max-height: 250px;
    overflow-y: auto;
    width: 245px;
    border: 1px solid #ddd;
    box-shadow: none;
    &::before {
      content: '';
      background: #ebeff1;
      height: 5px;
      width: 30px;
      position: absolute;
      top: 0;
      margin-left: 5px;
      left: 0;
    }
  }

.iconpicker-popover {
  z-index: 9999;
  .iconpicker-items {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);

    .iconpicker-item {
      display: inline-block;
      width: 30px;
      height: 30px;
      margin: 3px;
      text-align: center;
      line-height: 30px;
      cursor: pointer;
      border-radius: 3px;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f5f5;
      }

      &.iconpicker-selected {
        background: #007bff;
        color: #fff;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .iconpicker-search {
    padding: 10px;
    border-bottom: 1px solid #ddd;
    
    input {
      width: 100%;
      padding: 5px;
      border: 1px solid #ddd;
      border-radius: 3px;
    }
  }
}