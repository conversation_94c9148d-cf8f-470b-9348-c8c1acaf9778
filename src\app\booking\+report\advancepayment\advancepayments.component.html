<section class="widget  revenue-report">
    <header>
        <div class="row">
            <div class="col-sm-6">
                <h4>
                    <span class="red-header">
                        <i class="fa fa-clipboard"></i>&nbsp;&nbsp;
                        <span class="text-capitalize">
                            {{'Advance Payment' }}
                        </span>
                        {{'COLL_BREAKUP_REPORT.REP_MAN' | translate:param}}
                    </span>
                </h4>
            </div>
            <div class="col-sm-2 __download">
                <button class="btn btn-sm btn-primary pull-right" *ngIf="data && data.length > 0" (click)="printRecords()">
                    {{'COLL_BREAKUP_REPORT.PRINT' | translate:param}}
                    <i class="fa fa-print"></i>
                </button>
            </div>
             <button class="btn btn-sm btn-primary pull-right" *ngIf="data && data.length > 0"
              (click)="printRecords()">
              {{reportType !== 'revenue' ? ('REPORT.REP_DOWNLOAD' | translate:param) : ('REPORT.PRINT' | translate:param)}}
              <i class="fa" [ngClass]="{
                              'fa-print': reportType === 'revenue',
                              'fa-download': reportType !== 'revenue'
                          }"></i>
          </button>
            <!-- <div class="float-sm-right text-right col-sm-4">
                <div class="row">
                </div>
                <div class="form-group __search">
                    <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="search()" placeholder="{{'COLL_BREAKUP_REPORT.SEARCH_TXT' | translate:param}}">
                    <span class="form-group-addon">
                        <i class="fa fa-search"></i>
                    </span>
                    <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()">
                        <i class="fa fa-times"></i>
                    </span>
                </div>
            </div> -->
        </div>
    </header>
    <hr class="large-hr">
    <form [formGroup]="searchForm" (ngSubmit)="searchReports()">
        <!-- Filters -->
        <div class="row filter-row" >
            <div class="col-sm-4" *ngIf="isAdmin">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px;">
                         {{'COLL_BREAKUP_REPORT.USER_NAME' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                       <ng-select [items]="usersList"  
                       (change)="userChanged($event)" 
                       bindLabel="text" 
                       bindValue="id"
                       placeholder="Select User"
                       [ngModelOptions]="{standalone: true}"
                       ></ng-select>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-5" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px;">
                         {{'COLL_BREAKUP_REPORT.PAY_MODE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-7" style="padding-left: 0px;">
                       <ng-select [items]="paymentType" 
                       (change)="paymentModeChanged($event)" 
                       bindLabel="text" 
                       bindValue="id" 
                       placeholder="Select Payment Mode"
                       [(ngModel)]="selectedFilterTypes.payment_mode" 
                       [ngModelOptions]="{standalone: true}"></ng-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row filter-row" style="margin-top: 15px;">
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px;">
                            {{'COLL_BREAKUP_REPORT.F_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime [timepicker]="false" formControlName="fromDate"
                            [datepicker]="datepickerOpts" (ngModelChange)="toDataChange($event)"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px;">
                            {{'COLL_BREAKUP_REPORT.T_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime  [timepicker]="false" formControlName="toDate"
                            [datepicker]="datepickerOpts"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4 btn-margin">
                <button type="button" (click)="onReset()" style="margin-left: 5px;"
                    class="btn btn-danger pull-right">{{'COLL_BREAKUP_REPORT.RESET' | translate:param}}</button>
                <button type="submit" class="btn btn-primary pull-right">{{'COLL_BREAKUP_REPORT.SEARCH' | translate:param}}</button>
            </div>
        </div>
        <!-- Amount Summary -->
        <div class="row" *ngIf="false" style="margin-top: 17px;">
            <div class="col-sm-12 col-md-8 col-lg-5">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'COLL_BREAKUP_REPORT.SUMM' | translate:param}}
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr>
                            <td>
                                1) &nbsp; {{'COLL_BREAKUP_REPORT.AGENT_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <span style="color: green;">(+) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{agentAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                2) &nbsp; {{'COLL_BREAKUP_REPORT.ADV_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <span style="color: green;">(+) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{advanceAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <!-- <!-- <tr>
                            <td>
                              3) &nbsp; {{'COLL_BREAKUP_REPORT.CHECKOUt_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <span style="color: green;">(+) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{checkinAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr> -->
                        <!-- <tr>
                            <td>
                              4) &nbsp;  {{'COLL_BREAKUP_REPORT.PEN_PAY' | translate:param}}
                            </td> -->
                            <!-- <td style="text-align: right;">
                                <span style="color: green;">(+) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{pendingAmt | number: '1.2-2'}}
                                </strong>
                            </td> -->
                        <!-- </tr> -->
                        <!-- <tr>
                            <td>
                             5) &nbsp; {{'COLL_BREAKUP_REPORT.REF_AMT' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <span style="color: red;">(-) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{returnAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr> -->
                        <tr>
                            <td>
                               &nbsp;  <strong>  {{'COLL_BREAKUP_REPORT.TOT_AMT' | translate:param}} </strong>
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{totalAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <!-- <div class="col-sm-12 col-md-8 col-lg-5">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'COLL_BREAKUP_REPORT.SUMM_MODE' | translate:param}}
                        </strong>
                    </div>
                    <!-- <table class="table table-no-mar">
                        <tr>
                            <td>
                                1) &nbsp; {{'COLL_BREAKUP_REPORT.AGENT_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{agentAmtPay | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                2) &nbsp; {{'COLL_BREAKUP_REPORT.CARD_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{cardAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                              3) &nbsp; {{'COLL_BREAKUP_REPORT.CHQ_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{chequeAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                              4) &nbsp;  {{'COLL_BREAKUP_REPORT.BANK_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                               <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{bankAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                             5) &nbsp; {{'COLL_BREAKUP_REPORT.CASH_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;color: red;">
                               <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{cashAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                               &nbsp;  <strong>  {{'COLL_BREAKUP_REPORT.TOT_AMT' | translate:param}} </strong>
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{totalPaymentAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                    </table> -->
                <!-- </div> -->
            <!-- </div> -->
        </div>
    </form>
    <div class="clearfix"></div>
    <div class="widget-body table-scroll">
        <div class="mt">
            <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
                <thead>
                    <tr>
                        <th>
                            <mfDefaultSorter by="id">#</mfDefaultSorter>
                        </th>
                        <th>
                            {{'COLL_BREAKUP_REPORT.DATE' | translate:param}}
                        </th>
                        <th>
                            <mfDefaultSorter >{{'COLL_BREAKUP_REPORT.GUEST_NAME' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                          <mfDefaultSorter >{{'COLL_BREAKUP_REPORT.BOOKING_BILL_NO' | translate:param}}</mfDefaultSorter>
                      </th>
                        <th>
                            <mfDefaultSorter>{{'COLL_BREAKUP_REPORT.GUEST_TYPE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter>{{'COLL_BREAKUP_REPORT.PAY_MODE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter >{{'COLL_BREAKUP_REPORT.AMOUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter>{{'REPORT.ACTION' | translate:param}}</mfDefaultSorter>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <ng-template ngFor let-gts [ngForOf]="mf.data" let-i="index">
                        <!-- <tr (click)="ds.checked = !ds.checked" class="reference-level">
                            <td class="fw-semi-bold">{{ i + 1 }}</td>
                            <td class="uppercase" colspan="8">
                                <span class="fw-semi-bold">{{ds.name}} Payment</span>
                            </td>
                        </tr> -->
                        <tr  class="guest-type-level">
                            <td>{{i + 1}}</td>
                            <td>{{gts.payment_date | date}}</td>
                            <td>{{gts.name | uppercase}}</td>
                            <td>{{gts.bill_no}}</td>

                            <td>{{gts.guest_type}}</td>
                            <td style="text-align: left;">
                              <span class="text-capitalize" style="display: inline-block;" *ngIf="gts.payment_mode == 0">
                                <strong>
                                    Cash Mode
                                </strong><br>
                            </span>
                            <span class="text-capitalize" style="display: inline-block;" *ngIf="gts.payment_mode == 1">
                                <strong>
                                    Card Mode
                                </strong>
                            </span>
                            <span class="text-capitalize" style="display: inline-block;" *ngIf="gts.payment_mode == 2">
                                <strong>
                                    Cheque Mode
                                </strong>
                            </span>
                            <span class="text-capitalize" style="display: inline-block;" *ngIf="gts.payment_mode == 3">
                                <strong>
                                    Bank Mode
                                </strong>
                            </span>
                            </td>
                            <td style="text-align: right;padding-right: 20px;">
                                <span *ngIf></span>
                                {{(gts.card_charge_amount > 0 ?  gts.amount + gts.card_charge_amount : gts.amount) | number:'1.2-2' }}
                            </td>
                            <td>
                              <button (click)="viewInvoice(gts.booking_id)" class="btn btn-sm btn-default" tooltip="{{'REPORT.VIEW_IN_VOICE' | translate:param}}" placement="left">
                                {{'REPORT.IN_VOICE' | translate:param}}
                              </button>
                            </td>
                        </tr>
                        <!-- <ng-template ngFor let-gts [ngForOf]="ds.data" let-j="index" >
                        </ng-template> -->
                    </ng-template>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                        <td colspan="100">
                           {{'COLL_BREAKUP_REPORT.NO MATCHES' | translate:param}}
                        </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                        <td class="text-danger" colspan="100">
                            {{'COLL_BREAKUP_REPORT.PERMISSION_DENIED' | translate:param}}
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="12">
                            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</section>
<!-- In-voice View -->
<div bsModal #invoiceModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
    [config]="{backdrop: 'static',keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="max-width: 842px;">
            <div *ngIf="invoiceDataForView" #invoiceHtml>
                <div class="widget-body invoice-view" *ngFor="let item of invoiceDataForView.bookingPayment">
                        <div class="mt billing-guest">
                            <div class="invoice-header-image">
                            </div>
                            <div class="row invoice-view-bootstrap-convertion background-logo-image">
                                <div class="col-sm-12 invoice-view-bootstrap-convertion">
                                    <div class="row invoice-view-bootstrap-convertion">
                                        <div class="col-sm-12 invoice-view-bootstrap-convertion">
                                            <p class="bill-no">
                                                <strong>{{'REPORT.BILL_NO' | translate:param}} :
                                                    {{invoiceDataForView.booking.bill_no == null ? item.bill_no : invoiceDataForView.booking.bill_no}}</strong>
                                                <span style="float:right;display: inline-block;">
                                                    <strong>
                                                        {{'REPORT.DATE' | translate:param}} :
                                                        <!-- {{item.createdAt | date:'dd/MM/y'}} -->
                                                        <!-- {{ item.is_pending_payment == true ? ( item.payment_date | date:'dd/MM/y')  : (invoiceDataForView.bookingRoom[0].check_out | date:'dd/MM/y' )}} -->
                                                        {{ ( item.payment_date | date:'dd/MM/y') }}
                                                    </strong>
                                                </span>
                                            </p>
                                            <div class="customer-name">
                                                <div>
                                                    <span>
                                                        {{'REPORT.NAME' | translate:param}} :
                                                    </span>
                                                    <span>
                                                        <!-- <% if(bookingPayData.name == null){ %>
                                                            <%=bookingRoom[0].guest.name %>
                                                            <% }else{ %>
                                                            <%= bookingPayData.name %> -->
                                                        <span class="text-capitalize">
                                                            {{item.name == null ? invoiceDataForView.booking.guest.name : item.name}}
                                                            <!-- {{invoiceDataForView.booking['is_alternate_bg_active'] ?
                                                        invoiceDataForView.booking['alternate_billing_guest'] :
                                                        invoiceDataForView.booking['guest.name']}} -->
                                                        </span>
                                                    </span>

                                                </div>
                                            </div>
                                            <div class="customer-name">
                                                <span>
                                                    City/Village :
                                                </span>
                                                <span>
                                                    <span class="text-capitalize">
                                                        <!-- {{invoiceDataForView.booking['guest.city']}} -->
                                                        {{item.city == null ? invoiceDataForView.booking.guest.city : item.city}}
                                                    </span>
                                                </span>
                                            </div>
                                            <div class="customer-name ">
                                                <span class="uppercase">
                                                    {{'REPORT.PAN_NO' | translate:param}} :
                                                    {{item.panno == "NA" ? (item.payment_verification_id == null ? invoiceDataForView.booking.payment_verification_id : item.payment_verification_id) : item.panno}}
                                                    <!-- {{invoiceDataForView.booking['guest.city']}} -->
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt invoice-body">
                                        <table class="table table-condence no-m-b">
                                            <thead>
                                                <tr>
                                                    <th>
                                                        {{'REPORT.SR_NO' | translate:param}}
                                                    </th>
                                                    <th style="text-align: center;">
                                                        {{'REPORT.SUB' | translate:param}}
                                                    </th>
                                                    <th>
                                                        {{'REPORT.AMT' | translate:param}}
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr *ngFor="let fund of invoiceDataForView.fundsList; let i = index;">
                                                    <td>
                                                        {{ i + 1 }}
                                                    </td>
                                                    <td>
                                                        {{ fund.name }}
                                                    </td>
                                                    <td *ngIf="fund.name == 'Bhet'" style="text-align: right !important;padding-right: 10%;">
                                                        <div class="image rupee-icon-for-amount"></div>
                                                        {{invoiceDataForView.bookingPayment.length == 0 ? fund.amount : item.amount}}
                                                    </td>
                                                    <td *ngIf="fund.name !== 'Bhet'" style="text-align: right !important;padding-right: 10%;">
                                                        <div class="image rupee-icon-for-amount"></div>
                                                        0
                                                    </td>
                                                </tr>
                                                <tr class="invoice-total-amount">
                                                    <td width="width: 10%;">
                                                    </td>
                                                    <td style="width: 40%;">
                                                        {{'REPORT.TOTAL' | translate:param}}
                                                    </td>
                                                    <td style="text-align: right !important;padding-right: 10%;">
                                                        <div class="image rupee-icon-for-amount"></div>
                                                        {{item.amount}}
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dharamshala-guide-lines">
                            <br />

                            <p class="bill-no" style="text-align: center;">

                                <span class="text-capitalize" style="display: inline-block;" *ngIf="item.payment_mode == 0">
                                    <strong>
                                        Cash Mode
                                    </strong><br>
                                </span>
                                <span class="text-capitalize" style="display: inline-block;" *ngIf="item.payment_mode == 1">
                                    <strong>
                                        Card Mode
                                    </strong><br>
                                    <strong>
                                        Card No:
                                        {{item.payment_reciept_number}}
                                    </strong><br>
                                    <strong>
                                        Card Charge :
                                        {{item.card_charges}}
                                    </strong><br>
                                </span>
                                <span class="text-capitalize" style="display: inline-block;" *ngIf="item.payment_mode == 2">
                                    <strong>
                                        Cheque Mode
                                    </strong><br>
                                    <strong>
                                        Cheque No : :
                                        {{item.bank_cheque_no}}
                                    </strong><br>
                                    <strong>
                                        Bank Name :
                                        {{item.bank_name}}
                                    </strong><br>
                                </span>
                                <span class="text-capitalize" style="display: inline-block;" *ngIf="item.payment_mode == 3">
                                    <strong>
                                        Bank Mode
                                    </strong><br>
                                    <strong>
                                        Trans No:
                                        {{item.bank_cheque_no}}
                                    </strong><br>
                                    <strong>
                                        Bank Name :
                                        {{item.bank_name}}
                                    </strong><br>
                                </span>

                            </p>
                            <p class="bill-no" style="text-align: center;">


                                <span class="text-capitalize" style="display: inline-block;">
                                    <strong>
                                        Total :
                                        {{item.amount | numberToWords}}
                                        only
                                    </strong>
                                </span>

                            </p>
                            <br />
                            <p class="bill-no">
                                <strong>{{'REPORT.PAN_NO' | translate:param}}: **********
                                    <!-- {{invoiceDataForView.booking.payment_verification_id ? invoiceDataForView.booking.payment_verification_id : 'N/A'}} -->
                                </strong>
                                <span style="float:right;display: inline-block;">
                                    <strong>
                                        {{'REPORT.RECE_SIGN' | translate:param}}
                                    </strong>
                                </span>
                            </p><br />
                            <p *ngIf="item.payment_mode != 0">
                                "As per FORM #10AC order for provisional approval of the exemption Under Section 80 G (5) of The Income Tax Act, 1961 bearing provisional approval # **********F20214 dated 28/05/2021 is being granted from AY 2022 23 (FY 2021 22) to AY 2026 27 (FY 2025 26)."
                            </p>
                            <p class="receipt-footer-text">|| Jay Shree Swaminarayan ||</p>
                        </div>
                        <hr style="border-top: 1px dashed rgb(0, 0, 0);margin-top: 2.6rem;">
                        <!-- <div class="mt billing-guest">
                        <div class="invoice-header-image">
                        </div>
                        <div class="row invoice-view-bootstrap-convertion background-logo-image">
                            <div class="col-sm-12 invoice-view-bootstrap-convertion">
                                <div class="row invoice-view-bootstrap-convertion">
                                    <div class="col-sm-12 invoice-view-bootstrap-convertion">
                                        <p class="bill-no">
                                            <strong>{{'REPORT.BILL_NO' | translate:param}} :
                                                {{invoiceDataForView.booking.bill_no}}</strong>
                                            <span style="float:right;display: inline-block;">
                                                <strong>
                                                    {{'REPORT.DATE' | translate:param}} :
                                                    {{invoiceDataForView.bookingRoom[0].check_out | date:'dd/MM/y'}}
                                                </strong>
                                            </span>
                                        </p>
                                        <div class="customer-name">
                                            <div>
                                                <span>
                                                    {{'REPORT.NAME' | translate:param}} :
                                                </span>
                                                <span>
                                                    <span class="text-capitalize">
                                                        {{invoiceDataForView.booking['is_alternate_bg_active'] ?
                                                        invoiceDataForView.booking['alternate_billing_guest'] :
                                                        invoiceDataForView.booking['guest.name']}}
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="customer-name">
                                            <span>
                                                City/Village :
                                            </span>
                                            <span>
                                                <span class="text-capitalize">
                                                    {{invoiceDataForView.booking['guest.city']}}
                                                </span>
                                            </span>

                                        </div>
                                    </div>
                                </div>
                                <div class="mt invoice-body">
                                    <table class="table table-condence no-m-b">
                                        <thead>
                                            <tr>
                                                <th>
                                                    {{'REPORT.SR_NO' | translate:param}}
                                                </th>
                                                <th>
                                                    {{'REPORT.SUB' | translate:param}}
                                                </th>
                                                <th>
                                                    {{'REPORT.AMT' | translate:param}}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let fund of invoiceDataForView.fundsList; let i = index;">
                                                <td>
                                                    {{ i + 1 }}
                                                </td>
                                                <td>
                                                    {{ fund.name }}
                                                </td>
                                                <td>
                                                    <div class="image rupee-icon-for-amount"></div>
                                                    {{ fund.amount }}
                                                </td>
                                            </tr>
                                            <tr class="invoice-total-amount">
                                                <td width="width: 10%;">
                                                </td>
                                                <td style="width: 40%;">
                                                    {{'REPORT.TOTAL' | translate:param}}
                                                </td>
                                                <td>
                                                    <div class="image rupee-icon-for-amount"></div>
                                                    {{ invoiceDataForView.paidAmount }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div> -->
                        <!-- <div class="dharamshala-guide-lines">
                        <br />
                        <p class="bill-no" style="text-align: center;">


                            <span class="text-capitalize" style="display: inline-block;">
                                <strong>
                                    Total : {{ totalAmaunt | numberToWords}} only
                                </strong>
                            </span>

                        </p>
                        <br />
                        <p class="bill-no">
                            <strong>{{'REPORT.PAN_NO' | translate:param}}: **********

                            </strong>
                            <span style="float:right;display: inline-block;">
                                <strong>
                                    {{'REPORT.RECE_SIGN' | translate:param}}
                                </strong>
                            </span>
                        </p>
                        <br />
                        <p>
                            "As per FORM #10AC order for provisional approval of the exemption Under Section 80 G (5) of The Income Tax Act, 1961 bearing provisional approval # **********F20214 dated 28/05/2021 is being granted from AY 2022 23 (FY 2021 22) to AY 2026 27 (FY 2025 26)."
                        </p>
                        <p class="receipt-footer-text">|| Jay Shree Swaminarayan||</p>
                    </div> -->
                </div>
            </div>
            <div class="modal-footer">
                <div class="btn-group">
                    <button type="button" class="btn btn-md btn-inverse" aria-label="Close"
                        (click)="invoiceModal.hide()">
                        {{'REPORT.OK' | translate:param}}
                    </button>
                    <button class="btn btn-md btn-inverse" aria-label="Close"
                        (click)="getHtml(invoiceDataForView.booking.id)">
                        {{'REPORT.GEN_PDF' | translate:param}}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
