import { UserRoleService } from './../../shared/services/userrole.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****


@Component({
    selector: 'user-roles',
    templateUrl: './userRoles.component.html'
})
export class UserRolesComponent implements OnInit {
    config: any;// New Change ****
    data: any[];
    originalData: any[];
    searchQuery: string;
    groupListData: any[];
    selectedUR: any;
    // services variables
    private sub: any;
    private groupList: any;
    public canViewRecords: boolean;

    // hide/show userRoles child components
    hideAddUR: boolean = true;
    hideEditUR: boolean = true;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private URS: UserRoleService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        this.canViewRecords = true;
        this.sub = this.URS.getUserRoles().subscribe((res) => {
            // console.log("Current role list : ",res);
            this.data = res.data.roles;
            // console.log("this.data : ", this.data);
            this.originalData = res.data.roles;
            this.groupListData = res.data.groups;
        }, error => {
            if (error.status == 403) {
                this.canViewRecords = false;
            }
        });

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });  
    }

    ngOnInit() { }
    
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    findIndex(searchTerm, searchArray?: any[]) {
        searchArray = searchArray ? searchArray :  this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i].id === searchTerm) return (i + 1);
        }
        return -1;
    }

    // add user Role components
    showAddUR() {
        this.hideAddUR = !this.hideAddUR;
    }
    handlehideAddUR(event) {
        this.hideAddUR = event.gethideAddUR;
        if (this.canViewRecords && event.data) {
            this.originalData.push(event.data);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }
    // edit user role component
    showEditUR(ele) {
        this.selectedUR = ele;
        this.hideEditUR = !this.hideEditUR;
    }
    handlehideEditUR(event) {
        this.hideEditUR = event.gethideEditUR;
        if (this.canViewRecords && event.data) {
            this.data[this.findIndex(event.data.id) - 1] = event.data;
            this.originalData[this.findIndex(event.data.id, this.originalData) - 1] = event.data;
        }
    }
    searchEvent() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter( data => {
                return ((<string>data.name).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
        this.initializeData();
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    initializeData() {
        this.data = this.originalData;
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if(this.sub){
            this.sub.unsubscribe();
        }
        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}