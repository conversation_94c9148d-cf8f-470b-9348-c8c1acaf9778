import { Component, Input, OnInit, OnDestroy, Output, ViewEncapsulation, ViewChild, ElementRef, Renderer2, EventEmitter } from "@angular/core";
import { FormGroup, FormBuilder, FormArray, FormControl, Validators } from '@angular/forms';
import { BookingService } from './../../../shared/services/booking.service';
import { AuthGuard } from '../../../shared/guards/auth-guard.service';
// import { EventEmitter } from '@angular/common/src/facade/async';
import { paymentType } from "../../../reservation/data";
import { CustomValidators } from 'ng2-validation';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
import * as moment from 'moment';

interface NgSelectOption {
    id: string;
    text: string;
}

@Component({
    selector: 'update-pending-payments',
    templateUrl: './pending-payments.component.html',
    styleUrls: ['./pending-payments.style.scss'],
    encapsulation: ViewEncapsulation.None
})
export class PendingPaymentsComponent implements OnInit, OnDestroy {
    @ViewChild('totalPayableAmount') totalPayableAmount: ElementRef;
    @ViewChild('returnAmountValue') returnAmountValue: ElementRef;
    @ViewChild('fundtypeID') fundtypeID: ElementRef;
    @Input('selectedReport') selectedReport: any;
    @Input('paymentType') paymentType: string;
    @Input('fromReport') fromReport: boolean;
    @Output('cancelled') cancelled = new EventEmitter();

    config: any;// New Change ****
    fundAmount: any;
    referenceUser: any[];
    selectedFundValue: any;
    customerTypeList: any[];
    selectCustomerValue: any;
    referenceUsersMain: any[];
    refereceUserSelected: any;
    disableFund: boolean = true;
    paymentAtcheckout: FormGroup;
    referenceUsersOriginal: any[];
    paymentTypeSelection: string = '0';
    paymentTypeList: any = paymentType;
    checkoutReferenceUserSelected: any;
    isCheckoutReference: boolean = false;
    fndType: NgSelectOption[] = [];
    bookingTypeOptions = {
        clearable: false,
        searchable: false
    };
    public panNo: any[] = [];
    public spliteData = []
    public cardSelected = false;
    public apiValue
    //private
    private checkoutGetAPI: any;
    private payFinalPayments: any;
    private updatePaymentApi: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private fb: FormBuilder,
        private BS: BookingService,
        private renderer: Renderer2,
        private authGuard: AuthGuard,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    ngOnInit() {
        this.checkoutGetAPI = this.BS.getCheckoutDetails({}, this.selectedReport.id, 'payment', this.selectedReport.customer_id, true)
            .subscribe((res) => {
                if (res.status === "success") {
                    this.apiValue = res.data
                    this.isCheckoutReference = false;
                    this.selectedReport['process_type'] = 'payment';
                    this.selectedReport['payments'] = res.data;
                    this.fndType = res.data.fund.map(function (obj) {
                        return { id: obj.id, text: obj.name };
                    });
                    if (res.data.customers.length) {
                        this.customerTypeList = res.data.customers.map(function (obj) {
                            return { id: obj.id, text: obj.name };
                        });
                    }
                    let index = this.findIndex(true, 'is_default', res.data.customers);
                    this.selectCustomerValue = index > -1 ? res.data.customers[index].id : this.customerTypeList[0].id;
                    if (res.data.referenceUsers.length) {
                        this.referenceUsersMain = res.data.referenceUsers;
                        let referenceUser: any[];
                        referenceUser = res.data.referenceUsers.map(function (obj) {
                            return { id: obj.id, text: obj.name };
                        });
                        this.referenceUser = referenceUser;
                        this.referenceUsersOriginal = referenceUser;
                        let tempRefereceUser = this.getFilteredReferences(this.selectCustomerValue);
                        if (tempRefereceUser.length > 0) {
                            this.referenceUser = tempRefereceUser;
                        } else {
                            this.referenceUser = referenceUser;
                        }
                        this.refereceUserSelected = this.referenceUser[0].id;
                        this.checkoutReferenceUserSelected = this.referenceUser[0].id;
                    }
                    this.initFinalPaymentForm(this.selectedReport, this.paymentType);
                    if (res.data.defaultFund) {
                        this.selectedFundValue = res.data.defaultFund.fund_id;
                    } else {
                        this.selectedFundValue = this.fndType.length > 0 ? this.fndType[0].id : undefined;
                    }
                    this.selectedReport['payments']['cardSwipeChargesOriginal'] = JSON.parse(JSON.stringify(this.selectedReport['payments']['cardSwipeCharges']));
                    this.paymentAtcheckout.controls['custom_discount'].patchValue(res.data.bookingCustomDiscounts1);
                    this.selectedReport['cardPaymentTotal'] = res.data.early_checkin_Charge + res.data.total_payable_amount - (res.data.paidAmount ? res.data.paidAmount : 0) - res.data.bookingCustomDiscounts1;
                    this.fundAmount = 0;
                    let needPaymentVerificationId = res.data.early_checkin_Charge + res.data.cardSwipeCharges + res.data.net_amount;
                    // if (needPaymentVerificationId >= 10000) {
                        this.paymentAtcheckout.addControl('payment_verification_id', new FormControl(''));
                    // }

                }
            })
    }

    getFilteredReferences(selectedValue: any) {
        let referencesAllowed = [];
        this.referenceUsersMain.forEach(reference => {
            if (reference.customer_type_ids) {
                reference.customer_type_ids.forEach((customerIds: any[]) => {
                    if ((customerIds.indexOf(selectedValue) >= 0) && (referencesAllowed.indexOf(reference.id) < 0)) {
                        referencesAllowed.push(reference.id);
                    }
                });
            }
        });
        let refereceUser = [];
        referencesAllowed.forEach(id => {
            refereceUser.push(this.referenceUsersOriginal[this.findIndex(id, "id", this.referenceUsersOriginal)]);
        });
        return refereceUser;
    }
    findIndex(searchTerm: any, property: string, targetArray: any[]) {
        for (let i = 0; i < targetArray.length; i++) {
            if (targetArray[i][property] == searchTerm) { return i; }
        }
        return -1;
    }
    /**
    * Initialise payment form at checkout process
    *
    * @param {any} item
    * @memberof ReservationsComponent
    */
    initFinalPaymentForm(item: any, type: string) {
        this.paymentAtcheckout = this.fb.group({
            fund_type: [],
            fund_amount: [0],
            payment_mode: ['0'], // cash payment type selected
            payment_amount: [0],
            room_id: [item.room_id],
            pavati_no: [null],
            booking_id: [item.payments.booking_id],
            custom_discount: [0, CustomValidators.digits],
            is_early_checkout: [item.payments.is_early_checkout],
            payment_date: [moment().format('YYYY-MM-DD') + 'T00:00:00.000Z'],
            items: this.fb.array([])
        });
        // if (type == "checkout") {
        //     this.paymentAtcheckout.addControl('pavati_no', new FormControl(null, Validators.required));
        // }
    }
    spliteNameCity() {
        const a = this.fb.group({
            name: [null],
            city: [null],
            panno:[null]
        });
        (<FormArray>this.paymentAtcheckout.get('items')).push(a);
    }
    PaymentAtcheckoutSelectionChanged(event, data: any, type?: string) {
        this.cardSelected = false;
        this.paymentTypeSelection = event.value;
        this.paymentAtcheckout.controls['payment_mode'].patchValue(event.value);
        if (event.value == 1 || event.value == 2 || event.value == 3) {
            this.cardSelected = true;
            // this.paymentAtcheckout.get('payment_verification_id').setValidators(Validators.required)
           
        }
        // else{
        //     this.paymentAtcheckout.get('payment_verification_id').clearValidators()
        // }
     
        // console.log('---------- ', this.paymentAtcheckout.value, event.value, data)

        if (event.value == 1) {
            if (this.checkoutTotalWithCardSwipeCharges(data.payments) >= 10000) {
                this.paymentAtcheckout.get('payment_verification_id').setValidators([Validators.required])
                this.highLighValidationsForField('payment_verification_id');
            }
            this.paymentAtcheckout.addControl('payment_reciept_number', new FormControl('', Validators.required));
            this.paymentAtcheckout.addControl('card_charge', new FormControl(0, [Validators.required, CustomValidators.lte(100)]));
            data['cardPaymentTotal'] = this.checkoutTotalWithCardSwipeCharges(data.payments);
            // this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, CustomValidators.equal((<number>data['cardPaymentTotal']).toString())]);
            this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required]);
            this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
            this.paymentAtcheckout.controls['payment_reciept_number'].updateValueAndValidity();
            this.paymentAtcheckout.controls['card_charge'].updateValueAndValidity();
            if (this.paymentAtcheckout.get('bank_name')) {
                this.paymentAtcheckout.removeControl('bank_name');
                this.paymentAtcheckout.removeControl('bank_cheque_no');
            }
        }
        else if (event.value == 2) {
            if (this.checkoutTotalWithCardSwipeCharges(data.payments) >= 10000) {
                this.paymentAtcheckout.get('payment_verification_id').setValidators([Validators.required])
                this.highLighValidationsForField('payment_verification_id');
            }
            this.highLighValidationsForField('payment_verification_id');
            data.payments['cardSwipeCharges'] = data.payments['cardSwipeChargesOriginal'];
            this.paymentAtcheckout.addControl('bank_name', new FormControl('', Validators.required));
            this.paymentAtcheckout.addControl('bank_cheque_no', new FormControl('', Validators.required));
            this.paymentAtcheckout.controls['payment_amount'].setValidators(Validators.required);
            this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
            this.paymentAtcheckout.controls['bank_name'].updateValueAndValidity();
            this.paymentAtcheckout.controls['bank_cheque_no'].updateValueAndValidity();
            if (this.paymentAtcheckout.get('payment_reciept_number')) {
                this.paymentAtcheckout.removeControl('payment_reciept_number');
                this.paymentAtcheckout.removeControl('card_charge');
            }
        }
        else if (event.value == 3) {
            if (this.checkoutTotalWithCardSwipeCharges(data.payments) >= 10000) {
                this.paymentAtcheckout.get('payment_verification_id').setValidators([Validators.required])
                this.highLighValidationsForField('payment_verification_id');
            }
            data['cardPaymentTotal'] = this.checkoutTotalWithCardSwipeCharges(data.payments);
            this.paymentAtcheckout.addControl('bank_name', new FormControl('', Validators.required));
            this.paymentAtcheckout.addControl('bank_cheque_no', new FormControl('', Validators.required));
            this.paymentAtcheckout.controls['payment_amount'].setValidators(Validators.required);
            this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
            this.paymentAtcheckout.controls['bank_name'].updateValueAndValidity();
            this.paymentAtcheckout.controls['bank_cheque_no'].updateValueAndValidity();
            if (this.paymentAtcheckout.get('payment_reciept_number')) {
              this.paymentAtcheckout.removeControl('payment_reciept_number');
              this.paymentAtcheckout.removeControl('card_charge');
            }
          }
        else {
            this.paymentAtcheckout.get('payment_verification_id').clearValidators();
            this.paymentAtcheckout.get('payment_verification_id').markAsPristine();
            data.payments['cardSwipeCharges'] = data.payments['cardSwipeChargesOriginal'];
            if (this.paymentAtcheckout.get('payment_reciept_number')) {
                this.paymentAtcheckout.removeControl('payment_reciept_number');
                this.paymentAtcheckout.removeControl('card_charge');
            }
            if (this.paymentAtcheckout.get('bank_name')) {
                this.paymentAtcheckout.removeControl('bank_name');
                this.paymentAtcheckout.removeControl('bank_cheque_no');
            }
            this.paymentAtcheckout.controls['payment_amount'].clearValidators();
            this.paymentAtcheckout.controls['payment_amount'].setValidators(CustomValidators.digits);
            this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
        }
        this.manipulateReferenceAndNote(type);
    }
    manipulateReferenceAndNote(type?: string) {
        let totalPayable = parseInt((<string>this.totalPayableAmount.nativeElement.innerText).split('.')[0].replace(/,/g, ''));
        // && this.paymentAtcheckout.get('payment_mode').value != "1"
        if (typeof totalPayable == 'number' && totalPayable > 0 && type == "checkout") {
            if (!this.paymentAtcheckout.get('reference_user')) {
                this.paymentAtcheckout.addControl('reference_user', new FormControl('', [Validators.required]));
                this.paymentAtcheckout.get('reference_user').markAsDirty();
                this.paymentAtcheckout.get('reference_user').markAsTouched();
            }
            if (!this.paymentAtcheckout.get('note')) {
                this.paymentAtcheckout.addControl('note', new FormControl('', [Validators.required]));
                this.paymentAtcheckout.get('note').markAsDirty();
                this.paymentAtcheckout.get('note').markAsTouched();
            }
        }
        else {
            if (this.paymentAtcheckout.get('reference_user')) {
                this.paymentAtcheckout.removeControl('reference_user');
            }
            if (this.paymentAtcheckout.get('note')) {
                this.paymentAtcheckout.removeControl('note');
            }
        }
    }
    updateCheckoutCardSwipeCharges(data: any) {
        data['cardPaymentTotal'] = this.checkoutTotalWithCardSwipeCharges(data.payments);
        // this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, CustomValidators.equal((<number>data['cardPaymentTotal']).toString())]);
        // this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
    }
    getPanNo() {
        this.panNo = [];
        if (this.paymentAtcheckout.get('payment_verification_id').value) {
            this.panNo.push(this.paymentAtcheckout.get('payment_verification_id').value);
        }
    }
    updateFunds(data: any, type?: string) {
        this.spliteData = [];
        if(this.paymentAtcheckout.value.payment_amount < 10000){
            this.cardSelected = true
            // console.log("ifffff")
          }else{
            this.cardSelected = false
          }
        if (type === "payment") {
            // if( this.paymentAtcheckout.controls['payment_verification_id'] == null){
            let apiData = {
                payment_amount: this.paymentAtcheckout.value.payment_amount,
                payment_verification_id: '',//this.paymentAtcheckout.controls['payment_verification_id'],
                payment_mode: this.paymentAtcheckout.get('payment_mode').value,
                booking_id: this.apiValue.booking_id,
            }
            // console.log("",apiData)
            this.BS.getSpliteData(apiData).subscribe((res) => {
                if (res.data && res.status == "success") {
                    this.spliteData = res.data
                    this.paymentAtcheckout.removeControl('items');
                    this.paymentAtcheckout.addControl('items',this.fb.array([]));
                    for (let index = 0; index <  this.spliteData.length; index++) {
                        this.spliteNameCity()
                    }
                }
                // console.log("===========", res)
            }, (error: any) => {
                if (error) {
                    console.log(error)
                }
            })
            // }
        }


        let amount = this.returnAmountValue.nativeElement.innerText === '' ? 0 : parseInt(this.returnAmountValue.nativeElement.innerText);
        // if (amount > 0 && this.paymentAtcheckout.get('payment_mode').value != "1") {
        if (amount > 0 ) {
            this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
            this.paymentAtcheckout.controls['fund_type'].patchValue(this.selectedFundValue);
            if (this.fundtypeID && !this.fundtypeID.nativeElement.checked) {
                this.renderer.setProperty(this.fundtypeID.nativeElement, 'checked', true);
            }
        }
        else {
            this.paymentAtcheckout.controls['fund_amount'].patchValue(0);
            this.paymentAtcheckout.controls['fund_type'].patchValue(null);
            if (this.fundtypeID) {
                this.renderer.setProperty(this.fundtypeID.nativeElement, 'checked', false);
            }
        }
        if (this.paymentAtcheckout.get('pavati_no')) {
            this.paymentAtcheckout.controls['pavati_no'].markAsDirty();
            this.paymentAtcheckout.controls['pavati_no'].markAsTouched();
        }
        this.manipulateReferenceAndNote(type);
        if (this.paymentAtcheckout.get('payment_mode').value == '1') {
            data['cardPaymentTotal'] = this.checkoutTotalWithCardSwipeCharges(data.payments);
            // this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required, CustomValidators.equal((<number>data['cardPaymentTotal']).toString())]);
            this.paymentAtcheckout.controls['payment_amount'].setValidators([Validators.required]);
            this.paymentAtcheckout.controls['payment_amount'].updateValueAndValidity();
        }
        if (!(amount > 0)) {
            this.fundAmount = 0;
        }
        // this.paymentVerification(data,type)
    }

    //********  ADD VALIDATION IN PAN NUMBER  ***** V ****/

    highLighValidationsForField(field: string) {
        this.paymentAtcheckout.get(field).markAsDirty();
        this.paymentAtcheckout.get(field).markAsTouched();
      }

    // paymentVerification(data,type){
    //     if (type == 'checkout' || type == 'payment') {
    //         let customDiscount = this.paymentAtcheckout.controls['custom_discount'].value > 0 ? this.paymentAtcheckout.controls['custom_discount'].value : 0;
    //         let needPaymentVerificationId = data.payments.early_checkin_Charge + data.payments.cardSwipeCharges + data.payments.net_amount - customDiscount;
    //         if (!this.paymentAtcheckout.get('payment_verification_id') && needPaymentVerificationId >= 10000) {
    //           this.paymentAtcheckout.addControl('payment_verification_id', new FormControl('', Validators.required));
    //           this.highLighValidationsForField('payment_verification_id');
    //         } else if (this.paymentAtcheckout.get('payment_verification_id')) {
    //           this.highLighValidationsForField('payment_verification_id');
    //           if (needPaymentVerificationId < 10000) {
    //             this.paymentAtcheckout.removeControl('payment_verification_id');
    //           }
    //         }
    //       }
    // }

    //******** END ADD VALIDATION IN PAN NUMBER  ***** V ****/

    checkoutTotalWithCardSwipeCharges(data: any) {
        let customDiscount = 0;
        if (this.paymentAtcheckout.get('custom_discount') && this.paymentAtcheckout.get('custom_discount').value !== '') {
            customDiscount = this.paymentAtcheckout.get('custom_discount').value;
        }
        let cardCharge = this.paymentAtcheckout.get('card_charge') ? parseInt(this.paymentAtcheckout.get('card_charge').value) : 0;
        let totalAmount = data.early_checkin_Charge + data.total_payable_amount - data.paidAmount - customDiscount - data.cardSwipeChargesOriginal;
        // let cardSwipeCharges = ((totalAmount * cardCharge) / 100);
        // data['cardSwipeCharges'] = cardSwipeCharges;
        // return (totalAmount + cardSwipeCharges);
        // console.log('-------', data )
        if (data.bookingPayment && data.bookingPayment.length != 0) {
            let paidAm = 0;
            for (let i = 0; i < data.bookingPayment.length; i++) {
              paidAm = paidAm + data.bookingPayment[i].amount;
            }
            totalAmount = data.early_checkin_Charge + data.total_payable_amount - paidAm - customDiscount;
        } else {
            totalAmount = data.early_checkin_Charge + data.total_payable_amount - data.paidAmount - customDiscount - data.cardSwipeChargesOriginal;
        }
        let cardSwipeCharges = ((this.paymentAtcheckout.get('payment_amount').value * cardCharge) / 100)
        data['cardSwipeCharges'] = cardSwipeCharges;
        // console.log('-------', totalAmount - (+this.paymentAtcheckout.get('payment_amount').value) )
        return (totalAmount - (+this.paymentAtcheckout.get('payment_amount').value))
    }
    hasCheckoutReference() {
        this.isCheckoutReference = !this.isCheckoutReference;
        this.setCheckoutReferenceUser();
    }
    checkoutReferenceTypeChanged(event: any) {
        this.checkoutReferenceUserSelected = event.value;
        this.setCheckoutReferenceUser(event.value);
    }
    setCheckoutReferenceUser(selectedCheckoutReference?: any) {
        if (this.isCheckoutReference) {
            this.paymentAtcheckout.controls['reference_user'].patchValue(selectedCheckoutReference ? selectedCheckoutReference : this.checkoutReferenceUserSelected);
        } else {
            this.paymentAtcheckout.controls['reference_user'].patchValue('');
        }
    }
    fundTypeEnable(event) {
        this.disableFund = !event.target.checked;
        if (event.target.checked) {
            this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
            this.paymentAtcheckout.controls['fund_type'].patchValue(this.selectedFundValue);
        } else {
            this.paymentAtcheckout.controls['fund_amount'].patchValue(0);
            this.paymentAtcheckout.controls['fund_type'].patchValue(null);
        }
    }
    FundTypeChanges(event) {
        this.selectedFundValue = event.value;
        this.paymentAtcheckout.controls['fund_type'].patchValue(event.value);
    }
    makePositive(num) {
        num = Math.abs(num); // convert from negative to positive
        this.fundAmount = num;
        return num;
    }
    returnZero() {
        this.paymentAtcheckout.controls['fund_amount'].patchValue(0);
        this.paymentAtcheckout.controls['fund_type'].patchValue(null);
        if (this.fundtypeID) {
            this.renderer.setProperty(this.fundtypeID.nativeElement, 'checked', false);
        }
    }
    /**
   * The payment process if there ara some amount is still remaining to be paid.
   *
   * @memberof ReservationsComponent
   */
    paymentAtcheckoutProcess(data: any, customerId: number) {
        let totalPayable = parseInt((<string>this.totalPayableAmount.nativeElement.innerText).split('.')[0].replace(/,/g, ''));
        this.paymentAtcheckout.controls['fund_amount'].patchValue(this.fundAmount);
        if (this.paymentAtcheckout.valid) {
            if (data.process_type == 'payment') {
                let postData = this.paymentAtcheckout.value;
                postData['is_pending_payment'] = true;
                if (postData.payment_mode == '1') {
                    // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
                }
                let paymentCompleted = false;
                if(typeof totalPayable == 'number' && totalPayable === 0) {
                    paymentCompleted = true;
                }
                this.updatePaymentApi = this.BS.updatePayment(postData.booking_id, postData, 'pending-payment').
                    subscribe((res) => {
                        if (res.status == "success") {
                            this.paymentTypeSelection = '0';
                            this.cancel(paymentCompleted, res.data);
                        }
                    });
            }
            else {
                let postData = this.paymentAtcheckout.value;
                postData['is_pending_payment'] = false;
                if (postData.payment_mode == '1') {
                    // postData['payment_amount'] = data.cardPaymentTotal - data.payments.cardSwipeCharges;
                }
                postData['isAdmin'] = this.authGuard.isAdmin();
                let paymentCompleted = false;
                if(typeof totalPayable == 'number' && totalPayable === 0) {
                    paymentCompleted = true;
                }
                this.payFinalPayments = this.BS.payFinalPayments(postData.booking_id, postData, customerId)
                    .subscribe((res) => {
                        if (res.status == "success") {
                            this.paymentTypeSelection = '0';
                            this.cancel(false);
                        }
                    })
            }
        }
        else {
            for (let field in this.paymentAtcheckout.controls) {
                this.paymentAtcheckout.get(field).markAsTouched();
                this.paymentAtcheckout.get(field).markAsDirty();
            }
        }
    }
    cancel(paymentCompleted: boolean, data?: any) {
        if(this.fromReport && data) {
            data['id'] = this.selectedReport.id;
            if(paymentCompleted) {
                data['payment_completed_status'] = true;
            }
            else {
                data['payment_completed_status'] = false;
            }
        }
        this.cancelled.emit(data);
    }
    ngOnDestroy() {
        if (this.checkoutGetAPI) {
            this.checkoutGetAPI.unsubscribe();
        }
        if (this.updatePaymentApi) {
            this.updatePaymentApi.unsubscribe();
        }
        if (this.payFinalPayments) {
            this.payFinalPayments.unsubscribe();
        }
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}