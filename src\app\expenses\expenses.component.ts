import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { AuthGuard } from './../shared/guards/auth-guard.service';
import { Component, OnInit } from '@angular/core';
import { ExpensesService } from 'app/shared/services/expenses.service';
import { TranslateService } from '@ngx-translate/core';
import { AppConfig } from 'app/app.config';
import { TranslateEventService } from 'app/shared/services/translation.service';
import { Subject, Subscription } from 'rxjs';

@Component({
    selector: 'app-expenses',
    templateUrl: 'expenses.component.html'
})

export class ExpensesComponent implements OnInit, OnDestroy {
    data: any[] = [];
    config: any;
    searchQuery: string;
    originalData: any[] = [];
    hiddenAdd: boolean = true;
    hiddenEdit: boolean = true;
    public canViewRecords: boolean;
    private sub: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,
        private auth: AuthGuard,
        private EX: ExpensesService,
        config: AppConfig,
        private TS: TranslateEventService, // New Change ****
    ) {
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);
         // New Change ****
         this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    showAdd() {
        this.hiddenAdd = false;
        this.hiddenEdit = true;
    }

    ngOnInit() {
        this.canViewRecords = true;
        this.sub = this.EX.getShiftExpenses()
            .subscribe((res) => {
                if (res.status == "success") {
                    this.data = res.data;
                    this.originalData = res.data;
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    addtoList(event) {
        if (this.canViewRecords && event) {
            this.originalData.push(event);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }

    initializeData() {
        this.data = this.originalData;
    }
    
    closeAdd(event) {
        if (event) {
            this.hiddenAdd = true;
        }
    }

    ngOnDestroy() {
        if(this.sub) {
            this.sub.unsubscribe();
        }
    }
}