.payroll {
    padding-top: 15px;
    .text-right {
      text-align: right;
    }
    .table-no-border {
      td,th {
        padding: 0.15rem;
        border-color: transparent !important
        }
    }
    .fundCheck.abc-checkbox {
        border-radius: 3px !important;
        label::before,
        label::after {
            margin-top: -5px;
            margin-left: -8px;
        }
        .select2-selection.select2-selection--single {}
    }
    .is_reference.abc-checkbox {
        padding-left: 0;
        label::before {
            margin-left: -14px;
            top: 0;
            margin-top: -5px;
        }
        label::after {
            margin-left: -14px;
            margin-top: -3px;
            padding: 0;
        }
        input[type="checkbox"]:focus+label::before {
            outline: none;
        }
    }
    .referenceSelections {
        .select2-selection__rendered {
            padding-left: 90px !important;
            line-height: 33px !important;
            text-transform: capitalize;
        }
        .select2-container--disabled {
            opacity: 0.5
        }
        .select2-container:not(.select2-container--disabled) {
       F     .select2-selection--single {
            border-radius: 0 3px 3px 0 !important;
            }
        }
    }
    @media screen and (min-width: 998px) {
        .border-sm-left {
          border-left: 1px solid #ddd;
        }
      }
}

.relative {
    position: relative;
    label.absolute {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      margin-left: 10px;
      z-index: 0;
    }
  }

.select2-container--default .select2-selection--single {
    background-color: rgba(255, 255, 255, 0) !important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
line-height: 35px !important;
}

.select2-container .select2-selection--single {
height: 37px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
height: 35px !important;
width: 35px !important;
}
.advancePaymentSelection {
    .select2-selection__rendered {
        padding-left: 125px !important;
        line-height: 38px !important;
        text-transform: capitalize;
    }
}