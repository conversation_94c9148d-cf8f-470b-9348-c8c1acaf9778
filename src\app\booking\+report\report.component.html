<update-pending-payments *ngIf="isPendingPayments" [selectedReport]="selectedReport" [fromReport]="true"
    (cancelled)="cancelPayment($event)"></update-pending-payments>
<view-report-details *ngIf="isPoliceInquiry && viewComp" [isPoliceInquiry]="isPoliceInquiry"
    [selectedData]="selectedReport" [referenceTypeList]="referenceTypeList" (goBack)="closeComp()">
</view-report-details>
<section class="widget" [ngClass]="{'revenue-report': reportType == 'revenue' ? true : false}"
    [ngStyle]="{'display': ((isPoliceInquiry && viewComp) || isPendingPayments) ? 'none' : 'block'}">
    <header>
        <!-- <button type="button" *ngIf="reportType == 'discount'" [routerLink]="['/admin/shift']" style="position: absolute;top: -37px;margin-left: -19px;color: white;background-color: #c54e4e;" class="btn btn-sm btn-primary pull-left">Go To Home</button> -->
        <div class="row">
            <div class="col-sm-6">
                <h4>
                    <span class="red-header" *ngIf="reportType != 'discount'">
                        <i class="fa fa-clipboard"></i>&nbsp;&nbsp;
                        <span class="text-capitalize">
                            {{reportName}}
                        </span>
                        {{'REPORT.REP_MANAGE' | translate:param}}
                    </span>

                    <span class="red-header" *ngIf="reportType == 'discount'">
                            <i class="fa fa-clipboard"></i>&nbsp;&nbsp;
                            <span class="text-capitalize">
                                {{'REPORT.DISCOUNT_APPROVAL' | translate:param}}
                            </span>
                    </span>
                </h4>
            </div>
            <div class="__download" [ngClass]="{'float-sm-right': reportType === 'revenue' ? true : false,
                            'col-sm-2': reportType !== 'revenue' ? true : false,
                            'col-sm-6': reportType === 'revenue' ? true : false}">
                <button class="btn btn-sm btn-primary pull-right" *ngIf="data && data.length > 0 && reportType != 'discount'"
                    (click)="printRecords()">
                    {{reportType !== 'revenue' ? ('REPORT.REP_DOWNLOAD' | translate:param) : ('REPORT.PRINT' | translate:param)}}
                    <i class="fa" [ngClass]="{
                                    'fa-print': reportType === 'revenue',
                                    'fa-download': reportType !== 'revenue'
                                }"></i>
                </button>
            </div>
            <div *ngIf="reportType !== 'revenue'" class="float-sm-right text-right col-sm-4">
                <div class="row">
                </div>
                <div class="form-group __search">
                    <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="search()"
                        placeholder="{{'REPORT.SEARCH_REPORT' | translate:param}}">
                    <span class="form-group-addon">
                        <i class="fa fa-search"></i>
                    </span>
                    <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()">
                        <i class="fa fa-times"></i>
                    </span>
                </div>
            </div>
        </div>
    </header>
    <hr class="large-hr" [ngClass]="{'hide-in-print': reportType === 'revenue'}">
    <form [formGroup]="searchForm" (ngSubmit)="searchReports()">
        <!-- Filters -->
        <div class="row filter-row" style="margin-bottom: 15px;" *ngIf="reportType != 'agents'">
            <div *ngIf="reportType !== 'revenue'" class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.ROOM_TYP' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <ng-select [items]="roomTypeList" bindValue="id" bindLabel="text" formControlName="room_type_id" (change)="roomTypeChanged($event)" ></ng-select>
                    </div>
                </div>
            </div>
            <div *ngIf="reportType !== 'revenue'" class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.GUE_TYPE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <ng-select [items]="customerTypeList" bindValue="id" bindLabel="text" formControlName="guest_id" (change)="guestTypeChanged($event)" ></ng-select>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-5" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.REF_TYPE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-7" style="padding-left: 0px;">
                        <ng-select [items]="referenceTypeList" bindValue="id" bindLabel="text" formControlName="reference_id" (change)="referenceTypeChanged($event)" ></ng-select>
                    </div>
                </div>
            </div>
            <div class="col-sm-4" *ngIf="reportType === 'revenue'">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.CUSTOMER' | translate:param}}:
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <ng-select [items]="allCustomer" bindValue="id" bindLabel="text" (change)="customerTypeChanged($event)" ></ng-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row filter-row" style="margin-bottom: 15px;" *ngIf="reportType == 'agents'">
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.AGENT_LOCATION' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <ng-select [items]="agentList" bindValue="id" bindLabel="text" formControlName="location" (change)="locationChanged($event)" ></ng-select>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.BOOK_STAT' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <ng-select [items]="bookingStatusList" bindValue="id" bindLabel="text" formControlName="booking_status" (change)="bookingStatusChanged($event)" ></ng-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row filter-row">
            <div class="col-sm-4">
                <div class="row" *ngIf="reportType != 'guest' && reportType != 'discount'">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.F_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <!-- class="custom-width-datetime" -->
                        <datetime  [timepicker]="false" formControlName="fromDate"
                            [datepicker]="datepickerOpts" (ngModelChange)="toDataChange($event)"></datetime>
                        <!-- <div *ngIf="searchForm.controls['fromDate'].dirty && searchForm.controls['fromDate'].invalid" class="alert alert-danger">
                            <div *ngIf="searchForm.controls['fromDate'].errors?.required ">From date is required.</div>
                        </div> -->
                    </div>
                </div>

                <div class="row" *ngIf="reportType == 'discount'">
                    <div class="col-sm-6" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.F_CHECKING_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-6" style="padding-left: 0px;">
                        <!-- class="custom-width-datetime" -->
                        <datetime  [timepicker]="false" formControlName="fromDate"  (ngModelChange)="toDataChange($event)"
                            [datepicker]="datepickerOpts"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row" *ngIf="reportType != 'guest' && reportType != 'discount'">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.T_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <!-- class="custom-width-datetime" -->
                        <datetime  [timepicker]="false" formControlName="toDate"
                            [datepicker]="datepickerOpts"></datetime>
                        <!-- <div *ngIf="searchForm.controls['toDate'].dirty && searchForm.controls['toDate'].invalid" class="alert alert-danger">
                            <div *ngIf="searchForm.controls['toDate'].errors?.required ">To date is required.</div>
                        </div> -->
                    </div>
                </div>

                <div class="row" *ngIf="reportType == 'discount'">
                    <div class="col-sm-5" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'REPORT.T_CHECKING_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-7" style="padding-left: 0px;">
                        <!-- class="custom-width-datetime"  -->
                        <datetime [timepicker]="false" formControlName="toDate"
                            [datepicker]="datepickerOpts"></datetime>
                    </div>
                </div>
            </div>
            <!-- <div class="col-sm-4">
                <button type="reset" class="btn btn-primary pull-right">Reset</button>
            </div> -->
            <div class="col-sm-4 btn-margin">
                <button type="button" (click)="onReset()" style="margin-left: 5px;"
                    class="btn btn-danger pull-right">{{'REPORT.RESET' | translate:param}}</button>
                <button type="submit" class="btn btn-primary pull-right">{{'REPORT.SEARCH' | translate:param}}</button>
            </div>
        </div>
        <!-- Amount Summary -->
        <div class="row" *ngIf="this.data && this.data.length != 0 && reportType != 'guest' &&  reportType != 'discount'" style="margin-top: 15px;">
            <div class="col-sm-12 col-md-8 col-lg-4">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'REPORT.SUMM' | translate:param}}
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr *ngIf="reportType !== 'revenue'">
                            <td>
                                {{'REPORT.TOT_AMT' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{totalAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr *ngIf="reportType === 'checkout'">
                          <td>
                              {{'REPORT.TOT_ADV_AMT' | translate:param}} :
                          </td>
                          <td class="text-align">
                              <i class="fa fa-inr"></i>&nbsp;
                              <strong>
                                  {{totalAdvanceAmount | number: '1.2-2'}}
                              </strong>
                          </td>
                        </tr>
                        <tr *ngIf="reportType === 'revenue'">
                            <td>
                                {{'REPORT.AMOUNT' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{netAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr *ngIf="reportType === 'revenue'">
                            <td>
                                {{'REPORT.TOT_DIS' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{totalCustomDiscount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr *ngIf="reportType === 'revenue'">
                            <td>
                                {{'REPORT.TOT_AMT' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{netAmount - totalCustomDiscount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr *ngIf="reportType != 'revenue'">
                            <td>
                                {{'REPORT.TOT_DIS' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{totalDiscount + totalCustomDiscount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr *ngIf="reportType !== 'revenue'">
                            <td>
                                {{'REPORT.TOT_AMT_REC' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{totalAmountReceived | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr *ngIf="reportType !== 'revenue'">
                            <td>
                                {{'REPORT.TOT_PEN_AMT' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{totalPendingAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr *ngIf="reportType === 'cancelled'">
                            <td>
                                {{'REPORT.TOT_REF_AMT' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{totalRefundAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr *ngIf="reportType === 'booking'">
                            <td>
                                {{'REPORT.TOT_AVAI_ROOM' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <strong>
                                    {{freeRooms}}
                                </strong>
                            </td>
                        </tr>
                        <tr *ngIf="reportType !== 'revenue'">
                            <td>
                                {{'REPORT.TOT_REC' | translate:param}} :
                            </td>
                            <td class="text-align">
                                <strong>
                                    {{data.length}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="col-sm-12 col-md-8 col-lg-5">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'REPORT.SUMM_MODE' | translate:param}}
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr>
                            <td>
                                1) &nbsp; {{'REPORT.T_CASH_AMT' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{cashAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                2) &nbsp; {{'REPORT.T_CARD_AMT' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{cardAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                              3) &nbsp; {{'REPORT.T_BANK_AMT' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{bankAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                              4) &nbsp;  {{'REPORT.T_CHEQUE_AMT' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                               <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{chequeAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                             5) &nbsp; {{'REPORT.T_PAID_PANDING_AMOUNT' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                               <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{paidPandingAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                               &nbsp;  <strong>  {{'REPORT.T_AMT' | translate:param}} </strong>
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong> 
                                    {{totalAmounts | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <!-- <div *ngIf="this.data && reportType === 'revenue'" class="col-sm-4 float-sm-right">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            Search Parameters
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr>
                            <td>
                                Reference :
                            </td>
                            <td>
                                <strong>
                                    {{getSearchedReference()}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                From Date :
                            </td>
                            <td>
                                <strong>
                                    {{getSearchedFromDate() != 'N/A' ? (getSearchedFromDate() | date) : 'N/A'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                To Date :
                            </td>
                            <td>
                                <strong>
                                    {{getSearchedToDate() != 'N/A' ? (getSearchedToDate() | date) : 'N/A'}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div> -->
        </div>
        <div class="row">
            <div *ngIf="this.data && reportType === 'revenue'" class="col-sm-12 col-md-8 col-lg-4 float-sm-right">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'REPORT.SEARCH_PARAM' | translate:param}}
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr>
                            <td>
                                {{'REPORT.REF' | translate:param}} :
                            </td>
                            <td>
                                <strong>
                                    {{getSearchedReference()}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                {{'REPORT.F_DATE' | translate:param}} :
                            </td>
                            <td>
                                <strong>
                                    {{getSearchedFromDate() != 'N/A' ? (getSearchedFromDate() | date) : 'N/A'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                {{'REPORT.T_DATE' | translate:param}} :
                            </td>
                            <td>
                                <strong>
                                    {{getSearchedToDate() != 'N/A' ? (getSearchedToDate() | date) : 'N/A'}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </form>

    <div class="clearfix"></div>
    <!-- Table for All reports except Guest Checked-in Report -->
    <div *ngIf="reportType != 'guest' && reportType != 'revenue' && reportType != 'discount'" class="widget-body table-scroll">
        <div class="mt">
            <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable"
                [mfRowsOnPage]="25">
                <thead>
                    <tr>
                        <th *ngIf="reportType !== 'checkout'">
                            <mfDefaultSorter by="id">#</mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType == 'agents'">
                            <mfDefaultSorter by="bookingDate">{{'REPORT.BOOK_DATE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType != 'agents'">
                            <mfDefaultSorter by="room_no">{{'REPORT.ROOM_NO' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType === 'cancelled'">
                            <mfDefaultSorter>{{'REPORT.CANCE_BY' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType !== 'checkout'">
                            <mfDefaultSorter [by]="reservationID">{{'REPORT.RES_ID' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType == 'agents'">
                            <mfDefaultSorter [by]="agentReceiptNo">{{'REPORT.AGEN_RECEIP_NO' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType != 'agents'">
                            <mfDefaultSorter by="room_type">{{'REPORT.ROOM_TYP' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType != 'agents'">
                            <mfDefaultSorter by="guest_type">{{'REPORT.GUE_TYPE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="name">{{'REPORT.GUEST_NAME' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType == 'agents'">
                            <mfDefaultSorter by="check_in">{{'REPORT.CHE_IN' | translate:param}}</mfDefaultSorter>
                        </th>
                        <!-- <th>
                            <mfDefaultSorter [by]="checkOutDate">Guest Short Addrerss</mfDefaultSorter>
                        </th> -->
                        <th *ngIf="reportType !== 'agents' && reportType !== 'checkout'">
                            <mfDefaultSorter [by]="referenceName">{{'REPORT.REF_NAME' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType == 'cancelled'">
                            <mfDefaultSorter [by]="referenceName">{{'REPORT.AGENT_LOCATION' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType != 'agents'">
                            <mfDefaultSorter by="start">{{'REPORT.STAY_DURA' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="(reportType === 'booking') || (reportType === 'reservation')">
                            <mfDefaultSorter by="expected_check_in">{{'REPORT.EXPEC_CHECK_IN' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="(reportType === 'booking') || (reportType === 'reservation')">
                            <mfDefaultSorter by="expected_check_out">{{'REPORT.EXPEC_CHECK_OUT' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType != 'agents'">
                            <mfDefaultSorter [by]="pax">{{'REPORT.PAX' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType != 'checkin' && reportType != 'agents'">
                            <mfDefaultSorter [by]="billNo">{{'REPORT.BILL_N' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="total_amount">{{'REPORT.AMOUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType != 'checkin'">
                            <mfDefaultSorter by="discount">{{'REPORT.DISCOUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="reportType != 'checkin'">
                            <mfDefaultSorter>{{reportType == 'agents' ? ('REPORT.AGENT_AMT_RECEIVED'| translate:param) : ('REPORT.AMOUNT_RECEIVED' | translate:param)}}
                            </mfDefaultSorter>
                        </th>
                        <!-- <th *ngIf="reportType == 'agents'">
                            <mfDefaultSorter>{{'REPORT.UPDAT_PAY' | translate:param}}</mfDefaultSorter>
                        </th> -->
                        <th *ngIf="reportType == 'cancelled'">
                            <mfDefaultSorter by="refundable_amount">{{'REPORT.REF_AMT' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="(reportType == 'agents') || (reportType == 'pending-payments')">
                            <mfDefaultSorter>{{reportType == 'agents' ? ('REPORT.BALAN_AMT' | translate:param) : ('REPORT.REP_PENDING' | translate:param)}}
                            </mfDefaultSorter>
                        </th>
                        <th class="text-center" *ngIf="reportType === 'cancelled' || reportType === 'reservation'">
                            <mfDefaultSorter>{{'REPORT.STATUS' | translate:param}}</mfDefaultSorter>
                        </th>
                        <!-- <th *ngIf="reportType == 'checkout' || reportType == 'pending-payments'">
                            <mfDefaultSorter by="pavati_no">{{'REPORT.PAVATI_NO' | translate:param}}</mfDefaultSorter>
                        </th> -->
                        <th *ngIf="reportType != 'agents' && reportType !== 'checkout'">
                            <mfDefaultSorter>{{'REPORT.ADD_PROOF' | translate:param}}</mfDefaultSorter>
                        </th>
                        <!-- <th *ngIf="reportType == 'checkout'">
                            <mfDefaultSorter>Print Bill</mfDefaultSorter>
                        </th> -->
                        <th class="text-center">
                            <mfDefaultSorter>{{'REPORT.ACTION' | translate:param}}</mfDefaultSorter>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let ds of mf.data; let i = index;">
                        <td *ngIf="reportType !== 'checkout'">{{ ds.id }}</td>
                        <td class="uppercase" *ngIf="reportType == 'agents'">
                            <span class="fw-semi-bold">{{ds.booking_date ? ( ds.booking_date | date:'MMM d, y' ) :
                                '-'}}</span>
                        </td>
                        <td class="uppercase" *ngIf="reportType != 'agents'">
                            <span class="fw-semi-bold">{{ds.room_no}}</span>
                        </td>
                        <td class="uppercase" *ngIf="reportType === 'cancelled'">
                            <span class="fw-semi-bold">{{ds.first_name}}</span>
                        </td>
                        <td *ngIf="reportType !== 'checkout'" class="capitalize">{{ds.reg_id}}</td>
                        <td class="capitalize" *ngIf="reportType == 'agents'" style="text-align: center;">{{ds.agent_receipt_no}}</td>
                        <td *ngIf="reportType != 'agents'">{{ds.room_type}}</td>
                        <td *ngIf="reportType != 'agents'" class="capitalize">{{ds.guest_type}}</td>
                        <td class="capitalize">{{ds.name | uppercase}}</td>
                        <td class="capitalize" *ngIf="reportType == 'agents'">{{ds.check_in ? (ds.check_in | date:'MMM
                            d,
                            y') : '-'}}</td>
                        <td *ngIf="reportType !== 'agents' && reportType !== 'checkout'" class="capitalize">
                            {{ds.reference_name | uppercase}}</td>
                        <td *ngIf="reportType == 'cancelled'" class="capitalize">{{getAgentLocation(ds.agent_id)}}</td>
                        <td *ngIf="reportType != 'agents'" class="capitalize">
                            {{ds.start ? (ds.start | date:'d MMM') : '-'}} - {{ds.end ? (ds.end | date:'d MMM') : '-'}}
                        </td>

                        <td *ngIf="(reportType === 'booking') || (reportType === 'reservation')" class="capitalize">
                            {{ds.expected_check_in ? (ds.expected_check_in | date:'h:mm a') : '-'}}
                        </td>
                        <td *ngIf="(reportType === 'booking') || (reportType === 'reservation')" class="capitalize">
                            {{ds.expected_check_out ? (ds.expected_check_out | date:'h:mm a') : '-'}}
                        </td>
                        <td *ngIf="reportType != 'agents'" class="capitalize">
                            {{ds.adult ? ds.adult + ' (AD)' : '-'}} {{ds.child ? ds.child + ' (CH)' : '-'}}
                        </td>
                        <td *ngIf="reportType != 'checkin' && reportType != 'agents'">
                            <span class="capitalize" *ngIf="ds.bill_no != null">
                                {{ds.bill_no}}
                            </span>
                            <span class="capitalize bgcolor" *ngIf="ds.bp_bill_no.length > 0" [ngClass]="{'bgcolor':ds.bp_bill_no.length > 1}"
                                tooltip="{{ds.bp_bill_no}}">
                                {{ds.bp_bill_no[0]}}
                            </span>
                        </td>
                        <td class="capitalize" style="text-align: right;padding-right: 25px;">
                            <i *ngIf="ds.total_amount" class="fa fa-inr"></i>&nbsp;{{round(getTotalAmount(ds))}}
                        </td>
                        <td class="capitalize" *ngIf="reportType != 'checkin'" style="text-align: right;padding-right: 25px;">
                            <i *ngIf="ds.discount" class="fa fa-inr"></i>&nbsp;{{round(getTotalDiscount(ds))}}
                        </td>
                        <td class="capitalize" *ngIf="reportType != 'checkin'" style="text-align: right;padding-right: 25px;">
                            <i *ngIf="ds.amount_paid" class="fa fa-inr"></i>&nbsp;{{round(getAmountReceived(ds))}}
                        </td>
                        <!-- <td class="capitalize" *ngIf="reportType === 'agents'">
                            <i *ngIf="ds.total_amount" class="fa fa-inr"></i>&nbsp;{{round(getUpdatedPayments(ds))}}
                        </td> -->
                        <td class="capitalize" *ngIf="reportType === 'cancelled'" style="text-align: right;padding-right: 25px;">
                            <i *ngIf="ds.refundable_amount" class="fa fa-inr"></i>&nbsp;{{ds.refundable_amount}}
                        </td>
                        <td class="capitalize" style="text-align: right;padding-right: 25px;" *ngIf="(reportType === 'agents') || (reportType == 'pending-payments')">
                            <i *ngIf="ds.total_amount" class="fa fa-inr"></i>&nbsp;{{round(getBalanceAmount(ds))}}
                        </td>
                        <td class="capitalize text-center"
                            *ngIf="reportType === 'cancelled' || reportType === 'reservation'">
                            <label for="" class="label" [ngClass]="{
                                'label-warning': ds.current_status == 'checkout',
                                'label-success': ds.current_status == 'checkin',
                                'label-primary': ds.current_status == 'reserved',
                                'label-danger': ds.current_status == 'cancelled',
                                'label-light': (ds.current_status == 'cancelled' && ds.is_no_show)}">
                                {{(ds.is_no_show ? 'No show' : ds.current_status)}}
                            </label>
                        </td>
                        <!-- <td class="capitalize" *ngIf="reportType == 'checkout' || reportType == 'pending-payments'">
                            {{ds.pavati_no}}
                        </td> -->
                        <td class="capitalize width-100 text-center"
                            *ngIf="reportType != 'agents' && reportType !== 'checkout'">
                            <button *ngIf="ds.mimetype" (click)="canShow = false;viewDocumentProof(ds)"
                                class="btn btn-sm btn-default" tooltip="{{'REPORT.VIEW_DOC_PROOF' | translate:param}}"
                                placement="left" style="width: 130px;">
                                <i class="fa fa-image"></i> &nbsp;{{'REPORT.VIEW_DOC' | translate:param}}
                            </button>
                            <button *ngIf="!ds.mimetype" class="btn btn-sm btn-default"
                                tooltip="{{'REPORT.NO_DOC_PROOF' | translate:param}}" placement="left"
                                style="width: 130px; background: #a9a9a9;">
                                &nbsp;{{'REPORT.NO_DOC' | translate:param}}
                            </button>
                        </td>
                        <td class="btn-group" [ngClass]="{'btn-group': (reportType === 'checkout') || (reportType === 'reservation') || (reportType === 'pending-payments'),
                                'capitalize': reportType != 'checkout',
                                'width-150': reportType != 'checkout',
                                'text-center': reportType != 'checkout'}">
                            <button *ngIf="reportType == 'checkout'" (click)="viewInvoice(ds)"
                                class="btn btn-sm btn-default" tooltip="{{'REPORT.VIEW_IN_VOICE' | translate:param}}"
                                placement="left">
                                {{'REPORT.IN_VOICE' | translate:param}}
                            </button>
                            <button (click)="viewDetails(ds)" class="btn btn-sm btn-default"
                                tooltip="{{'REPORT.VIEW_DE' | translate:param}}" placement="left">
                                {{'REPORT.DETAILS' | translate:param}}
                            </button>
                            <button *ngIf="ds.note && ds.note !== 'null' && ds.note.length >= 0"
                                class="btn btn-sm btn-default" tooltip="{{ds.note[0].message}}" placement="left"
                                style="background: #a9a9a9;">
                                &nbsp;{{'VIEW_BOOKI_DETAI.NOTES' | translate:param}}
                            </button>

                            <button *ngIf="reportType === 'pending-payments'" (click)="updatePendingPayments(ds)"
                                class="btn btn-sm btn-default" tooltip="{{'REPORT.UP_PAY' | translate:param}}"
                                placement="left">
                                {{'REPORT.PAY' | translate:param}}
                            </button>
                            <button *ngIf="reportType === 'reservation' || reportType === 'checkout'"
                                (click)="policeInquiry(ds)" class="btn btn-sm btn-default"
                                tooltip="{{'REPORT.POLI_INQ' | translate:param}}" placement="left">
                                {{'REPORT.P.I' | translate:param}}
                            </button>
                        </td>
                    </tr>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                        <td colspan="100">
                            {{'REPORT.NO MATCHES' | translate:param}}
                        </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                        <td class="text-danger" colspan="100">
                            {{'REPORT.PERMISSION_DENIED' | translate:param}}
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="100">
                            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- Table for Guest Checked-in Report -->
    <div *ngIf="reportType == 'guest'" class="widget-body table-scroll">
        <div class="mt">
            <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable"
                [mfRowsOnPage]="25">
                <thead>
                    <tr>
                        <th>
                            <mfDefaultSorter by="id">#</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="title">{{'REPORT.ROOM_NO' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter [by]="guestName">{{'REPORT.GUEST_NAME' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="start">{{'REPORT.STAY_DURA' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="name">{{'REPORT.ROOM_TYP' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="c_name">{{'REPORT.GUE_TYPE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="unique_booking_id">{{'REPORT.RES_ID' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter [by]="referenceName">{{'REPORT.REF_NAME' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter>{{'REPORT.ADD_PROOF' | translate:param}}</mfDefaultSorter>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let ds of mf.data; let i = index;">
                        <td>{{ i + 1 }}</td>
                        <td class="uppercase">
                            <span class="fw-semi-bold">{{ds.title}}</span>
                        </td>
                        <td class="capitalize">{{ds.guests.name | uppercase}}</td>
                        <td class="capitalize">
                            {{ds.start ? (ds.start | date:'d MMM') : '-'}} - {{ds.end ? (ds.end | date:'d MMM') : '-'}}
                        </td>
                        <td>{{ds.name}}</td>
                        <td class="capitalize">{{ds.c_name}}</td>
                        <td class="capitalize">{{(ds.unique_booking_id)}}</td>
                        <td>
                            {{ds.reference_name | uppercase}}
                        </td>
                        <td class="capitalize width-100 text-center">
                            <button *ngIf="ds.guests.mimetype" (click)="canShow = false;viewDocumentProof(ds.guests)"
                                class="btn btn-sm btn-default" tooltip="{{'REPORT.VIEW_DOC_PROOF' | translate:param}}"
                                placement="left" style="width: 130px;">
                                <i class="fa fa-image"></i> &nbsp;{{'REPORT.VIEW_DOC' | translate:param}}
                            </button>
                            <button *ngIf="!ds.guests.mimetype" class="btn btn-sm btn-default"
                                tooltip="{{'REPORT.NO_DOC_PROOF' | translate:param}}" placement="left"
                                style="width: 130px; background: #a9a9a9;">
                                &nbsp;{{'REPORT.NO_DOC' | translate:param}}
                            </button>
                        </td>
                    </tr>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                        <td colspan="100">
                            {{'REPORT.NO MATCHES' | translate:param}}
                        </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                        <td class="text-danger" colspan="100">
                            {{'REPORT.PERMISSION_DENIED' | translate:param}}
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="100">
                            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- Table for Revenue Report -->
    <div *ngIf="reportType == 'revenue'" class="widget-body table-scroll">
        <div class="mt">
            <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable"
                [mfRowsOnPage]="25">
                <thead>
                    <tr *ngIf="selectedCustomer.cust_id == ''">
                        <th>
                            <mfDefaultSorter by="id">#</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="title">{{'REPORT.REF-NAME' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="level1">
                            <mfDefaultSorter by="start">{{'REPORT.GUE_TYPE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th *ngIf="level1 && level2">
                            <mfDefaultSorter [by]="referenceName">{{'REPORT.GUEST_NAME' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="level1 && level2">
                            <mfDefaultSorter [by]="referenceName">{{'REPORT.NOTE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th class="text-center">
                            <mfDefaultSorter [by]="guestName">{{'REPORT.COUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="c_name">{{'REPORT.AMOUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="name">{{'REPORT.DISCOUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="c_name">{{'REPORT.TOT_AMT' | translate:param}}<code>*</code>
                            </mfDefaultSorter>
                        </th>

                        <th *ngIf="level1">
                            <mfDefaultSorter by="unique_booking_id">{{'REPORT.STAY_PERI' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                    </tr>
                    <tr *ngIf="selectedCustomer.cust_id != ''">
                        <th>
                            <mfDefaultSorter by="id">#</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter [by]="referenceName">{{'REPORT.GUEST_NAME' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <!-- <th>
                            <mfDefaultSorter by="start">{{'REPORT.GUE_TYPE' | translate:param}}</mfDefaultSorter>
                        </th> -->
                        <th *ngIf="level1 && level2">
                            <mfDefaultSorter [by]="referenceName">{{'REPORT.GUEST_NAME' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="level1 && level2">
                            <mfDefaultSorter [by]="referenceName">{{'REPORT.NOTE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th class="text-center">
                            <mfDefaultSorter *ngIf="selectedCustomer.cust_id == ''" [by]="guestName">
                                {{'REPORT.COUNT' | translate:param}}</mfDefaultSorter>
                            <mfDefaultSorter *ngIf="selectedCustomer.cust_id != ''" [by]="guestName">Booking Id
                            </mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="c_name">{{'REPORT.AMOUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="name">{{'REPORT.DISCOUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="c_name">{{'REPORT.TOT_AMT' | translate:param}}<code>*</code>
                            </mfDefaultSorter>
                        </th>
                        <th *ngIf="level1">
                            <mfDefaultSorter by="unique_booking_id">{{'REPORT.STAY_PERI' | translate:param}}
                            </mfDefaultSorter>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <ng-template ngFor let-ds [ngForOf]="mf.data" let-i="index">
                        <!-- *ngFor="let ds of mf.data; let i = index;" -->
                        <tr (click)="ds.checked=!ds.checked; getGuestTypeList(ds);" class="reference-level">
                            <td>{{ i + 1 }}</td>
                            <td class="uppercase">
                                <span class="fw-semi-bold"
                                    *ngIf="selectedCustomer.cust_id == ''">{{ds.reference_name}}</span>
                                <span class="fw-semi-bold" *ngIf="selectedCustomer.cust_id != ''">{{ds.name}}</span>
                            </td>
                            <td *ngIf="level1">-</td>
                            <td *ngIf="level2" class="capitalize">-</td>
                            <td *ngIf="level2" class="capitalize">-</td>
                            <td class="capitalize text-center">{{ds.booking}}</td>
                            <td>
                                <i class="fa fa-inr"></i>
                                <!-- {{(ds.total_amount ? ds.total_amount : 0) + (ds.earlycheckincharge ? ds.earlycheckincharge : 0)}} -->
                                {{(ds.net_amount ? ds.net_amount : 0)}}
                            </td>
                            <td class="capitalize">
                                <i class="fa fa-inr"></i>
                                <!-- {{(ds.discount ? ds.discount : 0)}} -->
                                {{(ds.custom_discount ? ds.custom_discount : 0)}}
                            </td>
                            <td>
                                <i class="fa fa-inr"></i>
                                {{(ds.net_amount ? ds.net_amount : 0) -  (ds.custom_discount ? ds.custom_discount : 0)}}
                            </td>
                            <td *ngIf="level1">-</td>
                        </tr>
                        <ng-template [ngIf]="ds.checked">
                            <ng-template ngFor let-gts [ngForOf]="ds.guestTypesList" let-j="index">
                                <tr class="guest-type-level" tooltip="{{'REPORT.GUE_TYP_LIST' | translate:param}}"
                                    (click)="gts.checked = !gts.checked; getGuestList(ds.reference_id, gts);">
                                    <td>{{ j + 1 }}</td>
                                    <td class="uppercase">
                                        <span class="fw-semi-bold">-</span>
                                    </td>
                                    <td class="capitalize">
                                    <span *ngIf="gts.customer_id != 4">{{gts.c_name}}</span>
                                    <!-- As per Dipak sir suggestion -->
                                    <span *ngIf="gts.customer_id === 4">FREE GUEST</span>
                                  </td>
                                    <td *ngIf="level2" class="capitalize">-</td>
                                    <td *ngIf="level2" class="capitalize">-</td>
                                    <td class="capitalize text-center">{{gts.booking}}</td>
                                    <td>
                                        <i class="fa fa-inr"></i>
                                        <!-- {{(gts.total_amount ? gts.total_amount : 0) + (gts.earlycheckincharge ? gts.earlycheckincharge : 0)}} -->
                                        {{(gts.net_amount ? gts.net_amount : 0)}}
                                    </td>
                                    <td class="capitalize">
                                        <i class="fa fa-inr"></i>
                                        <!-- {{(gts.discount ? gts.discount : 0)}} -->
                                        {{(gts.custom_discount ? gts.custom_discount : 0)}}
                                    </td>
                                    <td>
                                        <i class="fa fa-inr"></i>
                                        {{(gts.net_amount ? gts.net_amount : 0) -  (gts.custom_discount ? gts.custom_discount : 0)}}
                                    </td>
                                    <td class="capitalize">-</td>
                                </tr>
                                <ng-template [ngIf]="ds.checked && gts.checked">
                                    <tr class="guest-level" tooltip="{{'REPORT.GUE_LIST' | translate:param}}"
                                        *ngFor="let item of gts.guestList; let k = index;">
                                        <td>{{ k + 1 }}</td>
                                        <td class="uppercase">
                                            <!-- <span class="fw-semi-bold">{{item.reference_name}}</span> -->
                                            <span class="fw-semi-bold">-</span>
                                        </td>
                                        <td class="capitalize">-</td>
                                        <td *ngIf="level2" class="capitalize">
                                            {{item.name}}
                                        </td>
                                        <td *ngIf="level2" class="capitalize">
                                            <span class="revenue-report-note"
                                                *ngFor="let note of item.note; let i = index;">
                                                {{i + 1}}.&nbsp;&nbsp;{{getNotes(note.message) ? getNotes(note.message) : '-'}}
                                                <br>
                                            </span>
                                            <span *ngIf="item.note.length == 0">&nbsp;&nbsp;-</span>
                                        </td>
                                        <td class="capitalize text-center">1</td>
                                        <td>
                                            <i class="fa fa-inr"></i>
                                            <!-- {{(item.total_amount ? item.total_amount : 0) + (item.earlycheckincharge ? item.earlycheckincharge : 0)}} -->
                                            {{(item.net_amount ? item.net_amount : 0)}}
                                        </td>
                                        <td class="capitalize">
                                            <i class="fa fa-inr"></i>
                                            <!-- {{(item.discount ? item.discount : 0)}} -->
                                            {{(item.custom_discount ? item.custom_discount : 0)}}
                                        </td>
                                        <td>
                                            <i class="fa fa-inr"></i>
                                            {{(item.net_amount ? item.net_amount : 0) -  (item.custom_discount ? item.custom_discount : 0)}}
                                        </td>
                                        <td class="capitalize">{{item.start ? (item.start | date:'d MMM') : '-'}} -
                                            {{item.end ? (item.end | date:'d MMM') : '-'}}</td>
                                    </tr>
                                </ng-template>
                            </ng-template>
                        </ng-template>
                    </ng-template>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                        <td colspan="100">
                            {{'REPORT.NO MATCHES' | translate:param}}
                        </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                        <td class="text-danger" colspan="100">
                            {{'REPORT.PERMISSION_DENIED' | translate:param}}
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="100">
                            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

   <!-- Table for discount reports -->
   <div *ngIf="reportType == 'discount'" class="widget-body table-scroll">
    <div class="mt">
        <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable"
            [mfRowsOnPage]="25">
            <thead>
                <tr>
                    <th>
                       {{'REPORT.ROOM_NO' | translate:param}}
                    </th>
                    <th>
                        <mfDefaultSorter by="room_type">{{'REPORT.ROOM_TYP' | translate:param}}</mfDefaultSorter>
                    </th>
                    <th>
                        <mfDefaultSorter by="name">{{'REPORT.GUEST_NAME' | translate:param}}</mfDefaultSorter>
                    </th>
                    <th>
                        <mfDefaultSorter by="guest_type">{{'REPORT.GUE_TYPE' | translate:param}}</mfDefaultSorter>
                    </th>
                    <th>
                        <mfDefaultSorter [by]="referenceName">{{'REPORT.REF_NAME' | translate:param}}</mfDefaultSorter>
                    </th>
                    <th>
                        <mfDefaultSorter by="start">{{'REPORT.STAY_DURA' | translate:param}}</mfDefaultSorter>
                    </th>
                    <th>
                        <mfDefaultSorter [by]="pax">{{'REPORT.PAX' | translate:param}}</mfDefaultSorter>
                    </th>
                    <th>
                        <mfDefaultSorter by="total_amount">{{'REPORT.AMOUNT' | translate:param}}</mfDefaultSorter>
                    </th>
                    <th>
                        {{'REPORT.DIS_AMOUNT' | translate:param}}
                    </th>
                    <th>
                        {{'REPORT.TOTAL_DISCOUNT' | translate:param}}
                    </th>
                    <th>
                        {{'REPORT.ROOM_CHARGE_DISCOUNT' | translate:param}}
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let ds of mf.data; let i = index;">
                    <td>{{ds.room_no}}</td>
                    <td >{{ds.room_type}}</td>
                    <td class="capitalize">{{ds.name | uppercase}}</td>
                    <td  class="capitalize">
                     <button (click)="requestBookingGuestTypeChange(ds)" class="btn btn-sm btn-default">
                        {{ds.guest_type}}
                      </button>
                    </td>
                    <td class="capitalize">
                        <span>{{ds.reference_name | uppercase}}</span>
                        <span *ngIf="ds.message && ds.message !== ' '"
                             tooltip="{{ds.message}}" placement="right">
                            <i class="fa fa-info-circle" aria-hidden="true"></i>
                        </span>
                    </td>
                    <td  class="capitalize">
                        {{ds.start ? (ds.start | date:'d MMM') : '-'}} - {{ds.end ? (ds.end | date:'d MMM') : '-'}}
                    </td>
                    <td class="capitalize">
                        {{ds.adult ? ds.adult + ' (AD)' : '-'}} {{ds.child ? ds.child + ' (CH)' : '-'}}
                    </td>
                    <td class="capitalize" style="text-align: right;padding-right: 25px;">
                        <i *ngIf="ds.total_amount" class="fa fa-inr"></i>&nbsp;{{ds.net_amount}}
                    </td>
                    <td  class="capitalize" style="width: 115px;">
                        <div class="row">
                        <input type="text" style="width: 68px;text-align: right;" [id]="i" [name]="i"  [(ngModel)]="ds.custom_discount_per_room" class="form-control" placeholder="Enter Amt" #nameControl="ngModel" pattern="^[0-9]\d*$"  [disabled]="ds.net_amount <= 0">
                        <button (click)="addCustomeDiscount(ds)"  tooltip="Click to add discount" placement="right" style="width: 30px;margin-left: 4px;" class="btn btn-sm btn-primary" [disabled]="ds.custom_discount_per_room  <= 0 || nameControl.pristine || nameControl.invalid ">
                            <i class="fa fa-save"></i>
                         </button>
                         </div>
                    </td>
                    <td style="text-align: right;">
                        {{ds?.totalDiscountCharges}}
                    </td>
                    <td style="text-align: right;">
                        {{ds?.totalChargesAfterDiscount}}
                    </td>
                </tr>
                <tr *ngIf="canViewRecords && mf.data.length === 0">
                    <td colspan="100">
                        {{'REPORT.NO MATCHES' | translate:param}}
                    </td>
                </tr>
                <tr *ngIf="!canViewRecords">
                    <td class="text-danger" colspan="100">
                        {{'REPORT.PERMISSION_DENIED' | translate:param}}
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="100">
                        <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
   </div>

   <!-- <button type="button" *ngIf="data && data.length > 5 && reportType == 'discount'" [routerLink]="['/admin/shift']" style="margin-left: -19px;margin-top: 25px;color: white;background-color: #c54e4e;" class="btn btn-sm btn-primary pull-left">Go To Home</button> -->
</section>

<!-- Document Viewer -->
<div bsModal #imageModal="bs-modal" id="imageModal" class="modal fade" tabindex="-1" role="dialog"
    [config]="{backdrop: 'static',keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content py-1">
            <!-- <img *ngIf="image?.isImage" class="image-modal-view" src="{{image?.guest_document}}" alt=""> -->
            <ng-container *ngFor="let imageUrl of imageUrls">
                <img   class="image-modal-view px-1 pb-1" [src]="imageUrl" alt="">
            </ng-container>
            <!-- <pdf-viewer *ngIf="!image?.isImage" src="{{image?.guest_document}}" [render-text]="true" style="display: block;"> -->
            <!-- </pdf-viewer> -->
            <div class="modal-footer">
                <div class="btn-group">
                    <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="closeModal()">
                        {{'REPORT.CLOSE' | translate:param}}
                    </button>
                    <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="PrintDocument()">
                        {{'REPORT.PRINT' | translate:param}}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- In-voice View -->
<div bsModal #invoiceModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
    [config]="{backdrop: 'static',keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="max-width: 842px;">
            <div *ngIf="invoiceDataForView" #invoiceHtml>
                <div class="widget-body invoice-view" *ngFor="let item of invoiceDataForView.bookingPayment">
                        <div class="mt billing-guest">
                            <div class="invoice-header-image">
                            </div>
                            <div class="row invoice-view-bootstrap-convertion background-logo-image">
                                <div class="col-sm-12 invoice-view-bootstrap-convertion">
                                    <div class="row invoice-view-bootstrap-convertion">
                                        <div class="col-sm-12 invoice-view-bootstrap-convertion">
                                            <p class="bill-no">
                                                <strong>{{'REPORT.BILL_NO' | translate:param}} :
                                                    {{invoiceDataForView.booking.bill_no == null ? item.bill_no : invoiceDataForView.booking.bill_no}}</strong>
                                                <span style="float:right; display: inline-block;">
                                                    <strong>
                                                        {{'REPORT.DATE' | translate:param}} :
                                                        <!-- {{item.createdAt | date:'dd/MM/y'}} -->
                                                        {{ ( item.payment_date | date:'dd/MM/y')   }}
                                                    </strong>
                                                </span>
                                            </p>
                                            <div class="customer-name">
                                                <div>
                                                    <span>
                                                        {{'REPORT.NAME' | translate:param}} :
                                                    </span>
                                                    <span>
                                                        <!-- <% if(bookingPayData.name == null){ %>
                                                            <%=bookingRoom[0].guest.name %>
                                                            <% }else{ %>
                                                            <%= bookingPayData.name %> -->
                                                        <span class="text-capitalize">
                                                            {{item.name == null ? invoiceDataForView.booking.guest.name : item.name}}
                                                            <!-- {{invoiceDataForView.booking['is_alternate_bg_active'] ?
                                                        invoiceDataForView.booking['alternate_billing_guest'] :
                                                        invoiceDataForView.booking['guest.name']}} -->
                                                        </span>
                                                    </span>

                                                </div>
                                            </div>
                                            <div class="customer-name">
                                                <span>
                                                    City/Village :
                                                </span>
                                                <span>
                                                    <span class="text-capitalize">
                                                        <!-- {{invoiceDataForView.booking['guest.city']}} -->
                                                        {{item.city == null ? invoiceDataForView.booking.guest.city : item.city}}
                                                    </span>
                                                </span>
                                            </div>
                                            <div class="customer-name ">
                                                <span class="uppercase">
                                                    {{'REPORT.PAN_NO' | translate:param}} :
                                                    {{item.panno == "NA" ? (item.payment_verification_id == null ? invoiceDataForView.booking.payment_verification_id : item.payment_verification_id) : item.panno}}
                                                    <!-- {{invoiceDataForView.booking['guest.city']}} -->
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt invoice-body">
                                        <table class="table table-condence no-m-b">
                                            <thead>
                                                <tr>
                                                    <th>
                                                        {{'REPORT.SR_NO' | translate:param}}
                                                    </th>
                                                    <th style="text-align: center;">
                                                        {{'REPORT.SUB' | translate:param}}
                                                    </th>
                                                    <th>
                                                        {{'REPORT.AMT' | translate:param}}
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr *ngFor="let fund of invoiceDataForView.fundsList; let i = index;">
                                                    <td>
                                                        {{ i + 1 }}
                                                    </td>
                                                    <td>
                                                        {{ fund.name }}
                                                    </td>
                                                    <td *ngIf="fund.name == 'Bhet'" style="text-align: right !important;padding-right: 10%;">
                                                        <div class="image rupee-icon-for-amount"></div>
                                                        {{invoiceDataForView.bookingPayment.length == 0 ? fund.amount : item.amount}}
                                                    </td>
                                                    <td *ngIf="fund.name !== 'Bhet'" style="text-align: right !important;padding-right: 10%;">
                                                        <div class="image rupee-icon-for-amount"></div>
                                                        0
                                                    </td>
                                                </tr>
                                                <tr class="invoice-total-amount">
                                                    <td width="width: 10%;">
                                                    </td>
                                                    <td style="width: 40%;">
                                                        {{'REPORT.TOTAL' | translate:param}}
                                                    </td>
                                                    <td style="text-align: right !important;padding-right: 10%;">
                                                        <div class="image rupee-icon-for-amount"></div>
                                                        {{item.amount}}
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dharamshala-guide-lines">
                            <br />

                            <p class="bill-no" style="text-align: center;">

                                <span class="text-capitalize" style="display: inline-block;" *ngIf="item.payment_mode == 0">
                                    <strong>
                                        Cash Mode
                                    </strong><br>
                                </span>
                                <span class="text-capitalize" style="display: inline-block;" *ngIf="item.payment_mode == 1">
                                    <strong>
                                        Card Mode
                                    </strong><br>
                                    <strong>
                                        Card No:
                                        {{item.payment_reciept_number}}
                                    </strong><br>
                                    <strong>
                                        Card Charge :
                                        {{item.card_charges}}
                                    </strong><br>
                                </span>
                                <span class="text-capitalize" style="display: inline-block;" *ngIf="item.payment_mode == 2">
                                    <strong>
                                        Cheque Mode
                                    </strong><br>
                                    <strong>
                                        Cheque No : :
                                        {{item.bank_cheque_no}}
                                    </strong><br>
                                    <strong>
                                        Bank Name :
                                        {{item.bank_name}}
                                    </strong><br>
                                </span>
                                <span class="text-capitalize" style="display: inline-block;" *ngIf="item.payment_mode == 3">
                                    <strong>
                                        Bank Mode
                                    </strong><br>
                                    <strong>
                                        Trans No:
                                        {{item.bank_cheque_no}}
                                    </strong><br>
                                    <strong>
                                        Bank Name :
                                        {{item.bank_name}}
                                    </strong><br>
                                </span>

                            </p>
                            <p class="bill-no" style="text-align: center;">


                                <span class="text-capitalize" style="display: inline-block;">
                                    <strong>
                                        Total :
                                        {{item.amount | numberToWords}}
                                        only
                                    </strong>
                                </span>

                            </p>
                            <br />
                            <p class="bill-no">
                                <strong>{{'REPORT.PAN_NO' | translate:param}}: **********
                                    <!-- {{invoiceDataForView.booking.payment_verification_id ? invoiceDataForView.booking.payment_verification_id : 'N/A'}} -->
                                </strong>
                                <span style="float:right;display: inline-block;">
                                    <strong>
                                        {{'REPORT.RECE_SIGN' | translate:param}}
                                    </strong>
                                </span>
                            </p><br />
                            <p *ngIf="item.payment_mode != 0">
                                "As per FORM #10AC order for provisional approval of the exemption Under Section 80 G (5) of The Income Tax Act, 1961 bearing provisional approval # **********F20214 dated 28/05/2021 is being granted from AY 2022 23 (FY 2021 22) to AY 2026 27 (FY 2025 26)."
                            </p>
                            <p class="receipt-footer-text">|| Jay Shree Swaminarayan ||</p>
                        </div>
                        <hr style="border-top: 1px dashed rgb(0, 0, 0);margin-top: 2.6rem;">
                        <!-- <div class="mt billing-guest">
                        <div class="invoice-header-image">
                        </div>
                        <div class="row invoice-view-bootstrap-convertion background-logo-image">
                            <div class="col-sm-12 invoice-view-bootstrap-convertion">
                                <div class="row invoice-view-bootstrap-convertion">
                                    <div class="col-sm-12 invoice-view-bootstrap-convertion">
                                        <p class="bill-no">
                                            <strong>{{'REPORT.BILL_NO' | translate:param}} :
                                                {{invoiceDataForView.booking.bill_no}}</strong>
                                            <span style="float:right;display: inline-block;">
                                                <strong>
                                                    {{'REPORT.DATE' | translate:param}} :
                                                    {{ item.is_pending_payment == true ? ( item.payment_date | date:'dd/MM/y') : (invoiceDataForView.bookingRoom[0].check_out | date:'dd/MM/y' )  }}
                                                </strong>
                                            </span>
                                        </p>
                                        <div class="customer-name">
                                            <div>
                                                <span>
                                                    {{'REPORT.NAME' | translate:param}} :
                                                </span>
                                                <span>
                                                    <span class="text-capitalize">
                                                        {{invoiceDataForView.booking['is_alternate_bg_active'] ?
                                                        invoiceDataForView.booking['alternate_billing_guest'] :
                                                        invoiceDataForView.booking['guest.name']}}
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="customer-name">
                                            <span>
                                                City/Village :
                                            </span>
                                            <span>
                                                <span class="text-capitalize">
                                                    {{invoiceDataForView.booking['guest.city']}}
                                                </span>
                                            </span>

                                        </div>
                                    </div>
                                </div>
                                <div class="mt invoice-body">
                                    <table class="table table-condence no-m-b">
                                        <thead>
                                            <tr>
                                                <th>
                                                    {{'REPORT.SR_NO' | translate:param}}
                                                </th>
                                                <th>
                                                    {{'REPORT.SUB' | translate:param}}
                                                </th>
                                                <th>
                                                    {{'REPORT.AMT' | translate:param}}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let fund of invoiceDataForView.fundsList; let i = index;">
                                                <td>
                                                    {{ i + 1 }}
                                                </td>
                                                <td>
                                                    {{ fund.name }}
                                                </td>
                                                <td>
                                                    <div class="image rupee-icon-for-amount"></div>
                                                    {{ fund.amount }}
                                                </td>
                                            </tr>
                                            <tr class="invoice-total-amount">
                                                <td width="width: 10%;">
                                                </td>
                                                <td style="width: 40%;">
                                                    {{'REPORT.TOTAL' | translate:param}}
                                                </td>
                                                <td>
                                                    <div class="image rupee-icon-for-amount"></div>
                                                    {{ invoiceDataForView.paidAmount }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div> -->
                        <!-- <div class="dharamshala-guide-lines">
                        <br />
                        <p class="bill-no" style="text-align: center;">


                            <span class="text-capitalize" style="display: inline-block;">
                                <strong>
                                    Total : {{ totalAmaunt | numberToWords}} only
                                </strong>
                            </span>

                        </p>
                        <br />
                        <p class="bill-no">
                            <strong>{{'REPORT.PAN_NO' | translate:param}}: **********

                            </strong>
                            <span style="float:right;display: inline-block;">
                                <strong>
                                    {{'REPORT.RECE_SIGN' | translate:param}}
                                </strong>
                            </span>
                        </p>
                        <br />
                        <p>
                            "As per FORM #10AC order for provisional approval of the exemption Under Section 80 G (5) of The Income Tax Act, 1961 bearing provisional approval # **********F20214 dated 28/05/2021 is being granted from AY 2022 23 (FY 2021 22) to AY 2026 27 (FY 2025 26)."
                        </p>
                        <p class="receipt-footer-text">|| Jay Shree Swaminarayan||</p>
                    </div> -->
                </div>
            </div>
            <div class="modal-footer">
                <div class="btn-group">
                    <button type="button" class="btn btn-md btn-inverse" aria-label="Close"
                        (click)="invoiceModal.hide()">
                        {{'REPORT.OK' | translate:param}}
                    </button>
                    <button class="btn btn-md btn-inverse" aria-label="Close"
                        (click)="getHtml(invoiceDataForView.booking.id)">
                        {{'REPORT.GEN_PDF' | translate:param}}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Details Modal -->
<div bsModal #viewDetailsModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
    [config]="{backdrop: 'static', keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <view-report-details *ngIf="viewComp" [isPoliceInquiry]="isPoliceInquiry" [reportType]="reportType"
                [selectedData]="selectedReport" [referenceTypeList]="referenceTypeList" (goBack)="closeComp($event)">
            </view-report-details>
        </div>
    </div>
</div>

<!-- Change Customer Type Modal (Before CHECKOUT) -->
<div class="modal fade" bsModal #changeCustomerTypeModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Change Guest Type - {{guessName | uppercase}}
      </div>
      <div class="modal-body">
        <form [formGroup]="customerTypeChangeForm" (ngSubmit)="checkoutAfterCustomerChange()">
          <div class="change-customer-type">
            <div class="row extra-padding">
              <div class="col-sm-4">{{'RESERVATIONS.SEL_GUE_TYP' | translate:param }}:</div>
              <div class="col-sm-8">
                <ng-select [items]="customerTypeListMain" bindValue="id" bindLabel="text" formControlName="customer_id" [(ngModel)]="selectedCustomerType" [disabled]="referenceUserForDis && !referenceUserForDis?.allow_to_update_customer_type"
                  (change)="checkoutCustomerTypeChanged($event)" [(ngModel)]="bookingTypeOptions"></ng-select>
              </div>
            </div>
            <div class="row extra-padding">
              <div class="col-sm-4">{{'RESERVATIONS.REF_TYPE' | translate:param}}:</div>
              <div class="col-sm-8">
                <ng-select [items]="referenceUserCustomerTypeChange" bindValue="id" bindLabel="text" formControlName="reference_id" [(ngModel)]="selectedReferenceUserCustomerTyepChange" [disabled]="referenceUserForDis && !referenceUserForDis?.allow_to_update_reference_user"
                  (change)="preCheckoutReferenceTypeChanged($event)" [(ngModel)]="bookingTypeOptions"></ng-select>
                <div
                  *ngIf="customerTypeChangeForm.controls['reference_id']?.dirty && customerTypeChangeForm.controls['reference_id']?.invalid"
                  class="alert alert-danger" style="width: 50%;">
                  <div
                    *ngIf="customerTypeChangeForm.controls['reference_id']?.errors?.required || customerTypeChangeForm.controls['reference_id']?.errors?.notEqual">
                    {{'RESERVATIONS.VALID_MSG.REF_IS_REQ' | translate:param}}</div>
                </div>
              </div>
            </div>
            <div class="row extra-padding" *ngIf="isNoteNeccessary">
              <div class="col-sm-4">{{'RESERVATIONS.NOTE' | translate:param}}:</div>
              <div class="col-sm-8">
                <textarea type="text" class="form-control" placeholder="{{'RESERVATIONS.TXT_MSG' | translate:param}}"
                  formControlName="note"></textarea>
                <div
                  *ngIf="customerTypeChangeForm.controls['note']?.dirty && customerTypeChangeForm.controls['note']?.invalid"
                  class="alert alert-danger">
                  <div *ngIf="customerTypeChangeForm.controls['note']?.errors?.required">
                    {{'RESERVATIONS.VALID_MSG.NOT_REQ' | translate:param}}</div>
                </div>
              </div>
            </div>
            <div class="row extra-padding">
              <div class="col-sm-12">
                <button type="submit" class="btn btn-sm btn-inverse">{{bookingCheckoutDetailsType === 'customer-type-change' ? 'Change' : 'Continue'}}
                  <i *ngIf="modelLoading" class="fa fa-circle-o-notch"></i>
                </button>
                <button type="button" class="btn btn-sm btn-inverse"
                  (click)="cancelCheckout()">{{'RESERVATIONS.VALID_MSG.CANCEL' | translate:param}}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Change Customer Type Modal (Before CHECKOUT) -->
