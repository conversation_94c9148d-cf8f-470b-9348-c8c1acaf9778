<section class="card notifications">
  <header class="card-header">
    <div class="text-xs-center mb-sm">
      <strong>{{'NOTIFICATION.YUO_HAVE' | translate:param}}</strong>
    </div>
    <div class="btn-group btn-group-sm btn-group-justified" id="notifications-toggle" data-toggle="buttons">
      <label class="btn btn-secondary active">
        <!-- ajax-load plugin in action. setting data-ajax-load & data-ajax-target is the
             only requirement for async reloading -->
        <input type="radio"
               notification-load
               data-ajax-trigger="change"
               data-ajax-load="assets/demo/notifications/notifications.html"
               data-ajax-target="#notifications-list"> {{'NOTIFICATION.NOTIFI' | translate:param}}
      </label>
      <label class="btn btn-secondary">
        <input type="radio"
               notification-load
               data-ajax-trigger="change"
               data-ajax-load="assets/demo/notifications/messages.html"
               data-ajax-target="#notifications-list"> {{'NOTIFICATION.MESSAGES' | translate:param}}
      </label>
      <label class="btn btn-secondary">
        <input type="radio"
               notification-load
               data-ajax-trigger="change"
               data-ajax-load="assets/demo/notifications/progress.html"
               data-ajax-target="#notifications-list"> {{'NOTIFICATION.PROG' | translate:param}}
      </label>
    </div>
  </header>
  <!-- notification list with .thin-scroll which styles scrollbar for webkit -->
  <div id="notifications-list" class="list-group thin-scroll">
    <div class="list-group-item">
      <span class="thumb-sm float-xs-left mr clearfix">
        <img class="rounded-circle" src="assets/img/people/a3.jpg" alt="...">
      </span>
      <p class="no-margin overflow-hidden">
        {{'NOTIFICATION.NEW_JUST_SIGN' | translate:param}}
        <a href="#">{{'NOTIFICATION.MONICA' | translate:param}}</a>'{{'NOTIFICATION.S_ACCO' | translate:param}}.
        <time class="help-block no-margin">
          {{'NOTIFICATION.TWO_MIN_AGO' | translate:param}}
        </time>
      </p>
    </div>
    <a class="list-group-item" href="#">
      <span class="thumb-sm float-xs-left mr">
        <i class="glyphicon glyphicon-upload fa-lg"></i>
      </span>
      <p class="text-ellipsis no-margin">
        {{'NOTIFICATION.PRE_ALPHA' | translate:param}}. </p>
      <time class="help-block no-margin">
        {{'NOTIFICATION.FIVE_AGO' | translate:param}}
      </time>
    </a>
    <a class="list-group-item" href="#">
      <span class="thumb-sm float-xs-left mr">
        <i class="fa fa-bolt fa-lg"></i>
      </span>
      <p class="text-ellipsis no-margin">
        {{'NOTIFICATION.SERVER_LOAD_LIMIT' | translate:param}}. </p>
      <time class="help-block no-margin">
        {{'NOTIFICATION.SEVEN_AGO' | translate:param}}
      </time>
    </a>
    <div class="list-group-item">
      <span class="thumb-sm float-xs-left mr clearfix">
        <img class="rounded-circle" src="assets/img/people/a5.jpg" alt="...">
      </span>
      <p class="no-margin overflow-hidden">
        {{'NOTIFICATION.USER' | translate:param}} <a href="#">{{'NOTIFICATION.JEFF' | translate:param}}</a> {{'NOTIFICATION.REGD' | translate:param}}
        &nbsp;&nbsp;
        <button class="btn btn-xs btn-success">{{'NOTIFICATION.ALLOW' | translate:param}}</button>
        <button class="btn btn-xs btn-danger">{{'NOTIFICATION.DENY' | translate:param}}</button>
        <time class="help-block no-margin">
          12:18 AM
        </time>
      </p>
    </div>
    <div class="list-group-item">
      <span class="thumb-sm float-xs-left mr">
        <i class="fa fa-shield fa-lg"></i>
      </span>
      <p class="no-margin overflow-hidden">
        {{'NOTIFICATION.INST' | translate:param}}
        {{'NOTIFICATION.CHECK_ACC' | translate:param}} <a href="#">{{'NOTIFICATION.SEC_PAGE' | translate:param}}</a>.
        <time class="help-block no-margin">
          12:18 AM
        </time>
      </p>
    </div>
    <a class="list-group-item" href="#">
      <span class="thumb-sm float-xs-left mr">
        <span class="rounded bg-primary rounded-lg">
          <i class="fa fa-facebook text-white"></i>
        </span>
      </span>
      <p class="text-ellipsis no-margin">
         {{'NOTIFICATION.NEW' | translate:param}}<strong>76</strong> {{'NOTIFICATION.FACEBOOK_LIKE_REC' | translate:param}}.</p>
      <time class="help-block no-margin">
        15 Apr 2014
      </time>
    </a>
    <a class="list-group-item" href="#">
      <span class="thumb-sm float-xs-left mr">
        <span class="circle circle-lg bg-gray-dark">
          <i class="fa fa-circle-o text-white"></i>
        </span>
      </span>
      <p class="text-ellipsis no-margin">
        {{'NOTIFICATION.DARK_MATTE_DETEC' | translate:param}}.</p>
      <time class="help-block no-margin">
        15 Apr 2014
      </time>
    </a>
  </div>
  <footer class="card-footer text-sm">
    <!-- ajax-load button. loads assets/demo/notifications/notifications.php to #notifications-list
         when clicked -->
    <button class="btn btn-xs btn-link float-xs-right btn-notifications-reload"
            id="load-notifications-btn"
            notification-load
            data-ajax-load="assets/demo/notifications/notifications.php"
            data-ajax-target="#notifications-list"
            data-loading-text="<i class='fa fa-refresh fa-spin mr-xs'></i> Loading...">
      <i class="fa fa-refresh"></i>
    </button>
    <span class="fs-mini">{{'NOTIFICATION.SYNC_AT' | translate:param}}: 21 Apr 2014 18:36</span>
  </footer>
</section>
