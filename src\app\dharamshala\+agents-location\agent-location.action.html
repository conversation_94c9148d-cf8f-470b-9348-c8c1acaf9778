<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-user-secret"></i>&nbsp;&nbsp;{{pageType}} {{'AGENT_LOCATION.ADD_PAGE.AGENT_LOCATION' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="agentLocationForm" (ngSubmit)="saveAgentType()">
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'AGENT_LOCATION.ADD_PAGE.PLACE' | translate:param}}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="agentLocationForm.controls.name.errors?.backend">{{agentLocationForm.controls.name.errors?.backend}}</span>
            <input type="text"  class="form-control" formControlName="name" name="name" placeholder="">
            <span class="errMsg" *ngIf="!agentLocationForm.controls.name.valid && !agentLocationForm.controls.name.untouched">
              <span [hidden]="!agentLocationForm.controls.name.errors.required">{{'AGENT_LOCATION.ADD_PAGE.VALID_MSG.PLACE_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'AGENT_LOCATION.ADD_PAGE.STATE' | translate:param}}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="agentLocationForm.controls.state.errors?.backend">{{agentLocationForm.controls.state.errors?.backend}}</span>
            <!-- <input type="text"  class="form-control" formControlName="location" name="location" placeholder=""> -->
            <ng-select [items]="stateList" (change)="stateChanged($event)" formControlName="state" [(ngModel)]="selectedState" bindLabel="text" bindValue="id"></ng-select>
            <span class="errMsg" *ngIf="!agentLocationForm.controls.state.valid && !agentLocationForm.controls.state.untouched">
              <span [hidden]="!agentLocationForm.controls.state.errors.required">{{'AGENT_LOCATION.ADD_PAGE.VALID_MSG.STATE_REQ' | translate:param}}</span>
              <!-- <span [hidden]="!agentLocationForm.controls.state.errors?.notEqual">State is required</span> -->
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'AGENT_LOCATION.ADD_PAGE.STATUS' | translate:param}}</label>
          <div class="col-md-8 ">
            <div class="radio-horizontal">
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-1" [value]="true">
                <label for="radio-1">
                  {{'AGENT_LOCATION.ADD_PAGE.ACTIVE' | translate:param}}
                      </label>
              </div>
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-2" [value]="false">
                <label for="radio-2">
                  {{'AGENT_LOCATION.ADD_PAGE.INACTIVE' | translate:param}}
                      </label>
              </div>
            </div>
          </div>
        </div>

        <div class="form-group row">
          <div class="col-md-8 offset-md-3">
            <div class="">
              <button type="submit" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'AGENT_LOCATION.ADD_PAGE.SAVE' | translate:param}}</button>
              <button type="button" (click)="hideComponent()" class="btn btn-sm btn-secondary">{{'AGENT_LOCATION.ADD_PAGE.CANCEL' | translate:param}}</button>
            </div>
          </div>
        </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
