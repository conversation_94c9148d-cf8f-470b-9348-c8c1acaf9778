<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-user"></i>&nbsp;&nbsp;{{pageType}} {{'USER_ROLE.ROLES' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item">{{'USER_ROLE.ROLE_MANAG' | translate:param}}</li>
    <li class="breadcrumb-item "><a href="javascript:void(0)" (click)="toggleChild()">{{'USER_ROLE.ROLES' | translate:param}}</a></li>
    <li class="breadcrumb-item active">{{pageType}} {{'USER_ROLE.ROLES' | translate:param}}</li>
  </ol>

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="userRole" (ngSubmit)="addUserRole()">
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'USER_ROLE.ROLE_NAME' | translate:param}}*</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="userRole.controls.name.errors?.backend">{{userRole.controls.name.errors?.backend}}</span>
              <input type="text" formControlName="name" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!userRole.controls.name.valid && !userRole.controls.name.pristine">
                <span [hidden]="!userRole.controls.name.errors.required">{{'USER_ROLE.ROLE_NAME_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'USER_ROLE.MENU_GRP' | translate:param}}*</label>
            <div class="col-md-8 ">
              <input type="hidden" formControlName="group_id">
              <span class="errMsg __fromBackend" *ngIf="userRole.controls.group_id.errors?.backend">{{userRole.controls.group_id.errors?.backend}}</span>
              <ng-select [items]="getSelect2DefaultList()"
                        [bindLabel]="'text'"
                        [bindValue]="'id'"
                        [(ngModel)]="dropdownSelect"
                        [formControlName]="'group_id'"
                        (change)="groupChanges($event)">
              </ng-select>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'USER_ROLE.DESC' | translate:param}}</label>
            <div class="col-md-8 ">
              <textarea rows="3" class="autogrow form-control transition-height" id="elastic-textarea" autosize formControlName="description"
                placeholder=""></textarea>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'USER_ROLE.STATUS' | translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio1" [value]="true">
                  <label for="radio1">
                    {{'USER_ROLE.ACTIVE' | translate:param}}
                      </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio2" [value]="false">
                  <label for="radio2">
                    {{'USER_ROLE.INACTIVE' | translate:param}}
                      </label>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" class="btn btn-sm btn-inverse capitalized" [disabled]="!userRole.valid"><i class="fa fa-check"></i>{{'USER_ROLE.SAVE' | translate:param}}</button>
                <button class="btn btn-sm btn-secondary" (click)="toggleChild()">{{'USER_ROLE.CANCEL' | translate:param}}</button>
              </div>
            </div>
          </div>
        </form>
        <!--<pre>
          {{ userRole.value | json}}
        </pre>-->
      </fieldset>
    </div>
  </div>
</section>
