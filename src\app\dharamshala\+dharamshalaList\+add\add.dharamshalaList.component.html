<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-building"></i>&nbsp;&nbsp;{{pageName}} {{'DHARAMSHALA.ADD_PAGE.DHARAMSHALA' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item "><a [routerLink]="['/admin/dharamshala']">{{'DHARAMSHALA.ADD_PAGE.DHARAMSHALA' | translate:param}}</a></li>
    <li class="breadcrumb-item active">{{pageName}} {{'DHARAMSHALA.ADD_PAGE.DHARAMSHALA' | translate:param}}</li>
  </ol>

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="dharamshalaForm" (ngSubmit)="saveDharamshala(dharamshalaForm)">

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.NAME' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.name.errors?.backend">{{dharamshalaForm.controls.name.errors?.backend}}</span>
              <input type="text" class="form-control" formControlName="name">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls.name.valid && !dharamshalaForm.controls.name.pristine">
              <span [hidden]="!dharamshalaForm.controls.name.errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.NAME_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.TAT_BAD' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.total_bed.errors?.backend">{{dharamshalaForm.controls.total_bed.errors?.backend}}</span>
              <input type="text" class="form-control" formControlName="total_bed">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls.total_bed.valid && !dharamshalaForm.controls.total_bed.pristine">
              <span [hidden]="!dharamshalaForm.controls.total_bed.errors.required"> {{'DHARAMSHALA.ADD_PAGE.VALID_MSG.TOT_BAD_REQ' | translate:param}}</span>
              <span [hidden]="!dharamshalaForm.controls.total_bed.errors.digits"> {{'DHARAMSHALA.ADD_PAGE.VALID_MSG.ONLY_DIGIT_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.EMAILID' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.email.errors?.backend">{{dharamshalaForm.controls.email.errors?.backend}}</span>
              <input type="email" class="form-control" formControlName="email">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls.email.valid && !dharamshalaForm.controls.email.pristine">
              <span [hidden]="!dharamshalaForm.controls.email.errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.EMAIL_REQ' | translate:param}}</span>
              <span [hidden]="!dharamshalaForm.controls.email.errors.email && dharamshalaForm.controls.email.touched">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.VALID_EMAIL' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.CONC_NO' | translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.contact.errors?.backend">{{dharamshalaForm.controls.contact.errors?.backend}}</span>
              <input type="tel" maxlength="10" class="form-control" formControlName="contact">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls.contact.valid && !dharamshalaForm.controls.contact.pristine">
              <span [hidden]="!dharamshalaForm.controls.contact.errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.CON_REQ' | translate:param}}</span>
              <span [hidden]="!dharamshalaForm.controls.contact.errors.digits">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.VALID_CONT' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.ZIP_POST' | translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.zip.errors?.backend">{{dharamshalaForm.controls.zip.errors?.backend}}</span>
              <input type="text" maxlength="6" class="form-control" formControlName="zip">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls.zip.valid && !dharamshalaForm.controls.zip.pristine">
              <span [hidden]="!dharamshalaForm.controls.zip.errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.ZIP_POS_REQ' | translate:param}}</span>
              <span [hidden]="!dharamshalaForm.controls.zip.errors.digits">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.ONLY_DIGIT_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.CITY' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.city.errors?.backend">{{dharamshalaForm.controls.city.errors?.backend}}</span>
              <!-- <input type="text" class="form-control" formControlName="city"> -->
              <ng-select class="countrySelections" [items]="citiesList" bindLabel="name" bindValue="id" [(ngModel)]="city" (change)="cityChanged($event)" [searchable]="true" [clearable]="true" placeholder="Select a city"> </ng-select>

              <span class="errMsg" *ngIf="!dharamshalaForm.controls.city.valid && !dharamshalaForm.controls.city.pristine">
              <span [hidden]="!dharamshalaForm.controls.city.errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.CITY_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.COUNTRY' | translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.country.errors?.backend">{{dharamshalaForm.controls.country.errors?.backend}}</span>
              <!-- <input type="text" class="form-control" formControlName="country"> -->
              <ng-select class="countrySelections" [items]="countryList" bindLabel="name" bindValue="id" [(ngModel)]="country" (change)="countryChanged($event)" [searchable]="true" [clearable]="true" placeholder="Select a country"></ng-select>

              <span class="errMsg" *ngIf="!dharamshalaForm.controls.country.valid && !dharamshalaForm.controls.country.pristine">
              <span [hidden]="!dharamshalaForm.controls.country.errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.COUNTRY_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.ADDRESS' | translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.address.errors?.backend">{{dharamshalaForm.controls.address.errors?.backend}}</span>
              <input type="text" id="autocompleteInput" class="form-control" formControlName="address">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls.address.valid && !dharamshalaForm.controls.address.pristine">
              <span [hidden]="!dharamshalaForm.controls.address.errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.ADDRE_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.LOCATION' | translate:param}}</label>
            <div class="col-md-8 " style="height: 300px;">
              <input type="hidden" formControlName="lat">
              <input type="hidden" formControlName="lng">
              <agm-map [latitude]="lat" [longitude]="lng" [zoom]="zoom" class="content-map">
                <agm-marker 
                  [latitude]="lat" 
                  [longitude]="lng" 
                  [markerDraggable]="true"
                  (dragEnd)="getCordinates($event)">
                </agm-marker>
              </agm-map>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.IMAGES' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="file" name="avatar" class="form-control" ng2FileSelect (fileOver)="fileOverCatch($event)" [uploader]="uploader" multiple /><br/>
              <!-- display already uploded image here -->
              <div class="amenities-area __image_container" *ngIf="uploadedImageURLs && uploadedImageURLs.length > 0">
                <span *ngFor="let img of uploadedImageURLs;let i = index">
                <span  class="__images" *ngIf="!img.is_deleted">
                  <a href="javascript:void(0)" (click)="deleteImage(img)"><i class="fa fa-times"></i></a>
                  <img src="{{img.url}}" alt="">
                  <span class="__filename">{{img.oldName}}</span>
                </span>
                </span>
              </div>
              <!-- file upload extra ************************** -->
              <div class="files" *ngIf="uploader.queue.length" class="well well-sm">
                <table class="table">
                  <thead>
                    <tr>
                      <th width="50%">{{'DHARAMSHALA.ADD_PAGE.FILE_NAME' | translate:param}}</th>
                      <th>&nbsp;</th>
                      <th width="230px">{{'DHARAMSHALA.ADD_PAGE.FILE_PROG' | translate:param}}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let item of uploader.queue">
                      <td><strong>{{ item?.file?.name }}</strong></td>


                      <td class="text-center">
                        <span *ngIf="item.isSuccess"><i class="glyphicon glyphicon-ok"></i></span>
                        <span *ngIf="item.isCancel"><i class="glyphicon glyphicon-ban-circle"></i></span>
                        <span *ngIf="item.isError"><i class="glyphicon glyphicon-remove"></i></span>
                      </td>
                      <td nowrap>

                        <button type="button" class="btn btn-success btn-xs" (click)="item.upload()" [disabled]="item.isReady || item.isUploading || item.isSuccess">
                            <span class="glyphicon glyphicon-upload"></span> {{'DHARAMSHALA.ADD_PAGE.FILE_UPLOAD' | translate:param}}
                        </button>
                        <!-- <button type="button" class="btn btn-warning btn-xs" (click)="item.cancel()" [disabled]="!item.isUploading">
                            <span class="glyphicon glyphicon-ban-circle"></span> Cancel
                        </button> -->
                        <button type="button" class="btn btn-danger btn-xs" (click)="item.remove()">
                            <span class="glyphicon glyphicon-trash"></span> {{'DHARAMSHALA.ADD_PAGE.FILE_REMOVE' | translate:param}}
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <hr>

                <div *ngIf="uploader.queue.length > 1">
                  <div *ngIf="uploader.progress">
                    {{'DHARAMSHALA.ADD_PAGE.QUEUE_PROGRESS' | translate:param}}
                    <div class="progress">
                      <div class="progress-bar" role="progressbar" [ngStyle]="{ 'width': uploader.progress + '%' }"></div>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-success btn-sm" (click)="uploader.uploadAll()" [disabled]="!uploader.getNotUploadedItems().length">
                        <span class="glyphicon glyphicon-upload"></span> {{'DHARAMSHALA.ADD_PAGE.UPLOAD_ALL' | translate:param}}
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" (click)="uploader.cancelAll()" [disabled]="!uploader.isUploading">
                        <span class="glyphicon glyphicon-ban-circle"></span> {{'DHARAMSHALA.ADD_PAGE.CANCEL_ALL' | translate:param}}
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" (click)="uploader.clearQueue()" [disabled]="!uploader.queue.length">
                        <span class="glyphicon glyphicon-trash"></span> {{'DHARAMSHALA.ADD_PAGE.REMOVE_ALL' | translate:param}}
                    </button>
                  </div>
                  <div class="clearfix"></div>
                </div>
              </div>
              <!-- file upload extra ************************** -->
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.DOC' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="file" name="avatar" class="form-control" ng2FileSelect [uploader]="uploader2" multiple /><br/>
              <!-- display already uploded image here -->
              <div class="amenities-area __image_container" *ngIf="uploadedDocsURLs && uploadedDocsURLs.length > 0">
                <span *ngFor="let doc of uploadedDocsURLs;let i = index">
                <span  class="__docs" *ngIf="!doc.is_deleted">
                  <a href="javascript:void(0)" (click)="deleteDoc(doc)"><i class="fa fa-times"></i></a>
                  <a href="{{doc.url}}.{{doc.extension}}" class="__doc" target="_blank" alt="">
                    <img *ngIf="doc.extension == 'pdf'" src="data:image/svg+xml;utf8;base64,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" />
                    <img *ngIf="doc.extension != 'pdf'" src="data:image/svg+xml;utf8;base64,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" />
                    <span class="__filename">{{doc.oldName}}</span>
                </a>
                </span>
                </span>
              </div>
              <!-- file upload extra ************************** -->
              <div class="files" *ngIf="uploader2.queue.length" class="well well-sm">
                <table class="table">
                  <thead>
                    <tr>
                      <th>{{'DHARAMSHALA.ADD_PAGE.FILE_NAME' | translate:param}}</th>
                      <th>&nbsp;</th>
                      <th width="230px">{{'DHARAMSHALA.ADD_PAGE.FILE_PROG' | translate:param}}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let item of uploader2.queue">
                      <td><strong>{{ item?.file?.name }}</strong></td>


                      <td class="text-center">
                        <span *ngIf="item.isSuccess"><i class="glyphicon glyphicon-ok"></i></span>
                        <span *ngIf="item.isCancel"><i class="glyphicon glyphicon-ban-circle"></i></span>
                        <span *ngIf="item.isError"><i class="glyphicon glyphicon-remove"></i></span>
                      </td>
                      <td nowrap>

                        <button type="button" class="btn btn-success btn-xs" (click)="item.upload()" [disabled]="item.isReady || item.isUploading || item.isSuccess">
                            <span class="glyphicon glyphicon-upload"></span> {{'DHARAMSHALA.ADD_PAGE.FILE_UPLOAD' | translate:param}}
                        </button>
                        <!-- <button type="button" class="btn btn-warning btn-xs" (click)="item.cancel()" [disabled]="!item.isUploading">
                            <span class="glyphicon glyphicon-ban-circle"></span> Cancel
                        </button> -->
                        <button type="button" class="btn btn-danger btn-xs" (click)="item.remove()">
                            <span class="glyphicon glyphicon-trash"></span> {{'DHARAMSHALA.ADD_PAGE.FILE_REMOVE' | translate:param}}
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <hr>

                <div *ngIf="uploader2.queue.length > 1">
                  <div *ngIf="uploader2.progress">
                    {{'DHARAMSHALA.ADD_PAGE.QUEUE_PROGRESS' | translate:param}}
                    <div class="progress">
                      <div class="progress-bar" role="progressbar" [ngStyle]="{ 'width': uploader2.progress + '%' }"></div>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-success btn-sm" (click)="uploader2.uploadAll()" [disabled]="!uploader2.getNotUploadedItems().length">
                        <span class="glyphicon glyphicon-upload"></span> {{'DHARAMSHALA.ADD_PAGE.UPLOAD_ALL' | translate:param}}
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" (click)="uploader2.cancelAll()" [disabled]="!uploader2.isUploading">
                        <span class="glyphicon glyphicon-ban-circle"></span> {{'DHARAMSHALA.ADD_PAGE.CANCEL_ALL' | translate:param}}
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" (click)="uploader2.clearQueue()" [disabled]="!uploader2.queue.length">
                        <span class="glyphicon glyphicon-trash"></span> {{'DHARAMSHALA.ADD_PAGE.REMOVE_ALL' | translate:param}}
                    </button>
                  </div>
                  <div class="clearfix"></div>
                </div>
              </div>
              <!-- file upload extra ************************** -->
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.EXT_LINKS' | translate:param}}</label>
            <div class="col-md-8 ">
              <div class="input-group">
                <div class="toolbar-inputs">
                  <input type="text" placeholder="{{'DHARAMSHALA.ADD_PAGE.TITLE' | translate:param}}" #externalinkTitle class="form-control small">
                  <input type="text" placeholder="{{'DHARAMSHALA.ADD_PAGE.LINK' | translate:param}}" #externallinkBody class="form-control large">
                  <div class="display-inline-block" style="padding: 5px 0 0 15px">
                    <a href="javascript:void(0)" (click)="addExternalLink(externalinkTitle.value,externallinkBody.value)" class="btn btn-xs btn-primary"><i class="fa fa-plus"></i></a>
                  </div>
                </div>

              </div>
              <div *ngIf="externalLinks.length > 0">
                <table class="table table-bordered">
                  <tr *ngFor="let link of externalLinks; let i =index">
                    <td style="white-space:nowrap">

                      {{i+1}}.&nbsp;<strong> {{link.title}}</strong>
                    </td>
                    <td>
                      <p style="margin-bottom: 0px">
                        <a [href]="link.body" class="__break-all" target="_blank">{{link.body}}</a>&nbsp;&nbsp;-&nbsp;&nbsp;
                        <a href="javascript:void(0)" (click)="deleteExternalLink(i,link)"><i class="fa fa-trash-o text-danger"></i></a>
                      </p>

                      <div class="clearfix"></div>
                    </td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
            <div class="form-group row" style="align-items: center;">
              <div class="col-md-3 text-md-right">
                <label for="normal-field" class=" col-form-label ">{{'DHARAMSHALA.ADD_PAGE.CHECK_IN' | translate:param}}</label>
              </div>
          <div class="col-md-3">
             <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.check_in.errors?.backend">{{dharamshalaForm.controls.check_in.errors?.backend}}</span>
              <timepicker [minuteStep]="1" formControlName="check_in"></timepicker>
              <!--{{dharamshalaForm.controls.check_in.value}}
              {{dharamshalaForm.controls.check_in.value | json}}-->
          </div>
          <div class="col-md-2 text-md-right">
          <label for="normal-field" class="  col-form-label">{{'DHARAMSHALA.ADD_PAGE.CHECK_OUT' | translate:param}}</label>

          </div>
          <div class="col-md-3">
             <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls.check_out.errors?.backend">{{dharamshalaForm.controls.check_out.errors?.backend}}</span>
            <timepicker formControlName="check_out"></timepicker>
          </div>
        </div>
        <!-- <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">Signin Authority</label>
          <div class="col-md-8 ">
            <input type="hidden" class="form-control" formControlName="signing_authority">
            <ng-select [items]="getUsersList()" [(ngModel)]="selectedSigninAutority"></ng-select>
          </div>
        </div> -->
        <div class="form-group row">
          <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'DHARAMSHALA.ADD_PAGE.STATUS' | translate:param}}</label>
          <div class="col-md-8 ">
            <div class="radio-horizontal">
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-1" [value]="true">
                <label for="radio-1">
                  {{'DHARAMSHALA.ADD_PAGE.ACTIVE' | translate:param}}
                    </label>
              </div>
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-2" [value]="false">
                <label for="radio-2">
                  {{'DHARAMSHALA.ADD_PAGE.INACTIVE' | translate:param}}
                    </label>
              </div>
            </div>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.EAR_CHE_CHARGE' | translate:param}}</label>
          <div class="col-md-8 ">
            <span *ngIf="dharamshalaForm.controls.early_checkout_charges.errors?.backend" class="errMsg __fromBackend">{{dharamshalaForm.controls.early_checkout_charges.errors?.backend}}</span>
            <div class="input-group" [ngClass]="{'has-error': (!dharamshalaForm.controls.early_checkout_charges.valid && dharamshalaForm.controls.early_checkout_charges.touched)}">
              <span class="input-group-addon"><i class="fa fa-percent"></i></span>
              <input type="text" class="form-control" formControlName="early_checkout_charges">
            </div>
             <span *ngIf="!dharamshalaForm.controls.early_checkout_charges.valid && dharamshalaForm.controls.early_checkout_charges.touched" class="errMsg">
                <span [hidden]="!dharamshalaForm.controls.early_checkout_charges.errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.EAR_CHE_CHARGE_REQ' | translate:param}}</span>
                <span [hidden]="!dharamshalaForm.controls.early_checkout_charges.errors.number">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.ONLY_DIGIT_REQ' | translate:param}}</span>
                <span [hidden]="!dharamshalaForm.controls.early_checkout_charges.errors.lte">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.INVALID_VAL' | translate:param}}</span>
            </span>
          </div>
        </div>
        <!-- *********************************************************************************************************** -->
        <!-- <pre>
          {{dharamshalaForm.value}}
        </pre> -->
        <h4>
          <span class="capitalized" style="font-weight:600;padding-left:15px;">
            {{'DHARAMSHALA.ADD_PAGE.DHARAM_AD_DETA' | translate:param}}
          </span>
        </h4><br>
        <div formGroupName="user">

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.FNAME' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls['user'].controls['first_name'].errors?.backend">{{dharamshalaForm.controls.user.controls['first_name'].errors?.backend}}</span>
              <input type="text" class="form-control" formControlName="first_name">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls.user.controls['first_name'].valid && !dharamshalaForm.controls.user.controls['first_name'].pristine">
                <span [hidden]="!dharamshalaForm.controls.user.controls['first_name'].errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.FIR_NAME_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.LAS_NAME' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls['user'].controls['last_name'].errors?.backend">{{dharamshalaForm.controls['user'].controls['last_name'].errors?.backend}}</span>
              <input type="text" class="form-control" formControlName="last_name">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls['user'].controls['last_name'].valid && !dharamshalaForm.controls['user'].controls['last_name'].pristine">
                <span [hidden]="!dharamshalaForm.controls['user'].controls['last_name'].errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.LAST_NAME_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
          <div class="form-group row">
            <label for="default-select" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.USER_ROLE' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls['user'].controls['role_id'].errors?.backend">{{dharamshalaForm.controls['user'].controls['role_id'].errors?.backend}}</span>
              <input type="hidden" formControlName="role_id">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls['user'].controls['role_id'].valid && !dharamshalaForm.controls['user'].controls['role_id'].pristine">
                <span [hidden]="!dharamshalaForm.controls['user'].controls['role_id'].errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.USER_ROLE_REQ' | translate:param}}</span>
              </span>
              <ng-select id="default-select" [items]="getRoleList()" bindLabel="text" bindValue="id" [(ngModel)]="initRolelist" (change)="userRoleChanged($event)" [searchable]="true" [clearable]="true" placeholder="Select a role"></ng-select>
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.EMAIL_ID' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls['user'].controls['email'].errors?.backend">{{dharamshalaForm.controls['user'].controls['email'].errors?.backend}}</span>
              <input type="text" class="form-control" formControlName="email">
              <span class="errMsg" *ngIf="!dharamshalaForm.controls['user'].controls['email'].valid && !dharamshalaForm.controls['user'].controls['email'].pristine">
                <span [hidden]="!dharamshalaForm.controls['user'].controls['email'].errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.EMAIL_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.DOB' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls['user'].controls['dob'].errors?.backend">{{dharamshalaForm.controls['user'].controls['dob'].errors?.backend}}</span>
              <datetime formControlName="dob" [timepicker]="false" [datepicker]="datepickerOpts"></datetime>
            </div>
          </div>
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DHARAMSHALA.ADD_PAGE.COUNTRY' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="dharamshalaForm.controls['user'].controls['country'].errors?.backend">{{dharamshalaForm.controls['user'].controls['country'].errors?.backend}}</span>
              <!-- <input type="text" class="form-control" formControlName="country"> -->
              <ng-select class="countrySelections" [items]="countryList" bindLabel="name" bindValue="id" [(ngModel)]="userCountry" (change)="userCountryChanged($event)" [searchable]="true" [clearable]="true" placeholder="Select a country"></ng-select>
              <span class="errMsg" *ngIf="!dharamshalaForm.controls['user'].controls['country'].valid && !dharamshalaForm.controls['user'].controls['country'].pristine">
                <span [hidden]="!dharamshalaForm.controls['user'].controls['country'].errors.required">{{'DHARAMSHALA.ADD_PAGE.VALID_MSG.COUNTRY_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
        </div>
        <!-- *********************************************************************************************************** -->
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right"></label>
          <div class="col-md-8 ">
              <button type="submit" class="btn btn-sm btn-inverse" [disabled]="!dharamshalaForm.valid"><i class="fa fa-check"></i>{{'DHARAMSHALA.ADD_PAGE.SAVE' | translate:param}}</button>
            <a [routerLink]="['/admin/dharamshala']" class="btn btn-sm btn-default">{{'DHARAMSHALA.ADD_PAGE.CANCEL' | translate:param}}</a>
          </div>
        </div>
        </form>
        <!-- <pre>
			{{ dharamshalaForm.value | json}}
		</pre> -->
      </fieldset>
    </div>
  </div>
</section>
