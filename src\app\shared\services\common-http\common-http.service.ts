import { apiUrl } from './../../../api-env';
/**
* CommonHttpService
* 
* Provide this file on the root module so
* other services can inject this CommonHttpService and
* use all common functions and member variables
*/

import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { _secretKey_auth } from './../../globals/config';

import * as CryptoJS from 'crypto-js';
import 'messenger/build/js/messenger.js';
import { ServiceResponse } from './service-response';
import { AuthGuard } from './../../guards/auth-guard.service';
import { catchError, map } from 'rxjs/operators';

declare var Messenger: any;

@Injectable()
export class CommonHttpService {
    options: any;
    public apiBaseUrl: String = apiUrl;
    public doorLockServiceURL: string = 'http://localhost:49904/';

    constructor(private http: HttpClient,
        private router: Router,
        private authGuard: AuthGuard) {
        Messenger.options = {
            theme: 'air',
            extraClasses: 'messenger-fixed messenger-on-right messenger-on-top'
        };

        this.checkToken();
    }

    checkToken(isBlob = false) {
        let headerConfig = {
            'Content-Type': 'application/json'
        };
        
        if (this.authGuard.ud) {
            let token = this.authGuard.ud.session_id;
            let timestamp = (+ new Date()).toString();
            let generatedToken = CryptoJS.AES.encrypt(token, `${_secretKey_auth}${timestamp}`);
            headerConfig['Token'] = generatedToken.toString();
            headerConfig['Timestamp'] = timestamp;
        }

        const headers = new HttpHeaders(headerConfig);

        this.options = isBlob ? 
            { responseType: 'blob' as 'blob', headers } : 
            { headers };
    }

    /**
     * handleResponse : it will show respected message as per the response
     * @param res : will handel all the response provided by the api call
     */
    handleResponse(res: any, isCsv?: boolean): ServiceResponse {
        if (isCsv) {
            return res;
        }
        
        if (res.status === 'error') {
            this.showMessage(res.message, 'error');
        } else {
            this.showMessage(res.message, 'success');
        }
        return res;
    }

    /**
     * get : overwrite the http.get method , 
     *       so we dont have to add options each time and handle response
     *       differently 
     *        
     * @param url : url to call the api
     * @param showAlert : to show a success message or not
     */
    get(url: string, showAlert: boolean = false): Observable<ServiceResponse | any> {
        this.checkToken();
        return this.http.get<ServiceResponse>(this.apiBaseUrl + url, this.options).pipe(
            map(res => showAlert ? this.handleResponse(res) : res),
            catchError(error => this.handleError(error))
        );
    }

    /**
     * post : overwrite the http.get method , 
     *       so we dont have to add options each time and handle response
     *       differently 
     *        
     * @param url : url to call the api
     * @param data : data to post with api
     * @param showAlert : to show a success message or not
     */
    post(url: string, data: any, showAlert: boolean = false, isCsv?: boolean, isBlob?: boolean): Observable<ServiceResponse | any> {
        this.checkToken(isBlob);
        url = this.apiBaseUrl + url;
        
        if (isCsv) {
            // For CSV responses, we need to handle the response as text
            return this.http.post(url, data, { ...this.options, responseType: 'text' }).pipe(
                map(res => res),
                catchError(error => this.handleError(error))
            );
        }
        
        return this.http.post<ServiceResponse>(url, data, this.options).pipe(
            map(res => showAlert ? this.handleResponse(res, isCsv) : res),
            catchError(error => this.handleError(error))
        );
    }
    doorLockPost(url: string, data: any): Observable<any> {
        const headers = new HttpHeaders({
            'Content-Type': 'application/json',
        });
        this.options = { headers };
        url = this.doorLockServiceURL + url;
        return this.http.post(url, data, this.options).pipe(
            catchError(error => this.handleError(error))
        );
    }
    showMessage(message: String, status: String) {
        Messenger().post({
            hideAfter: 5,
            message: message,
            type: status,
            showCloseButton: true
        });
    }

    /**
     * handleError : Error handler function 
     * 
     * @param error : thown error by the server / all the values of error 
     * @param that : reference to the current class 
     * @param url : url for corresponding API call
     *              (beacuse this is pointing to cacheSubscriber / so not be 
     *              able to call the router or any memeber of current class)
     */
    private handleError(error: HttpErrorResponse) {
        let errMsg: string;
        
        if (error.error instanceof ErrorEvent) {
            errMsg = error.error.message;
        } else {
            errMsg = `${error.status} - ${error.statusText || ''} ${error.error?.message || ''}`;
        }

        this.showMessage(error.error?.message || "API is not Responding. Please Try Again.", 'error');
        console.error('Dharamshala Error:', error);

        if (error.status === 401) {
            this.authGuard.removeUserShift();
            this.authGuard.removeUser();
            this.router.navigate(['']);
        }

        if (error.status === 403 && error.error?.message !== 'UnAuthorized user') {
            this.router.navigate(['admin']);
        }

        return throwError(() => error);
    }
    /**
     * convert given menu to a flatted menu to avail all unique children at first depth
     * 
     * @param {any} menuGroup - menu to be converted into a flattened menu
     * 
     * @memberof ManageMenuComponent
     */
    convertMenuGroupToDefault(menuGroup: any[]) {
        let menu = [];
        menuGroup.forEach(element => {
            menu.push(element);
            if (element.children) {
                element.children.forEach(ele => {
                    menu.push(ele);
                });
            }
        });
        return menu;
    }
    /**
     * finds the index of a value in a given array
     * 
     * @param arr : target for index search
     * @param property : property in the given array
     * @param value : target value for value
     */
    findIndex(arr: any[], property: string, value: any) {
        return arr.findIndex(item => item[property] === value);
    }
}
