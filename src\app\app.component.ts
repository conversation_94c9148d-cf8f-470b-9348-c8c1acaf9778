import { Component, ViewEncapsulation, OnInit } from '@angular/core';
import { ErrorLoggingService } from './errorlogging/errorlogging';
import { LoggingService } from './logging/logging.service';
import { Router, NavigationEnd } from '@angular/router';; // Import the Router service
import { apiUrl } from './api-env';
import { AppState } from './app.service';

/*
 * App Component
 * Top Level Component
 */
@Component({
  selector: 'app',
  encapsulation: ViewEncapsulation.None,
  styleUrls: [
    './scss/application.scss'
  ],
  template: `<router-outlet></router-outlet>`
})
//export class App {
  export class App implements OnInit  {  //
  
    public apiBaseUrl =apiUrl; 
  constructor(
     public loggingservice: LoggingService,
     public errorloggingservice: ErrorLoggingService,
    public appState: AppState,
    private router: Router 
    ) {}
  
     ngOnInit() {
     
      // const originalConsoleLog = console.log;
      // const originalConsoleError = console.error;
  
      // const customConsoleLog = function () {
      //   // Call your custom log method on the LoggingService instance
      //   this.loggingservice.log.apply(this.loggingservice, arguments);
      
      //   // Call the original console.log
      //   originalConsoleLog.apply(console, arguments);
      // };
      
      // const customConsoleError = function (...args: any[]) {
      //   // Call your custom error method on the LoggerService instance
      //   this.loggerService.error(...args);
      
      //   // Call the original console.error
      //   originalConsoleError.apply(console, args);
      // };
      
      // console.error = customConsoleError.bind(this);
      // console.log = customConsoleLog.bind(this);

      this.router.events.subscribe(event => {
        if (event instanceof NavigationEnd) {
          const newUrl = event.url;
          this.loggingservice.logHttpRequest(newUrl).subscribe(
            (response) => {
              console.log('HTTP Request Success:', response);
              // You can handle the response data here.
            },
            (error) => {
              console.error('HTTP Request Error:', error);
              this.errorloggingservice.log(error);
            }
          );
          console.log('Current URL:', newUrl);
        }
      });
    }


  
  }