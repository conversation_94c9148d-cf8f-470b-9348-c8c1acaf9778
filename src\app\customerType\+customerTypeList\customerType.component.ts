import { Component, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { CustomerTypeService } from './../../shared/services/customerType.service';

import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

import * as _ from "lodash";

@Component({
    selector: 'customer-list-list',
    templateUrl: './customerType.component.html'
})
export class CustomerListComponent {
    config: any;// New Change ****
    data: any[];
    selectedCT: any;
    public prevVal: any;
    originalData: any[];
    searchQuery: string;
    // show/hide child components
    hiddenAddCT: boolean = true;
    hiddenEditCT: boolean = true;
    private changeDefaultSer: any;
    public canViewRecords: boolean;

    private sub: any;
    private updateBakhiStatusAPI: any;

    @ViewChild('mf') mf: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private CTS: CustomerTypeService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        this.canViewRecords = true;
        this.sub = this.CTS.getAllCustomerType()
            .subscribe((res) => {
                if (res.status == "success") {
                    this.data = res.data;
                    // if (res.data.customer_id > -1) {
                    //     console.log("customer_id found : ", res.data.customer_id);
                    //     this.data.forEach(element => {
                    //         element.is_default = false;
                    //     });
                    //     this.data[this.findIndex(res.data.customer_id, "id")].is_default = true;
                    // }
                    this.originalData = res.data;
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });

             // New Change ****
            this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
            });
    }

    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }

    // add cutomer type component
    showAddCT() {
        this.hiddenAddCT = !this.hiddenAddCT;
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    getHiddenAddCTfromChild(event) {
        this.hiddenAddCT = event.gethiddenAddCT;
        if (this.canViewRecords && event.data) {
            // push to show on list
            this.originalData.push(event.data);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }
    // edit customer type component
    showEditCT(ele) {
        this.hiddenEditCT = !this.hiddenEditCT;
        this.selectedCT = ele;
    }
    getHiddenEditCTfromChild(event) {
        this.hiddenEditCT = event.gethiddenEditCT;
        if (this.canViewRecords && event.data) {
            // push to show on list
            this.data[this.findIndex(event.data.id, "id", this.data) - 1] = event.data;
            this.originalData[this.findIndex(event.data.id, "id", this.originalData) - 1] = event.data;
            // if (event.customer_id) {

            //     _.map(this.originalData, original => { original.is_default = false; });
            //     _.map(this.data, data => { data.is_default = false; });
            //     this.originalData[this.findIndex(event.customer_id, "id", this.originalData)].is_default = true;
            //     this.data[this.findIndex(event.customer_id, "id", this.data)].is_default = true;
            // }            
        }
    }

    updateBakhiStatus(customer: any) {
        let condition = customer.checkout_without_full_payment;
        let index = this.findIndex(customer.id, "id", this.data) - 1;
        this.data[index].checkout_without_full_payment = !condition;
        index = this.findIndex(customer.id, "id", this.originalData) - 1;
        this.originalData[index].checkout_without_full_payment = !condition;
        // console.log("checkout_without_full_payment : ", customer.checkout_without_full_payment);
        this.updateBakhiStatusAPI = this.CTS.updateBakhiStatus(customer.id, this.data[index])
            .subscribe((res) => {
                if (res.status != "success") {
                    customer.checkout_without_full_payment = condition;
                    this.originalData[index].checkout_without_full_payment = condition;
                }
            }, (err) => {
                customer.checkout_without_full_payment = condition;
                this.originalData[index].checkout_without_full_payment = condition;
            });
    }

    updateCustomerReferenceStatus(customer: any) {
        let condition = customer.is_reference_necessary;
        let index = this.findIndex(customer.id, "id", this.data) - 1;
        this.data[index].is_reference_necessary = !condition;
        index = this.findIndex(customer.id, "id", this.originalData) - 1;
        this.originalData[index].is_reference_necessary = !condition;
        // console.log("is_reference_necessary : ", customer.is_reference_necessary);
        this.updateBakhiStatusAPI = this.CTS.updateCustomerReferenceStatus(customer.id, this.data[index])
            .subscribe((res) => {
                if (res.status != "success") {
                    customer.is_reference_necessary = condition;
                    this.originalData[index].is_reference_necessary = condition;
                }
            }, (err) => {
                customer.is_reference_necessary = condition;
                this.originalData[index].is_reference_necessary = condition;
            });
    }

    updateCustomerCommentStatus(customer: any) {
        let condition = customer.is_comment_necessary;
        let index = this.findIndex(customer.id, "id", this.data) - 1;
        this.data[index].is_comment_necessary = !condition;
        index = this.findIndex(customer.id, "id", this.originalData) - 1;
        this.originalData[index].is_comment_necessary = !condition;
        // console.log("is_comment_necessary : ", customer.is_comment_necessary);
        this.updateBakhiStatusAPI = this.CTS.updateCustomerCommentStatus(customer.id, this.data[index])
            .subscribe((res) => {
                if (res.status != "success") {
                    customer.is_comment_necessary = condition;
                    this.originalData[index].is_comment_necessary = condition;
                }
            }, (err) => {
                customer.is_comment_necessary = condition;
                this.originalData[index].is_comment_necessary = condition;
            });
    }

    changeDefault(customer, property: string) {
        // Save the previous default index
        const prevDefaultIndex = this.originalData.findIndex(item => item.is_default);
        const prevDefaultId = prevDefaultIndex > -1 ? this.originalData[prevDefaultIndex].id : null;

        // Set all to false, set current to true
        this.originalData.forEach(item => item.is_default = false);
        this.data.forEach(item => item.is_default = false);
        customer.is_default = true;
        const dataIdx = this.data.findIndex(item => item.id === customer.id);
        if (dataIdx > -1) this.data[dataIdx].is_default = true;

        this.changeDefaultSer = this.CTS.updateDefaultCustomer(customer.id, customer, property)
            .subscribe((res) => {
                if (res.status != "success") {
                    // Revert to previous default
                    this.originalData.forEach(item => item.is_default = false);
                    this.data.forEach(item => item.is_default = false);
                    if (prevDefaultId !== null) {
                        const origIdx = this.originalData.findIndex(item => item.id === prevDefaultId);
                        const dataIdx = this.data.findIndex(item => item.id === prevDefaultId);
                        if (origIdx > -1) this.originalData[origIdx].is_default = true;
                        if (dataIdx > -1) this.data[dataIdx].is_default = true;
                    }
                }
            }, (err) => {
                // Revert to previous default on error
                this.originalData.forEach(item => item.is_default = false);
                this.data.forEach(item => item.is_default = false);
                if (prevDefaultId !== null) {
                    const origIdx = this.originalData.findIndex(item => item.id === prevDefaultId);
                    const dataIdx = this.data.findIndex(item => item.id === prevDefaultId);
                    if (origIdx > -1) this.originalData[origIdx].is_default = true;
                    if (dataIdx > -1) this.data[dataIdx].is_default = true;
                }
            });
    }

    // search function
    searchEvent() {
        this.initializeData();
        if (this.searchQuery && (<string>this.searchQuery).trim() != '') {
            this.data = this.data.filter(data => {
                return ((<string>data.name.toLowerCase()).indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    // --- Initial edited array to the original one --- //
    initializeData() {
        this.data = this.originalData;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if (this.sub) {
            this.sub.unsubscribe();
        }
        if (this.changeDefaultSer) {
            this.changeDefaultSer.unsubscribe();
        }
         // New Change ****
         if (this.langChangeSub)
         this.langChangeSub.unsubscribe();
    }
}