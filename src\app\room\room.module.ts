import { BookingService } from './../shared/services/booking.service';
import { BookingSettingComponent } from './+setting/booking.setting.component';
import { RoomsService } from './../shared/services/room.service';
import { IsDisabled } from './../shared/directive/isDisabled.directive';
import { RoomCategoryService } from './../shared/services/roomCategory.service';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

import { SharedModule } from './../shared/shared.module';

import { RoomsComponent } from './+roomList/rooms.component';
import { AddRoomComponent } from './+roomList/+add/add.room.component';
import { EditRoomComponent } from './+roomList/+edit/edit.room.component';
import { RoomCategoryComponent } from './+roomCategory/roomCategory.component';
import { AddRoomCategoryComponent } from './+roomCategory/+add/add.roomCategory.component';
import { EditRoomCategoryComponent } from './+roomCategory/+edit/edit.roomCategory.component';

import { NgSelectModule } from '@ng-select/ng-select';
import { FileUploadModule } from 'ng2-file-upload';
import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';

import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
//all routes related to this module
const route = [
    { path: '', component: RoomsComponent, pathMatch: 'full' },
    { path: 'room', component: RoomsComponent },
    { path: 'setting', component: BookingSettingComponent},
    { path: 'add', component: AddRoomComponent },
    { path: 'edit/:id', component: EditRoomComponent },
    { path: 'category', component: RoomCategoryComponent },
    { path: 'category/add', component: AddRoomCategoryComponent},
    { path: 'category/edit/:id', component: EditRoomCategoryComponent},
];

@NgModule({
    imports: [
        FormsModule,
        CommonModule,
        SharedModule,
        TooltipModule.forRoot(),
        NgSelectModule,
        DataTableModule,
        FileUploadModule,
        ReactiveFormsModule,
        RouterModule.forChild(route),
        TranslateModule.forRoot({
            loader:{ 
                provide: TranslateLoader, 
                useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
                deps: [HttpClient] 
            }})
    ],
    exports: [],
    declarations: [
        RoomCategoryComponent,
        RoomsComponent,
        AddRoomCategoryComponent,
        EditRoomCategoryComponent,
        AddRoomComponent,
        EditRoomComponent,
        IsDisabled,
        BookingSettingComponent
    ],
    providers: [RoomCategoryService,RoomsService, BookingService],
})
export class RoomModule { }
