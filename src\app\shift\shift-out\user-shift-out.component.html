<section class="widget col-lg-6 col-md-8 col-sm-10">
    <header>
        <h4><span class="capitalized"><i class="fa fa-clock-o"></i>&nbsp;&nbsp;{{'SHIFT_OUT.SHIFTT_OUT' | translate:param}}</span></h4>
    </header>
    <hr>
    <div class="widget-body">
        <div class="mt">
            <div class="form-group row">
                <div class="col-md-6 col-xs-8 text-md-right">
                    {{'SHIFT_OUT.PREV_SHIF_AMT' | translate:param}}:
                </div>
                <div class="col-md-6 col-xs-4">
                    + <strong>
                        {{userShiftDetails.pre_petty_amount}}
                    </strong>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-6 col-xs-8 text-md-right">
                    {{'SHIFT_OUT.DAY_INC' | translate:param}}:
                </div>
                <div class="col-md-6 col-xs-4">
                    +
                    <strong>
                        {{userShiftDetails.day_income ? userShiftDetails.day_income : 0}}
                    </strong>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-6 col-xs-8 text-md-right">
                    {{'SHIFT_OUT.DAY_EXP' | translate:param}}:
                </div>
                <div class="col-md-6 col-xs-4">
                    <div class="form-group row" *ngFor="let expense of expenses">
                        <div class="col-md-12">
                            -
                            <strong>
                                {{expense.amount}}
                            </strong> &nbsp;<i class="fa fa-info-circle" [tooltip]="expense.note"></i>
                        </div>
                    </div>
                    <div class="form-group row" *ngIf="!expenses?.length">
                        <div class="col-md-12">
                            - <strong>0</strong>
                        </div>
                    </div>
                </div>
            </div>
            <hr>
            <div class="form-group row">
                <div class="col-md-6 col-xs-8 text-md-right">
                    <strong>
                        {{'SHIFT_OUT.BAL_PAT_CASH' | translate:param}}:
                    </strong>
                </div>
                <div class="col-md-6 col-xs-4">
                    <i class="fa fa-inr"></i>
                    <strong>
                        {{userShiftDetails.current_petty_amount ? userShiftDetails.current_petty_amount : 0}}
                    </strong>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-8 offset-md-6">
                    <div>
                        <button type="button" class="btn btn-sm btn-inverse capitalized" (click)="shiftOut()">
                            <i class="fa fa-sign-out"></i> &nbsp;&nbsp;{{'SHIFT_OUT.SHIFT-OUT' | translate:param}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>