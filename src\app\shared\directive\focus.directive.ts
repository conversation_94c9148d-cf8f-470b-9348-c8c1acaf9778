import {Directive, ElementRef, Renderer2, Input} from '@angular/core'
//  Replaced Renderer to Renderer2 while updating angular version 4 from 2 [it is depreacted]

@Directive({
  // selector: '.focus-directive'
  selector: 'focus-input'
})
export class SelectFirstInputDirective {
  constructor(private _eRef: ElementRef, private _renderer : Renderer2) { }

  private _getInputElement(nativeElement: any): any {
    if (!nativeElement || !nativeElement.children) return undefined;
    if (!nativeElement.children.length && nativeElement.localName === 'input' && !nativeElement.hidden) return nativeElement;

    let input;

    [].slice.call(nativeElement.children).every(c => {
      input = this._getInputElement(c);
      if (input) return false; // break
      return true; // continue!
    });

    return input;
  }

  ngAfterViewInit() {
    setTimeout(() => {
      let formChildren = [].slice.call(this._eRef.nativeElement.children);
      formChildren.every(child => {
        let input = this._getInputElement(child);
        if (input) {
          this._renderer.selectRootElement(input).focus();
          return false; // break!
        }
  
        return true; // continue!
      });
    }, 0);
  }
}