import { Injectable } from "@angular/core";
import { trim } from "jquery";
import { catchError, map } from 'rxjs/operators';
import { CommonHttpService } from "./../shared/services/common-http/common-http.service";

@Injectable()
export class OnlineBooking {

  constructor(private http: CommonHttpService) {
  }

  GetAvailableCategory(data){
    return this.http.post(`onlinebooking/findavilablecategory` , data)
  }
      /*Online booking services */
      saveOnlineRoombooking(data: any) {
        console.log('api calling now')
        return this.http.post(`bookingApprover/createOnlineBooking`, data)
    } 


}
