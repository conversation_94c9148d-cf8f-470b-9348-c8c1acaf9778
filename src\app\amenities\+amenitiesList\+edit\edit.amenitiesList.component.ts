import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { AmenitiesService } from './../../../shared/services/amenities.service';
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;

@Component({
    selector: 'edit-amenities',
    templateUrl: '../amenities.actions.html'
})
export class EditAmenitiesComponent implements OnInit {
    pageType: string = "Edit";
    amenitiesAdd: FormGroup;
    private sub: any;

    id: number;
    // Input/Output
    @Input() gethideEditAminity;
    @Input() selectedAmenity; // data to be edit
    @Output() sendhideEditAmenity = new EventEmitter();

    constructor(
        private _fb: FormBuilder,
        private AS: AmenitiesService,
        public translate: TranslateService
    ) {
        this.buidForm(); // build and init form values
        translate.get('AMENITIES.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        // --- Updates the matched keys --- //
        this.amenitiesAdd.patchValue(this.selectedAmenity);
        console.log("this.selectedAmenity : ",this.selectedAmenity);
        console.log("this.amenitiesAdd : ",this.amenitiesAdd);
    }

    buidForm() {
        this.amenitiesAdd = this._fb.group({
            name: ['', Validators.required],
            charge: ['0', [Validators.required, CustomValidators.number]],
            status: ['', Validators.required]
        });

    }
    saveAmenities() {
        if (this.amenitiesAdd.valid) {
            this.sub = this.AS.updateAmenity(this.selectedAmenity.id, this.amenitiesAdd.value).subscribe((res) => {
                if (res.status === "success") {
                    this.amenitiesAdd.reset();
                    console.log("Updated Amenity : ",res.data);
                    this.toggleChild(res.data);
                }
            },
                (err) => {
                    console.log("Main Error : ",err);
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        console.log("Data is available in Main Error : ");
                        errors.forEach(element => {
                            let control = this.amenitiesAdd.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }

    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
    toggleChild(data) {
        let result;
        this.gethideEditAminity = !this.gethideEditAminity;
        if (data) {
            result = { gethideEditAminity: this.gethideEditAminity, data: data }
        } else {
            result = { gethideEditAminity: this.gethideEditAminity }
        }
        this.sendhideEditAmenity.emit(result);
    }
}