import { NgModule, CUSTOM_ELEMENTS_SCHEMA, Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule,ReactiveFormsModule } from '@angular/forms';

import { AddUserComponent } from './+userManagement/+add/add.user.component';
import { EditUserComponent } from './+userManagement/+edit/edit.user.component';
import { UserManagementComponent } from './+userManagement/userManagement.component';
import { SearchPipe2 } from './searchText.pipe';

import { NgSelectModule } from '@ng-select/ng-select';
import { CustomFormsModule } from 'ng2-validation'
import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { NKDatetimeModule } from 'ng2-datetime/ng2-datetime';
import { TextMaskModule } from 'angular2-text-mask';

import { UserRoleService } from './../shared/services/userrole.service';
import { UserService } from './../shared/services/user.service';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClient } from '@angular/common/http';

import 'jasny-bootstrap/js/inputmask.js';
import 'ng2-datetime/src/vendor/bootstrap-datepicker/bootstrap-datepicker.min.js'; 

// all routes related to this Role module.
export const routes = [
  { path: '', component: UserManagementComponent, pathMatch: 'full' }
]

@NgModule({
    imports: [
        FormsModule,
        CommonModule,
        NgSelectModule,
        TooltipModule.forRoot(),
        DataTableModule,
        NKDatetimeModule,
        CustomFormsModule,
        ReactiveFormsModule,
        TextMaskModule,
        RouterModule.forChild(routes),
        TranslateModule.forRoot(
          { 
            loader: {
              provide: TranslateLoader,
              useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
              deps: [HttpClient] 
            }
          }
        ),
    ],
    exports: [],
    declarations: [
      SearchPipe2,
      AddUserComponent,
      EditUserComponent,
      UserManagementComponent,
    ],
    providers: [UserService,UserRoleService],
  schemas:  [ CUSTOM_ELEMENTS_SCHEMA ]
})
export class UserModule { }
