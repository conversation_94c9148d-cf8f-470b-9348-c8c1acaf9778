import { ExpensesService } from './../../shared/services/expenses.service';
import { CustomValidators } from 'ng2-validation';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
// import { EventEmitter } from '@angular/common/src/facade/async';
import { Output, EventEmitter } from '@angular/core';
import { Component, OnInit, Input } from '@angular/core';
import moment from 'moment';
import { TranslateService } from '@ngx-translate/core';


@Component({
    selector: 'add-expenses',
    templateUrl: '../expenses.action.component.html'
})

export class AddExpenseComponent implements OnInit {
    pageType: string = 'Add';
    @Input() selectedPolicy;
    @Output() sendAddedEdited = new EventEmitter();
    @Output() closeComp = new EventEmitter();
    expensesForm: FormGroup;
    constructor(
        private fb: FormBuilder,
        private ES: ExpensesService,
        public translate: TranslateService
    ) { 
        translate.get('EXPENSES.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() { 
        this.initForm();
    }

    initForm() {
        this.expensesForm = this.fb.group({
            note: ['', Validators.required],
            amount: ['', [Validators.required, CustomValidators.gte(0)]],
            expense_date: []
        });
    }

    addExpense() {
        if(this.expensesForm.valid) {
            this.expensesForm.get('expense_date').patchValue(moment().format('YYYY-MM-DD') + 'T00:00:00.000Z');
            this.ES.addExpense(this.expensesForm.value)
                .subscribe((res) => {
                    if(res.status === 'success') {
                        this.expensesForm.reset();
                        this.sendAddedEdited.emit(res.data);
                    }
                })
        }
    }

    closeThisComp(){
        this.closeComp.emit(true);
    }
}