import { DharamshalaService } from './../shared/services/dharamshala.service';
import { SelectFirstInputDirective } from './../shared/directive/focus.directive';
import { BookingService } from './../shared/services/booking.service';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReservationsComponent } from './+reservations/reservations.component';
import { ModalModule } from 'ngx-bootstrap/modal';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { ContextMenuModule } from 'ngx-contextmenu';
import { NgSelectModule } from '@ng-select/ng-select';
import { NKDatetimeModule } from 'ng2-datetime/ng2-datetime';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { TimepickerModule } from 'ngx-bootstrap/timepicker';
import { TextMaskModule } from 'angular2-text-mask';
import { FileUploadModule } from 'ng2-file-upload';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { InternationalPhoneModule } from 'ng4-intl-phone';
import { TruncatePipe } from "../layout/pipes/truncate.pipe";
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { SharedModule } from '../shared/shared.module';
import '../../../node_modules/jquery-ui/ui/selectable.js';
import 'bootstrap/dist/css/bootstrap.css';
import 'jquery/dist/jquery.min.js';
import 'ng2-datetime/src/vendor/bootstrap-datepicker/bootstrap-datepicker3.min.css';
import 'ng2-datetime/src/vendor/bootstrap-datepicker/bootstrap-datepicker.min.js';
import 'ng2-datetime/src/vendor/bootstrap-timepicker/bootstrap-timepicker.min.css';
import 'ng2-datetime/src/vendor/bootstrap-timepicker/bootstrap-timepicker.min.js';
import 'jasny-bootstrap/js/fileinput.js';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

const route = [
    { path: '', component: ReservationsComponent, pathMatch: 'full' }
];

@NgModule({
    imports: [
        RouterModule.forChild(route),
        FormsModule,
        CommonModule,
        SharedModule,
        TooltipModule,
        NgSelectModule,
        TextMaskModule,
        PdfViewerModule,
        NKDatetimeModule,
        FileUploadModule,
        ReactiveFormsModule,
        InternationalPhoneModule,
        TabsModule,
        ModalModule,
        BsDatepickerModule,
        TimepickerModule,
        ContextMenuModule.forRoot({
            useBootstrap4: true
        }),
        TranslateModule.forChild({
            loader:{ 
                provide: TranslateLoader, 
                useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
                deps: [HttpClient] 
        }}),
    ],
    exports: [],
    declarations: [
        TruncatePipe,
        ReservationsComponent,
        SelectFirstInputDirective
    ],
    // schemas:[CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
    providers: [BookingService,DharamshalaService],
})
export class ReservationModule { }
