import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DharamshalaService } from 'app/shared/services/dharamshala.service';

@Component({
    selector: 'splite',
    templateUrl: './splite.component.html'
})

export class SpliteComponent implements OnInit{
   
    public newUrl:string
    public data:any
    public isavelebal:boolean = false
    constructor(private router : Router,private DS: DharamshalaService,){

    }
    
    ngOnInit(){
      let a = this.DS.getSplite().subscribe((res) =>{
        if(res.data && res.status === "success")
        {
            console.log("data",res.data)
            this.data = res.data
        }
      })
    }
    public editSpliteData(){
       this.router.navigateByUrl(`/admin/dharamshala/split/${1}`).then((data) =>{
         console.log(data)
       },(error) =>{
         console.log(error)
       })
            
    }
}