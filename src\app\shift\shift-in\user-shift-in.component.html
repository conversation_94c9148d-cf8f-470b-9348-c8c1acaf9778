<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-sign-in"></i>&nbsp;&nbsp;{{'SHIFT_IN.SHIT_IN' | translate:param}}</span></h4>
  </header>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <br>
      <fieldset>
        <form [formGroup]="shiftForm" (ngSubmit)="shiftIn()">
          <div class="form-group row" *ngIf="availableUserShiftTransfersDetails.length > 0">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'SHIFT_IN.SHIFT_FRO_USER' | translate:param}}: </label>
            <div class="col-md-3">
              <ng-select
                [items]="availableUserShiftTransfers"
                bindLabel="text"
                bindValue="id"
                [(ngModel)]="shiftForm.controls.user_id.value"
                (change)="pettyCashUserChanged($event)"
                [clearable]="false"
                formControlName="user_id"
                placeholder="Select user">
              </ng-select>
              <span class="errMsg" *ngIf="!shiftForm.controls.user_id.valid && !shiftForm.controls.user_id.pristine">
                <span [hidden]="!shiftForm.controls.user_id.errors?.required">{{'SHIFT_IN.USER_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row" *ngIf="prePettyCash >= 0">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'SHIFT_IN.PETI_CASH' | translate:param}}: </label>
            <div class="col-md-3">
              <div class="form-control">
                <i class="fa fa-inr"></i>&nbsp;{{prePettyCash}}
              </div>
            </div>
          </div>

          <div class="form-group row" *ngIf="isInShift">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right"></label>
            <div class="col-md-3 text-success">
              <i class="fa fa-check"></i> {{'SHIFT_IN.CURR_SHIFT' | translate:param}}
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" class="btn btn-sm btn-inverse capitalized" [disabled]="isInShift || shiftForm.invalid">
                  <i class="fa fa-sign-in"></i> &nbsp;&nbsp;{{'SHIFT_IN.SHITF_IN' | translate:param}}
                </button>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
<div class="modal fade" bsModal #alertModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}" tabindex="-1"
  role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header text-danger">
        <h3>
          {{'SHIFT_IN.ALERT' | translate:param}}!
        </h3>
      </div>
      <div class="modal-body">
        <div class="change-customer-type">
          <h5 style="line-height: 1.5;">{{'SHIFT_IN.CONF_ACC_PETTY' | translate:param}}?</h5>
          <br>
          <form #f="ngForm" (ngSubmit)="proceedWithShiftIn(f.value)">
            <div class="form-group">
              <label for="" class="form-label">{{'SHIFT_IN.PETI_CASH' | translate:param}}</label>
              <input type="text" class="form-control" name="pre_petty_amount" required ngModel #pre_petty_amount="ngModel" placeholder="{{'SHIFT_IN.PET_RUPE' | translate:param}}"></div>
            <div class="row">
              <div class="col-sm-12">
                <button type="submit" [disabled]="f.invalid" class="btn btn-md btn-inverse padding-left-right-30">{{'SHIFT_IN.YES' | translate:param}}</button>
                <button type="button" class="btn btn-md btn-inverse padding-left-right-30" (click)="alertModal.hide()">{{'SHIFT_IN.NO' | translate:param}}</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>