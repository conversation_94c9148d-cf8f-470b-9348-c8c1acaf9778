import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { BookingService } from 'app/shared/services/booking.service';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ***
import { Subject, Subscription } from 'rxjs';
declare var Messenger: any;


@Component({
    selector: 'room-undermaintenance',
    templateUrl: 'room-undermaintenance.component.html',
    styleUrls: ['room-undermaintenance.component.scss']
})

export class RoomUnderMaintenanceComponent implements OnInit, OnDestroy {
    config: any;// New Change ****
    data: any[] = [];
    searchQuery: String;
    searchForm: FormGroup;
    canViewRecords: boolean;
    originalData: any[] = [];
    roomTypeList: any[] = [];
    getTypeList: any[] = [];
    customerTypeList: any[] = [];
    selectedFilterTypes: any = {
        guest_id: "000000",
        room_type_id: "000000",
        get_type: "occupied"
    };
    
    public datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        format: 'dd/mm/yyyy',
        todayHighlight: true,
        icon: 'fa fa-calendar',
    }
    public bookingTypeOptions: Select2.Options = {
        width: '100%',
    };

    private sub: Subscription;
    private csvSub: Subscription;
    private getReportDataSub: Subscription;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private BS: BookingService,
        private fb: FormBuilder,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    public paxCount = (a: any) => {
        return (a.adult ? a.adult : 0) + (a.child ? a.child : 0);
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    ngOnInit() {
        this.canViewRecords = true;
        this.getReportData()
    }

    getReportData() {
        this.getReportDataSub = this.BS.getRoomUnderMaintenanceReport()
            .subscribe((res) => {
                if (res.status === 'success') {
                    this.originalData = res.data;
                    this.initializeData();
                }
            },error => {
                this.canViewRecords = false;
            });
    }
    search() {
        if (this.data) {
            this.initializeData();
            if (this.data && this.searchQuery && this.searchQuery.trim() != '') {
                this.data = this.data.filter(data => {
                    let searchTarget = '';
                    Object.keys(data).forEach(key => {
                        searchTarget += data[key];
                    })
                    return (searchTarget.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
                });
            }
            else {
                this.initializeData();
            }
        }
    }
    clearSearch() {
        this.initializeData();
        this.searchQuery = undefined;
    }
    printRecords() {
        if (this.data && this.data.length > 0) {
            window.print();
        } else {
            this.showNotification('No data available!', 'error', true);
        }

    }
    showNotification(message: string, type: string, showCloseButton: boolean = true, hideAfter: number = 3) {
        Messenger().post({
          type: type,
          message: message,
          hideAfter: hideAfter,
          showCloseButton: showCloseButton,
        });
      }
    initializeData() {
        this.data = this.originalData;
    }
    ngOnDestroy(): void {
        if (this.getReportDataSub)
            this.getReportDataSub.unsubscribe();
        if (this.csvSub)
            this.csvSub.unsubscribe();
        
            this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}