import { Component, OnInit, Input, Output, EventEmitter, OnChanges, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'edit-funds',
    templateUrl: '../funds.action.html'
})

export class EditFundsComponent implements OnInit, OnDestroy, OnChanges {
    public fundsTypeForm: FormGroup;
    public pageType: string = "Edit";
    private sub: any;
    @Input() selectedFund;
    @Output() hideEditEvent = new EventEmitter();
    @Output() editToList = new EventEmitter();
    constructor(
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        public translate: TranslateService
    ) { 
        translate.get('FUNDS.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.initForm();
    }
    ngOnChanges(change: any) {
        if (this.fundsTypeForm) {
            this.fundsTypeForm.patchValue(change.selectedFund.currentValue);
        }
    }
    initForm() {
        // console.log("selected fund : ", this.selectedFund);        
        this.fundsTypeForm = this._fb.group({
            name: ['', Validators.required],
            status: [true, Validators.required],
            is_default: [false]
        })
        this.fundsTypeForm.patchValue(this.selectedFund);
        // console.log("fundsTypeForm : ",this.fundsTypeForm);
    }
    toggleChild(data: any = true) {
        this.editToList.emit(data);
    }
    hideComponent() {
        this.hideEditEvent.emit();
        this.fundsTypeForm.reset();
    }
    saveFundType() {
        if (this.fundsTypeForm.valid) {
            if(this.fundsTypeForm.value.is_default && !this.fundsTypeForm.value.status) {
                this.errorNotification("Default fund cannot be marked as in-active!");
            } else {
                // console.log("selected fund id : ",this.selectedFund.id);
                this.sub = this.DS.updateFunds(this.selectedFund.id, this.fundsTypeForm.value)
                    .subscribe((res) => {
                        if (res.status == "success") {
                            // --- Response isn't consistent when fund-list contains a checked is_default fund --- //
                            // console.log("Edited Fund : ",res.data);
                            this.toggleChild(res.data);
                            this.fundsTypeForm.reset();
                        }
                    }, (err) => {
                        let errBody = JSON.parse(err._body);
                        let errors = errBody.data;
                        if (errors.length > 0) {
                            errors.forEach(element => {
                                let control = this.fundsTypeForm.controls[element.fieldname];
                                control.setErrors({
                                    backend: element.error
                                });
                            });
                        }
                    })
            }
        } else {
            this.errorNotification("Form can not be submitted");
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
    errorNotification(text: string, duration: number = 5) {
        Messenger().post({  hideAfter: duration,
            message: text,
            type: "error",
            showCloseButton: true
        });
    }
}