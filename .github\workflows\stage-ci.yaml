name: stage-ci-pipeline

on:
  workflow_dispatch:

permissions:
  contents: write
  actions: write


jobs:
  build:
    runs-on: self-hosted-dhaharmshala
    
    strategy:
      matrix:
        node-version: [12.18.0]
    
    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
 
      - name: Create SSL Directory if Not Exists
        run: |
          if [ ! -d "./ciSsl" ]; then
            mkdir ./ciSsl
          fi
          if [ ! -f "./ciSsl/bookings.swaminarayangadi.com.crt" ]; then
            cp /home/<USER>/newSSL/bookings.swaminarayangadi.com.crt ./ciSsl/
          fi
          if [ ! -f "./ciSsl/bookings.swaminarayangadi.com_key.txt" ]; then
            cp /home/<USER>/newSSL/bookings.swaminarayangadi.com_key.txt ./ciSsl/
          fi


      - name: Build
        run: |
          npm install
          npm run build:prod

      - name: Login Dockerhub
        env:
          STACKDOT_DOCKER_USERNAME: ${{secrets.STACKDOT_DOCKER_USERNAME}}
          STACKDOT_DOCKER_PASSWORD: ${{secrets.STACKDOT_DOCKER_PASSWORD}}
        run: docker login -u $STACKDOT_DOCKER_USERNAME -p $STACKDOT_DOCKER_PASSWORD

      - name: Build the Docker image
        run: docker build -t jeeejagani/dharmshala-web .

      - name: Push to Dockerhub
        run: docker push jeeejagani/dharmshala-web

      - name: Change tag and push it to dockerhub   
        run: | 
          current_date=$(date "+%d.%m.%y.%H.%M.%S.%3N")
          docker tag jeeejagani/dharmshala-web jeeejagani/dharmshala-web:$current_date
          docker push jeeejagani/dharmshala-web:$current_date 
        
