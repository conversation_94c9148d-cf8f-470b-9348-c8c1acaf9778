<section class="widget">
  <header>
    <h4><span class="" style="color: red;"><i class="fa fa-calendar"></i>&nbsp;&nbsp;{{'ROOM_BOOKING_SETTING.ROOM_BOOK_SETI' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="clearfix"></div>
  <div class="widget-body">
    <div class="mt">
      <div *ngIf="canViewRecords" class="row">
        <div class="col-md-4">

        </div>
        <div class="col-md-8">
          <ul class="pagination">
            <li><a href="javascript:void(0)" (click)="prevYear()"><i class="fa fa-angle-left"></i></a></li>
            <li><a href="javascript:void(0)" (click)="prevMonth()"><i class="fa fa-angle-left"></i></a></li>
            <li class="active"><a href="javascript:void(0)" style="min-width: 200px;text-align: center;">{{monthNames[currMonth]}}/{{currYear}}</a></li>
            <li><a href="javascript:void(0)" (click)="nextMonth()"><i class="fa fa-angle-right"></i></a></li>
            <li><a href="javascript:void(0)" (click)="nextYear()"><i class="fa fa-angle-right"></i></a></li>
          </ul>
        </div>
        

      </div>
      <form *ngIf="canViewRecords" [formGroup]="bookingSettings" (ngSubmit)="savebookingSettings()">
        <div class="row">
          <div class="col-md-12 text-center" *ngIf="!roomCat">
            <br>
            <br>
            <br>
            <i class="fa fa-circle-o-notch fa-spin fa-2x"></i>
            <br>
            <br>
            <br>
          </div>
          <div class="col-md-12" *ngIf="roomCat">
            <table class="custome_table" formArrayName="room_settings">
              <tr class="date-list">
                <td> <span class="bold-fonts">{{'ROOM_BOOKING_SETTING.ROOM' | translate:param}} <br>{{'ROOM_BOOKING_SETTING.TYPE' | translate:param}}</span></td>
                <td *ngFor="let item of ranges" class="td-boxed" [ngClass]="{'disabled': item.is_disabled }">
                  <span class="date">{{item.date}}</span>
                  <span class="day_name">{{item.day}}</span>
                </td>
              </tr>
              <tr *ngFor="let item of bookingSettings.controls.room_settings.controls; let i = index" [formGroupName]="i">
                <td>
                  <div class="dates">
                    <div class="centered">
                      {{item.value.room}}
                    </div>
                    <div class="child-flexed">
                      <span>
                        {{'ROOM_BOOKING_SETTING.OQ' | translate:param}}
                      </span>
                      <span>
                        {{'ROOM_BOOKING_SETTING.PQ' | translate:param}}
                    </span>
                    </div>
                  </div>
                </td>
                <td formArrayName="settings" class="__settings" *ngFor="let item2 of item.controls.settings.controls; let j = index;">
                  <div [formGroupName]="j" class="__inputs">
                    <input type="text" formControlName="online_quota" [max]="item2.value.total_room" (keypress)="isNumber($event)" #onlinequota
                      (focus)="setInputVal(item2)" (blur)="saveDemo(item2,onlinequota,personalquota)">
                    <input type="text" #personalquota formControlName="personal_quota">
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </form>
      <!--<pre>
        {{ bookingSettings.value | json }}
      </pre>-->
      <span *ngIf="!canViewRecords">
         {{'ROOM_BOOKING_SETTING.PERMISSION_DENIED' | translate:param}}
      </span>
    </div>
  </div>
</section>
