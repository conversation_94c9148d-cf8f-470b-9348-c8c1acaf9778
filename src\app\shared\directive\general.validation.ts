import { AbstractControl } from "@angular/forms";
export class GeneralValidations {
    // --- Function prevents user from entering a blank space, e.g name ' ' ---//
    static spaceCharCheck(control: AbstractControl) {
        if (control.value != null && (<string>control.value).trim() == '') {
            return {
                spaceCharCheck: true
            };
        }
        else
            return null;
    }
    static defaultNameCheck(control: AbstractControl) {
        if (control.value.indexOf('guest') > -1) {
            return {
                defaultNameCheck: true
            };
        }
        else
            return null;
    }
}