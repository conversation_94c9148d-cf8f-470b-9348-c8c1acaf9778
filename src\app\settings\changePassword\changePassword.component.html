<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-lock"></i>&nbsp;&nbsp;{{'CHANGE_PASSW.CHANGE_PA' | translate:param}}</span></h4>
  </header>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <br>
      <fieldset>
        <form [formGroup]="changePassword" (ngSubmit)="savechangePassword()">
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'CHANGE_PASSW.OLD_PASS' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="password" formControlName="old_password" class="form-control">
               <span class="errMsg" *ngIf="!changePassword.controls.old_password.valid && !changePassword.controls.old_password.pristine">
                <span [hidden]="!changePassword.controls.old_password.errors?.required">{{'CHANGE_PASSW.CURR_PASS_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>
        
            <div class="form-group row">
              <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'CHANGE_PASSW.NEW_PASS' | translate:param}}</label>
              <div class="col-md-8 ">
                <input type="password" class="form-control" minlength="6" formControlName="new_password">
                 <span class="errMsg" *ngIf="!changePassword.controls.new_password.valid && !changePassword.controls.new_password.touch && !changePassword.controls.new_password.pristine">
                <span [hidden]="!changePassword.controls.new_password.errors?.required">{{'CHANGE_PASSW.NEW_PASS_REQ' | translate:param}}</span>
                 <span [hidden]="!changePassword.controls.new_password.errors?.minlength">{{'CHANGE_PASSW.NEW_PASS_SHORT' | translate:param}}</span>
              </span>
              </div>
            </div>

            <div class="form-group row">
              <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'CHANGE_PASSW.CONF_NEW_PASS' | translate:param}}</label>
              <div class="col-md-8 ">
                <input type="password" class="form-control" formControlName="cfm_password">
                 <span class="errMsg" *ngIf="!changePassword.controls.cfm_password.valid && !changePassword.controls.cfm_password.pristine">
                <span [hidden]="!changePassword.controls.cfm_password.errors?.required">{{'CHANGE_PASSW.CONF_PASS' | translate:param}}</span>
                <span [hidden]="!changePassword.controls.cfm_password.errors?.MatchPassword">{{'CHANGE_PASSW.PASS_NO_MATCH' | translate:param}} </span>
              </span>
              </div>
            </div>
           
          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" [disabled]="!changePassword.valid" class="btn btn-sm btn-inverse capitalized">{{'CHANGE_PASSW.SAVE' | translate:param}}</button>
                <a [routerLink]="['/']" class="btn btn-sm btn-secondary">{{'CHANGE_PASSW.CANCEL' | translate:param}}</a>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
