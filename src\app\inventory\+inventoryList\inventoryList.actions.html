<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-cubes"></i>&nbsp;&nbsp;{{pageType}} Inventory</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item "><a [routerLink]="['/admin/inventory']">Inventory</a></li>
    <li class="breadcrumb-item active">{{pageType}} Inventory</li>
  </ol>

  <div class="clearfix"></div>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="inventoryAdd" (ngSubmit)="saveInventory($event)">
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">Name</label>
            <div class="col-md-8 ">
              <input type="text" class="form-control" placeholder="" formControlName="name">
            </div>
          </div>
          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">Type</label>
            <div class="col-md-8 ">
              <input type="hidden" formControlName="type" required>
             <ng-select id="default-select" [items]="getInventoryType()" (change)="getInventoryTypeChanged($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Inventory Type"></ng-select>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">Vendor</label>
            <div class="col-md-8 ">
              <div class="input-group">
                <input type="text" #vandor class="form-control small-height" placeholder="">
                <span class="input-group-addon no-padd">
                <a class="btn btn-sm btn-inverse" href="javascript:void(0)" (click)="addVandor(vandor)"><i class="fa fa-plus"></i></a>
            </span>
              </div>
            </div>
          </div>
          <div class="form-group row" formArrayName="vandor">
            <div class="col-md-8 offset-md-3">
              <div class="vendor-container">
                <span class="vendor-added" *ngFor="let vl of inventoryAdd.controls.vandor.controls;let i = index" [formGroupName]="i">
                    <input type="text" formControlName="title" autoWidth>
                    <i class="fa fa-times" (click)="removeVandor(i)"></i>
                </span>
              </div>
            </div>
          </div>
          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">Status</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio" *ngFor="let s of status;let i = index">
                  <input type="radio" formControlName="status" id="radio-{{i}}" [value]="s">
                  <label for="radio-{{i}}">
                        {{s}}
                      </label>
                </div>
              </div>
            </div>
          </div>


          <div class="form-group row">

            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" class="btn btn-sm btn-inverse capitalized" [disabled]="!inventoryAdd.valid"><i class="fa" *ngIf="inventoryAdd.valid" [ngClass]="{'fa-spin fa-spinner': loadingStatus == 1,'fa-check': loadingStatus == 0, 'fa-repeat' : loadingStatus == 2}"></i>Save</button>
                <a [routerLink]="['/admin/inventory']" class="btn btn-sm btn-secondary">Cancel</a>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
