import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()
export class MenuGroupService{

    constructor(private chttp: CommonHttpService) {
    }

    addMenuGroup(data)
    {
        return this.chttp.post('menu/add', data , true);
    }
    getRouter_Parents(id) {
        return this.chttp.get(`menugroup/edit/manage/${id}`)        
    }
    editMenuGroup(id,data){
        return this.chttp.post(`menu/edit/${id}`, data, true)
    }
    getAllMenuGroup(){
        return this.chttp.get('menu/list');
    }
    // manage menu services
    getMenus(id){
        return this.chttp.get(`menugroup/edit/manage/${id}`);
    }
    saveMenu(id,data){
        return this.chttp.post(`menugroup/edit/manage/${id}`,data , true);
    }
    addMenuItem(id,data){
        return this.chttp.post(`menugroup/add/manage/${id}`,data, true);
    }
    saveMenuEdit(data,id){
        return this.chttp.post(`menugroup/edit/menu/${id}`, data, true);
    }
    deleteMenuItem(id) {
        return this.chttp.get(`menugroup/delete/${id}`, true);
    }
}