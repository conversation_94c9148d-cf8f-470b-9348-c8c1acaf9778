<div class="row">
  <div [ngClass]="{'col-sm-6': (!hideAdd || !hideEdit),'col-sm-12': (hideAdd && hideEdit)}">
    <section class="widget">
      <header>
        <h4><span class="" style="color: red;"><i class="fa fa-clock-o"></i>&nbsp;&nbsp;{{'STAY_TYPE.STAY_TYPE_MANA' | translate:param}}</span></h4>
      </header>
      <hr class="large-hr">
      <div class="float-sm-right text-right col-sm-12">
        <button type="button" *ngIf="auth.roleAccessPermission('staytype','add')" [disabled]="!hideAdd" (click)="showAdd()" class="display-inline-block btn btn-sm btn-inverse"
          tooltip="{{'STAY_TYPE.ADD_STA_TYPE' | translate:param}}" placement="top"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{'STAY_TYPE.ADD' | translate:param}}</button>
        <div class="form-group display-inline-block __search">
          <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'STAY_TYPE.SEARCH' | translate:param}}">
          <span class="form-group-addon"><i class="fa fa-search"></i></span>
          <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
        </div>
      </div>
      <div class="clearfix"></div>
      <div class="widget-body">
        <div class="mt">
          <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
            <thead>
              <tr>
                <th>
                  <mfDefaultSorter by="id">#</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="name">{{'STAY_TYPE.NAME' | translate:param}}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="duration">{{'STAY_TYPE.DURATION' | translate:param}}</mfDefaultSorter>
                </th>
                <th class="no-sort">
                  <mfDefaultSorter by="charge">{{'STAY_TYPE.CHARGE' | translate:param}}</mfDefaultSorter>
                </th>
                <th class="no-sort">
                  <mfDefaultSorter by="is_default">{{'STAY_TYPE.DEFAULT' | translate:param}}</mfDefaultSorter>
                </th>
                <th class="no-sort">
                  <mfDefaultSorter by="status">{{'STAY_TYPE.STATUS' | translate:param}}</mfDefaultSorter>
                </th>
                <th class="no-sort">
                  <mfDefaultSorter>{{'STAY_TYPE.ICON' | translate:param}}</mfDefaultSorter>
                </th>
                <th *ngIf="auth.roleAccessPermission('staytype','edit')" class="no-sort">
                  <mfDefaultSorter by="status">{{'STAY_TYPE.ACTION' | translate:param}}</mfDefaultSorter>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let ds of mf.data; let i = index;">
                <td>{{i + 1}}</td>
                <td><span class="uppercase fw-semi-bold"> {{ds.name}}</span></td>
                <td><span class="uppercase fw-semi-bold">
                    {{ds.duration}}
                  </span>
                  <small *ngIf="ds.duration">
                    <strong>{{'STAY_TYPE.HRS' | translate:param}}</strong>
                  </small>
                </td>
                <td><span class="uppercase fw-semi-bold"> {{ds.charge}}&nbsp;<i *ngIf="ds.charge" class="fa fa-percent"></i></span></td>
                <td>
                  <div style="position: relative" placement="left" [tooltip]="!auth.roleAccessPermission('staytype','edit') ? 'You do not have the permission to edit stay type!' : ''">
                    <input type="radio" name="is_default_radio" class="is_default_radio" (change)="changeDefault(ds)" id="{{ds.id}}_is_default_radio"
                      [attr.disabled]="!auth.roleAccessPermission('staytype','edit') ? true : null" [checked]="ds.is_default">
                    <label for="{{ds.id}}_is_default_radio"></label>
                  </div>
                </td>
                <td class=" ">
                  <span class="text-success" *ngIf="ds.status">{{'STAY_TYPE.ACTIVE' | translate:param}}</span>
                  <span class="text-danger" *ngIf="!ds.status">{{'STAY_TYPE.INACTIVE' | translate:param}}</span>
                </td>
                <td>
                  <span *ngIf="ds.stay_type_icon" class="badge">
                    <i class="fa fa-{{ds.stay_type_icon}}"></i>
                  </span>
                </td>
                <td *ngIf="auth.roleAccessPermission('staytype','edit')" class="width-100">
                  <button type="button" *ngIf="auth.roleAccessPermission('staytype','edit')" (click)="showEdit(ds)" class="btn btn-xs btn-default"
                    tooltip="{{'STAY_TYPE.EDIT_STAY_TYPE' | translate:param}}" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{'STAY_TYPE.EDIT' | translate:param}}</button>
                </td>
              </tr>
              <tr *ngIf="canViewRecords && mf.data.length === 0">
                <td colspan="100">
                  {{'STAY_TYPE.NO MATCHES' | translate:param}}
                </td>
              </tr>
              <tr *ngIf="!canViewRecords">
                <td class="text-danger" colspan="100">
                  {{'STAY_TYPE.PERMISSION_DENIED' | translate:param}}
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="12">
                  <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </section>
  </div>
  <add-stay-type class="col-sm-6" *ngIf="!hideAdd" (hideAddEvent)="handleAdd()" (addToList)="addTolistEvent($event)"></add-stay-type>
  <edit-stay-type class="col-sm-6" *ngIf="!hideEdit" [selectedStayType]="selectedStayType" (hideEditEvent)="handleEdit($event)"
    (editToList)="editTolistEvent($event)"></edit-stay-type>
</div>