import { NgModule }      from '@angular/core';
import { CommonModule }  from '@angular/common';

import { RouterModule } from '@angular/router';

import { Dashboard } from './dashboard.component';
import { SharedModule } from 'app/shared/shared.module';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

export const routes = [
  { path: '', component: Dashboard, pathMatch: 'full' }
];


@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    TranslateModule.forRoot({
      loader:{ 
        provide: TranslateLoader, 
        useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
      deps: [HttpClient] 
    }}),
    RouterModule.forChild(routes),
  ],
  declarations: [
    Dashboard
  ],
})
export class DashboardModule {
  static routes = routes;
}
