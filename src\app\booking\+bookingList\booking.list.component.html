<view-booking *ngIf="viewComp" [data]="selectedBooking" (goBack)="closeComp()"></view-booking>

<section class="widget" *ngIf="!viewComp">
  <header>
    <h4><span class=""><i class="fa fa-clipboard"></i>&nbsp;&nbsp;Bookings History Management</span></h4>
  </header>
  <hr class="large-hr">
  <!-- ******************Advanced search****************** -->
  <!-- <div class="float-sm-right text-right col-sm-8">
    <button class="display-inline-block btn btn-sm btn-inverse"><i class="fa fa-refresh"></i>&nbsp;&nbsp;Reset</button>
    <button class="display-inline-block btn btn-sm btn-default" (click)="showAdvance = !showAdvance"><i class="fa" [ngClass]="{'fa-angle-down': !showAdvance, 'fa-angle-up': showAdvance}"></i>&nbsp;&nbsp;Advance</button>
  </div> -->
  <div class="float-sm-right text-right col-sm-5">
    <div class="form-group __search">
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="search()" placeholder="Search">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="">
    <div class="advance-search" *ngIf="showAdvance">
      <div class="row">
        <div class="col-md-3">
          <input type="text" class="form-control" placeholder="Booking Id">
        </div>
        <div class="col-md-3">
          <datetime [(ngModel)]="date4" [timepicker]="false"  [datepicker]="datepickerOpts"></datetime>
        </div>
        <div class="col-md-3">
          <ng-select [items]="getCustomerType()" (change)="getCustomerTypeCahnged($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Customer Type"></ng-select>
        </div>
        <div class="col-md-3">
<ng-select [items]="getBookingStatus()" (change)="getBookingStatusCahnged($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Booking Status"></ng-select>
        </div>
      </div>
      <br>
      <div class="row">
        <div class="col-md-6">
          <input type="text" class="form-control" placeholder="Guest Name">
        </div>
        <div class="col-md-3">
<ng-select [items]="getRoomNo()" (change)="getRoomNoChanged($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Room Number"></ng-select>
        </div>
        <div class="col-md-3">
          <button class="btn btn-sm btn-inverse btn-block"><i class="fa fa-search"></i>&nbsp;&nbsp;Advance Search</button>
        </div>
      </div>
    </div>
  </div>
  <div class="widget-body table-scroll">
    <div class="mt">
        <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="25">
        <thead>
        <tr>
          <th>
            <mfDefaultSorter by="id">#</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="guest_name">Cust. Name</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter [by]="bookingDate">Booking Date</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter [by]="uniqueBookingId">Booking ID</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="customer_name">Cust. Type</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter [by]="checkInDate">Check In</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter [by]="checkOutDate">Check Out</mfDefaultSorter>
          </th>
          <th class="text-center">
            <mfDefaultSorter by="current_status">Status</mfDefaultSorter>
          </th>
          <th *ngIf="auth.roleAccessPermission('booking','view')" class="no-sort text-center">
            <mfDefaultSorter>Action</mfDefaultSorter>
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let ds of mf.data">
          <td>{{ ds.id }}</td>
          <td class="uppercase"><span class="fw-semi-bold">{{ds.guest_name}}</span></td>
          <td class="capitalize">{{(ds.booking_date) | date}}</td>
          <td>{{ds.unique_booking_id}}</td>
          <td class="capitalize">{{ds.customer_name}}</td>
          <td class="capitalize">{{ds.detail[0].expected_check_in | date:'MMM d, y, h:mm a'}}</td>
          <td class="capitalize">{{ds.detail[0].expected_check_out | date:'MMM d, y, h:mm a'}}</td>
          <td class="capitalize text-center" >
            <span class="badge" [ngStyle]="{'background-color':ds.booking_type == '0' ? '#009688' : '#673AB7'}">
              <i class="fa" [ngClass]="{'fa-user': ds.booking_type == '0','fa-users': ds.booking_type == '1'}"></i>
            </span> | 
           <label for="" class="label" 
            [ngClass]="{
                'label-warning': ds.current_status == 'checkout', 
                'label-success': ds.current_status == 'checkin',
                'label-primary': ds.current_status == 'reserved',
                'label-danger': ds.current_status == 'cancelled'}">
                {{ds.current_status}}
            </label>
          </td>
          <td *ngIf="auth.roleAccessPermission('booking','view')" class="width-100 text-center">
            <button (click)="viewBookingDetail(ds)" class="btn btn-sm btn-default" tooltip="View booking details" placement="top"><i class="fa fa-question"></i> &nbsp;View</button>
          </td>
        </tr>
        <tr *ngIf="canViewRecords && mf.data.length === 0">
          <td colspan="100">
            No matches
          </td>
        </tr>
        <tr *ngIf="!canViewRecords">
          <td class="text-danger" colspan="100">
            You currently don't have the permission to view these records.
          </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="12">
            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
          </td>
        </tr>
        </tfoot>
      </table>
    </div>
  </div>
</section>
