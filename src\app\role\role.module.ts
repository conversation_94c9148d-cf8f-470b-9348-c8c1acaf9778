import { UserRoleService } from './../shared/services/userrole.service';
import { NgModule }      from '@angular/core';
import { CommonModule }  from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTableModule } from 'angular2-datatable';

//other components here
import { UserRolesComponent } from './+userRoles/userRoles.component';
import { RolesPermissionComponent } from './+rolesPermission/rolesPermission.component';
import { AddRoleComponent } from './+userRoles/+add/add.userRole.component';
import { EditRoleComponent } from './+userRoles/+edit/edit.userRole.component';

import { NgSelectModule } from '@ng-select/ng-select';
import { Autosize } from 'angular2-autosize';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

// all routes related to this Role module.
export const routes = [
  { path: '', component: UserRolesComponent, pathMatch: 'full' },
  { path: 'user', component: UserRolesComponent },
  { path: 'user/add', component: AddRoleComponent },
  { path: 'user/edit/:id', component: EditRoleComponent },
  { path: 'permission', component: RolesPermissionComponent }
];


@NgModule({
  imports: [
    FormsModule,
    CommonModule,
    TooltipModule.forRoot(),
    NgSelectModule,
    DataTableModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    TranslateModule.forRoot({
      loader:{ 
        provide: TranslateLoader, 
        useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
      deps: [HttpClient] 
    }})
  ],
  declarations: [
    UserRolesComponent,
    RolesPermissionComponent,
    AddRoleComponent,
    EditRoleComponent,
    Autosize
  ],
  providers: [UserRoleService]
})
export class RoleModule {
  static routes = routes;
}
