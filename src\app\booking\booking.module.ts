import { CategoryWiseAvailabilityComponent } from './+report/category-wise-availability.component';
import { ReportComponent } from './+report/report.component';
import { EditExtraChargesComponent } from './+extraCharges/+edit/EditExtraCharges.component';
import { AddExtraChargesComponent } from './+extraCharges/+add/addExtraCharges.component';
import { ExtraChargesComponent } from './+extraCharges/extraCharges.component';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ViewBookingListComponent } from './+bookingList/+view/view.bookingList.component';
import { ViewReportDetailsComponent } from "./+report/+view/view.report-details.component";
import { EditCancelationPolicyComponent } from './+cancelationPolicy/+edit/edit.cancelationPolicy.component';
import { AddCancelationPolicyComponent } from './+cancelationPolicy/+add/add.cancelationPolicy.component';
import { BookingService } from './../shared/services/booking.service';
import { CancelationPlicyComponent } from './+cancelationPolicy/cancelationPlicy.component';
import { BookingListComponent } from './+bookingList/booking.list.component';

import { NgSelectModule } from '@ng-select/ng-select';
import { ModalModule } from 'ngx-bootstrap/modal';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { NKDatetimeModule } from 'ng2-datetime/ng2-datetime';


import 'ng2-datetime/src/vendor/bootstrap-datepicker/bootstrap-datepicker.min.js';
import { PendingPaymentsComponent } from './+report/pending-payments/pending-payments.component';
import { PendingPaymentCollectionsComponent } from './+report/pending-payment-collections/pending-payment-collections.component';
import { CommaSeparatedPipe } from './comma-separated.pipe';
import { RoomMaintenanceComponent } from './+report/room-maintenance/room-maintenance.component';
import { NumberToWordsPipe } from './+report/number-to-words.pipe';


import { SharedModule } from 'app/shared/shared.module';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { CollectionsBreakUpComponent } from './+report/collection-breakup/collections-breakup.component';
import { RoomMaintenanceReasonComponent } from 'app/roomMaintenanceReason/roomMaintenanceReason.component';
import { RoomMaintenanceReasonReportComponent } from './+report/room-maintenance-reason/room-maintenance-reason-report.component';
import { RoomUnderMaintenanceComponent } from './+report/room-undermaintenance/room-undermaintenance.component';
import { AdvancePaymentComponent } from './+report/advancepayment/advancepayments.component';
import { accountReport } from './+report/account-report/account-report.component';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
const route = [
    { path: '', component: BookingListComponent, pathMatch: 'full' },
    { path: 'view/:id', component: ViewBookingListComponent },
    { path: 'policy', component: CancelationPlicyComponent },
    { path: 'extracharges', component: ExtraChargesComponent },
    { path: 'report/booking', component: ReportComponent },
    { path: 'report/reservation', component: ReportComponent },
    { path: 'report/checkout', component: ReportComponent },
    { path: 'report/checkin', component: ReportComponent },
    { path: 'report/cancelled', component: ReportComponent },
    { path: 'report/pending-payments', component: ReportComponent },
    { path: 'report/revenue', component: ReportComponent },
    { path: 'report/agents', component: ReportComponent },
    { path: 'report/guest', component: ReportComponent },
    { path: 'report/discount', component: ReportComponent },
    { path: 'report/category-wise-availability', component: CategoryWiseAvailabilityComponent },
    { path: 'report/pending-payment-collections', component: PendingPaymentCollectionsComponent },
    { path: 'report/room-maintenance', component: RoomMaintenanceComponent },
    { path: 'report/collections-breakup', component: CollectionsBreakUpComponent },
    { path: 'report/room-undermaintenance', component: RoomUnderMaintenanceComponent },
    { path: 'report/advance-payment', component: AdvancePaymentComponent},
    { path: 'report/account-report', component: accountReport},


]

@NgModule({
    imports: [
        FormsModule,
        CommonModule,
        TooltipModule.forRoot(),
        NgSelectModule,
        DataTableModule,
        PdfViewerModule,
        NKDatetimeModule,
        ReactiveFormsModule,
        AccordionModule.forRoot(),
        ModalModule.forRoot(),
        RouterModule.forChild(route),
        SharedModule,
        TranslateModule.forRoot({
            loader:{
                provide: TranslateLoader,
                useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
                deps: [HttpClient]
            }})
    ],
    exports: [],
    declarations: [
        ReportComponent,
        NumberToWordsPipe,
        CommaSeparatedPipe,
        BookingListComponent,
        ExtraChargesComponent,
        PendingPaymentsComponent,
        ViewBookingListComponent,
        AddExtraChargesComponent,
        RoomMaintenanceComponent,
        EditExtraChargesComponent,
        CancelationPlicyComponent,
        ViewReportDetailsComponent,
        AddCancelationPolicyComponent,
        EditCancelationPolicyComponent,
        CategoryWiseAvailabilityComponent,
        PendingPaymentCollectionsComponent,
        CollectionsBreakUpComponent,
        RoomUnderMaintenanceComponent,
        AdvancePaymentComponent,
        accountReport
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
    providers: [BookingService],
})
export class BookingModule { }
