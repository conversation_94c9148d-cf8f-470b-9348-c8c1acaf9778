import { Component, ViewEncapsulation } from '@angular/core';
import { AppConfig } from '../app.config';
import {TranslateService} from '@ngx-translate/core';

@Component({
  selector: 'dashboard',
  templateUrl: './dashboard.template.html',
  styleUrls: ['./dashboard.style.scss'],
  encapsulation: ViewEncapsulation.None,
  providers: [TranslateService]
})
export class Dashboard {
  config: any;

  constructor(
    public translate: TranslateService,
    config: AppConfig) {
    this.config = config.getConfig();
    translate.setDefaultLang('hi');
  }

  ngOnInit(): void {

  }
}
