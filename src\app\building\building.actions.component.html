<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-building"></i>&nbsp;&nbsp;{{pageType}} {{'BUILDING.ADD_PAGE.BUILDING' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item "><a (click)="toggleChild()" href="javascript:void(0)">{{'BUILDING.ADD_PAGE.BUILDING' | translate:param}}</a></li>
    <li class="breadcrumb-item active">{{pageType}} {{'BUILDING.ADD_PAGE.BUILDING' | translate:param}}</li>
  </ol>

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="buildingForm" (ngSubmit)="saveBuilding()">
            <!-- <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">Dharamshala*</label>
            <div class="col-md-8 ">
                <input type="hidden" formControlName="dharamshala_id">
               <span class="errMsg __fromBackend" *ngIf="buildingForm.controls.dharamshala_id.errors?.backend">{{buildingForm.controls.dharamshala_id.errors?.backend}}</span>
                <ng-select [items]="getDharamshalaList()" [(ngModel)]="dropdownSelect" [width]="100"></ng-select>
            </div>
          </div> -->
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'BUILDING.ADD_PAGE.NAME' | translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="buildingForm.controls.name.errors?.backend">{{buildingForm.controls.name.errors?.backend}}</span>
              <input type="text" id="normal-field" class="form-control" formControlName="name" name="name" placeholder="">
              <span class="errMsg" *ngIf="!buildingForm.controls.name.valid && !buildingForm.controls.name.untouched">
              <span [hidden]="!buildingForm.controls.name.errors.required">{{'BUILDING.ADD_PAGE.VALID_MSG.BUILD_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'BUILDING.ADD_PAGE.ADD_FLOOR' | translate:param}}</label>
            <div class="col-md-8 " formArrayName="floors">
              <div class="input-group floor_inputs" *ngFor="let floor of buildingForm.controls.floors.controls; let i =index" [formGroupName]="i">
                <div>
                  <input class="form-control" type="text" formControlName="name">
                </div>
                <div class="radio-horizontal">
                  <div class="abc-radio">
                    <input type="radio" formControlName="status" id="{{i}}-1" [value]="true">
                    <label for="{{i}}-1">
                        {{'BUILDING.ADD_PAGE.ACTIVE' | translate:param}}
                      </label>
                  </div>
                  <div class="abc-radio">
                    <input type="radio" formControlName="status" id="{{i}}-2" [value]="false">
                    <label for="{{i}}-2">
                        {{'BUILDING.ADD_PAGE.INACTIVE' | translate:param}}
                      </label>
                  </div>
                </div>
                <div style="padding: 2px 0px;text-align: right;">
                  <a class="btn btn-sm btn-inverse" *ngIf="i == (buildingForm.controls.floors.controls.length - 1) && buildingForm.controls.floors.valid && buildingForm.controls.status.value"
                    href="javascript:void(0)" (click)="addFloors()"><i class="fa fa-plus"></i></a>
                  <a class="btn btn-sm btn-danger" *ngIf="i <(buildingForm.controls.floors.controls.length - 1)" href="javascript:void(0)"
                    (click)="deleteFloor(i)"><i class="fa fa-times"></i></a>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'BUILDING.ADD_PAGE.STATUS' | translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">
                  <input type="radio" formControlName="status" (change)="manageFloorStatus()" id="radio-1" [value]="true">
                  <label for="radio-1">
                      {{'BUILDING.ADD_PAGE.ACTIVE' | translate:param}}
                      </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" (change)="manageFloorStatus()" id="radio-2" [value]="false">
                  <label for="radio-2">
                      {{'BUILDING.ADD_PAGE.INACTIVE' | translate:param}}
                      </label>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" [disabled]="!buildingForm.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'BUILDING.ADD_PAGE.SAVE' | translate:param}}</button>
                <button (click)="toggleChild()" class="btn btn-sm btn-secondary">{{'BUILDING.ADD_PAGE.CANCEL' | translate:param}}</button>
              </div>
            </div>
          </div>
        </form>
        <!--<pre>
            {{ buildingForm.value | json}}
        </pre>-->
      </fieldset>
    </div>
  </div>
</section>
