<section class="widget">
  <header>
    <h4><span class=""><i class="fa fa-gavel"></i>&nbsp;&nbsp;{{'ROLE_PERMI.ROLE_PERMI_MANAGE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left">
    <li class="breadcrumb-item">{{'ROLE_PERMI.ROLE_MANAG' | translate:param}}</li>
    <li class="breadcrumb-item active">{{'ROLE_PERMI.ROLE_PER' | translate:param}}</li>
  </ol>
  <div class="clearfix"></div>
  <div class="widget-body">
    <div class="mt">
      <div class="row" *ngIf="userRoleData && userRoleData.length">
        <div class="col-sm-4 " *ngIf="userRoleData">
          <div class="panel panel-default">
            <div class="panel-heading">
              <p>{{'ROLE_PERMI.USER_ROLE' | translate:param}}</p>
            </div>
            <div class="panel-body">
              <div class="abc-radio abc-radio-success" *ngFor="let userRole of userRoleData">
                <input type="radio" name="userRole" id="{{userRole.id}}" value="" (change)="getModelLists(userRole.id)">
                <label for="{{userRole.id}}" class="capitalized">
                        {{userRole.name}}
                      </label>
              </div>
            </div>
          </div>
        </div>

        <div class="col-sm-4 " *ngIf="userRoleModalData && roleId">
          <div class="panel panel-default">
            <div class="panel-heading">
              <p>{{'ROLE_PERMI.ACCE_CONT' | translate:param}}</p>
            </div>
            <div class="panel-body">
              <div class="abc-radio abc-radio-success" *ngFor="let userRoleModel of userRoleModalData;let i = index">
                <input type="radio" class="radioReset"  name="userRoleModelname" id="{{userRoleModel.role}}" value="" (change)="changeUserRoleModel(i)">
                <label for="{{userRoleModel.role}}" class="capitalized">
                        {{userRoleModel.name}}
                      </label>
              </div>
            </div>
          </div>
        </div>

        <div class="col-sm-4 " *ngIf="userRoleModalActionsData">
          <div class="panel panel-default">
            <div class="panel-heading">
              <p>{{'ROLE_PERMI.ACC_ACT' | translate:param}}</p>
            </div>
            <div class="panel-body">
              
              <!--<div class="__accessActions" *ngFor="let action of userRoleModalActionsData;let i =index">
                <div class="display-inline-block __checkbox">
                  <input type="checkbox" id="checkbox-{{action}}" [(ngModel)]="accessAction"><i></i>
                  <label for="checkbox-{{action}}" [ngClass]="{'bg-success': accessAction, 'bg-danger': !accessAction}">
                    <i class="fa fa-check" *ngIf="accessAction"></i>
                    <i class="fa fa-times" *ngIf="!accessAction"></i>
                </label>
                </div>
                <p class="display-inline-block capitalized">{{action}}</p>
              </div>-->

              <div class="abc-checkbox abc-checkbox-success" *ngFor="let action of userRoleModalActionsData">
                <input type="checkbox" name="{{action}}" #actions id="{{action}}" [disabled]="!auth.roleAccessPermission('rolepermission','edit')" [checked]="(assignedactionsData?.indexOf(action) != -1) ? true : false " (change)="changeAction(actions)">
                <label for="{{action}}" class="capitalized">
                        {{action}}
                      </label>
                   
              </div>
             
            </div>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="userRoleData && !userRoleData.length">
        <div class="col-sm-12">
        <br>
          <p class="text-center text-danger"><i class="fa fa-exclamation-triangle"></i>&nbsp;&nbsp;<strong>{{'ROLE_PERMI.ACC_ACT' | translate:param}}</strong></p>
        <br>
        <br>
        </div>

      </div>
        <span *ngIf="!canViewRecords" class="text-danger">  
          {{'ROLE_PERMI.PERMISSION_DENIED' | translate:param}}
        </span>
    </div>
  </div>
</section>
