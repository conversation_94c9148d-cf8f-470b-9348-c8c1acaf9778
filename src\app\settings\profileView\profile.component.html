<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-user-o"></i>&nbsp;&nbsp;{{'PROFILE.MY_PROF' | translate:param}}</span></h4>
  </header>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="profile" (ngSubmit)="saveProfileData()">
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'PROFILE.FIRST_NAME' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="text" id="normal-field" formControlName="first_name" class="form-control" placeholder="">
               <span class="errMsg" *ngIf="!profile.controls.first_name.valid && !profile.controls.first_name.pristine">
                <span>{{'PROFILE.FNAME_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'PROFILE.LAST_NAME' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="text" id="normal-field" formControlName="last_name" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!profile.controls.last_name.valid && !profile.controls.last_name.pristine">
                <span>{{'PROFILE.LNAME_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'PROFILE.EMAILID' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="text" id="normal-field" class="form-control" placeholder="" formControlName="email" disabled>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'PROFILE.CONC_NO' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="text" id="normal-field" maxlength="10" class="form-control" placeholder="" formControlName="mobile_no">
              <span class="errMsg" *ngIf="!profile.controls.mobile_no.valid && !profile.controls.mobile_no.pristine">
                <span>{{'PROFILE.MOB_NUM_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'PROFILE.DOB' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="text" id="normal-field" class="form-control" placeholder="" formControlName="dob" [textMask]="dateMask">
               <span class="errMsg" *ngIf="!profile.controls.dob.valid && !profile.controls.dob.pristine">
                <span>{{'PROFILE.DOB_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'PROFILE.ADDRESS' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="text" id="normal-field" class="form-control" placeholder="" formControlName="address">
               <span class="errMsg" *ngIf="!profile.controls.address.valid && !profile.controls.address.pristine">
                <span>{{'PROFILE.ADDRE_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'PROFILE.CITY' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="text" id="normal-field" class="form-control" placeholder="" formControlName="city">
               <span class="errMsg" *ngIf="!profile.controls.city.valid && !profile.controls.city.pristine">
                <span>{{'PROFILE.CITY_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'PROFILE.COUNTRY' | translate:param}}</label>
            <div class="col-md-8 ">
              <!-- <input type="text" id="normal-field" class="form-control" placeholder="" formControlName="country"> -->
             <ng-select [items]="countryList" [ngModel]="profile.value.country" formControlName="country" (change)="countryChanged($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" style="width: 100%;"></ng-select>
              <span class="errMsg" *ngIf="!profile.controls.country.valid && !profile.controls.country.pristine">
                <span>{{'PROFILE.COUNTRY_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

           <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'PROFILE.ZIP_POSTAL' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="text" id="normal-field" maxlength="6" class="form-control" placeholder="" formControlName="zip">
               <span class="errMsg" *ngIf="!profile.controls.zip.valid && !profile.controls.zip.pristine">
                <span>{{'PROFILE.ZIP_POS_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'PROFILE.GENDER' | translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio" *ngFor="let g of gender">
                  <input type="radio" formControlName="gender" [id]="g" [value]="g">
                  <label class="capitalize" [for]="g">
                        {{g}}
                      </label>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" [disabled]="!profile.valid" class="btn btn-sm btn-inverse capitalized">{{'PROFILE.SAVE' | translate:param}}</button>
                <a [routerLink]="['/']" class="btn btn-sm btn-secondary">{{'PROFILE.CANCEL' | translate:param}}</a>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
