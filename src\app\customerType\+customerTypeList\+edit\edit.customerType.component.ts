import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { CustomerTypeService } from './../../../shared/services/customerType.service';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'edit-customer-type',
    templateUrl: '../customerType.actions.html'
})
export class EditCustomerTypeComponent implements OnInit {
    pageType: string = "Edit";
    customerTypeAdd: FormGroup;
    isPercentageValid: boolean = true;
    discountTypeRadio: string[] = ['amount', 'percentage', 'none'];

    //service variables
    private cutomerTypeService: any;

    // Input/Output variables
    @Input() gethiddenEditCT;
    @Input() selectedCT;
    @Output() sendhiddenAddCT = new EventEmitter();

    constructor(
        private _fb: FormBuilder,
        private CTS: CustomerTypeService,
        public translate: TranslateService,
    ) {
        this.buildForm();
        translate.get('CUSTOMER_TYPE.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    buildForm() {
        this.customerTypeAdd = this._fb.group({
            name: ['', Validators.required],
            discount_type: [''],
            discount_value: ['0', CustomValidators.number],
            extra_bed_charge: ['0', [CustomValidators.number]],
            status: ['', Validators.required]
        });

    }
    ngOnInit() {
        this.customerTypeAdd.patchValue(this.selectedCT); // initialising form values
    }

    saveCustomerType() {
        if (this.customerTypeAdd.valid) {
            this.cutomerTypeService = this.CTS.UpdateCustomerType(this.selectedCT.id, this.customerTypeAdd.value)
                .subscribe((res) => {
                    if (res.status === "success") {
                        console.log("Saved customer type data : ",res);
                        this.customerTypeAdd.reset();
                        this.toggleChild(res.data);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.customerTypeAdd.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    toggleChild(data) {
        let result;
        this.gethiddenEditCT = !this.gethiddenEditCT; // show/hide component
        if (data) {
            // send data for to replace to list
            result = { gethiddenEditCT: this.gethiddenEditCT, data: data }
        } else {
            result = { gethiddenEditCT: this.gethiddenEditCT }
        }
        this.sendhiddenAddCT.emit(result);
    }
    resrValue() {
        if (this.customerTypeAdd.controls['discount_type'].value == 'none') {
            this.customerTypeAdd.controls['discount_value'].patchValue('');
        }
        this.changeDiscountValue();
    }
    changeDiscountValue() {
        let discountValue = this.customerTypeAdd.controls['discount_value'];
        let discountType = this.customerTypeAdd.value.discount_type;
        if (discountType == 'percentage' && this.customerTypeAdd.value.discount_value > 100) {
            discountValue.setErrors({"valid":false});
            this.isPercentageValid = false;
        }
        else {
            discountValue.setErrors(null);
            this.isPercentageValid = true;
        }
    }
    ngOnDestroy() {
        if (this.cutomerTypeService) {
            this.cutomerTypeService.unsubscribe();
        }
    }
}