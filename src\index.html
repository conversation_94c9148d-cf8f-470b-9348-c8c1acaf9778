<!DOCTYPE html>
<html lang="">
<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title><%= htmlWebpackPlugin.options.title %></title>

  <meta name="description" content="<%= htmlWebpackPlugin.options.title %>">

  <% if (webpackConfig.htmlElements.headTags) { %>
  <!-- Configured Head Tags  -->
  <%= webpackConfig.htmlElements.headTags %>
  <% } %>

  <!-- base url -->
  <base href="<%= htmlWebpackPlugin.options.metadata.baseUrl %>">
  <script>
    document.addEventListener('extensionResponse', function(event) {
      var extensionResponse = event.detail;
      document.apiSecretKey= extensionResponse; 
    });
    
        </script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

</head>

<body>

  <app>
    <style>
      /*
       * Inline css to add simple styles for loader.
       * Is going to be replaced by angular after bootstrap.
       */
      app {
        position: absolute;
        top: 0; right: 0; bottom: 0; left: 0;
        padding-top: 45vh;
        text-align: center;
        background-color: #eee;
        font: 14px sans-serif;
      }
    </style>
    Loading...
  </app>

  <!-- Google Analytics: change UA-XXXXXXXX-X to be your site's ID -->
  <script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
    (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
    m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

    ga('create', 'UA-XXXXXXXX-X', 'auto');
    ga('send', 'pageview');
  </script>
  <script data-pace-options='{ "target": ".content-wrap", "ghostTime": 1000 }'></script>

  <% if (htmlWebpackPlugin.options.metadata.isDevServer && htmlWebpackPlugin.options.metadata.HMR !== true) { %>
  <!-- Webpack Dev Server reload -->
  <script src="http://<%= htmlWebpackPlugin.options.metadata.host %>:<%= htmlWebpackPlugin.options.metadata.port %>/webpack-dev-server.js"></script>
 <!--// <script src="/webpack-dev-server.js"></script>-->
  <% } %>


</body>
</html>
