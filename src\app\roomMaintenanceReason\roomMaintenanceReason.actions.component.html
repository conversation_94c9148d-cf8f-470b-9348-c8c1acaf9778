<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-sitemap"></i>&nbsp;&nbsp;{{pageName}} {{'ROOM_MAINTENANCE_REASON.ADD_PAGE.ROOM'| translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="roomForm" (ngSubmit)="saveRoom()">

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM_MAINTENANCE_REASON.ADD_PAGE.NO_TITLE'| translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="roomForm.controls.title.errors?.backend">{{roomForm.controls.title.errors?.backend}}</span>
              <input type="text" formControlName="title" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!roomForm.controls.title.valid && !roomForm.controls.title.pristine">
              <span [hidden]="!roomForm.controls.title.errors.required">{{'ROOM_MAINTENANCE_REASON.ADD_PAGE.VALID_MSG.TIT_REQ'| translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM_MAINTENANCE_REASON.ADD_PAGE.DESC'| translate:param}}</label>
            <div class="col-md-8 ">
              <textarea type="text" formControlName="description" class="form-control" placeholder=""></textarea>
            </div>
          </div>
         
         
          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'ROOM_MAINTENANCE_REASON.ADD_PAGE.STATUS'| translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio1" [value]="true">
                  <label for="radio1">
                      {{'ROOM_MAINTENANCE_REASON.ADD_PAGE.ACTIVE'| translate:param}}
                      </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio2" [value]="false">
                  <label for="radio2">
                      {{'ROOM_MAINTENANCE_REASON.ADD_PAGE.INACTIVE'| translate:param}}
                      </label>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" [disabled]="!roomForm.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'ROOM.ROOM_MANAGE.ADD_PAGE.SAVE'| translate:param}}</button>
                <button (click)="toggleChild()" class="btn btn-sm btn-secondary">{{'ROOM.ROOM_MANAGE.ADD_PAGE.CANCEL'| translate:param}}</button>
              </div>
            </div>
          </div>
        </form>
        <!--<pre>
          {{ roomForm.value | json}}
        </pre>-->
      </fieldset>
    </div>
  </div>
</section>
