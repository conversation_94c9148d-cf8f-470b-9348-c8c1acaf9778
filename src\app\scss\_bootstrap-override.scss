//
// Typography
// --------------------------------------------------

.blockquote-sm{
  padding: 3px 15px;
  font-size: $font-size-base;
}

blockquote{
  font-size: $font-size-lg;

  footer {
    font-size: 80%;
    &:after {
      content: '\00A0 \2014';
    }
  }
}
// Headings
// -------------------------

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6{
  small,
  .small {
    font-weight: $headings-font-weight;
    font-size: 75%;
    color: $text-muted;
  }
}

.h4, .h5, .h6, h4, h5, h6 {
  margin-top: 10px;
}

//
// cards
// --------------------------------------------------

// Base class
.card {
  margin-bottom: $line-height-computed;
  background-color: $card-bg;
  border: none;
}

// card contents
.card-body {
  padding: $card-body-padding;
  @include clearfix;
}

// Optional heading
.card-header {
  padding: 13px 15px;
  @include border-top-radius(($border-radius));

  > .dropdown .dropdown-toggle {
    color: inherit;
  }



  +.list-group .list-group-item:first-child {
    border-top-width: 0;
  }
}


accordion-group .card-title a.accordion-toggle i {
  transform: rotate(180deg);
  transition: transform 0.35s ease;
}

accordion-group.card-open .card-title a.accordion-toggle i {
  transform: rotate(0deg);
  transition: transform 0.35s ease;
}

.card-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: $font-size-base;
  color: inherit;

  > a,
  > small,
  > .small,
  > small > a,
  > .small > a {
    color: inherit;
    display: block;
  }

  > a:focus {
    outline: none;
  }
}

small, .small {
  font-size: 85%;
}

// Collapsable cards (aka, accordion)
//
// Wrap a series of cards in `.card-group` to turn them into an accordion with
// the help of our collapse JavaScript plugin.

.card-group {
  margin-bottom: $line-height-computed;

  // Tighten up margin so it's only between cards
  .card {
    margin-bottom: 0;
    border-radius: $border-radius;
  }

  accordion-group.card-open .card-title a {
    &,
    & i {
      opacity: 1;
    }
  }

  accordion-group .card-title a {
    &,
    & i {
      opacity: 0.6;
    }

    &:hover,
    &:hover i {
      opacity: 1;
    }
  }

  accordion-group + accordion-group {
    .card {
      margin-top: 5px;
    }
  }

  .card-header {
    border-bottom: 0;

    + .card-collapse > .card-body,
    + .card-collapse > .list-group {
      border-top: 1px solid $list-group-border-color;
    }

  }
}


// Contextual variations
.card-default {
  @include card-variant($card-default-heading-bg, $card-default-border);
}

//
// Forms
// --------------------------------------------------


.form-control {
  font-weight: $font-weight-normal;
  padding: $padding-base-vertical $padding-base-horizontal;
  @include box-shadow(none);
  &:focus{
    @include box-shadow(none);
  }
}

.form-control-lg {
  padding: $padding-large-vertical $padding-large-horizontal;
}

.form-control-sm {
  padding: $padding-small-vertical $padding-small-horizontal;
}

.input-group-btn>.btn {
  padding-top: $padding-base-vertical;
  padding-bottom: $padding-base-vertical;
}

.btn-group.bootstrap-select > .btn {
  padding-top: $padding-base-vertical;
  padding-bottom: $padding-base-vertical;
}

.btn-lg, .btn-group-lg>.btn {
 padding: $btn-padding-y-lg $btn-padding-x-lg !important;
}

label {
  font-weight: $font-weight-normal;
}

.help-block{
  margin-top: 5px;
  font-size: $font-size-smaller;
  color: $text-muted;
  display: block;
}

legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: $line-height-computed;
  line-height: inherit;
  color: $legend-color;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
  font-size: 15px;
  font-weight: $font-weight-normal;
}

.form-group {
  margin-bottom: $line-height-computed;

  .input-group-addon{
    line-height: inherit;
  }
}

.input-group-addon{
  &.bg-primary{
    border-color: darken($brand-primary, 10%);
  }
  &.bg-success{
    border-color: darken($state-success-bg, 10%);
  }
  &.bg-info{
    border-color: darken($state-info-bg, 10%);
  }
  &.bg-warning{
    border-color: darken($state-warning-bg, 10%);
  }
  &.bg-danger{
    border-color: darken($state-danger-bg, 10%);
  }
}

.form-horizontal.form-label-left {
  @include media-breakpoint-up(md){
    .control-label {
      text-align: left;
    }
  }
}

.form-inline .input-group-addon {
  display: table-cell;
}

// Feedback states
.has-success {
  @include form-control-validation($brand-success);
}
.has-warning {
  @include form-control-validation($brand-warning);
}
.has-error {
  @include form-control-validation($brand-danger);
}

//turn off shadow
.has-success,
.has-warning,
.has-error {
  .form-control {
    &:focus {
      @include box-shadow(none);
    }
  }
}

.input-rounded{
  border-radius: $border-radius;
}

.form-actions{
  margin: $line-height-computed 0 0;
  padding: $line-height-computed;
  background-color: #f5f5f5;

  .form-horizontal & .row{
    margin-left: ($grid-gutter-width/-2) - 20px;
    margin-right: ($grid-gutter-width/-2) - 20px;

  }
}


// Checkboxes and radios
//

.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-top: .25rem;
  // margin-top: 4px \9;
  margin-left: -1.25rem;
}

.radio,
.checkbox {
  padding-left: 20px;

  label {
    margin-bottom: 5px;
    min-height: 0;
  }
}

//
// Tables
// --------------------------------------------------

.table td > .abc-checkbox,
.table th > .abc-checkbox{
  position: relative;
  top: -8px;
  margin-top: 0;
  margin-bottom: 0;
  margin-right: -16px;
}

.table > thead > tr > th {
  border-top: none;
}

.table > thead > tr > th{
  font-weight: $font-weight-semi-bold;
  text-transform: uppercase;
  border-bottom-width: 1px;
}

.table-bordered th{
  background-color: $gray-lighter;
}

.table-lg {
  > thead,
  > tbody,
  > tfoot {
    > tr {
      > th,
      > td {
        padding: 10px;
      }
    }
  }
}

.table-sm {
  > thead,
  > tbody,
  > tfoot {
    > tr {
      > th,
      > td {
        padding: 6px;
      }
    }
  }
}

.table-hover > tbody > tr:hover .progress{
  background-color: $white;
}

.table th, .table td {
  padding: 0.55rem;
}

//
// Dropdown menus
// --------------------------------------------------

.dropdown-toggle {
  &:after {
    content: none;
  }
}

.dropdown-menu{
  font-size: $font-size-mini;

  & > li > a{
    padding-top: 5px;
    padding-bottom: 5px;
  }

  > .card {
    margin-top: -5px;
    margin-bottom: -5px;
  }
}

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top:   4px dashed;
  border-top:   4px solid \9; // IE8
  border-right: 4px solid transparent;
  border-left:  4px solid transparent;
}
//
// Navs
// --------------------------------------------------
//
// Navs
// --------------------------------------------------
.nav-item .nav-link {
  position: relative;
  cursor: pointer;
}

.nav-pills .nav-link {
  border-radius: 0;
}

.nav-pills .nav-item + .nav-item {
  margin-left: 0;
}

.nav-tabs {
  & .nav-item+.nav-item { margin-left: 0}
  border-bottom: none;
  background-color: $gray-semi-lighter;
  border-top-left-radius: $border-radius;
  border-top-right-radius: $border-radius;
  > .nav-item {
    margin-bottom: -2px;
    > .nav-link {
      padding: 12px 18px;
      border: none;
      color: $text-muted;

      .label {
        margin-bottom: -2px;
      }

      @include transition(color .15s ease-in-out);
      &:hover {
        background-color: transparent;
        color: $text-color;
      }
    }

    .nav-link.open {
      &,
      &:hover,
      &:focus{
        background-color: $nav-tabs-active-link-hover-bg;
        color: $nav-tabs-active-link-hover-color;
      }
    }

    .nav-link.active {
      &,
      &:hover,
      &:focus {
        background-color: $nav-tabs-active-link-hover-bg;
        color: $nav-tabs-active-link-hover-color;
        border: none;
        @include box-shadow(1px 1px 2px #ccc);
      }
    }
  }
}

.tab-content {
  position: relative;
  z-index: 1;
  background-color: $white;
  > .tab-pane {
    padding: $line-height-computed*2 $line-height-computed*2;
  }
}

//
// Navbars
// --------------------------------------------------

.navbar {
  border: none;
  font-size: $navbar-font-size;
  font-weight: 500;
  min-height: 50px;
  margin-bottom: 0;
  padding: 0;

  h5{
    font-size: $navbar-font-size;
  }

  .deemphasize{
    font-size: $font-size-sm;
  }
}

.navbar-form{
  .form-control{
    font-size: $navbar-font-size;
    padding: 0.6rem 0.85rem;

  }

  .input-group{
    width: 245px;
  }

  .input-group-addon{
    color: $navbar-dashboard-link-color;
  }
}

.container,
.container-fluid {
  > .navbar-header,
  > .navbar-collapse {
    @media (min-width: map_get($grid-breakpoints, md)) {
      margin-right: -$navbar-padding-x;
      margin-left:  -$navbar-padding-x;
    }
  }
}

.navbar-header > .navbar-nav{
  float: left;
  margin-left: calc(#{$navbar-padding-x} + 10px);
  margin-right: $navbar-padding-x;

  &.navbar-right{
    float: right;

    @include media-breakpoint-down(sm) {

      > li > a {
        padding-right: 0;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    margin-left: $navbar-padding-y;

    + .navbar-brand{
      height: 60px;
      line-height: 30px;
    }
  }

  > li {
    float: left;
    > a {
      padding: 16px $navbar-padding-y $navbar-padding-x $navbar-padding-y;


    }
  }

  li:nth-child(3){
    margin-left: 0;
  }
}

@media (min-width: map_get($grid-breakpoints, md)) {
  .navbar-nav.navbar-right:last-child {
    margin-right: 0;
  }
}

.navbar-collapse {
  overflow-x: visible;
  padding-right: $navbar-padding-x;
  padding-left:  $navbar-padding-x;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255,255,255,.1);
  @include clearfix;

  &.in {
    overflow-y: auto;
  }

  @media (min-width: map_get($grid-breakpoints, md)) {
    width: auto;
    border-top: 0;
    box-shadow: none;

    &.collapse {
      display: block !important;
      height: auto !important;
      padding-bottom: 0; // Override default setting
      overflow: visible !important;
    }

    &.in {
      overflow-y: visible;
    }

    // Undo the collapse side padding for navbars with containers to ensure
    // alignment of right-aligned contents.
    .navbar-fixed-top &,
    .navbar-static-top &,
    .navbar-fixed-bottom & {
      padding-left: 0;
      padding-right: 0;
    }
  }

  .nav.navbar-nav > li {
    margin-left: 0;
  }

  .nav.navbar-nav > li > a {
    padding: 17px 14px 14px 14px;
  }

  .nav.navbar-nav > li:first-child .dropdown-toggle {

    .circle {
      margin: -1px 0;
    }
  }

  .nav.navbar-nav li:nth-child(2) > a:after {
    content: none;
  }

  .nav.navbar-nav a[data-toggle="chat-sidebar"] {
    position: relative;
  }
}

.navbar-nav .nav-item .nav-link {
    margin-left: 0;
}

//
// Breadcrumbs
// --------------------------------------------------


.breadcrumb {
  color: $gray-light;
  margin-bottom: $spacer-y*1.5;

  > .active {
    font-weight: $font-weight-semi-bold;
  }

  .content &{
    padding-top: 0;
    padding-bottom: 0;
  }
}
//
// Buttons
// --------------------------------------------------

.btn {
  .glyphicon {
    top: 3px;
  }
}

.btn, .btn-inverse, .btn-gray {
  &:not(.active) {
    box-shadow: none !important;
  }
}

.btn-sm {
  padding-top: .35rem;
  padding-bottom: .35rem;
}

.btn-rounded{
  @include border-radius(6px);
}

.btn-gray{
  @include button-variant($text-color, $gray-semi-lighter, darken($gray-semi-lighter, 5%));
}

.btn-outline{
  @include button-variant($white, transparent, $white);

  &:hover,
  &:focus {
    background-color: rgba($white, .1);
  }
}

.btn-link:focus,
.btn-link:active:focus,
.btn-link.active:focus{
  outline: 0;
}

.input-group > .input-group-btn:last-child > .btn[data-toggle="dropdown"] {
  @include border-right-radius($btn-border-radius);
}


.btn-group > .btn[data-toggle="dropdown"][data-original-title][title] {
  @include border-right-radius($btn-border-radius);

  &:focus,
  &:active{
    outline: none !important;
  }
}

//
// Panels
// --------------------------------------------------

// Base class
.panel {
  margin-bottom: $line-height-computed;
  background-color: $panel-bg;
  border: none;
}

// Panel contents
.panel-body {
  padding: $panel-body-padding;
  @include clearfix;
}

// Optional heading
.panel-heading {
  padding: 13px 15px;
  border-bottom: 1px solid transparent;
  @include border-top-radius(($border-radius));

  > .dropdown .dropdown-toggle {
    color: inherit;
  }
}


accordion-group .panel-title a.accordion-toggle i {
  transform: rotate(180deg);
  transition: transform 0.35s ease;
}

accordion-group.panel-open .panel-title a.accordion-toggle i {
  transform: rotate(0deg);
  transition: transform 0.35s ease;
}

.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: $font-size-base;
  color: inherit;

  > a,
  > small,
  > .small,
  > small > a,
  > .small > a {
    color: inherit;
    display: block;
  }

  > a:focus {
    outline: none;
  }
}

small, .small {
  font-size: 85%;
}

// Collapsable panels (aka, accordion)
//
// Wrap a series of panels in `.panel-group` to turn them into an accordion with
// the help of our collapse JavaScript plugin.

.panel-group {
  margin-bottom: $line-height-computed;

  // Tighten up margin so it's only between panels
  .panel {
    margin-bottom: 0;
    border-radius: $border-radius;
  }

  accordion-group.panel-open .panel-title a {
    &,
    & i {
      opacity: 1;
    }
  }

  accordion-group .panel-title a {
    &,
    & i {
      opacity: 0.6;
    }

    &:hover,
    &:hover i {
      opacity: 1;
    }
  }

  accordion-group + accordion-group {
    .panel {
      margin-top: 5px;
    }
  }

  .panel-heading {
    border-bottom: 0;

    + .panel-collapse > .panel-body,
    + .panel-collapse > .list-group {
      border-top: 1px solid $list-group-border-color;
    }

  }
}


// Contextual variations
.panel-default {
  @include panel-variant($panel-default-border, $panel-default-text, $panel-default-heading-bg, $panel-default-border);
}


//
// Labels
// --------------------------------------------------
.tag{
  font-size: 11px;
  font-weight: $font-weight-semi-bold;
  padding: 6px;
}

.tag-pill {
  color: $white;
  font-weight: $font-weight-bold;
}

.help-block{
  margin-top: 5px;
  font-size: $font-size-smaller;
  color: $text-muted;
  display: block;
}

//
// Button groups
// --------------------------------------------------

.btn-toolbar{
  margin-top: calc($line-height-computed / 2);
  margin-bottom: calc($line-height-computed / 2);
}

//
// Progress bars
// --------------------------------------------------

.js-progress-animate {
  transition: width 0.6s ease;
}

.progress{
  @include box-shadow(none);
  transition: width 0.6s ease;
  height: 1.3rem;
}

.progress-bar{
  border-radius: $border-radius;
  @include box-shadow(none);
}

.progress-sm{
  height: 10px;
  margin-bottom: calc($line-height-computed / 2);
}

.progress-xs{
  height: 5px;
  margin-bottom: calc($line-height-computed / 2);
}


.progress-bar-gray {
  @include progress-variant($gray);
}

.progress-bar-gray-light {
  @include progress-variant($gray-light);
}

.progress-primary {
  @include progress-variant($brand-primary)
}


//
// List groups
// --------------------------------------------------

.card {
  margin-bottom: 0;
  border: none;
}

.card-footer {
  border-top: none;
}

.card > .list-group > .list-group-item {
  border-width: 1px 0;

  &:first-child {
    border-radius: 0;
  }
}

.list-group{
  .widget-body.no-padding > &{
    margin: 0;

    .list-group-item{
      border-width: 1px 0;
    }
  }
}

.list-group-item{

  .list-group-lg & {
    padding: 15px 15px;
  }

  .list-group:last-of-type &:last-child{
    border-bottom: 0;
  }
}

a.list-group-item, button.list-group-item {
  width: 100%;
  color: #555;
  text-align: inherit;
}

//
// Tooltips
// --------------------------------------------------

.tooltip-inner{
  @include border-radius(3px);
}

//
// Alerts
// --------------------------------------------------


.alert-sm{
  padding: 10px 12px;
  font-size: $font-size-mini;

  .close{
    font-size: 18px;
  }
}

.alert-dismissible .close{
  position: static;
  color: $close-color;

}


//
// Popovers

.popover{
  border: none;
  @include box-shadow(none);
}

.popover-content {
  padding: 20px;
}

//
// Modals
// --------------------------------------------------

// z-index fix. modal doesn't show up properly when .content-wrap is relative
.modal-open{
  &, &.nav-collapsed, &.nav-static{
    .content-wrap{
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
      position: static;
    }

    .sidebar{
      z-index: -1;
    }
  }
}

//
// Pagination
// --------------------------------------------------

.pagination{
  font-weight: $font-weight-normal;

  > .page-item {
    display: inline; // Remove list-style and block-level defaults
    > .page-link,
    > span{
      border-radius: $border-radius;
      margin: 0 2px;
    }

    >.page-link {
      text-decoration: none;
      border: none;
    }
  }
}

.pagination{
  font-weight: $font-weight-normal;

  > li {
    display: inline; // Remove list-style and block-level defaults
    > a,
    > span{
      border-radius: $border-radius;
      margin: 0 2px;
    }

    > a {
      text-decoration: none;
      border: none;
    }
  }
}

//
// Jumbotron
// --------------------------------------------------

.jumbotron {
  @include media-breakpoint-up(md){
    padding-left: $jumbotron-padding;
    padding-right: $jumbotron-padding;
  }
}


// Navbar form
//
// Extension of the `.form-inline` with some extra flavor for optimum display in
// our navbars.

.navbar-form {
  padding: 6px 0 6px $navbar-padding-x;

  .form-group {
    margin-bottom: 0;
  }

  // Undo 100% width for pull classes
  @media (min-width: map_get($grid-breakpoints, md)) {
    width: auto;
    border: 0;
    margin-left: 0;
    margin-right: 0;
  }
}

//
// Navbar Dasboard
// --------------------------------------------------

.navbar-dashboard {
  background-color: $navbar-dashboard-bg;
  border-color: $navbar-dashboard-border;

  .navbar-brand {
    color: $navbar-dashboard-brand-color;
    margin-right: 0;
    padding-top: 10px;
    font-size: $font-size-lg;
    &:hover,
    &:focus {
      color: $navbar-dashboard-brand-hover-color;
      background-color: $navbar-dashboard-brand-hover-bg;
    }
  }

  .navbar-text {
    color: $navbar-dashboard-color;
  }

  .navbar-nav {
    > li > a {
      color: $navbar-dashboard-link-color;

      &:hover,
      &:focus {
        color: $navbar-dashboard-link-hover-color;
        background-color: $navbar-dashboard-link-hover-bg;
      }
    }
    > .active > a {
      &,
      &:hover,
      &:focus {
        color: $navbar-dashboard-link-active-color;
        background-color: $navbar-dashboard-link-active-bg;
      }
    }
    > .disabled > a {
      &,
      &:hover,
      &:focus {
        color: $navbar-dashboard-link-disabled-color;
        background-color: $navbar-dashboard-link-disabled-bg;
      }
    }
  }

  .navbar-nav {
    // Remove background color from open dropdown
    > .open > a {
      &,
      &:hover,
      &:focus {
        background-color: $navbar-dashboard-link-active-bg;
        color: $navbar-dashboard-link-active-color;
      }
    }

    @media (max-width: map_get($grid-breakpoints, md) - 1px) {
      // Dropdowns get custom display when collapsed
      .open .dropdown-menu {
        > li > a {
          color: $navbar-dashboard-link-color;
          &:hover,
          &:focus {
            color: $navbar-dashboard-link-hover-color;
            background-color: $navbar-dashboard-link-hover-bg;
          }
        }
        > .active > a {
          &,
          &:hover,
          &:focus {
            color: $navbar-dashboard-link-active-color;
            background-color: $navbar-dashboard-link-active-bg;
          }
        }
        > .disabled > a {
          &,
          &:hover,
          &:focus {
            color: $navbar-dashboard-link-disabled-color;
            background-color: $navbar-dashboard-link-disabled-bg;
          }
        }
      }
    }
  }

  // Links in navbars
  //
  // Add a class to ensure links outside the navbar nav are colored correctly.

  .btn-link {
    color: $navbar-dashboard-link-color;
    &:hover,
    &:focus {
      color: $navbar-dashboard-link-hover-color;
      outline: none;
      text-decoration: none;
    }
    &[disabled],
    fieldset[disabled] & {
      &:hover,
      &:focus {
        color: $navbar-dashboard-link-disabled-color;
      }
    }
  }
}


@mixin bg-variant($parent, $color) {
  #{$parent} {
    background-color: $color !important;
    color: $gray !important;
  }
  a#{$parent} {
    @include hover-focus {
      background-color: darken($color, 10%);
    }
  }
}

//
// Code
// -----------------

pre {
  display: block;
  padding: 6.5px;
  margin: 0 0 calc($line-height-computed / 2);
  font-size: 13px;
  line-height: $line-height-computed;
  word-break: break-all;
  word-wrap: break-word;
  color: $pre-color;
  background-color: $pre-bg;
  border: 1px solid $pre-border-color;
  border-radius: $border-radius;

  // Account for some code outputs that place code tags in pre tags
  code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0;
  }
}


//
// Badges
// --------------------------------------------------


// Base class
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: $font-size-sm;
  font-weight: $font-weight-bold;
  color: #fff;
  line-height: 1;
  white-space: nowrap;
  text-align: center;
  background-color: $gray;
  border-radius: 100px;

  // Empty badges collapse automatically (not available in IE8)
  &:empty {
    display: none;
  }

  // Quick fix for badges in buttons
  .btn & {
    position: relative;
    top: -1px;
  }

  .btn-xs &,
  .btn-group-xs > .btn & {
    top: 0;
    padding: 1px 5px;
  }

  .list-group-item > & {
    float: right;
  }

  .list-group-item > & + & {
    margin-right: 5px;
  }

  .nav-pills > li > a > & {
    margin-left: 3px;
  }
}
.breadcrumb-item + .breadcrumb-item::before{
  content: '\f105';
  font-family: fontAwesome;
}
.breadcrumb > .active{
  font-weight: 400;
}
.breadcrumb{
  margin-bottom: 0px;
}
.pagination {
  height: 36px;
  margin: 18px 0;
}
.pagination ul {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
  margin-left: 0;
  margin-bottom: 0;
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.pagination li {
  display: inline;
}
.pagination a {
  float: left;
  padding: 0 14px;
  line-height: 34px;
  text-decoration: none;
  border: 1px solid #d8d8d8 !important;
}
.pagination a:hover,
.pagination .active a {
  background-color: #f5f5f5;
}
.pagination .active a {
  color: #999999;
  cursor: default;
  
}
.pagination .disabled span,
.pagination .disabled a,
.pagination .disabled a:hover {
  color: #999999;
  background-color: transparent;
  cursor: default;
}
.pagination li:first-child a {
  border-left-width: 1px;
  -webkit-border-radius: 3px 0 0 3px;
  -moz-border-radius: 3px 0 0 3px;
  border-radius: 3px 0 0 3px;
}
.pagination li:last-child a {
  -webkit-border-radius: 0 3px 3px 0;
  -moz-border-radius: 0 3px 3px 0;
  border-radius: 0 3px 3px 0;
}
.pagination-centered {
  text-align: center;
}
.pagination-right {
  text-align: right;
}
.pager {
  margin-left: 0;
  margin-bottom: 18px;
  list-style: none;
  text-align: center;
  *zoom: 1;
}
.pager:before,
.pager:after {
  display: table;
  content: "";
}
.pager:after {
  clear: both;
}
.pager li {
  display: inline;
}
.pager a {
  display: inline-block;
  padding: 5px 14px;
  background-color: #fff;
  border: 1px solid #ddd;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  border-radius: 15px;
}
.pager a:hover {
  text-decoration: none;
  background-color: #f5f5f5;
}
.pager .next a {
  float: right;
}
.pager .previous a {
  float: left;
}
.pager .disabled a,
.pager .disabled a:hover {
  color: #999999;
}
.display-table{
  display: table;
}
.margin-center{
  margin: 0 auto
}
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
  .btn,
  .btn-group {
    float: none;
    display: table-cell;
    width: 1%;
    .btn {
      width: 100%;
    }
    .dropdown-menu {
      left: auto;
    }
  }
}
.table-no-border{
  tr{
    td,th{
      border-top: transparent
    }
  }
}
.table-verticle-middle{
  tr{
    th,td{
      vertical-align: middle
    }
  }
}
.table-no-margin{
  margin-bottom: 0px
}
.table-condence{
  thead{
    th{
     border-bottom-width: 2px !important;
    }
  }
}
.datepicker-dropdown{
  padding: 5px;
}
.datepicker-days{
  tr {
    td,th{
      padding: 10px;
      cursor: pointer;
      text-align: center;
      color: #000;
      &:not(.old):not(.new):not(.dow):not(.disabled){
        font-weight: 500;
      }
      &.today{
        background: #F2BE00;
        &:hover{
          background: #ddd;
        }
      }
      &.old.day,&.new.day{
        color: #888
      }
      &.dow{
        color: #999;
        font-weight: 700;
      }
      &:hover{
        background: #F2BE35;
      }
      &.disabled.day{
        pointer-events: none;
        user-select: none;
        color: #999
      }
    }
  }
  .datepicker-switch{
    text-align: center
  }
}
.input-group-addon{
      padding: 0.45rem 0.75rem;
}
.datepicker-months,.datepicker-years,.datepicker-decades,.datepicker-centuries{

  .datepicker-switch,.prev,.next,.today{
    text-align: center;
    cursor: pointer;
    &:hover{
        background: #F2BE35;
    }
  }
  .month,.year,.decade,.century{
    display: inline-block;
    padding: 25px 0px;
    cursor: pointer;
    text-align: center;
    color: #000;
    width: 33.33%;
    &:hover{
        background: #F2BE35;
    }
  }
  .year.old,.decade.old,.century.old{
    color: #888;
  }
}
.has-error .input-group-addon{
  color: inherit
}
.content-map{
  margin-right: 15px;
  margin-left: 15px;
}