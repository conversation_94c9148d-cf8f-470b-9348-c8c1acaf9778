import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()
export class InventoryService{

    constructor(private chttp: CommonHttpService) {
    }

    // getBusinessType()
    // {
    //     return this.chttp.get('businessType/list');
    // }

    addInventory(data)
    {
        return this.chttp.post('test', data , true);
    }

    // editBusinessType(data)
    // {
    //     return this.chttp.post('businessType/edit', data, true);
    // }

    // getBusinessTypeDetail(id: number)
    // {
    //     return this.chttp.get('businessType/details/' + id);
    // }
}