import { UserService } from './../../shared/services/user.service';
import { Router } from '@angular/router';
import { ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { AuthGuard } from './../../shared/guards/auth-guard.service';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
declare var Messenger: any;
@Component({
    selector: 'user-shift-in',
    templateUrl: './user-shift-in.component.html',
    providers: []
})

export class UserShiftInComponent implements OnInit, OnDestroy {
    config: any;// New Change ****
    prePettyCash: any;
    isInShift: boolean;
    selectedUserShift: any;
    prePettyCashBalance: number;
    availableUserShiftTransfers: any[] = [];
    availableUserShiftTransfersDetails: any[] = [];
    public selectOptions: Select2.Options = {
        width: '100%',
        minimumResultsForSearch: Infinity
    };
    @ViewChild('alertModal') alertModal : ModalDirective;

    // Form
    shiftForm: FormGroup;
    shiftInAPI: any;
    getPettyAmountFromUserAPI: any;
    getAvailableUserShiftTransferAPI: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        public router: Router,
        private fb: FormBuilder,
        private US: UserService,
        private authGuard: AuthGuard,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.initForm();
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    ngOnInit() {
        this.getAvailableUserShiftTransferAPI = this.US.getAvailableUserShiftTransfer().subscribe((res) => {
            if (res.status === "success") {
                this.availableUserShiftTransfersDetails = res.data.userShifts;
                this.availableUserShiftTransfers = (<any[]>res.data.userShifts).map(data => {
                    return { id: data.id, text: data['user_shift_id.first_name'] };
                });
                if (this.availableUserShiftTransfersDetails.length) {
                    this.shiftForm.get('user_id').setValidators(Validators.required);
                    this.shiftForm.get('user_id').updateValueAndValidity();
                    this.availableUserShiftTransfers.unshift({ id: '000000', text: 'Please select a user' });
                } else {
                    this.prePettyCash = 0;
                }
                this.isInShift = res.data.shiftStatus ? true : false;
                if (this.isInShift) {
                    this.prePettyCash = res.data.shiftStatus.current_petty_amount;
                    this.authGuard.storeUserShift(res.data.shiftStatus);
                    Messenger().post({
                        hideAfter: 5,
                        message: "Your are currently in a shift!",
                        type: "info",
                        showCloseButton: true
                    });
                } else {
                    this.authGuard.removeUserShift();
                 }
            }
        });
    }
    initForm() {
        this.shiftForm = this.fb.group({
            user_id: [null]
        });
    }
    pettyCashUserChanged(event: any) {
        let userId = <FormControl>this.shiftForm.controls['user_id'];
        userId.patchValue(event.id);
        if (event.id === '000000') {
            userId.markAsTouched();
            userId.markAsDirty();
            userId.updateValueAndValidity();
            userId.setErrors({ 'required': true });
            this.prePettyCash = undefined;
        } else {
            this.getPettyAmountFromUserAPI = this.US.getPettyAmountFromUser(event.id)
                .subscribe((res) => {
                    if (res.status === "success") {
                        this.prePettyCash = res.data.current_petty_amount;
                    }
                });
        }
    }
    shiftIn() {
        if (this.shiftForm.valid && this.shiftForm.value.user_id != '000000') {
            if(!this.shiftForm.value.user_id) {
                this.alertModal.show();
            } else {
                this.proceedWithShiftIn();
            }
        } else {
            this.shiftForm.get('user_id').markAsDirty();
            this.shiftForm.get('user_id').markAsTouched();
        }
    }
    proceedWithShiftIn(form?: any) {
        let data = {}
        if(form) {
            data = form;
        }
        this.shiftInAPI = this.US.shiftIn(this.shiftForm.value.user_id, data).subscribe((res) => {
            if (res.status === "success") {
                this.authGuard.storeUserShift(res.data);
                this.router.navigateByUrl('admin/dashboard');
            }
        });
    }
    ngOnDestroy() {
        if (this.shiftInAPI) {
            this.shiftInAPI.unsubscribe();
        }
        if (this.getAvailableUserShiftTransferAPI) {
            this.getAvailableUserShiftTransferAPI.unsubscribe();
        }
        if (this.getPettyAmountFromUserAPI) {
            this.getPettyAmountFromUserAPI.unsubscribe();
        }
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}