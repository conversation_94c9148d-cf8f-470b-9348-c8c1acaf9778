<h2>Check-In Process Popup</h2>
<p>Put your card on the device</p>
<hr>
<div style="margin-bottom:20px;">
    <!-- <button (click)="getCheckInData()" style="margin-right:10px">Get Check-In data API</button> -->
    <!-- <span *ngIf="!showData">Click Valid for : {{counter | formatTime}}    </span> -->

    <p>
        Put your card on reader device to start checkin process.
    </p>
</div>

<div *ngIf="showData">
    <p><b>Room ID</b> : {{checkInData?.roomId}}</p>
    <p><b>Door ID</b> : {{checkInData?.doorId}}</p>
    <p><b>CheckIn</b> : {{checkInData?.startDateAndTime}}</p>
    <p><b>CheckOut</b> : {{checkInData?.endDateAndTime}}</p>
</div>

<div *ngIf="showError">
    <p style="color:red">Show some Error on Failure!</p>
</div>

<div>
    <button #clsPopup (click)="close()">Close Popup</button>
</div>