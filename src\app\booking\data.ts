interface NgSelectOption {
    id: string;
    text: string;
}

export const bookingList = [
    {
        'id': 1,
        'bookingId': '0010010',
        'bookingDate': '16-02-2017',
        'checkIn': '16-02-2017',
        'checkOut': '21-02-2017',
        'room': 'Royal',
        'roomNo':'A1',
        'rate': '1500',
        'guestNo': '4015',
        'guest': 'Vasant <PERSON>',
        'customerType': 'Regular',
        'status': 'Advance'
    },
    {
        'id': 2,
        'bookingId': '0010011',
        'bookingDate': '17-02-2017',
        'checkIn': '17-02-2017',
        'checkOut': '27-02-2017',
        'room': 'Royal',
        'roomNo':'A1',
        'rate': '1500',
        'guestNo': '4016',
        'guest': '<PERSON><PERSON><PERSON>',
        'customerType': 'hari bhakt',
        'status': 'Advance'
    }
];

export const customerType: NgSelectOption[] = [
    {
        id: '', // just to let placeholder can display
        text: ''
    },
    {
        id: 'Royal',
        text: 'Royal'
    },
    {
        id: 'Exclusive',
        text: 'Exclusive'
    },
    {
        id: 'Regular',
        text: 'Regular'
    }
];
export const bookingStatus: NgSelectOption[] = [
    {
        id: '', // just to let placeholder can display
        text: ''
    },
    {
        id: 'Advance',
        text: 'Advance'
    },
    {
        id: 'Check In',
        text: 'Check In'
    },
    {
        id: 'Check Out',
        text: 'check Out'
    }
];
export const roomNo: NgSelectOption[] = [
    {
        id: '', // just to let placeholder can display
        text: ''
    },
    {
        id: 'Royal-A1',
        text: 'Royal-A1'
    },
    {
        id: 'Royal-A2',
        text: 'Royal-A2'
    },
    {
        id: 'Royal-A3',
        text: 'Royal-A3'
    }
];

export const paymentType: NgSelectOption[] = [
    {
        id: '0',
        text: 'Cash Payment'
    },
    {
        id: '1',
        text: 'Card Payment'
    },
    {
        id: '2',
        text: 'Cheque Payment'
    },
    {
        id: '3',
        text: 'Bank Payment'
    }
]