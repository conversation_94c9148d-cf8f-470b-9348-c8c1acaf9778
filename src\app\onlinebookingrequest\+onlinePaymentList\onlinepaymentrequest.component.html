<section class="widget">
  <header>
    <h4><span style="color: red;"><i class="fa fa-money"></i>&nbsp;&nbsp;Online Payment Requests</span></h4>
  </header>
  <hr class="large-hr">
  <!-- <div class="float-sm-right text-right col-sm-6">
    <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchPayments() : null" placeholder="Search Payments">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="searchQuery = ''; searchPayments()"><i class="fa fa-times"></i></span>
    </div>
  </div> -->
  <div class="clearfix"></div>
  <div class="widget-body table-scroll">
    <div *ngIf="isLoading" class="loader-parent-style" style="display: flex;justify-content: center;padding: 2rem 0;">
      <i class="fa fa-circle-o-notch fa-pulse fa-2x"></i>
    </div>
    <div class="mt" *ngIf="!isLoading">
      <table class="table table-condence no-m-b small-footprint" [mfData]="paymentsData" #mf="mfDataTable"
        [mfRowsOnPage]="10">
        <thead>
          <tr>
            <th>
              <mfDefaultSorter by="id">#</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="room_title">Room Title</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="customer_name">Customer Name</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="contact">Contact</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="amount">Amount</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="transaction_id">Transaction ID</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="bank_name">Bank Name</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="upi_id">UPI ID</mfDefaultSorter>
            </th>
            <th *ngIf="auth.roleAccessPermission('room','edit')" class="no-sort">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let payment of mf.data">
            <td>{{findIndex(payment.id,"id")}}</td>
            <td>{{ payment.room_detail.room_title }}</td>
            <td>{{ payment.room_detail.name }}</td>
            <td>{{ payment.room_detail.contact }}</td>
            <td>{{ payment.amount | currency:'INR' }}</td>
            <td>{{ payment.transaction_id }}</td>
            <td>{{ payment.bank_name }}</td>
            <td>{{ payment.upi_id }}</td>
            <td *ngIf="auth.roleAccessPermission('room','edit')" class="width-100 text-center"
              style="display: flex; gap: 0.3rem;">
              <button class="btn btn-xs btn-primary" (click)="showER(payment)" placement="top"><i
                  class="fa fa-eye"></i></button>
              <button class="btn btn-xs btn-success" (click)="acceptmodal(payment)" placement="top"><i
                  class="fa fa-check"></i></button>
              <button class="btn btn-xs btn-danger" (click)="openCancelModal(payment.id)"><i
                  class="fa fa-close"></i></button>
            </td>
          </tr>
          <tr *ngIf="canViewRecords && mf.data.length === 0">
            <td colspan="100">
              {{'ROOM.ROOM_MANAGE.NO MATCHES'| translate:param}}
            </td>
          </tr>
          <tr *ngIf="!canViewRecords">
            <td class="text-danger" colspan="100">
              {{'ROOM.ROOM_MANAGE.PERMISSION_DENIED'| translate:param}}
            </td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="12">
              <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
            </td>
          </tr>
        </tfoot>
      </table>
    </div>
  </div>
</section>

<!-- View all the payment Details Modal -->
<div class="modal" bsModal #viewPaymentRequest="bs-modal" role="dialog" [config]="{backdrop:false}"
  aria-labelledby="mySmallModalLabel2">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <section class="booking-info m-2 p-2">
        <header>
          <div class="row">
            <div class="col-sm-8">
              <h4>
                <span class="capitalized">
                  <i class="fa fa-clipboard"></i>&nbsp;&nbsp; View Payment Request</span>
              </h4>
            </div>
            <div class="col-sm-4 view-details-buttons">
              <button type="button" class="btn btn-sm btn-danger float-sm-right" (click)="goback()">
                <i class="fa fa-angle-left"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.BACK' | translate:param}}</button>
            </div>
          </div>
        </header>
        <hr class="large-hr" />
        <div class="clearfix"></div>
        <div class="widget-body" *ngIf="selectedPayment && paymentsData">
          <div class="mt">
            <div class="row">
              <!-- Booking Information Section -->
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h4>View Payment Details</h4>
                  </div>
                  <div class="panel-body">
                    <table class="table table-no-mar">
                      <tr>
                        <td>Payment Id:</td>
                        <td><strong>{{selectedPayment.id ? selectedPayment.id : '-'}}</strong></td>
                      </tr>
                      <tr>
                        <td>Transaction Id:</td>
                        <td>
                          <strong>{{selectedPayment?.transaction_id ? selectedPayment.transaction_id : '-' }}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Bank Name:</td>
                        <td>
                          <strong class="capitalize">{{selectedPayment.bank_name ? selectedPayment.bank_name :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Request Date:</td>
                        <td>
                          <strong>{{(selectedPayment.createdAt ? (selectedPayment.createdAt | date) : '-')}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Total Amount:</td>
                        <td>
                          <strong>
                            <span>+&nbsp;{{selectedPayment.room_detail.total_amount}}
                            </span>
                          </strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Paid Amount:</td>
                        <td>
                          <strong class="text-success">
                            {{selectedPayment.amount ?
                            selectedPayment.amount : ''}}
                          </strong>
                        </td>
                      </tr>
                      <tr>
                        <td>UPI ID:</td>
                        <td>
                          <strong class="text-success">{{selectedPayment.upi_id ? selectedPayment.upi_id : "-"
                            }}</strong>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h4>Guest Details</h4>
                  </div>
                  <div class="panel-body">
                    <table class="table table-no-mar">
                      <tr>
                        <td>Guest Name:</td>
                        <td style="text-transform:uppercase;">
                          <strong>{{selectedPayment.room_detail.name ? selectedPayment.room_detail.name : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Guest Type:</td>
                        <td class="capitalize">
                          <strong>{{selectedPayment.room_detail.customer_name ?
                            selectedPayment.room_detail.customer_name : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Contact Number:</td>
                        <td class="capitalize">
                          <strong>{{selectedPayment.room_detail.contact ? selectedPayment.room_detail.contact :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Guest Email:</td>
                        <td>
                          <strong>{{selectedPayment.room_detail.email ? selectedPayment.room_detail.email :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Room Number:</td>
                        <td class="capitalize">
                          <strong>{{selectedPayment.room_detail.room_title ? selectedPayment.room_detail.room_title :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Aadhar Card:</td>
                        <td>
                          <strong class="text-danger">
                            {{selectedPayment.room_detail.aadharcard_number ?
                            selectedPayment.room_detail.aadharcard_number : ''}}
                          </strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Pan Card:</td>
                        <td>
                          <strong>
                            {{selectedPayment.room_detail.pancard_number ?
                            selectedPayment.room_detail.pancard_number : ''}}
                          </strong>
                        </td>
                      </tr>
                      <!-- <tr>
                        <td>{{'VIEW_BOOKI_DETAI.ROOM_TYP' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedPayment.roomCategoryDetails.customer_name ? selectedPayment.roomCategoryDetails.name
                            : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.TOT_ADU' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedPayment.adult ? selectedPayment.adult : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.TOT_CHIL' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedPayment.child ? selectedPayment.child : '-'}}</strong>
                        </td>
                      </tr> -->
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row" style="align-items: center;">
            <!-- Transfer Room Dropdown -->
            <div class="col-md-6 my-0 py-0" style="margin-top: 20px;">
              <div class="row" style="align-items: center;">
                <div class="form-group col-md-3 col-sm-4 my-0 py-0">
                  <p class="m-0 p-0">Room No : </p>
                </div>
                <div class="form-group col-md-3 col-sm-3 my-0 p-0">
                  <select class="form-control" (change)="onRoomChange($event)">
                    <option [value]="selectedPayment.room_detail.room_title" disabled selected>
                      {{selectedPayment.room_detail.room_title}}</option>
                    <option *ngFor="let room of roomsList" [value]="room.id">{{room.title}}</option>
                  </select>
                </div>
                <div class="form-group col-md-4 col-sm-4 my-0 py-0">
                  <button class="btn btn-primary" style="margin-left: 10px;" (click)="updateRoomDetails(selectedPayment)" *ngIf="transferRoomId">Update</button>
                </div>
              </div>
            </div>
            <div class="col text-center">
              <button class="btn btn-primary" (click)="viewDocumentProof(selectedPayment.room_detail.guestFiles)">View Screenshot</button>
              <button class="btn btn-success" (click)="approvePaymentRequest()" [disabled]="PaymentButtonLoader">
                <span class="loader-parent-style" *ngIf="PaymentButtonLoader">
                  <i *ngIf="PaymentButtonLoader" class="fa fa-circle-o-notch fa-pulse fa-2x"></i>
                </span>
                <span *ngIf="!PaymentButtonLoader">Accept</span>
              </button>
              <!-- <button class="btn btn-success" style="font-size: 10px;">
                <i class="fa fa-circle-o-notch fa-pulse fa-2x"></i>
              </button> -->
              <button class="btn btn-danger" (click)="openCancelModal(selectedPayment.id)"
                [disabled]="PaymentButtonLoader">Reject</button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>

<!-- cancel Modal -->
<div class="modal" bsModal #cancelPaymentRequest="bs-modal" role="dialog">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Reject Payment Request</h5>
      </div>
      <div class="modal-body">
        <label for="cancelReason">Reason:</label>
        <textarea id="cancelReason" [(ngModel)]="cancelReason" class="form-control"
          placeholder="Enter cancel reason"></textarea>

        <!-- show message got from api -->
        <div *ngIf="cancelPaymentMsg" [class]="cancelPaymentMsgStyle" style="margin-top: 10px;">
          {{ cancelPaymentMsg }}
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-danger" (click)="cancelOnlinePaymentRequest()" [disabled]="cancelButtonLoader">
          <i *ngIf="cancelButtonLoader" class="fa fa-circle-o-notch fa-pulse fa-2x"></i>
          <span *ngIf="!cancelButtonLoader"> Reject</span>
        </button>

        <button class="btn" (click)="closeRejectPayment()" [disabled]="cancelButtonLoader">
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Model to accept payment -->
<div class="modal fade" bsModal #acceptPaymentModal="bs-modal" role="dialog" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true" tabindex="1">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Accept Payment request</h5>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <!-- Error Message -->
            <div *ngIf="acceptErrorMessage" [class]="successMessageStyle">
              {{ acceptErrorMessage }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <!-- Cancel Booking Button -->
            <button type="button" class="btn btn-success" (click)="approvePaymentRequest()"
              [disabled]="PaymentButtonLoader">
              <span class="loader-parent-style" *ngIf="PaymentButtonLoader"><i *ngIf="PaymentButtonLoader"
                  class="fa fa-circle-o-notch fa-pulse fa-2x"></i></span>
              <span *ngIf="!PaymentButtonLoader"> Accept</span>
            </button>
            <!-- Close Modal Button -->
            <button type="button" class="btn " (click)="closeAcceptPayment()"
              [disabled]="PaymentButtonLoader">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Model For View Document -->
<div bsModal #viewDocumentModal="bs-modal" id="viewDocumentModal" class="modal fade" tabindex="-1" role="dialog"
[config]="{backdrop: false, keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div style="display: flex; justify-content: space-evenly; align-items: center;">
        <ng-container *ngFor="let imageUrl of imageUrls">
          <div>
            <img   class="image-modal-view px-1 pb-1 pt-1" [src]="imageUrl.Url" alt="" style="max-width: 500px; max-height: 500px;">
            <p class="text-center"><b>{{imageUrl.doc_name}}</b></p>
          </div>
        </ng-container>
      </div>
      <div class="modal-footer">
        <div class="btn-group">
          <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="closeDocumentModal()">
            {{'VIEW_BOOKI_DETAI.CLOSE' | translate:param}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal " bsModal #successModal="bs-modal" role="dialog" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true" tabindex="1">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Message</h5>
      </div>
      <div class="modal-body">
        <div class="row">

          <div class="col-sm-12">
            <!-- Error Message -->
            <div *ngIf="acceptErrorMessage" [class]="successMessageStyle">
              {{ acceptErrorMessage }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <!-- Cancel Booking Button -->
            <!-- <button type="button" class="btn btn-success" (click)="acceptBookingRequest()"
              [disabled]="acceptButtonLoader">
              <span class="loader-parent-style" *ngIf="acceptButtonLoader"><i *ngIf="acceptButtonLoader"
                  class="fa fa-circle-o-notch fa-pulse fa-2x"></i></span>
              <span *ngIf="!acceptButtonLoader"> accept booking</span>
            </button> -->
            <!-- Close Modal Button -->
            <button type="button" class="btn btn-secondary" (click)="successModal.hide()"
              [disabled]="acceptButtonLoader">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>