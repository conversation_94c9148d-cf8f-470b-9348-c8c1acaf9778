  <!-- <div class="" [ngClass]="{'col-md-12': (addRUHidden && editRUHidden), 'col-md-7' : (!addRUHidden || !editRUHidden)}"> -->
    <section class="widget" *ngIf="addRUHidden && editRUHidden">
      <header>
        <h4><span class="" style="color: red;"><i class="fa fa-user"></i>&nbsp;&nbsp;{{'DIS_REF_USER.REF_USER_MAN' | translate:param}}</span></h4>
      </header>
      <hr class="large-hr">
      <div class="float-sm-right text-right col-sm-12">
        <button
          *ngIf="auth.roleAccessPermission('referenceuser','add')"
          (click)="showAddRU()" 
          [disabled]="!addRUHidden || !editRUHidden" 
          class="display-inline-block btn btn-sm btn-inverse" 
          tooltip="{{'DIS_REF_USER.ADD_TOOLTIP' | translate:param}}" 
          placement="top">
          <i class="fa fa-plus"></i>&nbsp;&nbsp;{{'DIS_REF_USER.ADD' | translate:param}}
        </button>
        <div class="form-group display-inline-block __search">
          <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'REF_USER.SEARCH' | translate:param}}">
          <span class="form-group-addon"><i class="fa fa-search"></i></span>
          <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
        </div>
      </div>
      <div class="clearfix"></div>
      <div class="widget-body table-scroll">
        <div class="mt">
          <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10" >
            <thead>
              <tr>
                <th>
                  <mfDefaultSorter by="id">#</mfDefaultSorter>
                </th>
                <th style="width: 170px;">
                  <mfDefaultSorter by="name">{{'DIS_REF_USER.USER' | translate:param}}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="contact">{{'DIS_REF_USER.REFERENCE' | translate:param}}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="contact">{{'DIS_REF_USER.GUEST_PER' | translate:param}}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="contact">{{'DIS_REF_USER.REFERENCE_PER' | translate:param}}</mfDefaultSorter>
                </th>
                <th *ngIf="auth.roleAccessPermission('referenceuser','edit')" class="no-sort text-center">
                  <mfDefaultSorter by="status">{{'DIS_REF_USER.ACTION' | translate:param}}</mfDefaultSorter>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let ds of mf.data; let i =index" [ngClass]="{'__selected': (ds.id == selectedRU?.id)}">
                <td>{{findIndex(ds.id,"id")}}</td>
                <td style="width: 170px;"><span class="uppercase fw-semi-bold">{{ds.first_name}}&nbsp;{{ds.last_name}}</span></td>
                <td><span class="">{{ds.reference_user_ids ? getCustomerTypeString(ds) : '-' }}</span></td>
                <td style="text-align: center;">
                  <span *ngIf="ds.allow_to_update_customer_type" style="color: green;"><i class="fa fa-check-square" aria-hidden="true"></i></span>
                  <span *ngIf="!ds.allow_to_update_customer_type" style="color: red;"><i class="fa fa-times" aria-hidden="true"></i></span>
                </td>
                <td style="text-align: center;">
                  <span *ngIf="ds.allow_to_update_reference_user" style="color: green;"><i class="fa fa-check-square" aria-hidden="true"></i></span>
                  <span *ngIf="!ds.allow_to_update_reference_user" style="color: red;"><i class="fa fa-times" aria-hidden="true"></i></span>
                </td>
                <td *ngIf="auth.roleAccessPermission('referenceuser','edit')" class="width-100 text-center">
                  <button (click)="showEditRU(ds)" class="btn btn-xs btn-default" tooltip="Edit Reference User" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;Edit</button>
                </td>
              </tr>
              <tr *ngIf="canViewRecords && mf.data.length === 0">
                <td colspan="100">
                  {{'REF_USER.NO_MATCHES' | translate:param}}
                </td>
              </tr>
              <tr *ngIf="!canViewRecords">
                <td class="text-danger" colspan="100">
                  {{'REF_USER.PERMISSION_DENIED' | translate:param}}
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="12">
                  <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </section>
  <!-- </div> -->

    <add-discount-reference-user class="col-md-12" *ngIf="!addRUHidden"
     [getHiddenaddRU]="addRUHidden" [userList]="users" [referenceList]="referenceUsers" (sendHiddenaddRU)="handleHiddenaddRU($event)"
     (listSavedData)="addDateTolist($event)"></add-discount-reference-user>
    
    <edit-discount-reference-user class="col-md-12" *ngIf="!editRUHidden" [data]="selectedRU"
    [getHiddeneditRU]="editRUHidden" [userList]="users" [referenceList]="referenceUsers" (sendHiddeneditRU)="handleHiddeneditRU($event)"
    (listeditedData)="updateTolist($event)" ></edit-discount-reference-user>

