// import { IsDisabled } from './../shared/directive/isDisabled.directive';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

import { SharedModule } from './../shared/shared.module';
import { OnlineBookingRequestComponent } from './+onlineBookingList/onlinerequests.component';
import { EditOnlineBookingRequest } from './+onlineBookingList/+edit/editbookingrequest.room.component';

import { NgSelectModule } from '@ng-select/ng-select';
// import { FileUploadModule } from 'ng2-file-upload';
import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { ModalModule } from 'ngx-bootstrap/modal';

import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { BookingService } from 'app/shared/services/booking.service';
import { OnlinePaymentRequestModule } from 'app/onlinepaymentrequest/onlinepaymentrequest.module';
import { OnlinePaymentRequestComponent } from './+onlinePaymentList/onlinepaymentrequests.component';
import { PaymentRequestService } from 'app/shared/services/payment.service';
import { ViewrequestBookingDetailsComponent } from 'app/popup-components/view-bookingrequest.component';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
//all routes related to this module
const route = [
    { path: '', component: OnlineBookingRequestComponent, pathMatch: 'full' },
    { path: 'requests', component: OnlineBookingRequestComponent },
    { path: 'payment-request', component: OnlinePaymentRequestComponent },
    { path: 'edit/:id', component: EditOnlineBookingRequest },
];

@NgModule({
    imports: [
        FormsModule,
        CommonModule,
        SharedModule,
        TooltipModule.forRoot(),
        NgSelectModule,
        DataTableModule,
        ModalModule.forRoot(),
        // FileUploadModule,
        ReactiveFormsModule,
        RouterModule.forChild(route),
        TranslateModule.forRoot({
            loader:{ 
                provide: TranslateLoader, 
                useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
                deps: [HttpClient] 
            }})
    ],
    exports: [],
    declarations: [
        EditOnlineBookingRequest,
        OnlineBookingRequestComponent,
        OnlinePaymentRequestComponent,
        ViewrequestBookingDetailsComponent
    ],
    providers: [BookingService, PaymentRequestService],
    schemas:[CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
})
export class OnlineBookingRequestModule { }
