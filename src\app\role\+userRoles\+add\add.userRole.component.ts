import { CustomValidators } from 'ng2-validation';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { UserRoleService } from './../../../shared/services/userrole.service';
import { TranslateService } from '@ngx-translate/core';

interface NgSelectOption {
    id: string | number;
    text: string;
}

declare var Messenger: any;
declare var jQuery: any;

@Component({
    selector: 'add-role',
    templateUrl: '../userRole.actions.html'
})
export class AddRoleComponent implements OnInit {
    userRole: FormGroup;
    dropdownSelect: number;
    pageType: string = "Add";
    
    // input/outputs
    @Input() gethideAddUR;
    @Input() getGroupListData;
    @Output() sendhideAddUR = new EventEmitter();

    // service variables
    private sub: any;
    private addRoleService: any;
    
    constructor(
        public translate: TranslateService,
        private _fb: FormBuilder,
        private URS: UserRoleService) {
        this.buildForm();
        translate.get('USER_ROLE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
        });
    }
    buildForm() {
        this.userRole = this._fb.group({
            name: ['', Validators.required],
            group_id: ['', [Validators.required,CustomValidators.digits]],
            status: [true, Validators.required],
            description: ['']
        })
    }
    ngOnInit() {
        if(this.getGroupListData.length > 0){
            this.dropdownSelect = this.getGroupListData[0].id;
            this.userRole.controls['group_id'].patchValue(this.getGroupListData[0].id)
        }
     }

    addUserRole() {
        // --- in other modules isValid condition is checked rather than hasValue.. Is there any significant difference? --- //
        if (this.userRole.value) {
            this.addRoleService = this.URS.saveUserRole(this.userRole.value)
                .subscribe((res) => {
                     if (res.status === "success") {
                        this.userRole.reset();
                        console.log("Added User Response : ",res);
                        // adding group name for listing 
                        res.data['group.name'] = this.getGroupListData[this.findIndex(res.data.group_id) - 1].name;
                        this.toggleChild(res.data)
                    }
                },
                (err) => {
                   let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.userRole.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        }else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    // change form control group value when change in dropdown
    groupChanges(event: any) {
        if (event) {
            this.userRole.patchValue({
                group_id: event.id
            });
        }
    }
    // method simply just returns data needed for to select dropdown to work
    getSelect2DefaultList(): NgSelectOption[] {
        if(this.getGroupListData.length > 0){
            return this.getGroupListData.map(obj => ({ 
                id: obj.id, 
                text: obj.name 
            }));
        }
        return [];
    }
    findIndex(searchTerm) {
        for (var i = 0, len = this.getGroupListData.length; i < len; i++) {
            if (this.getGroupListData[i].id === searchTerm) return (i + 1);
        }
        return -1;
    }
    toggleChild(data) {
        let result;
        this.gethideAddUR = !this.gethideAddUR;
        if (data) {
            result = { gethideAddUR: this.gethideAddUR, data: data }
        } else {
            result = { gethideAddUR: this.gethideAddUR }
        }
        this.sendhideAddUR.emit(result);
    }
    ngOnDestroy() {
     if(this.addRoleService){
         this.addRoleService.unsubscribe();
     }
    }
}