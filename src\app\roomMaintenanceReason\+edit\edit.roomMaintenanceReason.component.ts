import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, Validators, FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Select2OptionData } from 'ng2-select2';
import { RoomMaintenanceReasonService } from 'app/shared/services/roomMaintenanceReason.service';
// import * as data from '../../data';
declare var Messenger: any;
@Component({
    selector: 'edit-room-maintenance',
    templateUrl: '../roomMaintenanceReason.actions.component.html'
})

export class EditRoomMaintenanceReasonComponent implements OnInit {
    public pageName: string = "Edit";
    select2Options: any = {
        width: '100%'
    };
    roomForm: FormGroup;
    //services
    private sub: any;
    private floor: any;
    private getData: any;
    private floorList: any;
    // Input and outputs
    @Input() getHiddenER;
    @Input() selectedRoom;
    @Output() sendHiddenER = new EventEmitter();
    public roomCat: any[];
    public wing: any[];
    public selectedFloorId: any;
    public selectedBuildingId: any;
    public selectedRoomCategoryId: any;
    constructor(
        private _fb: FormBuilder,
        private RS: RoomMaintenanceReasonService
    ) {}
        
    ngOnInit() {
        this.buildForm();
        this.roomForm.patchValue(this.selectedRoom);
    }
    buildForm() {
        this.roomForm = this._fb.group({
            title: ['', Validators.required],
            description: ['', []],
            status: [true, Validators.required]
        })
    }

    // save
    saveRoom() {
        if (this.roomForm.valid) {
            // console.log("Form : ",this.roomForm.value);
            this.sub = this.RS.updateRoomMaintenanceData(this.selectedRoom.id, this.roomForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.toggleChild(res.data);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.roomForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    // toggle to mains
    toggleChild(data) {
        this.getHiddenER = !this.getHiddenER;
        let result;
        if (data) {
            result = { 'getHiddenER': this.getHiddenER, 'data': data }
        } else {
            result = { 'getHiddenER': this.getHiddenER }
        }
        this.sendHiddenER.emit(result);
    }

    ngOnDestroy() {

    }
}