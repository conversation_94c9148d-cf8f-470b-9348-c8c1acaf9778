/**
 * AuthGuard Service
 *
 * -- This file is here to help auth the routes and prevent user from accessing the
 *    unauthorised routes , this will check all authorisation before the routing processed
 *
 * -- Also used to store user data in localStorage (encrypted)
 * -- Also used to get user data from localStorage (decrypted)
 * -- Also used to remove user data from localStorage
 *
 * This file is loaded on root level so it will be accessble throught the whole project
 *
 */
import { Injectable } from '@angular/core';
declare var Messenger: any;
import {
    CanActivate,
    CanActivateChild,
    Router,
    ActivatedRouteSnapshot,
    RouterStateSnapshot
} from '@angular/router';
import { _secretKey } from './../globals/config';

import * as CryptoJS from 'crypto-js';
import { concat } from 'rxjs/operator/concat';

@Injectable()
export class AuthGuard implements CanActivate, CanActivateChild {
    public loginRedirect: String = 'admin/shift'; // Default login redirect set to dashboard( From 26/10/2018 it was changed to user-shift module)
    public ud; // ud - userDetail will be used for (showing name / get role / get other details)
    public dm;
    public m;
    public us; // used to show current shift profile
    protected rp; // rp - role permissions of current user
    protected isSuperAdmin: boolean;

    private _secretKey: String = _secretKey;

    constructor(
        private router: Router,
    ) {
        this.getUser();
    }


    /**
     * canActivate : will check all root level routes ,
     *              that need to be added on root route file (e.g. : app.routes.ts)
     * @param route :
     * @param state :
     */
    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
        if (localStorage.getItem('0') && localStorage.getItem('m') && localStorage.getItem('dm')) // check if token is stored locally or not
        {
            return true;
        }
        // Used to redirect user on hitted url before redirected to login
        console.log('login ', state.url)
        this.loginRedirect = state.url;
        this.router.navigate(['login']);
        return false;
    }


    /**
     * canActivateChild : will check all child level routes ,
     *                    that need to be added on children route file (e.g. : layout.routes.ts)
     * @param route :
     * @param state :
     */
    canActivateChild(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
        // return this.canActivate(route, state);
        // check if the user is in a shift
        // if(this.checkUserShiftStatus() || this.isAdmin() || this.isSuperAdmin) {
        if (this.checkUserShiftStatus() || this.isSuperAdmin) {
            // --- check if token is stored locally or not --- //
            if (localStorage.getItem('0')) {
                let isAllow: boolean = false;
                let id: any;
                if (localStorage.getItem('dm') && localStorage.getItem('m')) {
                    // --- create route --- //
                    let currentRoute = '';
                    for (var i = 1; i < route['_urlSegment'].segments.length; i++) {
                        if (isNaN(parseInt(route['_urlSegment'].segments[i].path))) {
                            currentRoute = currentRoute + route['_urlSegment'].segments[i].path;
                        }
                        if (i < route['_urlSegment'].segments.length - 1 && isNaN(parseInt(route['_urlSegment'].segments[i + 1].path))) {
                            // --- will not add '/' if last segment --- //
                            currentRoute = currentRoute + '/';
                        }
                    }
                    // --- loop through default menu to find current route id --- //
                    this.dm.forEach(element => {
                        if (element.url == currentRoute) {
                            id = element.id;
                        }
                    });
                    // --- checking if given URL is defined --- //
                    if (id) {
                        this.m.forEach(element => {
                            if (element.router_link == id) {
                                isAllow = true;
                            }
                            else {
                                // --- child looping --- //
                                if (element.children) {
                                    element.children.forEach(element => {
                                        if (element.router_link == id) {
                                            isAllow = true;
                                        }
                                    });
                                }
                            }
                        });
                        if (isAllow) {
                            return isAllow;
                        }
                        else {
                            return this.CheckUserType(isAllow);
                        }
                    }
                    else {
                        return this.CheckUserType(isAllow);
                    }
                }
            }
        } else {
            this.router.navigate(['admin/shift']);
            this.showMessage('Please shift in first!', 'error');
            return false;
        }
        // --- Used to redirect user on hitted url before redirected to login --- //

    }
    /**
     * Checks User type and returns redirect navigation state accordingly
     * 
     * @param isAllow : boolean param for checking navigation state 
     */
    CheckUserType(isAllow: boolean) {
        let data = localStorage.getItem('0');
        let dmEnc = CryptoJS.AES.decrypt(data, this._secretKey);
        let udSA = JSON.parse(dmEnc.toString(CryptoJS.enc.Utf8));
        // --- bypass super admin from validating --- //
        if (udSA.session_data.is_super_admin) {
            isAllow = true;
            return isAllow;
        } else {
            this.router.navigate(['admin/dashboard']);
            this.showMessage("UnAuthorised User", "error");
            return isAllow;
        }
    }
    showMessage(message: any, status: any) {
        Messenger().post({
            hideAfter: 5,
            message: message,
            type: status,
            showCloseButton: true
        });
    }
    /**
     * storeUser : Centeral level function (can be access from anywhere in the project)
     * @param data : Data provide on login time
     */
    storeUser(data) {
        localStorage.setItem('0', CryptoJS.AES.encrypt(JSON.stringify(data), this._secretKey));
        localStorage.setItem('m', CryptoJS.AES.encrypt(JSON.stringify(data.menu), this._secretKey));
        localStorage.setItem('dm', CryptoJS.AES.encrypt(JSON.stringify(data.defaultMenu), this._secretKey));
        localStorage.setItem('rp', CryptoJS.AES.encrypt(JSON.stringify(data.rolePermission), this._secretKey));
    }

    /**
     * removeUser : Centeral level function (can be access from anywhere in the project)
     */
    removeUser() {
        localStorage.removeItem('0');
        localStorage.removeItem('m');
        localStorage.removeItem('dm');
        localStorage.removeItem('rp');
    }

    /**
     * Stores the current user shift details
     */
    storeUserShift(data) {
        localStorage.setItem('us', CryptoJS.AES.encrypt(JSON.stringify(data), this._secretKey));
    }

    /**
     * Removes the current user shift details
     */
    removeUserShift() {
        localStorage.removeItem('us');
    }
    /**
     * Checks if a user has been shift-in
     */
    checkUserShiftStatus() {
        return localStorage.getItem('us');
    }
    getUserShift() {
        let us = localStorage.getItem('us');
        if (us) {
            let bytes = CryptoJS.AES.decrypt(us, this._secretKey);
            this.us = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
            return this.us;
        } else {
            return false;
        }
    }
    /**
     * Return a boolean status for accessing API Controllers and corresponding actions
     * 
     * @param modal : API Controller to be accessed
     * @param action : Type of CRUD function to be performed
     */
    public roleAccessPermission(modal: string, action: string) {
        if (!this.isSuperAdmin) {
            return modal in this.rp ? action in this.rp[modal] ? this.rp[modal][action] : false : false;
        }
        else {
            return true;
        }
    }

    /**
     * Return a boolean status for current user type
     * 
     */
    public isAdmin() {
        return this.ud.userdata.is_admin;
    }
    public getDharamshalaID() {
        return this.ud.session_data.dharamshala_id;
    }
    /**
     * storeUser : Centeral level function (can be access from anywhere in the project)
     */
    getUser() {
        /**
         * Try Catch block :
         * it will throw an error if someone will change the data directly from browser
         * on catch it will remove all localStorage data and redirects to login page
         */
        try {
            // console.log("Fetching local data");
            let userData = localStorage.getItem('0');
            let bytes = CryptoJS.AES.decrypt(userData, this._secretKey);
            this.ud = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));

            let defaultMenu = localStorage.getItem('dm');
            let menu = localStorage.getItem('m');
            let rolePermission = localStorage.getItem('rp');
            // default menu
            let dmEnc = CryptoJS.AES.decrypt(defaultMenu, this._secretKey);
            this.dm = JSON.parse(dmEnc.toString(CryptoJS.enc.Utf8));

            // current menu
            let mEnc = CryptoJS.AES.decrypt(menu, this._secretKey);
            this.m = JSON.parse(mEnc.toString(CryptoJS.enc.Utf8));
            this.isSuperAdmin = this.m.length ? false : true;
            // role permissions
            let rp = CryptoJS.AES.decrypt(rolePermission, this._secretKey);
            this.rp = JSON.parse(rp.toString(CryptoJS.enc.Utf8));
        }
        catch (err) {
            this.removeUser();
            console.error('Please login again');
        }
    }
}
