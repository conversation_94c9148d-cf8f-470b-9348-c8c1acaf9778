import { Component, OnInit, <PERSON><PERSON><PERSON>roy, ViewEncapsulation } from '@angular/core';
import { UserService } from './../../shared/services/user.service';
import { Sidebar } from "../../layout/sidebar/sidebar.component";
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

@Component({
    selector: 'user-management',
    templateUrl: './userManagement.component.html',
    styleUrls: ['./userManagement.component.scss'],
    providers: [Sidebar]
    // encapsulation: ViewEncapsulation.None,
})
export class UserManagementComponent implements OnInit {
    config: any;// New Change ****
    data: any[];
    userRolesData: any;
    countryList: any[];
    originalData: any[];
    searchQuery: string;
    isSuperAdmin: boolean;
    // service variables
    private sub: any;
    private userRoles: any;
    public canViewRecords: boolean;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    // hide - show add and edit user 
    hiddenAddUser: boolean = true;
    hiddenEditUser: boolean = true;
    
    selectedUser: any;
    constructor(
        public translate: TranslateService,// New Change ****
        private US: UserService,
        private auth: AuthGuard,
        private sideBar: Sidebar,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        this.isSuperAdmin = this.sideBar._isSuperAdmin;
        // to get all users data
        this.canViewRecords = true;
        this.sub = this.US.getAllUsers()
            .subscribe((res) => {
                if (res.status === "success") {
                    console.log("User Management Data : ",res);
                    this.data = res.data.userData;
                    this.countryList = res.data.countries;
                    // --- Creating Clone of this.data to enable search functionality --- // 
                    this.originalData = res.data.userData;
                    // console.log("this.data : ",this.data);
                    this.userRolesData = res.data.roles;
                }// end of sub
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
             // New Change ****
            this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
            });
    }

    ngOnInit() {

    }
    findIndex(searchTerm, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i].id === searchTerm) return (i + 1);
        }
        return -1;
    }
    // for add user
    toggleAddUser() {
        // this will show hide AddUser child component
        this.hiddenAddUser = !this.hiddenAddUser;
    }
    hiddenAddUserToggled(event) {
        // this will cahnge hiddenAddUser value which was recieved by child component 
        // and make child component hidden
        this.hiddenAddUser = event.gethiddenAdduser;
        if (this.canViewRecords && event.data) {
            // push newly added user data to list array.
            this.originalData.push(event.data);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }
    // for edit user
    editUserData(user) {
        this.selectedUser = user;
        this.hiddenEditUser = !this.hiddenEditUser;
    }
    hiddenEditUserToggle(event) {
        // this will cahnge hiddenEditUser value which was recieved by child component 
        // and make child component hidden
        this.hiddenEditUser = event.gethiddenEditUser;
        if (this.canViewRecords && event.data) {
            // replace edited values to view in list
            this.data[this.findIndex(event.data.id) - 1] = event.data;
            this.originalData[this.findIndex(event.data.id,this.originalData) - 1] = event.data;
        }
    }
    getRoleName(id) {
        let index = -1;
        for (var i = 0, len = this.userRolesData.length; i < len; i++) {

            if (this.userRolesData[i].id == id) {
                index = i;
                break;
            }
        }
        return this.userRolesData[index].name;
    }
    searchEvent() {
        this.initializeData();;
        if(this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter( data => {
                return ((<string>(data.first_name + data.last_name)).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    initializeData() {
        this.data = this.originalData;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        if(this.sub) {
            this.sub.unsubscribe();
        }
         // New Change ****
         if (this.langChangeSub)
         this.langChangeSub.unsubscribe();
    }
}