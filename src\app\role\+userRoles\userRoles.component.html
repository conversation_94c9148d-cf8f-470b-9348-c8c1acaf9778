<add-role *ngIf="!hideAddUR" [getGroupListData]="groupListData"
  [gethideAddUR]="hideAddUR" (sendhideAddUR)="handlehideAddUR($event)"
></add-role>

<edit-role *ngIf="!hideEditUR" [selecteUR]="selectedUR" [getGroupListData]="groupListData"
  [gethideEditUR]="hideEditUR" (sendhideEditUR)="handlehideEditUR($event)"
></edit-role>

<section class="widget" *ngIf="hideAddUR && hideEditUR">
  <header>
    <h4><span class=""><i class="fa fa-user"></i>&nbsp;&nbsp;{{'USER_ROLE.ROLE_MANAGE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
    <ol class="breadcrumb float-sm-left">
      <li class="breadcrumb-item">{{'USER_ROLE.ROLE_MANAGE' | translate:param}}</li>
      <li class="breadcrumb-item active">{{'USER_ROLE.ROLES' | translate:param}}</li>
    </ol>
    <div class="float-sm-right">
      <button *ngIf="auth.roleAccessPermission('role','add')" (click)="showAddUR()" class="btn btn-sm btn-inverse" tooltip="{{'USER_ROLE.ADD_NEW_USER_ROLE' | translate:param}}" placement="top"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{'USER_ROLE.ADD_ROLE' | translate:param}}</button>
      <div class="form-group display-inline-block __search">
        <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'USER_ROLE.SEARCH' | translate:param}}">
        <span class="form-group-addon"><i class="fa fa-search"></i></span>
        <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
      </div>
    </div>
    <div class="clearfix"></div>
    <hr>
  <div class="widget-body table-scroll">
    <div class="mt">
         
           <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
          <tr>
            <th>
              <mfDefaultSorter by="id">#</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="name">{{'USER_ROLE.ROLE_NAME' | translate:param}}</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="name">{{'USER_ROLE.MENU_GRP' | translate:param}}</mfDefaultSorter>
            </th>
            <th class="no-sort text-center">
              <mfDefaultSorter by="status">{{'USER_ROLE.STATUS' | translate:param}}</mfDefaultSorter>
            </th>
            <th *ngIf="auth.roleAccessPermission('role','edit')" class="no-sort text-center">
              <mfDefaultSorter by="status">{{'USER_ROLE.ACTION' | translate:param}}</mfDefaultSorter>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let ds of mf.data; let i = index">
            <td>{{findIndex(ds.id)}}</td>
            <td><span class="fw-semi-bold uppercase">{{ds.name}}</span></td>
            <td class="capitalized">{{ds['group.name']}}</td>
            <td class="text-center ">
              <span class="text-success" *ngIf="ds.status">{{'USER_ROLE.ACTIVE' | translate:param}}</span>
              <span class="text-danger" *ngIf="!ds.status">{{'USER_ROLE.INACTIVE' | translate:param}}</span>
            </td>
            <td *ngIf="auth.roleAccessPermission('role','edit')" class="width-200 text-center">
              <div>
                <button (click)="showEditUR(ds)" class="btn btn-xs btn-default" tooltip="{{'USER_ROLE.EDIT_USER_ROLE' | translate:param}}" placement="top"><i class="fa fa-pencil"></i>&nbsp;{{'USER_ROLE.EDIT' | translate:param}}</button>
              </div>
            </td>
          </tr>
          <tr *ngIf="canViewRecords && mf.data.length === 0">
            <td colspan="100">
              {{'USER_ROLE.NO MATCHES' | translate:param}}
            </td>
          </tr>
          <tr *ngIf="!canViewRecords">
            <td class="text-danger" colspan="100">
              {{'USER_ROLE.PERMISSION_DENIED' | translate:param}}
            </td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="12">
              <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
            </td>
          </tr>
        </tfoot>
      </table>

    </div>
  </div>
</section>
