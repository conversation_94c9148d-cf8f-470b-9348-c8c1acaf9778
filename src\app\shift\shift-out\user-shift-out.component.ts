import { ExpensesService } from './../../shared/services/expenses.service';
import { Router } from '@angular/router';
import { AuthGuard } from './../../shared/guards/auth-guard.service';
import { Component, OnInit } from '@angular/core';
import { UserService } from 'app/shared/services/user.service';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
declare var Messenger: any;

@Component({
    selector: 'user-shift-out',
    templateUrl: 'user-shift-out.component.html',
    styleUrls: ['./user-shift.styles.scss']
})

export class UserShiftOutComponent implements OnInit {
    config: any;// New Change ****
    expenses: any[];
    userShiftDetails: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private router: Router,
        private ES: ExpensesService,
        private authGuard: AuthGuard,
        private uService: UserService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****   
    ) {
        this.userShiftDetails = this.authGuard.getUserShift();
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    ngOnInit() {
        if (this.userShiftDetails) {
            this.uService.getPettyAmountFromUser(this.userShiftDetails.id).subscribe((res) => {
                if (res.status) {
                    this.userShiftDetails = res.data;
                }
            });
            this.ES.getShiftExpenses()
                .subscribe((res) => {
                    if (res.status) {
                        this.expenses = res.data;
                    }
                });
        } else {
            this.router.navigateByUrl('admin/dashboard');
            Messenger().post({
                hideAfter: 5,
                message: "No shift found!",
                type: "error",
                showCloseButton: true
            });
        }
    }

    getBalancePettyCash() {
        let currentPettyAmount = this.userShiftDetails.current_petty_amount >= 0 ? this.userShiftDetails.current_petty_amount : 0;
        let prePettyAmount = this.userShiftDetails.pre_petty_amount >= 0 ? this.userShiftDetails.pre_petty_amount : 0;
        return currentPettyAmount + prePettyAmount;
    }

    shiftOut() {
        this.uService.shiftOut().subscribe((res) => {
            if (res.status === "success") {
                this.authGuard.removeUserShift();
                this.logout();
            }
        });
    }

    logout() {
        this.uService.userLogOut().subscribe(res => {
            if (res.status === 'success') {
                this.authGuard.removeUser();
                this.router.navigate(['']);
            }
        });
    }
    ngOnDestroy() {
        
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}