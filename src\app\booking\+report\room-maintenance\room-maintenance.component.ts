import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { BookingService } from 'app/shared/services/booking.service';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ***
import { Subject, Subscription } from 'rxjs';
declare var Messenger: any;


@Component({
    selector: 'room-maintenance',
    templateUrl: 'room-maintenance.component.html',
    styleUrls: ['room-maintenance.component.scss']
})

export class RoomMaintenanceComponent implements OnInit, OnDestroy {
    config: any;// New Change ****
    data: any[] = [];
    searchQuery: String;
    searchForm: FormGroup;
    canViewRecords: boolean;
    originalData: any[] = [];
    roomTypeList: any[] = [];
    getTypeList: any[] = [];
    customerTypeList: any[] = [];
    selectedFilterTypes: any = {
        guest_id: "000000",
        room_type_id: "000000",
        get_type: "occupied"
    };
    
    public datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        format: 'dd/mm/yyyy',
        todayHighlight: true,
        icon: 'fa fa-calendar',
    }
    public bookingTypeOptions: Select2.Options = {
        width: '100%',
    };

    public totalAdult = 0;
    public totalChild = 0;
    private sub: Subscription;
    private csvSub: Subscription;
    private getReportDataSub: Subscription;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private BS: BookingService,
        private fb: FormBuilder,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    public paxCount = (a: any) => {
        return (a.adult ? a.adult : 0) + (a.child ? a.child : 0);
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    ngOnInit() {
        this.initForm();
        this.canViewRecords = true;
        this.sub = this.BS.getBookingReportFilters()
            .subscribe(res => {
                if (res.status == "success") {

                    this.customerTypeList = res.data.customerTypeList.map((obj) => {
                        return { id: obj.id, text: obj.name.toUpperCase() };
                    });
                    this.roomTypeList = res.data.roomTypeList.map((obj) => {
                        return { id: obj.id, text: obj.name };
                    });
                    this.customerTypeList.unshift({ id: "000000", text: "All-togather" });
                    this.customerTypeList.sort(function (x, y) {
                        let a = x.text.toUpperCase(),
                            b = y.text.toUpperCase();
                        return a == b ? 0 : a > b ? 1 : -1;
                    });
                    // console.log("customer  : ",this.customerTypeList);

                    this.roomTypeList.unshift({ id: "000000", text: "All-togather" });
                    this.getTypeList = [
                        { id: "occupied", text: "All Occupied" },
                        { id: "today", text: "Today Check-in" },
                    ];
                }
            }, error => {
                this.canViewRecords = false;
            });
    }
    initForm() {
        this.searchForm = this.fb.group({
            // fromDate: [null],
            // toDate: [null],
            get_type: ['occupied'],
            guest_id: ['000000'],
            room_type_id: ['000000'],
        });
    }
    roomTypeChanged(event: any) {
        let roomType = <FormControl>this.searchForm.controls['room_type_id'];
        this.selectedFilterTypes.room_type_id = event?.id;
        roomType.patchValue(event?.id);
    }
    guestTypeChanged(event: any) {
        let guestType = <FormControl>this.searchForm.controls['guest_id'];
        this.selectedFilterTypes.guest_id = event?.id;
        guestType.patchValue(event?.id);
    }
    getTypeChanged(event: any) {
        let getType = <FormControl>this.searchForm.controls['get_type'];
        this.selectedFilterTypes.get_type = event?.id;
        getType.patchValue(event?.id);
    }
    getSearchParams() {
        if (this.searchForm.valid) {
            let searchParams = JSON.parse(JSON.stringify(this.searchForm.value));
            // searchParams.fromDate = moment(searchParams.fromDate).format('YYYY-MM-DD');
            // searchParams.toDate = moment(searchParams.toDate).format('YYYY-MM-DD');
            return searchParams;
        }
        else {
            for (let field in this.searchForm.controls) {
                this.searchForm.controls[field].markAsDirty();
                this.searchForm.controls[field].markAsTouched();
            }
        }
    }
    searchReports() {
        // console.log("FORM : ",this.searchForm.value);
        let searchParams = this.getSearchParams();
        this.getReportData(searchParams);
    }
    getReportData(searchParams) {
        this.getReportDataSub = this.BS.getRoomMaintenanceReport(searchParams)
            .subscribe((res) => {
                if (res.status === 'success') {
                    this.originalData = res.data;
                    this.initializeData();
                }
            });
    }
    search() {
        if (this.searchForm && this.data) {
            this.initializeData();
            if (this.data && this.searchQuery && this.searchQuery.trim() != '') {
                this.data = this.data.filter(data => {
                    let searchTarget = '';
                    Object.keys(data).forEach(key => {
                        searchTarget += data[key];
                    })
                    return (searchTarget.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
                });
            }
            else {
                this.initializeData();
            }
        }
    }
    clearSearch() {
        this.initializeData();
        this.searchQuery = undefined;
    }
    printRecords() {
        if (this.data && this.data.length > 0) {
            window.print();
        } else {
            this.showNotification('No data available!', 'error', true);
        }

    }
    showNotification(message: string, type: string, showCloseButton: boolean = true, hideAfter: number = 3) {
        Messenger().post({
          type: type,
          message: message,
          hideAfter: hideAfter,
          showCloseButton: showCloseButton,
        });
      }
    initializeData() {
        this.totalAdult = 0;
        this.totalChild = 0;
        this.data = this.originalData;
        this.data.map(res => {
            this.totalAdult = this.totalAdult + res.adult;
            this.totalChild = this.totalChild + res.child;
        })
    }
    ngOnDestroy(): void {
        if (this.getReportDataSub)
            this.getReportDataSub.unsubscribe();
        if (this.csvSub)
            this.csvSub.unsubscribe();
        
            this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}