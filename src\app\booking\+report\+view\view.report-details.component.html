<section class="widget" [ngClass]="{'enable-print': isPoliceInquiry}">
  <header>
    <div class="row">
      <div class="col-sm-8">
        <h4>
          <span class="capitalized">
            <i class="fa fa-clipboard"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.VIEW_BOOK' | translate:param}}</span>
        </h4>
      </div>
      <div class="col-sm-4 view-details-buttons">
        <button type="button" class="btn btn-sm btn-danger float-sm-right" (click)="gobacksimon()">
          <i class="fa fa-angle-left"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.BACK' | translate:param}}</button>
        <button type="button" *ngIf="isPoliceInquiry" class="btn btn-sm btn-danger float-sm-right"
          style="margin-right: 10px;" (click)="printDocument()">
          <i class="fa fa-angle-left"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.PRINT' | translate:param}}</button>
      </div>
    </div>
  </header>
  <hr class="large-hr" [ngClass]="{'enable-print': isPoliceInquiry}">
  <div class="clearfix"></div>
  <div class="widget-body" *ngIf="selectedData && data" [ngClass]="{'enable-print': isPoliceInquiry}">
    <!-- Summary Panels -->
    <div class="mt">
      <div class="row">
        <div class="col-md-6" *ngIf="!isPoliceInquiry">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4>{{'VIEW_BOOKI_DETAI.BOOK_DE' | translate:param}}</h4>
            </div>
            <div class="panel-body">
              <table class="table table-no-mar">
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.RES_ID' | translate:param}}:</td>
                  <td>
                    <strong>{{data[0].booking_id ? data[0].booking_id : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.STAY_TYPE' | translate:param}}:</td>
                  <td>
                    <strong>{{data[0].stay_type_id == "1" ? 'Full Day' : data[0].stay_type_id == "2" ? 'Half Day' : data[0].stay_type_id == "3" ? 'Shower' : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.BOOK_TYPE' | translate:param}}:</td>
                  <td>
                    <strong>{{data[0].booking_type == '0' ? 'Single Booking' : data[0].booking_type == '1' ? 'Group Booking' : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.BOOK_STAT' | translate:param}}:</td>
                  <td>
                    <strong [ngStyle]="{'color': 
                        data[0].current_status == 'checkin' ? '#64bd63' :
                        data[0].current_status == 'reserved' ? '#5d8fc2' :
                        data[0].current_status == 'checkout' ? '#d80000' : 'inherit'}" class="capitalize">{{data[0].current_status
                      ? data[0].current_status : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.BOOK_DATE' | translate:param}}:</td>
                  <td>
                    <strong>{{(data[0].booking_date ? (data[0].booking_date | date) : '-')}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.RATE' | translate:param}}:</td>
                  <td>
                    <strong>
                      <span *ngIf="selectedReport.total_amount">
                        +
                        <!-- <i class="fa fa-inr"></i> -->
                        &nbsp;{{selectedReport.total_amount}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr *ngIf="selectedReport.extra_pax_charges">
                  <td>{{'VIEW_BOOKI_DETAI.EX_AD_GU_CHA' | translate:param}}:</td>
                  <td>
                    <strong>
                      <span>
                        +
                        <!-- <i class="fa fa-inr"></i> -->
                        &nbsp;{{selectedReport.extra_pax_charges ? selectedReport.extra_pax_charges
                        : '-'}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr *ngIf="selectedReport.cardswipecharges">
                  <td>{{'VIEW_BOOKI_DETAI.CAR_SWIP_CHAR' | translate:param}}:</td>
                  <td>
                    <strong>
                      <span>
                        +
                        <!-- <i class="fa fa-inr"></i> -->
                        &nbsp;{{selectedReport.cardswipecharges}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr *ngIf="selectedReport.earlycheckincharge">
                  <td>{{'VIEW_BOOKI_DETAI.EA_CHECK_CHARG' | translate:param}}:</td>
                  <td>
                    <strong>
                      <span>
                        +
                        <!-- <i class="fa fa-inr"></i> -->
                        &nbsp;{{selectedReport.earlycheckincharge}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr *ngIf="selectedReport.fund_amount">
                  <td>{{'VIEW_BOOKI_DETAI.FUND_AMT' | translate:param}}:</td>
                  <td>
                    <strong>
                      <span>
                        +
                        <!-- <i class="fa fa-inr"></i> -->
                        &nbsp;{{selectedReport.fund_amount}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.DISCOUNT' | translate:param}}:</td>
                  <td>
                    <strong>
                      <span>
                        <!-- <i class="fa fa-inr"></i> -->
                        &nbsp;{{getTotalDiscount()}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.PAID' | translate:param}}:</td>
                  <td>
                    <strong>
                      <span>
                        -
                        <!-- <i class="fa fa-inr"></i> -->
                        &nbsp;{{getAmountPaid()}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.BALAN_AMT' | translate:param}}:</td>
                  <td>
                    <strong>
                      <span *ngIf="selectedReport.total_amount">
                        <!-- <i class="fa fa-inr"></i> -->
                        &nbsp;{{getBalanceAmount()}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.REF_AMT' | translate:param}} :</td>
                  <td>
                    <strong>
                      <span *ngIf="selectedReport.return_amount">
                        &nbsp;{{selectedReport.return_amount}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_IN' | translate:param}}:</td>
                  <td>
                    <strong class="text-success">{{(selectedReport.expected_check_in ? (selectedReport.expected_check_in
                      | date:'h:mm a') : '-' )}}&nbsp;Hrs.</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_OUT' | translate:param}}:</td>
                  <td>
                    <strong class="text-success">{{(selectedReport.expected_check_out ? (formatCheckoutDate(selectedReport.expected_check_out)
                      | date:'h:mm a') : '-')}}&nbsp;Hrs.</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.ACTU_CHECK_IN' | translate:param}}:</td>
                  <td>
                    <strong class="text-success">
                      {{selectedReport.check_in ? (selectedReport.check_in | date:'h:mm a, MMM d, y') : '-'}}
                      <span *ngIf="selectedReport.checkInBy !== null">by {{ data[0].checkInBy }}</span>
                    </strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.ACTU_CHECK_OUT' | translate:param}}:</td>
                  <td>
                    <strong class="text-danger">
                      {{selectedReport.check_out ? (selectedReport.check_out | date:'h:mm a, MMM d, y') : '-'}}
                      <span *ngIf="selectedReport.checkOutBy !== null">by {{ data[0].checkOutBy }}</span>
                    </strong>
                  </td>
                </tr>
                <tr>
                  <td>PAN Card:</td>
                  <td>
                    <strong class="text-danger">
                      {{data[0].guests.pancard_number ? data[0].guests.pancard_number : ''}}
                    </strong>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <div class="col-md-6" *ngIf="!isPoliceInquiry">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4>{{'VIEW_BOOKI_DETAI.BILL_GUE_DETAI' | translate:param}}</h4>
            </div>
            <div class="panel-body">
              <table class="table table-no-mar">
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.GUEST_NAME' | translate:param}}:</td>
                  <td style="text-transform:uppercase;">
                    <strong>{{selectedReport.name ? selectedReport.name : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.GUE_TYPE' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{selectedReport.guest_type ? selectedReport.guest_type : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.CONT' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{data[0].guests.contact ? data[0].guests.contact : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.EMAIL' | translate:param}}:</td>
                  <td>
                    <strong>{{data[0].guests.email ? data[0].guests.email : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.ADDRESS' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{data[0].guests.address ? data[0].guests.address : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.CITY' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{data[0].guests.city ? data[0].guests.city : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.ZIP' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{data[0].guests.zip ? data[0].guests.zip : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.ROOM_NO' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{selectedReport.room_no ? selectedReport.room_no : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.ROOM_TYP' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{selectedReport.room_type ? selectedReport.room_type : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.TOT_ADU' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{selectedReport.adult ? selectedReport.adult : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.TOT_CHIL' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{selectedReport.child ? selectedReport.child : '-'}}</strong>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <div class="col-xs-12 col-sm-8 col-md-6 col-lg-4" *ngIf="isPoliceInquiry">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4>{{'VIEW_BOOKI_DETAI.DETAILS' | translate:param}}</h4>
            </div>
            <div class="panel-body">
              <table class="table table-no-mar">
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.GUEST_NAME' | translate:param}}:</td>
                  <td style="text-transform:uppercase;">
                    <strong>{{selectedReport.name ? selectedReport.name : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.CONT' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{data[0].guests.contact ? data[0].guests.contact : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.ADDRESS' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{data[0].guests.address ? data[0].guests.address : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.CITY' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{data[0].guests.city ? data[0].guests.city : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.ZIP' | translate:param}}:</td>
                  <td class="capitalize">
                    <strong>{{data[0].guests.zip ? data[0].guests.zip : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.CHECK_IN' | translate:param}}:</td>
                  <td>
                    <strong class="text-success">{{selectedReport.check_in ? (selectedReport.check_in | date:'h:mm a, MMM
                      d, y') : '-'}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>{{'VIEW_BOOKI_DETAI.CHECK_OUT' | translate:param}}:</td>
                  <td>
                    <strong class="text-danger">{{selectedReport.check_out ? (selectedReport.check_out | date:'h:mm a, MMM
                      d, y') : '-'}}</strong>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="!isPoliceInquiry && selectedReport && selectedReport.note">
        <div class="col-md-6">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4>{{'VIEW_BOOKI_DETAI.NOTES' | translate:param}}</h4>
            </div>
            <div class="panel-body">
              <accordion [closeOthers]="true" class="mb-lg show" id="accordion">
                <accordion-group *ngFor="let note of selectedReport.note;">
                  <div accordion-heading>
                    {{getNoteTitle(note)}}
                    <i class="fa fa-angle-down pull-xs-right"></i>
                  </div>
                  <div>
                    {{note.message}}
                  </div>
                </accordion-group>
              </accordion>
            </div>
          </div>
        </div>
      </div>
    </div>
    <br *ngIf="!isPoliceInquiry">
    <!-- Booking Payments Table -->
    <div class="widget-body table-scroll" *ngIf="!isPoliceInquiry">
      <h5>{{'VIEW_BOOKI_DETAI.PAY_DETA' | translate:param}}</h5>
      <div class="mt">
        <table class="table with-labels table-condence no-m-b no-bottom-border" [mfData]="bookingPayments"
          #mf1="mfDataTable" [mfRowsOnPage]="25">
          <thead>
            <tr>
              <th>
                <mfDefaultSorter by="id">#</mfDefaultSorter>
              </th>
              <th>
                <mfDefaultSorter by="payment_mode">{{'VIEW_BOOKI_DETAI.PAY_MODE' | translate:param}}</mfDefaultSorter>
              </th>
              <th>
                <mfDefaultSorter by="payment_date">{{'VIEW_BOOKI_DETAI.DATE' | translate:param}}</mfDefaultSorter>
              </th>
              <!-- <th>
                <mfDefaultSorter by="card_charge">Card Charge</mfDefaultSorter>
              </th> -->
              <th>
                <mfDefaultSorter by="bank_name">{{'VIEW_BOOKI_DETAI.BANK_NAME' | translate:param}}</mfDefaultSorter>
              </th>
              <th>
                <mfDefaultSorter by="bank_cheque_no">{{'VIEW_BOOKI_DETAI.BANK_CHECK_NO' | translate:param}}
                </mfDefaultSorter>
              </th>
              <th>
                <mfDefaultSorter by="amount">{{'VIEW_BOOKI_DETAI.AMOUNT' | translate:param}}</mfDefaultSorter>
              </th>
          </thead>
          <tbody>
            <tr *ngFor="let ds of mf1.data; let i = index;">
              <td>{{ i + 1 }}</td>
              <td class="capitalize">
                {{ds.payment_mode ? paymentType[findIndex(ds.payment_mode, "id", paymentType)].text : '-'}}</td>
              <td class="capitalize">
                {{ds.payment_date ? (ds.payment_date | date) : '-'}}
              </td>
              <td class="capitalize">
                {{ds.bank_name ? ds.bank_name : '-'}}
              </td>
              <td class="capitalize">
                {{(ds.bank_cheque_no ? ds.bank_cheque_no : '-')}}
              </td>
              <td class="uppercase">
                <i class="fa fa-inr"></i>&nbsp;
                <span class="fw-semi-bold">{{ds.amount ? ds.amount : 0}}</span>
              </td>
            </tr>
            <tr>
              <td colspan="5" style="padding: 0.55rem 2rem;">
                <strong style="float: right;">
                  {{'VIEW_BOOKI_DETAI.TOT_PEN_AMT' | translate:param}}
                </strong>
              </td>
              <td colspan="1">
                <strong>
                  <i class="fa fa-inr"></i>
                  {{getBalanceAmount()}}
                </strong>
              </td>
            </tr>
            <tr *ngIf="mf1.data && mf1.data.length === 0">
              <td colspan="100">
                {{'VIEW_BOOKI_DETAI.NO MATCHES' | translate:param}}
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="100" style="border-top: none !important">
                <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
      <br>
    </div>
    <!-- Guest List Table -->
    <div class="widget-body table-scroll" [ngClass]="{'enable-print': isPoliceInquiry}">
      <h5>{{'VIEW_BOOKI_DETAI.GUE_DETA' | translate:param}}</h5>
      <div class="mt">
        <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="25">
          <thead>
            <tr>
              <th>
                <mfDefaultSorter by="id">#</mfDefaultSorter>
              </th>
              <th *ngIf="!isPoliceInquiry">
                <mfDefaultSorter by="title">{{'VIEW_BOOKI_DETAI.ROOM_NO' | translate:param}}</mfDefaultSorter>
              </th>
              <th>
                <mfDefaultSorter [by]="guestName">{{'VIEW_BOOKI_DETAI.GUEST_NAME' | translate:param}}</mfDefaultSorter>
              </th>
              <th>
                <mfDefaultSorter [by]="guestMaturityType">{{'VIEW_BOOKI_DETAI.MATU' | translate:param}}
                </mfDefaultSorter>
              </th>
              <th *ngIf="isPoliceInquiry">
                <mfDefaultSorter by="title">{{'VIEW_BOOKI_DETAI.CONT' | translate:param}}</mfDefaultSorter>
              </th>
              <th *ngIf="isPoliceInquiry">
                <mfDefaultSorter by="title">{{'VIEW_BOOKI_DETAI.ADDRESS' | translate:param}}</mfDefaultSorter>
              </th>
              <th *ngIf="isPoliceInquiry">
                <mfDefaultSorter by="title">{{'VIEW_BOOKI_DETAI.CITY' | translate:param}}</mfDefaultSorter>
              </th>
              <th *ngIf="isPoliceInquiry">
                <mfDefaultSorter by="title">{{'VIEW_BOOKI_DETAI.ZIP' | translate:param}}</mfDefaultSorter>
              </th>
              <th *ngIf="!isPoliceInquiry">
                <mfDefaultSorter by="start">{{'VIEW_BOOKI_DETAI.STAY_DURA' | translate:param}}</mfDefaultSorter>
              </th>
              <th *ngIf="!isPoliceInquiry">
                <mfDefaultSorter by="name">{{'VIEW_BOOKI_DETAI.ROOM_TYP' | translate:param}}</mfDefaultSorter>
              </th>
              <th *ngIf="!isPoliceInquiry">
                <mfDefaultSorter by="c_name">{{'VIEW_BOOKI_DETAI.GUE_TYPE' | translate:param}}</mfDefaultSorter>
              </th>
              <th *ngIf="!isPoliceInquiry">
                <mfDefaultSorter by="unique_booking_id">{{'VIEW_BOOKI_DETAI.RES_ID_D' | translate:param}}
                </mfDefaultSorter>
              </th>
              <th *ngIf="!isPoliceInquiry">
                <mfDefaultSorter [by]="referenceName">{{'VIEW_BOOKI_DETAI.REF_NAME' | translate:param}}
                </mfDefaultSorter>
              </th>
              <th>
                <mfDefaultSorter>{{'VIEW_BOOKI_DETAI.ADD_PROOF' | translate:param}}</mfDefaultSorter>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let ds of mf.data; let i = index;">
              <td>{{ i + 1 }}</td>
              <td class="uppercase" *ngIf="!isPoliceInquiry">
                <span class="fw-semi-bold">{{ds.title ? ds.title : '-'}}</span>
              </td>
              <td class="capitalize">{{ds.guests.name ? ds.guests.name : '-'}}</td>
              <td class="capitalize">{{ds.guests.guest_maturity_type ? ds.guests.guest_maturity_type : '-'}}</td>
              <td class="capitalize" *ngIf="isPoliceInquiry">{{ds.guests.contact ? ds.guests.contact : '-'}}</td>
              <td class="capitalize" *ngIf="isPoliceInquiry">{{ds.guests.address ? ds.guests.address : '-'}}</td>
              <td class="capitalize" *ngIf="isPoliceInquiry">{{ds.guests.city ? ds.guests.city : '-'}}</td>
              <td class="capitalize" *ngIf="isPoliceInquiry">{{ds.guests.zip ? ds.guests.zip : '-'}}</td>
              <td class="capitalize" *ngIf="!isPoliceInquiry">
                {{ds.start ? (ds.start | date:'d MMM') : '-'}} - {{ds.end ? (ds.end | date:'d MMM') : '-'}}
              </td>
              <td *ngIf="!isPoliceInquiry">{{ds.name ? ds.name : '-'}}</td>
              <td class="capitalize" *ngIf="!isPoliceInquiry">{{ds.c_name ? ds.c_name : '-'}}</td>
              <td class="capitalize" *ngIf="!isPoliceInquiry">{{(ds.unique_booking_id ? ds.unique_booking_id : '-')}}
              </td>
              <td *ngIf="!isPoliceInquiry">
                {{ds.reference_id ? referenceTypeList[findIndex(ds.reference_id,"id",referenceTypeList)].text : '-'}}
              </td>
              <td *ngIf="!isPoliceInquiry">
                {{ds.guests.proof_value ? ds.guests.proof_value : '-'}}
              </td>
              <td class="capitalize width-100 text-center">
                <button *ngIf="!isPoliceInquiry && ds.guests.mimetype"
                  (click)="canShow = false;viewDocumentProof(ds)" class="btn btn-sm btn-default"
                  tooltip="{{'VIEW_BOOKI_DETAI.VIEW_DOC_PROOF' | translate:param}}" placement="left"
                  style="width: 130px;">
                  <i class="fa fa-image"></i> &nbsp;{{'VIEW_BOOKI_DETAI.VIEW_DOC' | translate:param}}
                </button>
                <button *ngIf="!isPoliceInquiry && !ds.guests.mimetype" class="btn btn-sm btn-default"
                  tooltip="{{'VIEW_BOOKI_DETAI.NO_DOC_PROOF' | translate:param}}" placement="left"
                  style="width: 130px; background: #a9a9a9;">
                  &nbsp;{{'VIEW_BOOKI_DETAI.NO_DOC' | translate:param}}
                </button>
                {{(isPoliceInquiry && ds.guests.proof_value) ? ds.guests.proof_value : '-'}}
              </td>
            </tr>
            <tr *ngIf="mf.data && mf.data.length === 0">
              <td colspan="100">
                {{'VIEW_BOOKI_DETAI.NO MATCHES' | translate:param}}
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="100">
                <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
    <!-- Guest Photo ID -->
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h4>{{'VIEW_BOOKI_DETAI.PRI_GUE_PHO_ID' | translate:param}}</h4>
          </div>
          <ng-container *ngFor="let imageUrl of data[0].guest_Doc" style="text-align: center;">
            <img   class="image-modal-view px-1 pb-1" [src]="imageUrl" alt="" style="width: 100%;">
          </ng-container> 
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Document Viewer -->
<div bsModal #imageModal="bs-modal" id="imageModal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{backdrop: 'static',keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <ng-container *ngFor="let imageUrl of imageUrls">
        <img   class="image-modal-view px-1 pb-1" [src]="imageUrl" alt="">
      </ng-container>
      <div class="modal-footer">
        <div class="btn-group">
          <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="closeModal()">
            {{'VIEW_BOOKI_DETAI.CLOSE' | translate:param}}
          </button>
          <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="printDocument()">
            {{'VIEW_BOOKI_DETAI.PRINT' | translate:param}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>