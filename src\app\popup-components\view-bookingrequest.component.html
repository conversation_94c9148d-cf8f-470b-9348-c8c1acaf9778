<div class="modal-dialog modal-lg">
    <div class="modal-content">
      <section class="booking-info m-2 p-2">
        <header>
          <div class="row">
            <div class="col-sm-8">
              <h4>
                <span class="capitalized">
                  <i class="fa fa-clipboard"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.VIEW_BOOK' | translate:param}}</span>
              </h4>
            </div>
            <div class="col-sm-4 view-details-buttons">
              <button type="button" class="btn btn-sm btn-danger float-sm-right" (click)="gobacksimon()">
                <i class="fa fa-angle-left"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.BACK' | translate:param}}</button>
            </div>
          </div>
        </header>
        <hr class="large-hr" />
        <div class="clearfix"></div>
        <div class="widget-body" *ngIf="selectedBooking">
          <div class="mt">
            <div class="row">
              <!-- Booking Information Section -->
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h4>{{'VIEW_BOOKI_DETAI.BOOK_DE' | translate:param}}</h4>
                  </div>
                  <div class="panel-body">
                    <table class="table table-no-mar">
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.RES_ID' | translate:param}}:</td>
                        <td><strong>{{selectedBooking.id ? selectedBooking.id : '-'}}</strong></td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.STAY_TYPE' | translate:param}}:</td>
                        <td>
                          <strong>{{selectedBooking?.stayTypeDetails?.name}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.BOOK_STAT' | translate:param}}:</td>
                        <td>
                          <strong class="capitalize">{{selectedBooking.current_status ? selectedBooking.current_status : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.BOOK_DATE' | translate:param}}:</td>
                        <td>
                          <strong>{{(selectedBooking.reservation_date ? (selectedBooking.reservation_date | date) :
                            '-')}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.RATE' | translate:param}}:</td>
                        <td>
                          <strong>
                            <span
                              *ngIf="selectedBooking.roomCategoryDetails.charges">+&nbsp;{{selectedBooking.roomCategoryDetails.charges}}
                            </span>
                          </strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_IN' | translate:param}}:</td>
                        <td>
                          <strong class="text-success">{{(selectedBooking.check_in ? (selectedBooking.check_in | date:'dd-MM-yyyy') : '-' )}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_OUT' | translate:param}}:</td>
                        <td>
                          <strong class="text-success">{{(selectedBooking.check_out ? (selectedBooking.check_out | date:'dd-MM-yyyy') : '-')}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Aadhar Card:</td>
                        <td>
                          <strong class="text-danger">
                            {{selectedBooking.aadharcard_number ? selectedBooking.aadharcard_number : ''}}
                          </strong>
                        </td>
                      </tr>
                      <tr>
                        <td>PAN Card:</td>
                        <td>
                          <strong class="text-danger">
                            {{selectedBooking.pancard_number ? selectedBooking.pancard_number : 'not added'}}
                          </strong>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              <div class="col-md-6" >
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>{{'VIEW_BOOKI_DETAI.BILL_GUE_DETAI' | translate:param}}</h4>
                    </div>
                    <div class="panel-body">
                        <table class="table table-no-mar">
                            <tr>
                                <td>{{'VIEW_BOOKI_DETAI.GUEST_NAME' | translate:param}}:</td>
                                <td style="text-transform:uppercase;">
                                    <strong>{{selectedBooking.name ? selectedBooking.name : '-'}}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>{{'VIEW_BOOKI_DETAI.GUE_TYPE' | translate:param}}:</td>
                                <td class="capitalize">
                                    <strong>{{selectedBooking.customerDetails.name ? selectedBooking.customerDetails.name : '-'}}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>{{'VIEW_BOOKI_DETAI.CONT' | translate:param}}:</td>
                                <td class="capitalize">
                                    <strong>{{selectedBooking.contact ? selectedBooking.contact : '-'}}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>{{'VIEW_BOOKI_DETAI.EMAIL' | translate:param}}:</td>
                                <td>
                                    <strong>{{selectedBooking.email ? selectedBooking.email : '-'}}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>{{'VIEW_BOOKI_DETAI.ROOM_NO' | translate:param}}:</td>
                                <td class="capitalize">
                                    <strong>{{selectedBooking.roomDetails.title ? selectedBooking.roomDetails.title : '-'}}</strong>
                                </td>
                            </tr>
                            <tr>
                              <td>{{'referance user' | translate:param}}:</td>
                              <td class="capitalize">
                                  <strong>{{selectedBooking.reference_user_name ? selectedBooking.reference_user_name : '-'}}</strong>
                              </td>
                          </tr>
                            <tr>
                                <td>{{'VIEW_BOOKI_DETAI.ROOM_TYP' | translate:param}}:</td>
                                <td class="capitalize">
                                    <strong>{{selectedBooking.roomCategoryDetails.name ? selectedBooking.roomCategoryDetails.name : '-'}}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>{{'VIEW_BOOKI_DETAI.TOT_ADU' | translate:param}}:</td>
                                <td class="capitalize">
                                    <strong>{{selectedBooking.adult ? selectedBooking.adult : '-'}}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td>{{'VIEW_BOOKI_DETAI.TOT_CHIL' | translate:param}}:</td>
                                <td class="capitalize">
                                    <strong>{{selectedBooking.child ? selectedBooking.child : '-'}}</strong>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <button class="btn btn-primary">View Pancard</button>
          <button class="btn btn-primary">View aadhar card</button>
            </div>
            <div class="col-md-6 text-center">
              <button class="btn btn-success">Approve</button>
              <button class="btn btn-success" style="font-size: 10px;">
                <i class="fa fa-circle-o-notch fa-pulse fa-2x" ></i>
              </button>
              <button class="btn btn-danger" (click)="cancelRequest()">Cancel</button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>