import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormControl, FormGroup, FormBuilder, FormArray, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

import { Select2OptionData } from 'ng2-select2';
import { FileUploader, FileUploaderOptions } from 'ng2-file-upload';

import { _secret<PERSON>ey, _secretKey_auth } from './../../../shared/globals/config';
import { RoomCategoryService } from './../../../shared/services/roomCategory.service';
import { AuthGuard } from './../../../shared/guards/auth-guard.service';
import { apiUrl } from './../../../api-env';
import { TranslateService } from '@ngx-translate/core';

import * as CryptoJS from 'crypto-js';
declare var jQuery: any;
declare var Messenger: any;
const URL = apiUrl + 'roomcategory/add/image';

@Component({
    selector: 'edit-room-category',
    templateUrl: '../roomCategory.actions.component.html',
    styleUrls: ['./edit.roomCategory.component.scss']
})

export class EditRoomCategoryComponent implements OnInit {
    public uploader: FileUploader = new FileUploader({ url: URL });
    public uploadedImages: any[] = [];
    select2Options: any = {
        width: '100%'
    };
    pageName: string = "Edit";
    // service variables
    private sub: any;
    private getRoomCategory: any;
    private update: any;
    private deleteImages: any;
    //input / output
    @Input() id;
    @Input() gethiddenEditRC;
    @Output() sendhiddenEditRC = new EventEmitter();
    public eminityChargevalue: number;
    public roomCategory: FormGroup;
    public amenities: any[];
    public addedAmenities: any[];
    public aminityValue: any;
    public customerTypeData: any[];
    public dharmashalaList: any[];
    public uploadedImageURLs: any[];
    public SelectedRoomCat: any;
    public selectedDharamshala: any;
    public commonRoomCategory: any;
    constructor(
        private RCS: RoomCategoryService,
        private _fb: FormBuilder,
        private authGuard: AuthGuard,
        public translate: TranslateService,
    ) {
        // get all dropdown lists and othre usefull data before form init.
        this.sub = this.RCS.getInitForAdd()
            .subscribe((res) => {
                if (res.status = "success") {
                    this.amenities = res.data.amenities;
                    // set dharamshal dropdown if user is super admin
                    this.dharmashalaList = res.data.dharamshalas;
                    // if (this.dharmashalaList.length < 2) {
                    //     this.roomCategory.controls['dharamshala_id'].patchValue(this.dharmashalaList[0].id);
                    // }
                }
            })
        this.buildForm();
        translate.get('ROOM.ROOM_CAT.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageName = res;
            //=> 'hello world'
        });
    }
    /**
     * setting tariff (customer type) values to form
     * pushing form group as per recieved array
     */
    setCustomerType = () => {
        let control = <FormArray>this.roomCategory.controls['tariff'];
        if (this.customerTypeData) {
            this.customerTypeData.forEach(element => {
                control.push(this._fb.group({
                    customer_id: [(element.customer_id)],
                    discount_type: [element.discount_type,[Validators.required]],
                    customer_name: [(element.customer_name),[Validators.required]],
                    percentage: [((element.discount_type == "percentage") ? element.discount_value : ''),[ CustomValidators.number]],
                    amount: [((element.discount_type == "amount") ? element.discount_value : '0'),[ CustomValidators.number]],
                    extra_bed_charge: [element.extra_bed_charge,[Validators.required, CustomValidators.number]],
                    extra_adult_charge: [element.extra_adult_charge,[Validators.required, CustomValidators.number]],
                    extra_child_charge: [element.extra_child_charge,[Validators.required, CustomValidators.number]]
                }))
            });
        }
    }
    /**
     * build form for add room category
     * 
     */
    buildForm() {
        this.roomCategory = this._fb.group({
            dharamshala_id: [''],
            name: ['', [Validators.required]],
            total_room: ['0', [Validators.required, CustomValidators.digits]],
            charges: ['0', [Validators.required, CustomValidators.number]],
            default_online_quota: ['0', [Validators.required, CustomValidators.digits]],
            std_occupancy: ['0', [Validators.required, CustomValidators.digits]],
            // std_child_occupancy: ['', [Validators.required, CustomValidators.digits]],
            extra_adult_charges: ['0', [Validators.required, CustomValidators.digits]],
            extra_child_charges: ['0', [Validators.required, CustomValidators.digits]],
            max_occupancy: ['0', [Validators.required, CustomValidators.digits]],
            description: [''],
            amenities: this._fb.array([]),
            uploadedImages: this._fb.array([]),
            tariff: this._fb.array([]),
            release_quota_before: ['', [Validators.required, CustomValidators.digits]],
            status: [['', [Validators.required]]],
            is_common_room_category: ['']
        });
    }
    setAmenity = () => {
        this.addedAmenities.forEach(element => {
            this.addAminitControl(element.amenity_id, element['amenity.name'], element.charge);
        })
    }
    setUploadedImages = () => {
        this.uploadedImageURLs.forEach(element => {
            this.addUploadedImages(element);
        })
    }
    ngOnInit() {
        this.getRoomCategory = this.RCS.getOneRoomCat(this.id)
            .subscribe((res) => {
                if (res.status == "success") {
                    this.customerTypeData = res.data.tariff;
                    this.addedAmenities = res.data.amenities;
                    this.uploadedImageURLs = res.data.files;
                    this.SelectedRoomCat = res.data.roomcategory;
                    this.selectedDharamshala = res.data.roomcategory.dharamshala_id;
                    this.roomCategory.patchValue(res.data.roomcategory);
                    console.log("HEYYYYYYYYY : ",res.data.roomcategory.is_common_room_category);
                    this.commonRoomCategory = res.data.roomcategory.is_common_room_category;
                    this.setCustomerType();
                    this.setAmenity();
                    this.setUploadedImages();
                }
            })
        this.uploader.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
            //things to do on completion
            if (response) {
                let res = JSON.parse(response);
                this.addUploadedImages(res.data);
            }
        };
        this.uploader.onBeforeUploadItem = (item: any) => {
            // image uploaded - add token of auth
            let token = this.authGuard.ud.session_id;
            let timestamp = (+ new Date()).toString();
            let generatedToken = CryptoJS.AES.encrypt(token,
                _secretKey_auth);
            this.uploader.authToken = generatedToken;
        }
    }
    /**
     * this will add selected eminity and charges to aminity list
     * @param {any} eminityCharge : aminity charge (input element from local variable)
     */
    addAminity(eminityCharge) {
        this.addAminitControl(this.aminityValue, this.amenities[this.findIndex('id', this.aminityValue)].name, eminityCharge.value);
    }
    /**
     * this will remove aminity from aminity list at given index
     * @param {any} index : number
     */
    removeAminity(index) {
        let control = <FormArray>this.roomCategory.controls['amenities'];
        control.removeAt(index)
    }
    /**
     * Aminity control to add form control to aminity list (When form submitting)
     * @param {any} id : number - id of aminity (hidden)
     * @param {any} name : string - name of the aminity
     * @param {any} charge : number - charge of aminity
     */
    addAminitControl(id, name, charge) {
        let letmeaddyou: boolean = true;
        let control = <FormArray>this.roomCategory.controls['amenities'];
        let group = this._fb.group({
            amenity_id: [+ id],
            title: [name],
            charge: [charge]
        }) // create form group
        control.controls.forEach((element: FormGroup) => {
            if (element.controls['amenity_id'].value == id) {
                letmeaddyou = false;
            }
        });
        // if there is not element with the same name.
        if (letmeaddyou) {
            control.push(group);
        }
    }
    /**
     * when image has been uploaded but not saved, images will be list out to form control
     * @param {any} res : response - response from image upload api
     */
    addUploadedImages(res) {
        let control = <FormArray>this.roomCategory.controls['uploadedImages'];
        control.push(
            this._fb.group({
                mimetype: [res.mimetype],
                originalName: [res.originalName],
                size: [res.size],
                uploaded: [res.uploaded],
                is_deleted: [res.is_deleted]
            })
        );
    }
    /**
     * remove images that are already uploaded but not saved yet
     * 
     * @param {any} data : response - api response that received by api after marked as deleted 
     * @param {any} index : file index
     */
    removeUploadedImages(data, index) {
        let control = <FormArray>this.roomCategory.controls['uploadedImages'];
        let newControl = this._fb.group({
            mimetype: [data.mimetype],
            originalName: [data.originalName],
            size: [data.size],
            uploaded: [data.uploaded],
            is_deleted: [data.is_deleted]
        })
        control.setControl(index, newControl) // replace control is_deleted to true
        this.uploadedImageURLs[index].is_deleted = data.is_deleted; // remove image from view
    }
    // aminity dropdown initialization
    getSelect2DefaultList(): Select2OptionData[] {
        return jQuery.map(this.amenities, function (obj) {
            return { id: obj.id, text: obj.name };
        })
    }
    // dharamshala dropdown initialization if there is any
    getDharamshalaList(): Select2OptionData[] {
        return jQuery.map(this.dharmashalaList, function (obj) {
            return { id: obj.id, text: obj.name };
        })
    }
    // dharmashala list dropdown change event
    dharamshalaListchange(event) {
        this.roomCategory.controls['dharamshala_id'].patchValue(event.value);
    }
    // aminity dropdown list change event
    select2Changed(event) {
        this.aminityValue = event.value;
        this.eminityChargevalue = this.amenities[this.findIndex('id', event.value)].charge;
    }
    findIndex(params, searchTerm) {
        for (var i = 0, len = this.amenities.length; i < len; i++) {
            if (this.amenities[i][params] == searchTerm) return i;
        }
        return -1;
    }
    // toggle between parent and child
    toggleChild(data) {
        let result;
        this.gethiddenEditRC = !this.gethiddenEditRC;

        if (data) {
            // send data for to add to list
            result = { gethiddenEditRC: this.gethiddenEditRC, data: data }
        } else {
            result = { gethiddenEditRC: this.gethiddenEditRC }
        }
        this.sendhiddenEditRC.emit(result);
    }

    /**
     * function to delete a image
     * 
     * @param {any} img : image to delete
     * @param {any} index : index of image
     */
    deleteImage(img, index) {
        if (confirm('Are You Sure ?')) {
            this.deleteImages = this.RCS.deleteImage(img)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.removeUploadedImages(res.data, index);

                    } else {
                        // console.log(res)
                    }
                }, (err) => {
                    // console.log(err);
                })
        }
    }

    /**
     * this will change form control value of discount_type but emitted value of directive used 
     * when enable/disable input keypress
     * @param {any} event : value emmited from directive
     */
    handleDisType(event) {
        let arr = this.roomCategory.get('tariff') as FormArray;
        arr.controls[event.id].get('discount_type').patchValue(event.status);
        arr.controls[event.id].get(event.input).patchValue('');
    }
    setRoomCategoryCommon() {
        let category = <FormControl>this.roomCategory.controls['is_common_room_category'];
        let value = category.value;
        category.patchValue(!value);
        this.commonRoomCategory = !value;
        if(!value) {
            Messenger().post({
                message: "Common room category selected!",
                status: "success",
                showCloseButton: true
            })
        }
    }
    // update
    saveRoomCategory() {
        if (this.roomCategory.valid) {
            this.update = this.RCS.updateRoomCategory(this.SelectedRoomCat.id, this.roomCategory.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.roomCategory.reset();
                        Messenger().post({  hideAfter: 5,
                            message: res.message,
                            type: res.status,
                            showCloseButton: true
                        });
                        this.toggleChild(res.data);// send added result
                    } else {
                        // console.log(res)
                    }
                })
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
        if (this.deleteImages) {
            this.deleteImages.unsubscribe();
        }
        if (this.update) {
            this.update.unsubscribe();
        }
    }
}