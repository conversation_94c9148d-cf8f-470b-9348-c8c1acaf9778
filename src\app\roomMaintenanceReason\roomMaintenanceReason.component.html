
<add-room-maintenance *ngIf="!hiddenAR"
  [getHiddenAR]="hiddenAR" (sendHiddenAR)="handleHiddenAR($event)"
></add-room-maintenance>

<edit-room-maintenance *ngIf="!hiddenER"
  [getHiddenER]="hiddenER" [selectedRoom]="selectedRoom"
  (sendHiddenER)="handleHiddenER($event)"
></edit-room-maintenance>


<section *ngIf="hiddenAR && hiddenER" class="widget">
  <header>
    <h4><span class="" style="color: red;"><i class="fa fa-sitemap"></i>&nbsp;&nbsp;{{'ROOM_MAINTENANCE_REASON.ROOM_MAIN'| translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right text-right col-sm-6">

    <button
      *ngIf="auth.roleAccessPermission('room','add')"
      (click)="showAR()"
      class="display-inline-block btn btn-sm btn-inverse"
       tooltip="{{'ROOM_MAINTENANCE_REASON.ADD_NEW_ROOM'| translate:param}}" placement="top">
      <i class="fa fa-plus"></i>&nbsp;&nbsp;{{'ROOM_MAINTENANCE_REASON.ADD'| translate:param}}
    </button>
    <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'ROOM_MAINTENANCE_REASON.SEARCH'| translate:param}}">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body table-scroll">
    <div class="mt">
         <table class="table table-condence no-m-b small-footprint" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
        <tr>
          <th>
            <mfDefaultSorter by="id">#</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="title">{{'ROOM_MAINTENANCE_REASON.TITLE'| translate:param}}</mfDefaultSorter>
          </th>
          <th class="">
            <mfDefaultSorter by="description">{{'ROOM_MAINTENANCE_REASON.DES'| translate:param}}</mfDefaultSorter>
          </th>
          <th class="">
            <mfDefaultSorter by="status">{{'ROOM_MAINTENANCE_REASON.STATUS'| translate:param}}</mfDefaultSorter>
          </th>
          <th *ngIf="auth.roleAccessPermission('room','edit')" class="no-sort">
            <mfDefaultSorter by="status">{{'ROOM.ROOM_MANAGE.ACTION'| translate:param}}</mfDefaultSorter>
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let room of mf.data">
          <td>{{findIndex(room.id,"id")}}</td>
          <td><span class="fw-semi-bold uppercase">{{room.title}}</span></td>
          <td>{{room.description}}</td>
          <td class="">
            <span class="text-success" *ngIf="room.status">{{'ROOM.ROOM_MANAGE.ACTIVE'| translate:param}}</span>
            <span class="text-danger" *ngIf="!room.status">{{'ROOM.ROOM_MANAGE.INACTIVE'| translate:param}}</span>
          </td>
          <td *ngIf="auth.roleAccessPermission('room','edit')" class="width-100 text-center">
            <button class="btn btn-xs btn-default" (click)="showER(room)" tooltip="{{'ROOM.ROOM_MANAGE.EDIT_NEW_CAT'| translate:param}}" placement="top"><i class="fa fa-pencil"></i>&nbsp;{{'ROOM.ROOM_MANAGE.EDIT'| translate:param}}</button>
          </td>
        </tr>
        <tr *ngIf="canViewRecords && mf.data.length === 0">
          <td colspan="100">
              {{'ROOM.ROOM_MANAGE.NO MATCHES'| translate:param}}
          </td>
        </tr>
        <tr *ngIf="!canViewRecords">
          <td class="text-danger" colspan="100">
              {{'ROOM.ROOM_MANAGE.PERMISSION_DENIED'| translate:param}}
          </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="12">
            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
          </td>
        </tr>
        </tfoot>
      </table>


</div>
  </div>
</section>