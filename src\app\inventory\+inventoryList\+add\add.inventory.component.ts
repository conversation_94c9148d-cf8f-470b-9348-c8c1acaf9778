import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { InventoryService } from './../../../shared/services/inventory.service';

import { inventoryTypeData } from './../../data';
declare var jQuery: any;
declare var Messenger: any;

@Component({
    selector: 'add-inventory',
    templateUrl: '../inventoryList.actions.html',
    providers: [InventoryService]
})

export class AddInventoryComponent implements OnInit {
    pageType: string = "Add";
    loadingStatus: number = 0;
    inventoryType: any = '';
    inventoryAdd: FormGroup;

    inventoryTypeOptions: any = {
        width: '100%',
        minimumResultsForSearch: 7
    };
    status: any[] = [
        'Active', 'Inactive'
    ];
    vandorList: any[] = [];

    isAvailable: any;

    constructor(
        private _fb: FormBuilder, 
        private IS: InventoryService,
        private router : Router) {

        this.buildForm();
    }
    buildForm(){
        this.inventoryAdd = this._fb.group({
            name: ['', Validators.required],
            type: [null, Validators.required],
            status: [null, Validators.required],
            vandor: this._fb.array([])
        })
    }
    ngOnInit() {
        this.inventoryAdd.patchValue({ type: inventoryTypeData[0].text });
    }

    saveInventory() {
        this.loadingStatus = 1;
        if (this.inventoryAdd.valid) {
            this.IS.addInventory(this.inventoryAdd.value).subscribe(res => {
                if (res.status === 'success'){
                    // console.log(res.status);
                    this.loadingStatus = 0;
                    
                    this.buildForm();
                }else{
                    // when data not submitted
                   
                    // console.log(res.status);
                    this.loadingStatus = 2;
                }
            },(err) => {
                // on error
                  this.loadingStatus = 2;
            })
        }else{
            // console.log("Not Valid");
            this.loadingStatus = 0;
        }
    }

    getInventoryType() {
        return inventoryTypeData
    }
    getInventoryTypeChanged(event) {
      this.inventoryAdd.patchValue({ type: event.value });
    }
    initVandor(val) {
        // initialize our address
        return this._fb.group({
            title: [val, Validators.required]
        });
    }
    // add vandor event
    addVandor(van) {
        const control = <FormArray>this.inventoryAdd.controls['vandor'];
        if (van.value && /\S/.test(van.value)) {
            control.push(this.initVandor(van.value));
            van.value = "";
            van.focus();
        }
    }
    // remove vandoe event
    removeVandor(index) {
        const control = <FormArray>this.inventoryAdd.controls['vandor'];
        control.removeAt(index)
    }
}

