import { EditStayTypeComponent } from './stay-type-management/+edit/edit.stay-type.component';
import { EditAgentComponent } from './+agents/+edit/edit.agent.component';
import { AddAgentComponent } from './+agents/+add/add.agent.component';
import { AgentComponent } from './+agents/agents.component';
import { EditFundsComponent } from './+funds/+edit/edit.funds.component';
import { AddFundsComponent } from './+funds/+add/add.funds.component';
import { FundsComponent } from './+funds/funds.component';
import { AuthGuard } from './../shared/guards/auth-guard.service';
import { DharamshalaService } from './../shared/services/dharamshala.service';
import { GoogleMapService } from './../shared/services/google-map.service';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { DharamshalaListComponent } from './+dharamshalaList/dharamshala.component';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { AddDharamshalaComponent } from './+dharamshalaList/+add/add.dharamshalaList.component';
import { EditDharamshalaComponent } from './+dharamshalaList/+edit/edit.dharamshalaList.component';
import { ViewDharamshalaComponent } from './+dharamshalaList/+view/view.dharamshalaList.component';

import { SharedModule } from './../shared/shared.module';
import { DataTableModule } from 'angular2-datatable';
import { NgSelectModule } from '@ng-select/ng-select';
import { AgmCoreModule } from '@agm/core';
import { FileUploadModule } from 'ng2-file-upload';
import { NKDatetimeModule } from 'ng2-datetime/ng2-datetime';
import { TimepickerModule } from 'ngx-bootstrap/timepicker';
import { 
        // AlertModule, 
        TooltipModule } from 'ngx-bootstrap/tooltip';
import { IconPickerModule } from 'ngx-icon-picker';

import 'jasny-bootstrap/docs/assets/js/vendor/holder.js';
import 'jasny-bootstrap/js/fileinput.js';
import 'jasny-bootstrap/js/inputmask.js'; 
import 'ng2-datetime/src/vendor/bootstrap-timepicker/bootstrap-timepicker.min.js';
import 'ng2-datetime/src/vendor/bootstrap-datepicker/bootstrap-datepicker.min.js';
import { AgentLocationComponent } from './+agents-location/agent-location.component';
import { AddAgentLocationComponent } from './+agents-location/+add/add.agent-location.component';
import { EditAgentLocationComponent } from './+agents-location/+edit/edit.agent-location.component';
import { StayTypeComponent } from './stay-type-management/stay-type.component';
import { AddStayTypeComponent } from './stay-type-management/+add/add.stay-type.component';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { SpliteComponent } from './splite/splite.component';
import { SpliteEditComponent } from './splite/edit/edit.component';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

// all routes related to this Role module.
export const routes = [
  { path: '', component: DharamshalaListComponent, pathMatch: 'full' },
  { path: 'add', component: AddDharamshalaComponent, canActivateChild: [AuthGuard] },
  { path: 'edit/:id', component: EditDharamshalaComponent, canActivateChild: [AuthGuard]},
  { path: 'view/:id', component: ViewDharamshalaComponent, canActivateChild: [AuthGuard]},
  { path: 'funds', component: FundsComponent, canActivateChild: [AuthGuard]},
  { path: 'split', component: SpliteComponent, canActivateChild: [AuthGuard]},
  { path: 'split/:id', component: SpliteEditComponent, canActivateChild: [AuthGuard]},
  { path: 'agents', component: AgentComponent, canActivateChild: [AuthGuard]},
  { path: 'agent/location', component: AgentLocationComponent,canActivateChild: [AuthGuard]},
  { path: 'stay-type', component: StayTypeComponent,canActivateChild: [AuthGuard]}
];

@NgModule({
    imports: [
        FormsModule,
        // AlertModule,
        CommonModule,
        TooltipModule,
        NgSelectModule,
        DataTableModule,
        FileUploadModule,
        NKDatetimeModule,
        ReactiveFormsModule,
        IconPickerModule,
        TimepickerModule.forRoot(),
        RouterModule.forChild(routes),
        AgmCoreModule.forRoot({
            apiKey: 'AIzaSyDe_oVpi9eRSN99G4o6TwVjJbFBNr58NxE',
            libraries: ["places"],
            language: 'en'
        }),
        TranslateModule.forRoot({
           loader: { 
              provide: TranslateLoader, 
              useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
            deps: [HttpClient] 
          }}),
        SharedModule
    ],
    exports: [],
    declarations: [
        FundsComponent,
        AgentComponent,
        StayTypeComponent,
        AddFundsComponent,
        AddAgentComponent,
        EditFundsComponent,
        EditAgentComponent,
        AddStayTypeComponent,
        EditStayTypeComponent,
        AgentLocationComponent,
        AddDharamshalaComponent,
        DharamshalaListComponent,
        ViewDharamshalaComponent,
        EditDharamshalaComponent,
        AddAgentLocationComponent,
        EditAgentLocationComponent,
        SpliteComponent,
        SpliteEditComponent
    ],
    providers: [GoogleMapService,DharamshalaService, AuthGuard ],
    // schemas:[CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
})
export class DharamshalaModule { }
