import { CustomValidators } from 'ng2-validation';
import { Router } from '@angular/router';
import { UserService } from './../../shared/services/user.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

@Component({
    selector: 'profile-view',
    templateUrl: './profile.component.html'
})
export class ProfileComponent implements OnInit {
    // form object
    config: any;// New Change ****
    profile: FormGroup;
    // service variable
    private sub: any;
    private getUser: any;


    public userDetails: any;
    public countryList: any[];
    emailPattern: any = "[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?";

    dateMask = {
        mask: [/\d/, /\d/,
            '-', /\d/, /\d/,
            '-', /[1-9]/, /\d/, /\d/, /\d/]
    };
    // redio button settings
    gender: string[] = ['male', 'female'];
    public countryCustomOptions: Select2.Options = {
        width: '100%',
    }
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private _fb: FormBuilder,
        private US: UserService,
        private route: Router,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.buildForm();
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
        this.getUser = this.US.getUserDetails()
            .subscribe((res) => {
                this.userDetails = res.data.user;
                this.countryList = res.data.countryList;
                // console.log(res.data);
                this.profile.patchValue(this.userDetails);
            })
    }

    buildForm() {
        this.profile = this._fb.group({
            first_name: ['', Validators.required],
            last_name: ['', Validators.required],
            email: ['',[ Validators.required, Validators.pattern(this.emailPattern) ]],
            dob: ['', [Validators.required ]],
            address: [''],
            city: [''],
            country: ['', Validators.required],
            mobile_no: ['', [Validators.required , CustomValidators.digits]],
            zip: ['', [CustomValidators.digits]],
            gender: ['', Validators.required]
        })

    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    ngOnInit() { }

    countryChanged(event: any) {
        this.profile.controls['country'].patchValue(event.id);
    }

    saveProfileData() {
        if (this.profile.valid) {
            this.sub = this.US.saveUserDetails(this.profile.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.route.navigate(['admin/dashboard']);
                    }
                })
        }
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if (this.sub) {
            this.sub.unsubscribe();
        }
        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}