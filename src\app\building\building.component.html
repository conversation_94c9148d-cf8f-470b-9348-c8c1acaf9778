<add-building [gethiddenAddBuilding]="hiddenAddBuilding" [dharamshalaList]="dharamshalas" *ngIf="!hiddenAddBuilding"
    (sendBuildingEvent)="handleAddBuildingEvent($event)"
></add-building>

<edit-building  [gethiddenEditBuilding]="hiddenEditBuilding" [dharamshalaList]="dharamshalas" *ngIf="!hiddenEditBuilding"
    [building]="selectedBuilding" (sendBuildingEvent)="handleEditBuildingEvent($event)"
></edit-building>
<section  class="widget" *ngIf="hiddenAddBuilding && hiddenEditBuilding">
  <header>
    <h4><span class="" style="color: red;"><i class="fa fa-building"></i>&nbsp;&nbsp;{{'BUILDING.BUILDING_MANAGEMENT' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right text-right col-sm-6">

    <button
      *ngIf="auth.roleAccessPermission('building','add')"
      (click)="showAddBuilding()" 
      class="display-inline-block btn btn-sm btn-inverse" 
      tooltip="{{'BUILDING.ADD_NEW_BUILD' | translate:param}}" 
      placement="top">
      <i class="fa fa-plus"></i>&nbsp;&nbsp;{{'BUILDING.ADD' | translate:param}}
    </button>
    <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'BUILDING.SEARCH' | translate:param}}">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body table-scroll">
    <div class="mt">

         <table class="table table-condence no-m-b" [mfData]="buildings" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
        <tr>
          <th>
            <mfDefaultSorter by="id">#</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="name">{{'BUILDING.NAME' | translate:param}}</mfDefaultSorter>
          </th>
           <th>
            <mfDefaultSorter by="charge">{{'BUILDING.NO_OF_FLOOR' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="no-sort text-center">
            <mfDefaultSorter by="status">{{'BUILDING.STATUS' | translate:param}}</mfDefaultSorter>
          </th>
          <th *ngIf="auth.roleAccessPermission('building','edit')" class="no-sort text-center">
            <mfDefaultSorter by="status">{{'BUILDING.ACTION' | translate:param}}</mfDefaultSorter>
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let ds of mf.data; let i =index">
          <td>{{findIndex(ds.id,"id")}}</td>
          <td><span class="uppercase fw-semi-bold">{{ds.name}}</span></td>
         <td><span class="">{{ds.floors.length}}</span></td>
          <td class="text-center">
            <span class="text-success" *ngIf="ds.status">{{'BUILDING.ACTIVE' | translate:param}}</span>
            <span class="text-danger" *ngIf="!ds.status">{{'BUILDING.INACTIVE' | translate:param}}</span>
          </td>
          <td *ngIf="auth.roleAccessPermission('building','edit')" class="width-100 text-center">
            <button (click)="showEditbuilding(ds)" class="btn btn-xs btn-default" tooltip="{{'BUILDING.EDIT_BUILD_DETAILS' | translate:param}}" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{'BUILDING.EDIT' | translate:param}}</button>
          </td>
        </tr>
        <tr *ngIf="canViewRecords && mf.data.length === 0">
          <td colspan="100">
              {{'BUILDING.NO MATCHES' | translate:param}}
          </td>
        </tr>
        <tr *ngIf="!canViewRecords">
          <td class="text-danger" colspan="100">
             {{'BUILDING.PERMISSION_DENIED' | translate:param}}
          </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="12">
            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
          </td>
        </tr>
        </tfoot>
      </table>


    </div>
  </div>
</section>