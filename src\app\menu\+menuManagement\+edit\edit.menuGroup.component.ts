import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, ViewEncapsulation, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { MenuGroupService } from './../../../shared/services/menuGroup.service';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;
@Component({
    selector: 'edit-menu-group',
    templateUrl: '../menuManagement.action.html',
    encapsulation: ViewEncapsulation.None
})
export class EditMenuGroupComponent implements OnInit {
    id : number;
    menuAdd: FormGroup;
    pageType: string = "Edit";
    // service variable
    private sub: any;
    private sub2: any;
    private getMenu: any;
    isDirectLink: boolean;
    // Input/Outpus
    @Input() selectedMG;
    @Input() gethideEditMG;
    @Output() sendhideEditMG = new EventEmitter();
    // general referances
    name: any;
    status: any;

    constructor(
        private router: Router,        
        private _fb: FormBuilder,
        private MS: MenuGroupService,
        private route: ActivatedRoute, 
        public translate: TranslateService       
    ) {
        this.buildForm()
        translate.get('MENU_MANAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }
    buildForm() {
        this.menuAdd = this._fb.group({
            name: ['', Validators.required],
            status: ['', Validators.required]
        });
        this.name = this.menuAdd.controls['name'];
        this.status = this.menuAdd.controls['status'];
    }
    ngOnInit() {
        console.log("Selected menu group : ",this.selectedMG);
        if(this.selectedMG){
            this.isDirectLink = false;
            this.menuAdd.patchValue(this.selectedMG);
        }
        else{
            this.isDirectLink = true;
            this.directLinkProcess();
        }
    }

    directLinkProcess() {
        this.sub2 = this.route.params.subscribe( params => {
            this.id = params['id'];
            this.getMenu = this.MS.getMenus(this.id).subscribe( res => {
                if(res)
                    this.menuAdd.patchValue(res.data);
                else
                    Messenger().post({  hideAfter: 5,
                        message: 'Product not found!',
                        type: 'error',
                        showCloseButton: true
                    });
            },
        err => console.log("Menu Get Error : ",err));
        },
    error => console.log("Nav Params Get Error : ",error));
    }

    addMenu() {
        if (this.menuAdd.valid) {
            this.sub = this.MS.editMenuGroup(this.selectedMG.id, this.menuAdd.value).subscribe((res) => {
                if (res.status == "success") {
                    this.menuAdd.reset();
                   
                    this.toggleChild(res.data);
                } else {
                    // console.log(res);
                }
            },
                (err) => {
                    // console.log(err);
                })
        }
    }
    toggleChild(data) {
        if(this.isDirectLink){
            this.router.navigateByUrl('/admin/menu');
        }
        else {
            let result;
            this.gethideEditMG = !this.gethideEditMG;
            if (data) {
                result = { gethideEditMG: this.gethideEditMG, data: data }
            } else {
                result = { gethideEditMG: this.gethideEditMG }
            }
            this.sendhideEditMG.emit(result);
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}