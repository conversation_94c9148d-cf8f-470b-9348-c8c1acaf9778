<div class="js-sidebar-content  testing class">
  <header class="logo hidden-sm-down">
    <!--<a href="/"><img src="assets/img/logo-gray.png" alt="" class="logo"></a>-->
    <a href="javascript:void(0)">{{'SIDEBAR.DHARAMSHALA' | translate:param}}</a>
  </header>
  <div class="logo hidden-md-up">
    <!-- <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <span class="thumb-sm avatar pull-xs-right">
        <img class="img-circle" src="assets/img/people/a5.jpg" alt="...">
              </span> -->
      <!-- .circle is a pretty cool way to add a bit of beauty to raw data.
           should be used with bg-* and text-* classes for colors -->
              <!-- <span class="circle bg-warning fw-bold text-gray-dark">
                  13
              </span>
      &nbsp;
      <PERSON> <strong>Smith</strong>
      <b class="caret"></b>
    </a> -->
    <a href="javascript:void(0)">{{'SIDEBAR.DHARAMSHALA' | translate:param}}</a>
    <!-- #notifications-dropdown-menu goes here when screen collapsed to xs or sm -->
  </div>

   <!--<div [innerHTML]="sideMenuHtmlCode | safeHtml"></div> -->

  <ul class="sidebar-nav" *ngIf="menuListJson != null && menuListJson.length > 0 ">
    <!-- if Level 1 menu has children menus -->
    <li *ngFor="let level1 of menuListJson" [attr.data-menu-id]="level1.id">
      <a *ngIf="level1.children != null && level1.children.length > 0 && level1.is_enabled" 
        href="javascript:void(0)"
        [attr.aria-expanded]="!level1.isCollapsed"
        (click)="toggleMenuItem(level1)">

          <span class="icon">
            <i class="fa fa-{{level1.icon}}"></i>
          </span>
          {{level1.title}}
          <span *ngIf="level1.title == 'Online Booking'" class="badge">{{total_booking_request + total_payment_request}}</span>
          <i class="toggle fa" [class.fa-angle-down]="level1.isCollapsed" [class.fa-angle-up]="!level1.isCollapsed"></i>
      
      </a>
      <a *ngIf="level1.children != null && level1.children.length == 0 && !level1.custom_url && level1.is_enabled" [routerLink]="level1.routerLink?level1.routerLink:'./'">
          <span class="icon">
            <i class="fa fa-{{level1.icon}}"></i>
          </span>
          {{level1.title}}
      </a>
      <a *ngIf="level1.children != null && level1.children.length == 0 && level1.custom_url && level1.is_enabled" href="javascript:void(0)" (click)="openWindow(level1.custom_url)">
        <span class="icon">
          <i class="fa fa-{{level1.icon}}"></i>
        </span>
        {{level1.title}}
      </a>
       <!-- href="javascript:void(0)" -->
      <ul *ngIf="level1.children != null && level1.children.length > 0" [attr.id]="'sidebar-'+level1.title.replace(' ', '-').toLowerCase()"class="collapse"[class.in]="!level1.isCollapsed">
        <!--<li>
          <a [routerLink]="level1.routerLink?level1.routerLink:'./'">
              <span class="icon">
                <i class="fa fa-{{level1.childIcon}}"></i>
              </span>
              {{level1.title}}
          </a>
        </li>-->
        <li *ngFor="let level2 of level1.children">
         <!-- lvl 2 -->
          <a *ngIf="!level2.custom_url && level2.is_enabled" [routerLink]="level2.routerLink?level2.routerLink:'./'">
              <span class="icon">
                <i class="fa fa-{{level2.icon}}"></i>
              </span>
              {{level2.title}}
            <span *ngIf="level2.title == 'Online Booking Request'" class="badge">{{total_booking_request}}</span>
            <span *ngIf="level2.title == 'Online Payment Request'" class="badge">{{total_payment_request}}</span>
          </a>
          <a *ngIf="level2.custom_url && level2.is_enabled" href="javascript:void(0)" (click)="openWindow(level2.custom_url)">
            <span class="icon">
              <i class="fa fa-{{level2.icon}}"></i>
            </span>
            {{level2.title}}
          </a>
         <!-- lvl 3 -->
        </li>
      </ul>
    </li>
  </ul>
</div>
<!-- lvl 2 -->
 <!--<a *ngIf="level2.children.length > 0" 
            [attr.data-target]="'#sidebar-'+level2.title.replace(' ', '-').toLowerCase()" 
            data-toggle="collapse" 
            data-parent="#sidebar-levels">
              <span class="icon">
                <i class="fa fa-{{level2.icon}}"></i>
              </span>
              {{level2.title}}
              <i class="toggle fa fa-angle-down"></i>
          </a>-->
<!-- lvl 3 -->
 <!--<ul *ngIf="level2.children.length > 0" [attr.id]="'sidebar-'+level2.title.replace(' ', '-').toLowerCase()" class="collapse">
        <li *ngFor="let level3 of level2.children">
            <a [routerLink]="level3.routerLink?level3.routerLink:'./'">
                <span class="icon">
                  <i class="fa fa-{{level3.childIcon}}"></i>
                </span>
                {{level3.title}}
            </a>
        </li>
      </ul>-->