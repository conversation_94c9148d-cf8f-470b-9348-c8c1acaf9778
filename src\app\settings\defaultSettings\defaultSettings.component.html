<section class="widget">
  <header>
    <h4>
      <span class="capitalized">
        <i class="fa fa-user-o"></i>&nbsp;&nbsp;{{'DEF_SETTING.DEF_SET' | translate:param}}</span>
    </h4>
  </header>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="defaultSetting" (ngSubmit)="saveDefaultSettings()">
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DEF_SETTING.EAR_CHECK_LIM' | translate:param}}*</label>
            <div class="col-md-8 ">
              <timepicker class="early-check-in-limit" [minuteStep]="5" [mousewheel]="true" [formControl]="defaultSetting.controls['early_check_in_time']"></timepicker>
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" [disabled]="defaultSetting.invalid" class="btn btn-sm btn-inverse capitalized">{{'DEF_SETTING.SAVE' | translate:param}}</button>
                <a [routerLink]="['/']" class="btn btn-sm btn-secondary">{{'DEF_SETTING.CANCEL' | translate:param}}</a>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>