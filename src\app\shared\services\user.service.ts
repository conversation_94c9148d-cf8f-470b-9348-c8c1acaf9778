import { AuthGuard } from './../guards/auth-guard.service';
import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
declare var Messenger;
@Injectable()
export class UserService {

    constructor(private chttp: CommonHttpService,
        private authGuard: AuthGuard) {
    }
    userLogin(data) {
        data["apiSecretKey"]=(document as any).apiSecretKey; 
        return this.chttp.post('login', data, true).map(res => {

            if (res.status === 'success') {
                // console.log("User Data : ",res);
                this.authGuard.storeUser(res.data);
                console.log("res.data", res.data);
                
                if(res.data.hasOwnProperty('userShift')) {
                    this.authGuard.storeUserShift(res.data.userShift);
                    this.showNotification('You are currently in a shift', 'info');
                }
                this.authGuard.getUser();
            }
            return res;
        });
    }

    userLogOut() {
        let data = { 'token': this.authGuard.ud.session_id };
        return this.chttp.post('logout', data, true);
    }


    // user management functions
    getAllUsers() {
        return this.chttp.get('user/list');
    }
    saveUserData(data) {
        return this.chttp.post('user/add', data, true);
    }
    updateUserData(id, data) {
        return this.chttp.post(`user/edit/${id}`, data, true);
    }
    // get user data
    getUserDetails() {
        return this.chttp.get('user/edit/profile');
    }
    saveUserDetails(data) {
        return this.chttp.post('user/edit/profile', data, true);
    }
    // change password
    savechangedPassword(data) {
        return this.chttp.post('user/edit/change/password', data, true);
    }

    setPassword(data, hash) {
        return this.chttp.post(`register/${hash}`, data);
    }

    sendLink(data) {
        return this.chttp.post(`resetpassword`, data);
    }
    forgetPassword(data, hash) {
        return this.chttp.post(`recover/${hash}`, data);
    }

    // reference user servces
    fetchAllReferenceUsers() {
        return this.chttp.get(`referenceuser/list`);
    }
    saveReferenceUser(data) {
        return this.chttp.post(`referenceuser/add`, data, true);
    }
    updateReferenceUser(id, data) {
        return this.chttp.post(`referenceuser/edit/${id}`, data, true);
    }

    // guest
    getAllGuestDetails() {
        return this.chttp.get(`guest/list`, false);
    }
    getGuestDetail(id) {
        return this.chttp.get(`guest/edit/${id}`, false);
    }
    getAvailableUserShiftTransfer() {
        return this.chttp.get(`user/shifts`);
    }
    shiftIn(id, data) {
        return this.chttp.post(`user/shifts/shift_in/${id}`, data, true);
    }
    shiftOut() {
        return this.chttp.post(`user/shifts/shift_out`, {}, true);
    }
    getPettyAmountFromUser(id) {
        return this.chttp.get(`user/shift/petty/${id}`, false);
    }
    showNotification(message: string, type: string, showCloseButton: boolean = true) {
        Messenger().post({
            message: message,
            type: type,
            showCloseButton: showCloseButton
        });
    }

    //Discount reference user

    fetchAllDisReferenceUsers() {
        return this.chttp.get(`discountreferenceuser/list`);
    }
    saveDisReferenceUser(data) {
        return this.chttp.post(`discountreferenceuser/add`, data, true);
    }
    updateDisReferenceUser(id, data) {
        return this.chttp.post(`discountreferenceuser/edit/${id}`, data, true);
    }
}
