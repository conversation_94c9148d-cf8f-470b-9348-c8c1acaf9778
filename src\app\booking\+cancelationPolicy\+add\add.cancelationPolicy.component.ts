import { Component, OnInit, Output, EventEmitter, OnD<PERSON>roy } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { BookingService } from './../../../shared/services/booking.service';
import { CustomValidators } from 'ng2-validation';
import * as CryptoJS from 'crypto-js';
import { _secretKey } from '../../../shared/globals/config';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'add-cancelation-policy',
    templateUrl: '../cancelationPlicy.action.component.html'
})

export class AddCancelationPolicyComponent implements OnInit {
    policyForm: FormGroup;
    pageType: string = "Add";
    // service
    private sub: any;
    private _secretKey = _secretKey;

    @Output() sendaddedPolicy = new EventEmitter();
    @Output() closeComp = new EventEmitter();
    constructor(
        private _fb: FormBuilder,
        private BS: BookingService,
        public translate: TranslateService
    ) { 
        translate.get('CANCELLATION POLICY.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.buildForm();
    }

    buildForm() {
        this.policyForm = this._fb.group({
            name: ['', [Validators.required]],
            status: [true, Validators.required],
            passcode: ['', [Validators.required]],
            discount: ['0', [Validators.required, CustomValidators.number]],
            day_before: ['', [Validators.required, CustomValidators.digits]],
        })
    }
    savePolicy() {
        if (this.policyForm.valid) {
            let data = JSON.parse(JSON.stringify(this.policyForm.value));
            data.passcode = CryptoJS.AES.encrypt(data.passcode, this._secretKey).toString();
            this.sub = this.BS.savePlocy(data)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.policyForm.reset();
                        this.sendaddedPolicy.emit(res.data);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.policyForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({
                hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    closeThisComp() {
        this.closeComp.emit(true);
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}