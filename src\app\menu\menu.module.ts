import { MenuGroupService } from './../shared/services/menuGroup.service';
import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { MenuManagementComponent } from './+menuManagement/menuManagement.component';
import { AddMenuGroupComponent } from './+menuManagement/+add/add.menuGroup.component';
import { EditMenuGroupComponent } from './+menuManagement/+edit/edit.menuGroup.component';
import { ManageMenuComponent } from './+menuManagement/+manage/manage.menuGroup.component';

import { NgSelectModule } from '@ng-select/ng-select';
import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
 
import 'fontawesome-iconpicker/dist/js/fontawesome-iconpicker.min.js';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

const route = [
    { path : '' , component: MenuManagementComponent, pathMatch : 'full'},
    { path : 'add', component: AddMenuGroupComponent },
    { path : 'edit/:id', component: EditMenuGroupComponent },
    { path : 'manage/:id', component: ManageMenuComponent }
]

@NgModule({
    imports: [
        FormsModule,
        CommonModule,
        TooltipModule.forRoot(),
        NgSelectModule,
        DataTableModule,
        ReactiveFormsModule,
        RouterModule.forChild(route),
        TranslateModule.forRoot({
            loader:{ 
              provide: TranslateLoader, 
              useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
            deps: [HttpClient] 
          }})
    ],
    exports: [],
    declarations: [
        MenuManagementComponent,
        AddMenuGroupComponent,
        EditMenuGroupComponent,
        ManageMenuComponent
    ],
    providers: [MenuGroupService],
})
export class MenuModule { }
