<div class="row">
  <div [ngClass]="{'col-sm-6': (!hideAdd || !hideEdit),'col-sm-12': (hideAdd && hideEdit)}">
    <section class="widget">
      <header>
        <h4><span class="" style="color: red;"><i class="fa fa-user-secret"></i>&nbsp;&nbsp;{{'AGENT.AGENT_MANAGE' | translate:param}}</span></h4>
      </header>
      <hr class="large-hr">
      <div class="float-sm-right text-right col-sm-12">
        <button type="button" *ngIf="auth.roleAccessPermission('agent','add')" [disabled]="!hideAdd" (click)="showAdd()"  class="display-inline-block btn btn-sm btn-inverse" tooltip="{{'AGENT.ADD_NEW_AGEN' | translate:param}}" placement="top"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{'AGENT.ADD' | translate:param}}</button>
        <div class="form-group display-inline-block __search">
          <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'AGENT.SEARCH' | translate:param}}">
          <span class="form-group-addon"><i class="fa fa-search"></i></span>
          <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
        </div>
      </div>
      <div class="clearfix"></div>
      <div class="widget-body" *ngIf="cityList">
        <div class="mt">
          <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
            <thead>
              <tr>
                <th>
                  <mfDefaultSorter by="id">#</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="name">{{'AGENT.NAME' | translate:param}}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="location">{{'AGENT.LOCATION' | translate:param}}</mfDefaultSorter>
                </th>
                <th class="no-sort">
                  <mfDefaultSorter by="status">{{'AGENT.STATUS' | translate:param}}</mfDefaultSorter>
                </th>
                <th *ngIf="auth.roleAccessPermission('agent','edit')" class="no-sort">
                  <mfDefaultSorter by="status">{{'AGENT.ACTION' | translate:param}}</mfDefaultSorter>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let ds of mf.data">
                <td>{{ds.id}}</td>
                <td><span class="uppercase fw-semi-bold"> {{ds.name}}</span></td>
                <td>
                    {{ds.location ? cityList[findIndex(parseInteger(ds.location),"id",cityList)].text : '-'}}
                </td>
                <td class=" ">
                  <span class="text-success" *ngIf="ds.status">{{'AGENT.ACTIVE' | translate:param}}</span>
                  <span class="text-danger" *ngIf="!ds.status">{{'AGENT.INACTIVE' | translate:param}}</span>
                </td>

                <td class="width-100">
                  <button type="button" *ngIf="auth.roleAccessPermission('agent','edit')" (click)="showEdit(ds)" class="btn btn-xs btn-default" tooltip="{{'AGENT.EDIT_AGET_DETA' | translate:param}}" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{'AGENT.EDIT' | translate:param}}</button>
                </td>
              </tr>
              <tr *ngIf="canViewRecords && mf.data.length === 0">
                <td colspan="100">
                  {{'AGENT.NO MATCHES' | translate:param}}
                </td>
              </tr>
              <tr *ngIf="!canViewRecords">
                <td class="text-danger" colspan="100">
                  {{'AGENT.PERMISSION_DENIED' | translate:param}}
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="12">
                  <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </section>
  </div>
  <add-agent class="col-sm-6" *ngIf="!hideAdd" (hideAddEvent)="handleAddHide()" [cityList]="cityList" (addToList)="addToListProcess($event)"></add-agent>
  <edit-agent class="col-sm-6" *ngIf="!hideEdit" [selectedAgent]="selectedAgent" [cityList]="cityList" (hideEditEvent)="handleEditHide()" (updateToList)="updateToListProcess($event)"></edit-agent>
</div>
