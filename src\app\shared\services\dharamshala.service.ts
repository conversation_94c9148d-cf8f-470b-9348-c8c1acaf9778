import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()

export class DharamshalaService {

    constructor(
        private chttp: CommonHttpService
    ) { }

    getInitData() {
        return this.chttp.get(`dharamshala/add`);
    }

    listAllDharamshalas() {
        return this.chttp.get(`dharamshala/list`)
    }
    savedharamshala(data) {
        return this.chttp.post(`dharamshala/add`, data);
    }
    getAssignedDharamshala() {
        return this.chttp.get(`dharamshala/list/assigned`);
    }
    getDharamshalaDetails(id) {
        return this.chttp.get(`dharamshala/edit/${id}`);
    }
    saveEditedDharamshala(id, data) {
        return this.chttp.post(`dharamshala/edit/${id}`, data);
    }
    deleteImage(img) {
        return this.chttp.post(`roomCategory/edit/image/delete/${img.originalName}`, img);
    }
    // funds
    getAllFunds() {
        return this.chttp.get(`fund/list`, false);
    }
    saveFunds(data) {
        return this.chttp.post(`fund/add`, data, true);
    }
    updateFunds(id, data) {
        return this.chttp.post(`fund/edit/${id}`, data, true);
    }
    updateDefaultFund(id, data) {
        return this.chttp.post(`fund/edit/default/${id}`, data, true);
    }
    // splite
    
    getSplite(){
        return this.chttp.get(`booking/Settings/list`);
    }
    editSplite(id,data){
        return this.chttp.post(`booking/updateSplitAmount/${id}`,data)
    }













    // Stay Type
    getAllStayTypes() {
        return this.chttp.get(`staytype/list`, false);
    }
    saveStayType(data) {
        return this.chttp.post(`staytype/add`, data, true);
    }
    updateStayType(id, data) {
        return this.chttp.post(`staytype/edit/${id}`, data, true);
    }
    updateDefaultStayType(id) {
        return this.chttp.post(`staytype/edit/default/${id}`, {}, true);
    }
    // agents
    getAllAgentlist() {
        return this.chttp.get(`agent/list`, false);
    }
    getAgentLocationList() {
        return this.chttp.get(`agent/list/location`, false);
    }
    saveAgent(data) {
        return this.chttp.post(`agent/add`, data, true);
    }
    saveAgentLocation(data) {
        return this.chttp.post(`agent/add/location`, data, true);
    }
    updateAgent(id, data) {
        return this.chttp.post(`agent/edit/${id}`, data, true);
    }
    updateAgentLocation(id, data) {
        return this.chttp.post(`agent/edit/location/${id}`, data, true);
    }
    updateEarlyCheckInLimit(id: any, data: any) {
        return this.chttp.post(`dharamshala/edit/default/${id}`, data, true);
    }
    getEarlyCheckInLimit(id) {
        return this.chttp.get(`dharamshala/edit/default/${id}`, false);
    }
    getAllCities() {
        return this.chttp.get(`booking/list/cities`, false);
    }
}