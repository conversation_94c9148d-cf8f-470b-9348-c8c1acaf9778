import 'jquery-slimscroll';

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ModalModule } from 'ngx-bootstrap/modal';
import { TooltipModule } from 'ngx-bootstrap/tooltip';

import { ROUTES } from './layout.routes';

import { Layout } from './layout.component';
import { Sidebar } from './sidebar/sidebar.component';
import { Navbar } from './navbar/navbar.component';
import { SearchPipe } from './pipes/search.pipe';
import { NotificationLoad } from './notifications/notifications-load.directive';
import { Notifications } from './notifications/notifications.component';
import { SharedModule } from 'app/shared/shared.module';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateEventService } from 'app/shared/services/translation.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { BookingService } from 'app/shared/services/booking.service';
import { PaymentRequestService } from 'app/shared/services/payment.service';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  imports: [
    CommonModule,
    TooltipModule,
    ROUTES,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    SharedModule,
    ModalModule.forRoot(),
    FormsModule,
    NgSelectModule
  ],
  declarations: [
    Layout,
    Sidebar,
    Navbar,
    SearchPipe,
    Notifications,
    NotificationLoad
  ],
  providers: [
    TranslateEventService,
    BookingService,
    PaymentRequestService
  ]
})
export class LayoutModule { }
