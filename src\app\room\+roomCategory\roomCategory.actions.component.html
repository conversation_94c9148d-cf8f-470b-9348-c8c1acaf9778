<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-sitemap"></i>&nbsp;&nbsp;{{pageName}} {{'ROOM.ROOM_CAT.ADD_PAGE.ROOM_CATE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item "><a href="javascript:void(0)" (click)="toggleChild()">{{'ROOM.ROOM_CAT.ADD_PAGE.ROOM_CAT_MANAGE' | translate:param}}</a></li>
    <li class="breadcrumb-item active">{{pageName}} {{'ROOM.ROOM_CAT.ADD_PAGE.ROOM_CATE' | translate:param}}</li>
  </ol>
  <!-- <div class="abc-checkbox abc-checkbox-success pull-right">
    <input type="checkbox" id="common_room_category" (change)="setRoomCategoryCommon()" [checked]="commonRoomCategory">
    <label for="common_room_category" class="capitalized">
      Common Room Category
    </label>
  </div> -->

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="roomCategory" (ngSubmit)="saveRoomCategory()">

          <!-- <input type="hidden" formControlName="dharamshala_id">
          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">Dharamshala*</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.dharamshala_id.errors?.backend">{{roomCategory.controls.dharamshala_id.errors?.backend}}</span>
              <ng-select id="default-select" (change)="dharamshalaListchange($event)" [items]="getDharamshalaList()" [(ngModel)]="selectedDharamshala"></ng-select>
            </div>
          </div> -->
          <input type="hidden" formControlName="is_common_room_category">

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.NAME' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.name.errors?.backend">{{roomCategory.controls.name.errors?.backend}}</span>
              <input type="text" formControlName="name" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!roomCategory.controls.name.valid && !roomCategory.controls.name.pristine">
              <span [hidden]="!roomCategory.controls.name.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_CAT_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.CHARGE' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.charges.errors?.backend">{{roomCategory.controls.charges.errors?.backend}}</span>
              <input type="text" formControlName="charges" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!roomCategory.controls.charges.valid && !roomCategory.controls.charges.pristine">
              <span [hidden]="!roomCategory.controls.charges.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_CAT_CHARGE' | translate:param}}</span>
               <span [hidden]="!roomCategory.controls.charges.errors.number">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_NUM_ALLOW' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.TOTAL_ROOM' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.total_room.errors?.backend">{{roomCategory.controls.total_room.errors?.backend}}</span>
              <input type="text" formControlName="total_room" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!roomCategory.controls.total_room.valid && !roomCategory.controls.total_room.pristine">
              <span [hidden]="!roomCategory.controls.total_room.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_TOT_REQ' | translate:param}}</span>
               <span [hidden]="!roomCategory.controls.total_room.errors.digits">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_FIX_NUM_ALLO' | translate:param}} </span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.DEF_ONLINE_QUO' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.default_online_quota.errors?.backend">{{roomCategory.controls.default_online_quota.errors?.backend}}</span>
              <input type="text" formControlName="default_online_quota" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!roomCategory.controls.default_online_quota.valid && !roomCategory.controls.default_online_quota.pristine">
              <span [hidden]="!roomCategory.controls.default_online_quota.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_DEF_QUO_REQ' | translate:param}}</span>
               <span [hidden]="!roomCategory.controls.default_online_quota.errors.digits">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_FIX_NUM_ALLO' | translate:param}} </span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.STD_OCCU' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.std_occupancy.errors?.backend">{{roomCategory.controls.std_occupancy.errors?.backend}}</span>
              <input type="text" formControlName="std_occupancy" class="form-control" placeholder="">
               <span class="errMsg" *ngIf="!roomCategory.controls.std_occupancy.valid && !roomCategory.controls.std_occupancy.pristine">
              <span [hidden]="!roomCategory.controls.std_occupancy.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_STD_ADUL_REQ' | translate:param}}</span>
               <span [hidden]="!roomCategory.controls.std_occupancy.errors.digits">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_FIX_NUM_ALLO' | translate:param}} </span>
              </span>
            </div>
          </div>

          <!-- <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">Stadard Child Occupancy*</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.std_child_occupancy.errors?.backend">{{roomCategory.controls.std_child_occupancy.errors?.backend}}</span>
              <input type="text" formControlName="std_child_occupancy" class="form-control" placeholder="">
               <span class="errMsg" *ngIf="!roomCategory.controls.std_child_occupancy.valid && !roomCategory.controls.std_child_occupancy.pristine">
              <span [hidden]="!roomCategory.controls.std_child_occupancy.errors.required">Please enter Stadard Child Occupancy</span>
               <span [hidden]="!roomCategory.controls.std_child_occupancy.errors.digits">Only Fixed numbers allowed here. </span>
              </span>
            </div>
          </div> -->

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.EXT_ADU_CHA' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.extra_adult_charges.errors?.backend">{{roomCategory.controls.extra_adult_charges.errors?.backend}}</span>
              <input type="text" formControlName="extra_adult_charges" class="form-control" placeholder="">
               <span class="errMsg" *ngIf="!roomCategory.controls.extra_adult_charges.valid && !roomCategory.controls.extra_adult_charges.pristine">
              <span [hidden]="!roomCategory.controls.extra_adult_charges.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_ADU_OCCU' | translate:param}}</span>
               <span [hidden]="!roomCategory.controls.extra_adult_charges.errors.digits">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_FIX_NUM_ALLO' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.EXT_CHIL_CHAR' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.extra_child_charges.errors?.backend">{{roomCategory.controls.extra_child_charges.errors?.backend}}</span>
              <input type="text" formControlName="extra_child_charges" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!roomCategory.controls.extra_child_charges.valid && !roomCategory.controls.extra_child_charges.pristine">
              <span [hidden]="!roomCategory.controls.extra_child_charges.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_MAX_CHIL_OCCU' | translate:param}}</span>
               <span [hidden]="!roomCategory.controls.extra_child_charges.errors.digits">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_FIX_NUM_ALLO' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.MAX_OCCU' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.max_occupancy.errors?.backend">{{roomCategory.controls.max_occupancy.errors?.backend}}</span>
              <input type="text" formControlName="max_occupancy" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!roomCategory.controls.max_occupancy.valid && !roomCategory.controls.max_occupancy.pristine">
              <span [hidden]="!roomCategory.controls.max_occupancy.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_MAX_REQ' | translate:param}}</span>
               <span [hidden]="!roomCategory.controls.max_occupancy.errors.digits">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_FIX_NUM_ALLO' | translate:param}} </span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.DESC' | translate:param}}</label>
            <div class="col-md-8 ">
              <textarea name="" id="" formControlName="description" rows="5" class="form-control"></textarea>
              <span class="errMsg" *ngIf="!roomCategory.controls.description.valid && !roomCategory.controls.description.pristine">
              <span [hidden]="!roomCategory.controls.description.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_DESC_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.AMENITIES' | translate:param}}</label>
            <div class="col-md-4 ">
              <ng-select 
                [items]="amenities"
                bindLabel="name"
                bindValue="id"
                (change)="select2Changed($event)"
                [clearable]="false"
                placeholder="{{'ROOM.ROOM_CAT.ADD_PAGE.SELECT_AMENITY' | translate:param}}">
              </ng-select>
            </div>
            <div class="col-md-3">
              <input type="text" #eminityCharge class="form-control" value="{{eminityChargevalue}}" placeholder="{{'ROOM.ROOM_CAT.ADD_PAGE.CHARGE_RS' | translate:param}}">
            </div>
            <div class="col-md-1">
              <a href="javascript:void(0)" (click)="addAminity(eminityCharge)" class="btn btn-sm btn-default"><i class="fa fa-plus"></i></a>
            </div>
          </div>
          <div class="form-group row">
            <div class="col-md-8 offset-md-3" formArrayName="amenities" *ngIf="roomCategory.controls.amenities.controls.length > 0">

              <div class="amenities-area">
                <span class="amenities-selection" *ngFor="let aminity of roomCategory.controls.amenities.controls;let i = index" [formGroupName]="i">
                        <input type="hidden" formControlName="amenity_id" readonly> 
                       <input type="text" formControlName="title" readonly autoWidth> 
                       <span style="color:green">
                       ( <i class="fa fa-inr"></i>&nbsp;<input type="text" readonly formControlName="charge" autoWidth style="color:green">)
                       </span>


                <i class="fa fa-times amenities-action" (click)="removeAminity(i)"></i>
                </span>
              </div>

            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.IMAGES' | translate:param}}</label>
            <div class="col-md-8 ">
              <input type="file" name="avatar" class="form-control" ng2FileSelect [uploader]="uploader" multiple /><br/>
              <div class="amenities-area __image_container" *ngIf="uploadedImageURLs && uploadedImageURLs.length > 0">
                <span *ngFor="let img of uploadedImageURLs;let i = index">
                <span  class="__images" *ngIf="!img.is_deleted">
                  <a href="javascript:void(0)" (click)="deleteImage(img,i)"><i class="fa fa-times"></i></a>
                  <img src="{{img.url}}" alt="">

                </span>
                </span>
              </div>
              <!-- file upload extra ************************** -->
              <div class="files" *ngIf="uploader.queue.length" class="well well-sm">
                <table class="table">
                  <thead>
                    <tr>
                      <th width="50%">{{'ROOM.ROOM_CAT.ADD_PAGE.FILE_NAME' | translate:param}}</th>
                      <th>{{'ROOM.ROOM_CAT.ADD_PAGE.FILE_SIZE' | translate:param}}</th>
                      <th width="230px">{{'ROOM.ROOM_CAT.ADD_PAGE.PROGRESS' | translate:param}}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let item of uploader.queue">
                      <td><strong>{{ item?.file?.name }}</strong></td>
                      <td *ngIf="uploader.isHTML5" nowrap>{{ item?.file?.size/1024/1024 | number:'.2' }} MB</td>
                      <td *ngIf="uploader.isHTML5">
                        <div class="progress" style="margin-bottom: 0;">
                          <div class="progress-bar" role="progressbar" [ngStyle]="{ 'width': item.progress + '%' }"></div>
                        </div>
                      </td>
                      <td class="text-center">
                        <span *ngIf="item.isSuccess"><i class="glyphicon glyphicon-ok"></i></span>
                        <span *ngIf="item.isCancel"><i class="glyphicon glyphicon-ban-circle"></i></span>
                        <span *ngIf="item.isError"><i class="glyphicon glyphicon-remove"></i></span>
                      </td>
                      <td nowrap>

                        <button type="button" class="btn btn-success btn-xs" (click)="item.upload()" [disabled]="item.isReady || item.isUploading || item.isSuccess">
                            <span class="glyphicon glyphicon-upload"></span> {{'ROOM.ROOM_CAT.ADD_PAGE.FILE_UPLOAD' | translate:param}}
                        </button>
                        <button type="button" class="btn btn-warning btn-xs" (click)="item.cancel()" [disabled]="!item.isUploading">
                            <span class="glyphicon glyphicon-ban-circle"></span> {{'ROOM.ROOM_CAT.ADD_PAGE.CANCEL' | translate:param}}
                        </button>
                        <button type="button" class="btn btn-danger btn-xs" (click)="item.remove()">
                            <span class="glyphicon glyphicon-trash"></span> {{'ROOM.ROOM_CAT.ADD_PAGE.FILE_REMOVE' | translate:param}}
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <hr>

                <div *ngIf="uploader.queue.length > 1">
                  <div *ngIf="uploader.progress">
                      {{'ROOM.ROOM_CAT.ADD_PAGE.QUEUE_PROGRESS' | translate:param}}
                    <div class="progress">
                      <div class="progress-bar" role="progressbar" [ngStyle]="{ 'width': uploader.progress + '%' }"></div>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-success btn-sm" (click)="uploader.uploadAll()" [disabled]="!uploader.getNotUploadedItems().length">
                        <span class="glyphicon glyphicon-upload"></span> {{'ROOM.ROOM_CAT.ADD_PAGE.UPLOAD_ALL' | translate:param}}
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" (click)="uploader.cancelAll()" [disabled]="!uploader.isUploading">
                        <span class="glyphicon glyphicon-ban-circle"></span> {{'ROOM.ROOM_CAT.ADD_PAGE.CANCEL_ALL' | translate:param}}
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" (click)="uploader.clearQueue()" [disabled]="!uploader.queue.length">
                        <span class="glyphicon glyphicon-trash"></span> {{'ROOM.ROOM_CAT.ADD_PAGE.REMOVE_ALL' | translate:param}}
                    </button>
                  </div>
                  <div class="clearfix"></div>
                </div>
              </div>

              <!-- file upload extra ************************** -->
            </div>
          </div>

          <div class="form-group row">

            <div class="col-md-8">
              <div class="uploaded_images" formArrayName="uploadedImages">
                <span *ngFor="let image of roomCategory.controls.uploadedImages.controls; let i = index" [formGroupName]="i">
                    <input type="hidden" formControlName="mimetype">
                    <input type="hidden" formControlName="originalName">
                    <input type="hidden" formControlName="size">
                    <input type="hidden" formControlName="uploaded">
                    <input type="hidden" formControlName="is_deleted">
                  </span>
              </div>
            </div>
          </div>
          <div class="form-group row" *ngIf=" roomCategory.controls.tariff.length > 0">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.TARRIF_BY' | translate:param}}</label>
            <div class="col-md-8 table-scroll">
              <table class="table table-condence" formArrayName="tariff">
                <thead>
                  <tr>
                    <td><strong>{{'ROOM.ROOM_CAT.ADD_PAGE.CUSTOMER' | translate:param}}</strong></td>
                    <td><strong>{{'ROOM.ROOM_CAT.ADD_PAGE.IN_PERCE' | translate:param}}</strong></td>
                    <td><strong>{{'ROOM.ROOM_CAT.ADD_PAGE.IN_AMT' | translate:param}}<i class="fa fa-inr"></i>)</strong></td>
                    <td><strong>{{'ROOM.ROOM_CAT.ADD_PAGE.EXT_BAF_CHAR' | translate:param}}</strong></td>
                    <td><strong>{{'ROOM.ROOM_CAT.ADD_PAGE.EXT_ADU_CHAR' | translate:param}}</strong></td>
                    <td><strong>{{'ROOM.ROOM_CAT.ADD_PAGE.EXT_CHIL_CHAR' | translate:param}}</strong></td>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let cType of roomCategory.controls.tariff.controls; let i = index" [formGroupName]="i" isDisabled [percentageInput]="percentage"
                    [amountInput]="amount" [discountTypeInput]="discountType" (disType)="handleDisType($event)">
                    <td class="capitalized">{{customerTypeData[i].customer_name || customerTypeData[i].name}}
                      <input type="hidden" formControlName="customer_id">
                      <input type="hidden" #discountType formControlName="discount_type">
                      <input type="hidden" formControlName="customer_name">

                    </td>
                    <td>
                      <input type="text" #percentage class="form-control" formControlName="percentage" max="100">
                    </td>
                    <td>
                      <input type="text" #amount class="form-control" formControlName="amount">
                    </td>
                    <td>
                      <input type="text" class="form-control" formControlName="extra_bed_charge">
                    </td>
                    <td>
                      <input type="text" class="form-control" formControlName="extra_adult_charge">
                    </td>
                    <td>
                      <input type="text" class="form-control" formControlName="extra_child_charge">
                    </td>
                  </tr>

                </tbody>
              </table>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3  col-form-label text-md-right">{{'ROOM.ROOM_CAT.ADD_PAGE.REL_QUOT_BEFORE' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="roomCategory.controls.release_quota_before.errors?.backend">{{roomCategory.controls.release_quota_before.errors?.backend}}</span>
              <input type="text" formControlName="release_quota_before" class="form-control" placeholder="">
               <span class="errMsg" *ngIf="!roomCategory.controls.release_quota_before.valid && !roomCategory.controls.release_quota_before.pristine">
              <span [hidden]="!roomCategory.controls.release_quota_before.errors.required">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_REL_QUO_BEFORE' | translate:param}}</span>
               <span [hidden]="!roomCategory.controls.release_quota_before.errors.digits">{{'ROOM.ROOM_CAT.ADD_PAGE.VALID_MSG.ROOM_FIX_NUM_ALLO' | translate:param}} </span>
              </span>
            </div>
          </div>


          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'ROOM.ROOM_CAT.ADD_PAGE.STATUS' | translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio1" [value]="true">
                  <label for="radio1">
                      {{'ROOM.ROOM_CAT.ADD_PAGE.ACTIVE' | translate:param}}
                      </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio2" [value]="false">
                  <label for="radio2">
                      {{'ROOM.ROOM_CAT.ADD_PAGE.INACTIVE' | translate:param}}
                      </label>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" class="btn btn-sm btn-inverse capitalized" [disabled]="!roomCategory.valid"><i class="fa fa-check"></i>{{'ROOM.ROOM_CAT.ADD_PAGE.SAVE' | translate:param}}</button>
                <button (click)="toggleChild()" class="btn btn-sm btn-secondary">{{'ROOM.ROOM_CAT.ADD_PAGE.CANCEL' | translate:param}}</button>
              </div>
            </div>
          </div>

        </form>
        <!--<pre>
          {{roomCategory.value | json}}
        </pre>-->
      </fieldset>
    </div>
  </div>
</section>
