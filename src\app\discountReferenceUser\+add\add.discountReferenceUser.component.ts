import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { CustomValidators } from 'ng2-validation';
import { UserService } from '../../shared/services/user.service';
import { TranslateService } from '@ngx-translate/core';

interface NgSelectOption {
    id: string | number;
    text: string;
}

declare var Messenger: any;
@Component({
    selector: 'add-discount-reference-user',
    templateUrl: '../discountReferenceUser.actions.html'
})

export class AddDiscountReferenceUserComponent implements OnInit {
    referenceUserForm: FormGroup;
    public pageType: string = "Add";
    public customerTypes: string[];
    
    @Input() getHiddenaddRU;
    @Input() userList: Array<NgSelectOption>;
    @Input() referenceList: Array<NgSelectOption>;
    @Output() sendHiddenaddRU = new EventEmitter();
    @Output() listSavedData = new EventEmitter();

    user = '';
    private sub: any;
    
    constructor(
        private _fb: FormBuilder,
        private US: UserService,
        public translate: TranslateService
    ) { 
        translate.get('REF_USER.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            this.pageType = res;
        });
    }

    ngOnInit() {
        this.buildForm();
        this.userList = this.userList.map((obj: any) => ({
            id: obj.user_id,
            text: obj.first_name.toUpperCase() + ' ' + obj.last_name.toUpperCase()
        }));
        
        this.referenceList = this.referenceList.map((obj: any) => ({
            id: obj.id,
            text: obj.name.toUpperCase()
        }));
        
        this.referenceList.sort((x, y) => {
            let a = x.text.toUpperCase(),
                b = y.text.toUpperCase();
            return a == b ? 0 : a > b ? 1 : -1;
        });
        
        this.userList.sort((x, y) => {
            let a = x.text.toUpperCase(),
                b = y.text.toUpperCase();
            return a == b ? 0 : a > b ? 1 : -1;
        });
    }

    buildForm() {
        this.referenceUserForm = this._fb.group({
            user_id: ['', Validators.required],
            allow_to_update_customer_type: [false],
            allow_to_update_reference_user: [false],
            reference_user_ids: [[], Validators.required]
        });
    }

    toggleChild() {
        this.referenceUserForm.reset();
        this.getHiddenaddRU = !this.getHiddenaddRU;
        this.sendHiddenaddRU.emit(this.getHiddenaddRU);
    }

    saveReferenceUser() {
        if (this.referenceUserForm.valid) {
            this.sub = this.US.saveDisReferenceUser(this.referenceUserForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.referenceUserForm.reset();
                        this.customerTypes = [];
                        let response = res.data;
                        this.listSavedData.emit(response);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.referenceUserForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                });
        } else {
            Messenger().post({
                hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }

    customerTypeChanged(event: any) {
        if (event) {
            this.referenceUserForm.patchValue({
                reference_user_ids: event
            });
        }
    }

    changeUser(event: any) {
        if (event) {
            this.user = event.text;
            this.referenceUserForm.patchValue({
                user_id: event.id
            });
        }
    }

    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}