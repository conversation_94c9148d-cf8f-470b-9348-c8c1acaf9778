<section class="widget">
    <header>
        <h4><span class="capitalized"><i class="fa fa-inr"></i>&nbsp;&nbsp;{{pageType}}
                Edit split</span></h4>
    </header>
    <hr class="large-hr">
    <div class="widget-body">
        <div class="mt">
            <fieldset>
                <form [formGroup]="formData" (ngSubmit)="saveSpliteData()">
                    <div class="form-group row">
                        <label for="normal-field"
                            class="col-md-3  col-form-label text-md-right">Mexamount</label>
                        <div class="col-md-4 ">
                            <!-- <span class="errMsg __fromBackend" *ngIf="fundsTypeForm.controls.mexamount.errors?.backend">{{fundsTypeForm.controls.mexamount.errors?.backend}}</span> -->
                            <input type="text" class="form-control" formControlName="mexamount" name="mexamount"
                                placeholder="">

                            <!-- <span class="errMsg" *ngIf="!fundsTypeForm.controls.name.valid && !fundsTypeForm.controls.name.untouched">
                                <span [hidden]="!fundsTypeForm.controls.name.errors.required">{{'FUNDS.ADD_PAGE.VALID_MSG.NAME_REQ' | translate:param}}</span>
                            </span> -->
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-3 col-form-label text-md-right"
                            for="default-select">Minamount</label>
                        <div class="col-md-4 ">
                            <input type="text" class="form-control" formControlName="minamount" name="mexamount"
                                placeholder="">
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-md-8 offset-md-3">
                            <div class="">
                                <button type="submit" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>Edit</button>
                                <button type="button" (click)="cancelForm()" class="btn btn-sm btn-secondary">Cancel</button>
                            </div>
                        </div>
                    </div>
                </form>
            </fieldset>
        </div>
    </div>
</section>