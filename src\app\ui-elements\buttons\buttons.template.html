<ol class="breadcrumb">
  <li class="breadcrumb-item">YOU ARE HERE</li>
  <li class="active breadcrumb-item">UI Buttons</li>
</ol>
<h1 class="page-title">Buttons - <span class="fw-semi-bold">Styles</span></h1>
<div class="row">
  <div class="col-xl-4 col-lg-6 col-xs-12">
    <section class="widget" widget>
      <header>
        <h5>
          Color <span class="fw-semi-bold">Options</span>
        </h5>
        <div class="widget-controls">
          <a data-widgster="expand" title="Expand" href="#"><i class="glyphicon glyphicon-chevron-up"></i></a>
          <a data-widgster="collapse" title="Collapse" href="#"><i class="glyphicon glyphicon-chevron-down"></i></a>
          <a href="#" data-widgster="close"><i class="glyphicon glyphicon-remove"></i></a>
        </div>
      </header>
      <div class="widget-body">
        <p class="fs-mini text-muted">
          Use any of the available button classes to quickly create a styled button.
          Semantically distinguishable beauty.
        </p>
        <p class="text-xs-center">
          <button class="btn btn-secondary width-100 mb-xs" role="button">
            Secondary
          </button>
          <button class="btn btn-primary width-100 mb-xs" role="button">
            Primary
          </button>
          <button class="btn btn-info width-100 mb-xs" role="button">
            Info
          </button>
          <button class="btn btn-success width-100 mb-xs" role="button">
            Success
          </button>
          <button class="btn btn-warning width-100 mb-xs" role="button">
            Warning
          </button>
          <button class="btn btn-danger width-100 mb-xs" role="button">
            Danger
          </button>
          <button class="btn btn-gray width-100 mb-xs" role="button">
            Gray
          </button>
          <button class="btn btn-inverse width-100 mb-xs" role="button">
            Inverse
          </button>
        </p>
      </div>
    </section>
  </div>
  <div class="col-xl-4 col-lg-6 col-xs-12">
    <section class="widget" widget>
      <header>
        <h5>
          Size <span class="fw-semi-bold">Variants</span>
        </h5>
        <div class="widget-controls">
          <a data-widgster="expand" title="Expand" href="#"><i class="glyphicon glyphicon-chevron-up"></i></a>
          <a data-widgster="collapse" title="Collapse" href="#"><i class="glyphicon glyphicon-chevron-down"></i></a>
          <a href="#" data-widgster="close"><i class="glyphicon glyphicon-remove"></i></a>
        </div>
      </header>
      <div class="widget-body">
        <p class="fs-mini text-muted">
          Fancy larger or smaller buttons? Four separate sizes available for all use cases: from
          tiny 10px button to large one.
        </p>
        <p class="mb-xs">
          <button type="button" class="btn btn-primary btn-lg mb-xs">Large button</button>
          <button type="button" class="btn btn-secondary btn-lg mb-xs">Large button</button>
        </p>
        <p class="mb-xs">
          <button type="button" class="btn btn-primary mb-xs">Default button</button>
          <button type="button" class="btn btn-secondary mb-xs">Default button</button>
        </p>
        <p class="mb-xs">
          <button type="button" class="btn btn-primary btn-sm mb-xs">Small button</button>
          <button type="button" class="btn btn-secondary btn-sm mb-xs">Small button</button>
        </p>
        <p class="mb-xs">
          <button type="button" class="btn btn-primary btn-xs mb-xs">Tiny button</button>
          <button type="button" class="btn btn-secondary btn-xs mb-xs">Tiny button</button>
        </p>
      </div>
    </section>
  </div>
  <div class="col-xl-4 col-lg-6 col-xs-12">
    <section class="widget" widget>
      <header>
        <h5>
          Block <span class="fw-semi-bold">Buttons</span>
        </h5>
        <div class="widget-controls">
          <a data-widgster="expand" title="Expand" href="#"><i class="glyphicon glyphicon-chevron-up"></i></a>
          <a data-widgster="collapse" title="Collapse" href="#"><i class="glyphicon glyphicon-chevron-down"></i></a>
          <a href="#" data-widgster="close"><i class="glyphicon glyphicon-remove"></i></a>
        </div>
      </header>
      <div class="widget-body">
        <p class="fs-mini text-muted">
          Create block level buttons - those that span the full width
          of a parent— by adding <code>.btn-block</code>.
          Great for menu & social buttons.
        </p>
        <p>
          <button type="button" class="btn btn-info btn-block">Block Button</button>
        </p>
        <p>
          <button type="button" class="btn btn-secondary btn-block">Show Menu &nbsp;&nbsp;&nbsp;<i class="fa fa-bars"></i></button>
        </p>
        <p>
          <button type="button" class="btn btn-primary btn-block"><i class="fa fa-facebook"></i> Login mit Facebook</button>
        </p>
        <p>
          <button type="button" class="btn btn-warning btn-block">Are you sure?</button>
        </p>
      </div>
    </section>
  </div>
  <div class="col-xl-4 col-lg-6 col-xs-12">
    <section class="widget" widget>
      <header>
        <h5>
          Disabled <span class="fw-semi-bold">Buttons</span>
        </h5>
        <div class="widget-controls">
          <a data-widgster="expand" title="Expand" href="#"><i class="glyphicon glyphicon-chevron-up"></i></a>
          <a data-widgster="collapse" title="Collapse" href="#"><i class="glyphicon glyphicon-chevron-down"></i></a>
          <a href="#" data-widgster="close"><i class="glyphicon glyphicon-remove"></i></a>
        </div>
      </header>
      <div class="widget-body">
        <p class="fs-mini text-muted">
          Make buttons look unclickable by fading them back 50%.
          Add the <code>.disabled</code> class to <code>&lt;a&gt;</code> buttons.
        </p>
        <p>
          <button type="button" class="btn btn-primary" disabled="disabled">Primary button</button>
          <button type="button" class="btn btn-secondary" disabled="disabled">Button</button>
        </p>
        <p>
          <a href="#" class="btn btn-success btn-sm disabled" role="button">Primary link</a>
          <a href="#" class="btn btn-secondary btn-sm disabled" role="button">Link</a>
        </p>
      </div>
    </section>
  </div>
  <div class="col-xl-4 col-lg-6 col-xs-12">
    <section class="widget" widget>
      <header>
        <h5>
          Button <span class="fw-semi-bold">Groups</span>
        </h5>
        <div class="widget-controls">
          <a data-widgster="expand" title="Expand" href="#"><i class="glyphicon glyphicon-chevron-up"></i></a>
          <a data-widgster="collapse" title="Collapse" href="#"><i class="glyphicon glyphicon-chevron-down"></i></a>
          <a href="#" data-widgster="close"><i class="glyphicon glyphicon-remove"></i></a>
        </div>
      </header>
      <div class="widget-body">
        <p class="fs-mini text-muted">
          Group a series of buttons together on a single line with the button group.
          Add on optional JavaScript radio and checkbox style behavior with Bootstrap buttons plugin.
        </p>
        <div class="btn-group">
          <button type="button" class="btn btn-secondary">Left</button>
          <button type="button" class="btn btn-secondary">Middle</button>
          <button type="button" class="btn btn-secondary">Right</button>
        </div>
        <div class="btn-toolbar" role="toolbar">
          <div class="btn-group">
            <button type="button" class="btn btn-secondary">1</button>
            <button type="button" class="btn btn-secondary">2</button>
            <button type="button" class="btn btn-secondary">3</button>
            <button type="button" class="btn btn-secondary">4</button>
          </div>
          <div class="btn-group">
            <button type="button" class="btn btn-secondary">5</button>
            <button type="button" class="btn btn-secondary">6</button>
            <button type="button" class="btn btn-secondary">7</button>
          </div>
          <div class="btn-group">
            <button type="button" class="btn btn-secondary">8</button>
          </div>
        </div>
      </div>
    </section>
  </div>
  <div class="col-xl-4 col-lg-6 col-xs-12">
    <section class="widget" widget>
      <header>
        <h5>
          Button <span class="fw-semi-bold">Dropdowns</span>
        </h5>
        <div class="widget-controls">
          <a data-widgster="expand" title="Expand" href="#"><i class="glyphicon glyphicon-chevron-up"></i></a>
          <a data-widgster="collapse" title="Collapse" href="#"><i class="glyphicon glyphicon-chevron-down"></i></a>
          <a href="#" data-widgster="close"><i class="glyphicon glyphicon-remove"></i></a>
        </div>
      </header>
      <div class="widget-body">
        <p class="fs-mini text-muted">
          Add dropdown menus to nearly anything with this simple plugin, including the buttons,
          navbar, tabs, and pills.
          Both solid & segmented dropdown options available.
        </p>
        <div class="mb-sm">
          <div class="btn-group" dropdown>
            <button id="dropdown-btn-one" class="btn btn-danger" dropdownToggle>
              &nbsp; One &nbsp;
              <i class="fa fa-caret-down"></i>
            </button>
            <ul dropdownMenu role="menu" aria-labelledby="dropdown-btn-one">
              <li role="menuitem"><a class="dropdown-item" href="#">Action</a></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Another action</a></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Something else here</a></li>
              <li class="dropdown-divider"></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Separated link</a></li>
            </ul>
          </div>
          <div class="btn-group" dropdown>
            <button id="dropdown-btn-two" class="btn btn-gray btn-sm" dropdownToggle>
              &nbsp; One &nbsp;
              <i class="fa fa-caret-down"></i>
            </button>
            <ul dropdownMenu role="menu" aria-labelledby="dropdown-btn-two">
              <li role="menuitem"><a class="dropdown-item" href="#">Action</a></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Another action</a></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Something else here</a></li>
              <li class="dropdown-divider"></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Separated link</a></li>
            </ul>
          </div>
        </div>
        <div>
          <div class="btn-group" dropdown>
            <button id="dropdown-btn-three" class="btn btn-secondary">Gray</button>
            <button class="btn btn-secondary" dropdownToggle>
              <i class="fa fa-caret-down"></i>
            </button>
            <ul dropdownMenu role="menu" aria-labelledby="dropdown-btn-three">
              <li role="menuitem"><a class="dropdown-item" href="#">Action</a></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Another action</a></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Something else here</a></li>
              <li class="dropdown-divider"></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Separated link</a></li>
            </ul>
          </div>
          <div class="btn-group" dropdown>
            <button id="dropdown-btn-four" class="btn btn-gray btn-sm">Gray</button>
            <button class="btn btn-gray btn-sm" dropdownToggle>
              <i class="fa fa-caret-down"></i>
            </button>
            <ul dropdownMenu role="menu" aria-labelledby="dropdown-btn-four">
              <li role="menuitem"><a class="dropdown-item" href="#">Action</a></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Another action</a></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Something else here</a></li>
              <li class="dropdown-divider"></li>
              <li role="menuitem"><a class="dropdown-item" href="#">Separated link</a></li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
<section class="widget" widget>
  <header>
    <h6>
      Button <span class="fw-semi-bold">Options</span>
    </h6>
    <div class="widget-controls">
      <a data-widgster="expand" title="Expand" href="#"><i class="glyphicon glyphicon-chevron-up"></i></a>
      <a data-widgster="collapse" title="Collapse" href="#"><i class="glyphicon glyphicon-chevron-down"></i></a>
      <a href="#" data-widgster="close"><i class="glyphicon glyphicon-remove"></i></a>
    </div>
  </header>
  <div class="widget-body">
    <div class="row">
      <div class="col-xl-4 col-xs-12">
        <h4>
          Button <span class="fw-semi-bold">Checkboxes</span>
        </h4>
        <p class="fs-mini text-muted">
          Do more with buttons. Control button states or create groups of buttons for more components like toolbars.
          Add <code>data-toggle="buttons"</code> to a group of checkboxes for checkbox style toggling on btn-group.
        </p>
        <div class="mb-sm">
          <div class="btn-group">
            <label class="btn btn-gray" btnCheckbox [(ngModel)]="checkboxModel.left">
              Left way
            </label>
            <label class="btn btn-gray" btnCheckbox [(ngModel)]="checkboxModel.middle">
              Middle way
            </label>
            <label class="btn btn-gray" btnCheckbox [(ngModel)]="checkboxModel.right">
              Right way
            </label>
          </div>
        </div>
        <div class="mb-sm">
          <div class="btn-group btn-group-sm">
            <label class="btn btn-secondary" btnCheckbox [(ngModel)]="checkbox2Model.left">
              Left way
            </label>
            <label class="btn btn-secondary" btnCheckbox [(ngModel)]="checkbox2Model.middle">
              Middle way
            </label>
            <label class="btn btn-secondary" btnCheckbox [(ngModel)]="checkbox2Model.right">
              Right way
            </label>
          </div>
        </div>
      </div>
      <div class="col-xl-4 col-xs-12">
        <h4>
          Button <span class="fw-semi-bold">Radios</span>
        </h4>
        <p class="fs-mini text-muted">
          Do more with buttons. Control button states or create groups of buttons for more components like toolbars.
          Add <code>data-toggle="buttons"</code> to a group of radio inputs for radio style toggling on btn-group.
        </p>
        <div class="mb-sm">
          <div class="btn-group">
            <label class="btn btn-gray" btnRadio="'Left'" [(ngModel)]="radioModel">
              Left way
            </label>
            <label class="btn btn-gray" btnRadio="'Middle'" [(ngModel)]="radioModel">
              Middle way
            </label>
            <label class="btn btn-gray" btnRadio="'Right'" [(ngModel)]="radioModel">
              Right way
            </label>
          </div>
        </div>
        <div class="mb-sm">
          <div class="btn-group btn-group-sm">
            <label class="btn btn-secondary" btnRadio="'Left'" [(ngModel)]="radio2Model">
              Left way
            </label>
            <label class="btn btn-secondary" btnRadio="'Middle'" [(ngModel)]="radio2Model">
              Middle way
            </label>
            <label class="btn btn-secondary" btnRadio="'Right'" [(ngModel)]="radio2Model">
              Right way
            </label>
          </div>
        </div>
      </div>
      <div class="col-xl-4 col-xs-12">
        <h4>
          Use with <span class="fw-semi-bold">Icons</span>
        </h4>
        <p class="fs-mini text-muted">
          Fontawesome and Glyph- icons may be used in buttons, button groups for a toolbar, navigation, or prepended form inputs.
          Let your buttons shine!
        </p>
        <p class="text-xs-center">
          <button class="btn btn-secondary width-100 mb-xs" role="button">
            <i class="glyphicon glyphicon-tree-conifer text-success"></i>
            Forest
          </button>
          <button class="btn btn-secondary width-100 mb-xs" role="button">
            <i class="fa fa-check text-danger"></i>
            Submit
          </button>
          <button class="btn btn-secondary width-100 mb-xs" role="button">
            <i class="fa fa-facebook text-primary"></i>
            Login
          </button>
        </p>
        <p class="text-xs-center">
          <button class="btn btn-inverse width-100 mb-xs" role="button">
            <i class="fa fa-exclamation text-warning"></i>
            Error
          </button>
          <button class="btn btn-inverse width-100 mb-xs" role="button">
            <i class="glyphicon glyphicon-globe text-info"></i>
            <span class="text-info">Globe</span>
          </button>
          <button class="btn btn-inverse width-100 mb-xs" role="button">
                                <span class="circle bg-white">
                                    <i class="fa fa-map-marker text-gray"></i>
                                </span>
            Map
          </button>
        </p>
      </div>
    </div>
  </div>
</section>
