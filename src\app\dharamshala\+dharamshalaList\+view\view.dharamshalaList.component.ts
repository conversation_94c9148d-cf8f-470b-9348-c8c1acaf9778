import { ActivatedRoute } from '@angular/router';
import { Component, OnInit, OnDestroy, NgZone } from '@angular/core';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { GoogleMapService } from './../../../shared/services/google-map.service';
import { MapsAPILoader } from '@agm/core';
import { AuthGuard } from "../../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';

declare var google: any;
@Component({
    selector: 'view-dharamshala',
    templateUrl: './view.dharamshalaList.component.html'
})
export class ViewDharamshalaComponent implements OnInit {

    dharamshala: any;
    images: any;
    docs: any;
    external_link: any;

    lat: number = -37.813179;
	lng: number = 144.950259;
	zoom: number = 16;
	location_data = { coords: { lat: this.lat, lng: this.lng } };

    id: number;
    public markers: any[] = [];
    private sub: any;
    private getDharamshala : any;
    constructor(
        private _zone: NgZone,
        private auth: AuthGuard,
        private route: ActivatedRoute,
        private _loader: MapsAPILoader,
        private DS : DharamshalaService,
        private gmService: GoogleMapService,
        public translate: TranslateService
        ) {
        this.sub = this.route.params.subscribe(params => {
            this.id = +params['id'];
            this.getDharamshalaData();
            let currentLang = localStorage.getItem('currentLang'); // New Change ****
		    translate.setDefaultLang(currentLang);// New Change ****
        })
     }

    ngOnInit() {
    }

    getDharamshalaData(){
        this.getDharamshala = this.DS.getDharamshalaDetails(this.id)
        .subscribe((res) => {
            if(res.status == "success"){
                delete res.data.dharamshala.detail.external_link;
                this.dharamshala = res.data.dharamshala.detail;
                this.external_link = res.data.dharamshala.external_link;
                this.docs = res.data.docs;
                this.images = res.data.files;
            }
        })
    }

    ngOnDestroy(){
        this.sub.unsubscribe();
    }

}