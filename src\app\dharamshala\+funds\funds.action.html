<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-inr"></i>&nbsp;&nbsp;{{pageType}} {{'FUNDS.ADD_PAGE.FUND_TYPE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="fundsTypeForm" (ngSubmit)="saveFundType()">
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'FUNDS.ADD_PAGE.NAME' | translate:param}}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="fundsTypeForm.controls.name.errors?.backend">{{fundsTypeForm.controls.name.errors?.backend}}</span>
            <input type="text"  class="form-control" formControlName="name" name="name" placeholder="">
            <span class="errMsg" *ngIf="!fundsTypeForm.controls.name.valid && !fundsTypeForm.controls.name.untouched">
              <span [hidden]="!fundsTypeForm.controls.name.errors.required">{{'FUNDS.ADD_PAGE.VALID_MSG.NAME_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>

        <div class="form-group row">
          <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'FUNDS.ADD_PAGE.STATUS' | translate:param}}</label>
          <div class="col-md-8 ">
            <div class="radio-horizontal">
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-1" [value]="true">
                <label for="radio-1">
                  {{'FUNDS.ADD_PAGE.ACTIVE' | translate:param}}
                      </label>
              </div>
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-2" [value]="false">
                <label for="radio-2">
                  {{'FUNDS.ADD_PAGE.INACTIVE' | translate:param}}
                      </label>
              </div>
            </div>
          </div>
        </div>

        <div class="form-group row">
          <div class="col-md-8 offset-md-3">
            <div class="">
              <button type="submit" [disabled]="!fundsTypeForm.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'FUNDS.ADD_PAGE.SAVE' | translate:param}}</button>
              <button type="button" (click)="hideComponent()" class="btn btn-sm btn-secondary">{{'FUNDS.ADD_PAGE.CANCEL' | translate:param}}</button>
            </div>
          </div>
        </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
