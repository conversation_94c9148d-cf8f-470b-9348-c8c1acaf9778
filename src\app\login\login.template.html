<div class="container">
  <main id="content" class="widget-login-container" role="main">
    <div class="row">
      <div class="col-xl-4 col-md-6 col-xs-10 offset-xl-4 offset-md-3 offset-xs-1">
        <h5 class="widget-login-logo animated fadeInUp">
          <i class="fa fa-circle text-gray"></i> {{'LOGIN.DHARAMSHALA' | translate:param}}
          <i class="fa fa-circle text-warning"></i>
        </h5>
        <section class="widget widget-login animated fadeInUp">
          <header>
            <h3>{{'LOGIN.LOGIN_ACC' | translate:param}}</h3>
          </header>
          <div class="widget-body">


            <form class="login-form mt-lg" [formGroup]="userLogin" (ngSubmit)="login()">
              <div class="form-group">
                <input type="text" formControlName="email" class="form-control" placeholder="Email">
                <span class="errMsg" *ngIf="!email.valid && !email.untouched"><span [hidden]="!email.errors?.required">{{'LOGIN.EMAIL_REQ' | translate:param}}</span></span>
              </div>
              <div class="form-group">
                <input class="form-control" type="password" formControlName="password" placeholder="Password">
                <span class="errMsg" *ngIf="!password.valid && !password.untouched">
                    <span [hidden]="!password.errors?.required">{{'LOGIN.PASS_REQ' | translate:param}}</span>
                </span>
              </div>
              <div class="clearfix">
                <div class="row m-t-1">
                  <div class="col-md-12">
                    <div class="col-md-12">
                      <div class="abc-checkbox widget-login-info float-xs-left">
                        <input type="checkbox" id="checkbox1" formControlName="rememberMe" value="true">
                        <label for="checkbox1">{{'LOGIN.REM_ME' | translate:param}}</label>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="btn-toolbar float-xs-left m-t-1">
                  <button class="btn btn-inverse btn-sm" [disabled]="!userLogin.valid" type="submit">{{'LOGIN.LOGINN' | translate:param}}</button> &nbsp;&nbsp;
                  <a [routerLink]="['../resetpassword']">{{'LOGIN.FORG_PASS' | translate:param}}?</a>
                </div>
              </div>

            </form>
          </div>
        </section>
      </div>
    </div>
  </main>
  <footer class="page-footer">
    2017 &copy; {{'LOGIN.DHARAMSHALA' | translate:param}}
  </footer>
</div>
