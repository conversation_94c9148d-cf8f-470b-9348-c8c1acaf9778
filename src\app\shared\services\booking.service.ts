import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()
export class BookingService {
    constructor(private chttp: CommonHttpService) { }
    getSpliteDataAdvanc(data) {
        return this.chttp.post(`booking/edit/split/advanceReceipt`, data)
    }
    getSpliteData(data) {
        return this.chttp.post(`booking/edit/split/receipt`, data)
    }
    getRoomCat(month, year) {
        return this.chttp.get(`roombookingsetting/list/${month}/${year}`);
    }
    saveRoombookingSettings(data) {
        return this.chttp.post(`roombookingsetting/add`, data, false)
    }

    markRoomUnderMaintenance(data) {
        return this.chttp.post('booking/add/room/maintenance', data, true);
    }

    markRoomAsActive(bookingId) {
        return this.chttp.get(`booking/add/room/maintenance/active/${bookingId}`, true);
    }

    getAllPolicies() {
        return this.chttp.get(`policy/list/get?type=all`, false);
    }
    getActivePolicies(type: string) {
        return this.chttp.get(`policy/list/get?type=${type}`, false);
    }
    savePlocy(data) {
        return this.chttp.post(`policy/add`, data, true);
    }
    updatePolicy(id, data) {
        return this.chttp.post(`policy/edit/${id}`, data, true)
    }
    updateCancellationReference(id, data) {
        return this.chttp.post(`policy/edit/${id}/reference`, data, true);
    }
    updateCancellationNote(id, data) {
        return this.chttp.post(`policy/edit/${id}/note`, data, true);
    }
    getAllExtraChanges() {
        return this.chttp.get(`extracharge/list`, false);
    }
    saveExtraChages(data) {
        return this.chttp.post(`extracharge/add`, data, true);
    }
    UpdateExtraChages(id, data) {
        return this.chttp.post(`extracharge/edit/${id}`, data, true)
    }
    //booking module
    getBookingReportFilters() {
        return this.chttp.get(`booking/list/report/filters`);
    }
    getBookingReport(type: any, data: any) {
        if (type == 'checkout')
            return this.chttp.post('report/getCheckoutReport?type=' + type, data, false)
        if (type == 'pending-payments')
            return this.chttp.post('report/getPendingPaymentReport?type=' + type, data, false)
        return this.chttp.post(`booking/list/report?type=${type}`, data, false);
    }
    getAdvancePaymentReport(data: any) {
        return this.chttp.post(`booking/list/advancepaymentreport`, data, false);
    }
    getAdvanceBookingBillDetails(id) {
        return this.chttp.get(`booking/view/advancepayment/${id}`);
    }

    geCustomDiscountBookingReport(type: any, data: any) {
        return this.chttp.post(`booking/list/report/discount?type=${type}`, data, false);
    }
    getCategoryWiseAvailabilityReport(data: any) {
        return this.chttp.post(`booking/list/report/category_wise_availability`, data, false);
    }
    getPendingPaymentCollectionsReport(data: any) {
        return this.chttp.post(`booking/list/report/pending_payment_collections`, data, false);
    }
    getRoomMaintenanceReport(data: any) {
        return this.chttp.post(`booking/list/report/room_maintenance`, data, false);
    }
    getRoomUnderMaintenanceReport() {
        return this.chttp.get(`booking/list/report/roomundermaintenance`, false);
    }
    getCsvReport(data: any, type: string) {
        return this.chttp.post(`booking/list/report/csv?type=${type}`, data, true, true);
    }
    getAdvanceCsvReport(data: any) {
        return this.chttp.post(`booking/list/csvv`, data, true, true);
    }
    getAccountCsvReport(data: any) {
        return this.chttp.post(`booking/list/accountreportcsv`, data, false, false, true);
    }
    getBookingList() {
        return this.chttp.get(`booking/list`, false);
    }
    getBookingsByRange(data) {
        return this.chttp.post(`booking/list/by_range`, data, false);
    }
    getallBookingInfo() {
        return this.chttp.get(`booking/list/reservation`, true);
    }
    getRoomInfo() {
        return this.chttp.get(`booking/list/roomcategory/room`, false);
    }
    getCustomerTypes() {
        return this.chttp.get(`booking/add/customer/referenceuser/list`, false);
    }
    getRoomCharges(data) {
        return this.chttp.post(`booking/add/roomcategory/charges`, data, false);
    }
    getBookingBillDetails(id) {
        return this.chttp.get(`booking/view/bill/${id}`);
    }
    getOverBookingNote(id: number, date: string) {
        return this.chttp.get(`booking/edit/overbooking/list/${id}?notedate=${date}`, false);
    }
    getRoomCategoryOverBookingList(catId: number, date: string) {
        return this.chttp.get(`booking/edit/overbooking/list/${catId}?notedate=${date}`, false);
    }
    confirmSingleOverBooking(data: any) {
        return this.chttp.post(`booking/edit/overbooking/confirm/${data.booking_id}`, data, true);
    }
    deleteSingleOverBooking(data: any) {
        return this.chttp.post(`booking/edit/overbooking/delete/${data.booking_id}`, data, true);
    }
    addEditSingleOverBooking(data: any) {
        return this.chttp.post(`booking/edit/overbooking/add`, data, true);
    }
    getCRBookingList(id: number, date: string) {
        return this.chttp.get(`booking/add/common/${id}?date=${date}`, false);
    }
    saveThisBooking(data) {
        return this.chttp.post(`booking/add`, data, true);
    }
    GetAvailableCategory() {
        return this.chttp.get(`onlinebooking/findavilablecategory`)
    }
    // checkin / checkout
    executeCheckIn(id, room_id, data: any) {
        return this.chttp.post(`booking/edit/checkin/${id}/${room_id}`, data, true);
    }
    getCheckoutDetails(customerCheck: any, id, type: string, customerId: any, pendingPayment = false, date?: string) {
        let query = (type == 'payment') ? true : false;
        let showAlert = type === 'payment' ? false : true;
        let dateQueryString = date ? `&date=${date}` : '';
        if (type == 'payment' && !pendingPayment) {
            return this.chttp.post(`booking/edit/checkout/getdetails/${id}/payment?payment=${query}&customer=${customerId}${dateQueryString}`, customerCheck, showAlert);
        } else {
            return this.chttp.post(`booking/edit/checkout/getdetails/${id}?payment=${query}&customer=${customerId}${dateQueryString}`, customerCheck, showAlert);
        }
    }
    changeBookingGuestType(bookingId, data, date) {
        return this.chttp.post(`booking/edit/customer/edit/${bookingId}?date=${date}`, data, true);
    }
    guestRequiredFieldsCheck(id) {
        return this.chttp.get(`booking/edit/precheckoutcheck/${id}`);
    }
    getBookingPayments(id) {
        return this.chttp.get(`booking/view/payments/${id}`, false);
    }
    payFinalPayments(id, data: any, customerId: number) {
        return this.chttp.post(`booking/edit/checkout/${id}/${customerId}`, data, true);
    }
    getInvidualRoomCheckout(bookingId, roomId) {
        return this.chttp.get(`booking/edit/individual/room/checkout/${bookingId}/${roomId}`);
    }
    individualRoomCheckout(id, data) {
        return this.chttp.post(`booking/edit/individual/room/checkout/${id}`, data, true);
    }
    saveCustomerDetailsForCheckIn(guest_id, data) {
        return this.chttp.post(`guest/edit/${guest_id}`, data, true);
    }
    // view booking
    viewBookingDetails(id) {
        return this.chttp.get(`booking/view/${id}`, false);
    }
    viewOnlineBookingDetails(id) {
        return this.chttp.get(`onlineBooking/view/${id}`, false);
    }
    getBookingDateDetails(id) {
        return this.chttp.get(`booking/add/date/${id}`, false);
    }
    saveBookingNote(id, data) {
        return this.chttp.post(`booking/edit/note/add/${id}`, data, true);
    }
    getBookingNote(id) {
        return this.chttp.get(`booking/edit/note/add/${id}`, false);
    }
    // cancel booking
    processCheckCancellation(room_id, booking_id, policy_id) {
        return this.chttp.get(`booking/edit/cancel/${booking_id}/${room_id}/${policy_id}`, false);
    }
    processCancelBooking(data) {
        return this.chttp.post(`booking/edit/cancel/${data.booking_id}/${data.room_id}/${data.policy_id}?is_no_show=${data.is_no_show}`,
            data.validationCheck, false);
    }
    addAlternateBillingGuest(booking_id, data) {
        return this.chttp.post(`booking/add/alternate_billing_guest/${booking_id}`, data, true);
    }
    //guest
    getGuestInfo(id) {
        return this.chttp.get(`booking/edit/guest/edit/${id}`, false);
    }
    //guest
    deleteBookingGuest(bookingId: number, guestId: number, roomId: number) {
        return this.chttp.get(`booking/edit/guest/delete/${bookingId}/${guestId}/${roomId}`, true);
    }
    saveGuest(data: any, bookingId: number) {
        return this.chttp.post(`guest/edit/extraguest/edit/${bookingId}`, data, true);
    }
    addGuest(id: any, data: any) {
        return this.chttp.post(`booking/edit/guest/add/${id}`, data, true);
    }
    getAddGuestParams(bookingId: any, roomCategoryId: any, customerId: any, room_id: any) {
        return this.chttp.get(`booking/edit/guest/add/${bookingId}/${roomCategoryId}/${customerId}/${room_id}`, true);
    }
    //split booking
    getRoomAvailibility(data) {
        return this.chttp.post(`booking/edit/split/checkfreeroom`, data, false);
    }
    saveSplitBooking(id, roomId, data) {
        console.log("inside split booking", data);

        return this.chttp.post(`booking/edit/${id}/${roomId}/split`, data, true);
    }

    saveTransferRoomBooking(id, roomId, data) {
        console.log("inside Transfer Room booking", data);
        let {dates: [datas]} = data
        return this.chttp.post(`booking/edit/transfer`, datas, true);
    }


    getCustomerList(data: any) {
        return this.chttp.post('booking/add/guest/search', data, false);
    }
    updatePayment(id, data, type) {
        if (type === 'pending-payment') {
            return this.chttp.post(`booking/edit/${id}/pendingpayment`, data, true);
        } else {
            return this.chttp.post(`booking/edit/${id}/payment`, data, true);
        }
    }
    getCurrentGuestReport(type: string, data: any) {
        return this.chttp.post(`booking/list/report/guest?type=${type}`, data, false);
    }
    getRevenueReport(data: any, type: string, reference?: any, customer?: any) {
        let query = '';
        query += `?type=${type}`;
        if (reference) {
            query += `&reference=${reference}`;
        }
        if (customer) {
            query += `&customer=${customer}`;
        }
        return this.chttp.post(`booking/list/report/revenue${query}`, data, false);
    }
    saveHtmltoPDF(bookingId: number, data: any) {
        return this.chttp.post(`booking/list/pdf/bill/${bookingId}`, data, false, false, true);
    }
    getFileBase64(data: any) {
        return this.chttp.post(`booking/list/document/base64`, data, false, false, true);
    }
    readCard(data: any) {
        return this.chttp.doorLockPost(`readCard`, data);
    }
    writeCard(data: any) {
        return this.chttp.doorLockPost(`writeCard`, data);
    }
    customDiscount(bookingId, data) {
        return this.chttp.post(`booking/edit/customdiscount/${bookingId}`, data, true);
    }
    getCollectionBreakupReport(data: any) {
        return this.chttp.post(`booking/list/report/collectionbreakup`, data, false);
    }

    getAccountReport(data: any) {
        return this.chttp.post(`booking/list/report/account`, data, false);
    }

    DownlloadBill() {
        return this.chttp.get('bookingbill/Download')
    }



    /* ----- Endpoints for the online booking request ----- */
    //view particular data
    viewOnlineBooking(id: any) {
        return this.chttp.get(`onlinebooking/view/${id}`);
    }
    //cancel booking data
    cancelOnlineBooking(id: number, reason: any) {
        return this.chttp.post(`onlinebooking/cancel/${id}`, { reason: reason });
    }
    //list online bookings
    getOnlineBookingList() {
        return this.chttp.get(`onlinebooking/list`);
    }
    //save particular online booking data
    saveOnlineBookingList(id: any, data: any) {
        return this.chttp.post(`onlinebooking/save/${id}`, data, true);
    }
    //aprove onlinebooking data
    approveOnlineBooking(data: any)  {
        return this.chttp.post(`onlinebooking/approve`,{data: data});
    }
    getAvailableRooms(data: any){
        return this.chttp.post('onlinebooking/listrooms',data)
    }
    transferBookingRoom(data:any){
        return this.chttp.post('onlinebooking/transfer',data)
    }
}
