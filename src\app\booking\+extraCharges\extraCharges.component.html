<div class="row">
  <div class="" [ngClass]="{'col-md-12': (hiddenAdd && hiddenEdit),'col-md-6': (!hiddenAdd || !hiddenEdit)}">
    <section class="widget">
      <header>
        <h4><span class="" style="color: red;"><i class="fa fa-inr"></i>&nbsp;&nbsp;{{ 'MANAGE_EXTRA_CHARGES.MANAGE_EXT_CHARGE' | translate:param }}</span></h4>
      </header>
      <hr class="large-hr">
      <div class="float-sm-right text-right col-sm-12">
        <button
          *ngIf="auth.roleAccessPermission('extracharge','add')"
          class="display-inline-block btn btn-sm btn-inverse" 
          (click)="showAdd()" 
          [disabled]="!hiddenAdd || !hiddenEdit" 
          tooltip="{{ 'MANAGE_EXTRA_CHARGES.ADD_NEW_EXT_CHARGE' | translate:param }}" placement="top">
          <i class="fa fa-plus" ></i>&nbsp;&nbsp;{{ 'MANAGE_EXTRA_CHARGES.ADD' | translate:param }}
        </button>
        <div class="form-group display-inline-block __search">
          <input type="text" class="form-control" placeholder="{{ 'MANAGE_EXTRA_CHARGES.SEARCH' | translate:param }}" (keyup)="canViewRecords ? search() : null" [(ngModel)]="searchQuery">
          <span class="form-group-addon"><i class="fa fa-search"></i></span>
          <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
        </div>
      </div>
      <div class="clearfix"></div>
      <div class="widget-body table-scroll">
        <div class="mt">

          <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
            <thead>
              <tr>
                <th>
                  <mfDefaultSorter by="id">#</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="name">{{ 'MANAGE_EXTRA_CHARGES.NAME' | translate:param }}</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="charge">{{ 'MANAGE_EXTRA_CHARGES.CHARGE' | translate:param }}</mfDefaultSorter>
                </th>
                <th class="no-sort text-center">
                  <mfDefaultSorter by="status">{{ 'MANAGE_EXTRA_CHARGES.STATUS' | translate:param }}</mfDefaultSorter>
                </th>
                <th *ngIf="auth.roleAccessPermission('extracharge','edit')" class="no-sort text-center">
                  <mfDefaultSorter by="status">{{ 'MANAGE_EXTRA_CHARGES.ACTION' | translate:param }}</mfDefaultSorter>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let ds of mf.data; let i =index" [ngClass]="{'__selected': (ds.id == selectedElement?.id)}">
                <td>{{findIndex(ds.id,"id")}}</td>
                <td><span class="fw-semi-bold">{{ds.name}}</span></td>
                <td><span class=""><i class="fa fa-inr"></i>{{ds.charge}}</span></td>
                <td class="text-center">
                  <span class="text-success" *ngIf="ds.status">{{ 'MANAGE_EXTRA_CHARGES.ACTIVE' | translate:param }}</span>
                  <span class="text-danger" *ngIf="!ds.status">{{ 'MANAGE_EXTRA_CHARGES.INACTIVE' | translate:param }}</span>
                </td>
                <td *ngIf="auth.roleAccessPermission('extracharge','edit')" class="width-100 text-center">
                  <button (click)="showEdit(ds)" class="btn btn-xs btn-default" tooltip="{{ 'MANAGE_EXTRA_CHARGES.EDIT_EXTRA_CHARGES' | translate:param }}" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{ 'MANAGE_EXTRA_CHARGES.EDIT' | translate:param }}</button>
                </td>
              </tr>
              <tr *ngIf="canViewRecords && mf.data.length === 0">
                <td colspan="100">
                  {{ 'MANAGE_EXTRA_CHARGES.NO MATCHESE' | translate:param }}
                </td>
              </tr>
              <tr *ngIf="!canViewRecords">
                <td class="text-danger" colspan="100">
                  {{ 'MANAGE_EXTRA_CHARGES.PERMISSION_DENIED' | translate:param }}
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="12">
                  <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </section>
  </div>
 
    <add-extra-charges class="col-md-6" *ngIf="!hiddenAdd && hiddenEdit"
    (sendAdded)="addToData($event)" (closeAdd)="closeThisComp($event)"
    ></add-extra-charges>
    
    <edit-extra-charges class="col-md-6" *ngIf="!hiddenEdit && hiddenAdd"
    [data]="selectedElement" (sendEdited)="updateToList($event)" (closeEdit)="closeThisComp($event)" ></edit-extra-charges>
</div>
