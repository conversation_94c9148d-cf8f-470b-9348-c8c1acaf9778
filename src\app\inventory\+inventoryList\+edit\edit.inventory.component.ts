import { ActivatedRoute } from '@angular/router';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { inventoryTypeData, inventoryList } from './../../data';

@Component({
    selector: 'edit-inventory',
    templateUrl: '../inventoryList.actions.html'
})

export class EditInventoryComponent implements OnInit {
    pageType: string = "Edit";
    loadingStatus: number = 0;
    inventoryAdd: FormGroup;
    private sub: any;
    id: number;
    selectionDefaults: string;
    selectedInventory: any;
    // select 2 options
    inventoryTypeOptions: any = {
        width: '100%',
        minimumResultsForSearch: 7
    };
    status: any[] = [
        'Active', 'Inactive'
    ];

    constructor(
        private route: ActivatedRoute,
        private _fb: FormBuilder
    ) {

        this.sub = this.route.params.subscribe(params => {
            this.id = params['id'];
            this.selectedInventory = inventoryList[this.id - 1];
            // build form method
            this.buildForm()
            // patching default values
            this.inventoryAdd.patchValue(this.selectedInventory);
            // selection defaults
            this.selectionDefaults = this.inventoryAdd.controls['type'].value;
        });
    }

    buildForm() {
        this.inventoryAdd = this._fb.group({
            name: ['', Validators.required],
            type: ['', Validators.required],
            status: ['', Validators.required],
            vandor: this._fb.array([

            ])
        })
        this.settingUpFormArray(this.selectedInventory.vandor)
    }
    ngOnInit() { }

    saveInventory() {
        this.loadingStatus = 1;
        console.log(this.inventoryAdd.value)
    }
    getInventoryType() {
        return inventoryTypeData
    }
    getInventoryTypeChanged(event) {
        this.inventoryAdd.patchValue({ type: event.value });
    }

    settingUpFormArray(data: any) {
        const control = <FormArray>this.inventoryAdd.controls['vandor'];
        for (let i = 0; i < data.length; i++) {
            control.push(this.initVandor(data[i]))
        }
    }

    initVandor(val) {
        // initialize our address
        return this._fb.group({
            title: [val, Validators.required]
        });
    }
    // add vandor event
    addVandor(van) {
        const control = <FormArray>this.inventoryAdd.controls['vandor'];
        if (van.value && /\S/.test(van.value)) {
            control.push(this.initVandor(van.value));
            van.value = "";
            van.focus();
        }
    }
    // remove vandor event
    removeVandor(index) {
        const control = <FormArray>this.inventoryAdd.controls['vandor'];
        control.removeAt(index)
    }

    ngOnDestroy() {
        this.sub.unsubscribe();
    }

}