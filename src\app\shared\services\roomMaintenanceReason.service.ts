import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()

export class RoomMaintenanceReasonService {
    constructor(
        private chttp: CommonHttpService
    ) { }


    saveRoomMaintenanceData(data){
       return this.chttp.post(`maintenancereason/add`, data, true);
    }
    getAllRoomMaintenanceReasonList(){
        return this.chttp.get(`maintenancereason/list`);
    }

    updateRoomMaintenanceData(id,data){
        return this.chttp.post(`maintenancereason/edit/${id}`, data, true);
    }
}