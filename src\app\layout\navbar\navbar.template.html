<div class="container-fluid">
  <!-- .navbar-header contains links seen on xs & sm screens -->
  <div class="navbar-header">
    <ul class="nav navbar-nav">
      <li class="nav-item">
        <ng-template #sidebarToggleTooltip>
          {{'NAVBAR.TUR_ON_OFF' | translate:param}} <br> {{'NAVBAR.SIDEBAR' | translate:param}} <br>{{'NAVBAR.COLLAPSING' | translate:param}}
        </ng-template>
        <!-- whether to automatically collapse sidebar on mouseleave. If activated acts more like usual admin templates -->
        <a (click)="toggleSidebar('static')" class="nav-link hidden-md-down" [tooltip]="sidebarToggleTooltip" placement="bottom">
          <i class="fa fa-bars fa-lg"></i>
        </a>
        <!-- shown on xs & sm screen. collapses and expands navigation -->
        <a (click)="toggleSidebar('collapse')" class="hidden-lg-up nav-link" href="#" data-html="true" title="Show/hide<br>sidebar"
          data-placement="bottom">
          <span class="rounded rounded-lg bg-gray text-white hidden-md-up"><i class="fa fa-bars fa-lg"></i></span>
          <i class="fa fa-bars fa-lg hidden-sm-down"></i>
        </a>
      </li>
    </ul>
    <ul class="nav navbar-nav navbar-right hidden-md-up" (click)="$event.preventDefault()">
      <!-- <li class="nav-item"> -->
      <!-- toggles chat -->
      <!-- <a class="nav-link" href="#" data-toggle="dropdown">
          <span class="rounded rounded-lg bg-gray text-white"><i class="fa fa-globe fa-lg"></i></span>
        </a> -->
      <!-- </li> -->
      <li class="nav-item dropdown" [class.open]="isDropdownOpen">
        <a href class="nav-link dropdown-toggle" data-toggle="dropdown" (click)="toggleDropdown($event)">
          &nbsp; Hello,
          <strong class="capitalize">{{ ud.userdata.first_name }} {{ ud.userdata.last_name }}</strong>&nbsp;
          <b class="caret"></b>
        </a>
        <ul class="dropdown-menu dropdown-menu-right">
          <li>
            <a class="dropdown-item" [routerLink]="['./settings','profile']">
              <i class="fa fa-user"></i> &nbsp; {{'NAVBAR.MY_PROF' | translate:param}}</a>
          </li>

          <li>
            <a class="dropdown-item" [routerLink]=" ['./settings','default'] ">
              <i class="fa fa-wrench"></i> &nbsp; {{'NAVBAR.DEF_SET' | translate:param}}</a>
          </li>

          <li>
            <a class="dropdown-item" [routerLink]=" ['./settings','change-password'] ">
              <i class="fa fa-lock"></i> &nbsp; {{'NAVBAR.CHANGE_PA' | translate:param}}</a>
          </li>

          <li class="dropdown-divider"></li>
          <li>
            <a class="dropdown-item" href="javascript:void(0)" (click)="shiftOut()">
              <i class="fa fa-clock-o"></i> &nbsp; {{'NAVBAR.SHIFTT_OUT' | translate:param}}</a>
          </li>
          <li>
            <a class="dropdown-item" href="javascript:void(0)" (click)="logout()">
              <i class="fa fa-sign-out"></i> &nbsp; {{'NAVBAR.LOG_OUT' | translate:param}}</a>
          </li>
        </ul>
      </li>
    </ul>
    <!-- <a class="navbar-brand hidden-md-up" [routerLink]=" ['/app/dashboard'] ">
      <i class="fa fa-circle text-gray mr-n-sm"></i>
      <i class="fa fa-circle text-warning"></i>
      &nbsp;
      {{config.name}}
      &nbsp;
      <i class="fa fa-circle text-warning mr-n-sm"></i>
      <i class="fa fa-circle text-gray"></i>
    </a> -->
  </div>
  <!-- this part is hidden for xs screens -->
  <div class="collapse navbar-collapse">

    <ul class="nav navbar-nav float-xs-right" (click)="$event.preventDefault()">

      <li class="nav-item dropdown" [class.open]="isDropdownOpen">
        <a href class="nav-link dropdown-toggle" data-toggle="dropdown" (click)="toggleDropdown($event)" >
          &nbsp;
          {{'NAVBAR.HELLO' | translate:param}}, <strong class="capitalize">{{ ud.userdata.first_name }} {{ ud.userdata.last_name }}</strong>&nbsp;
          <b class="caret"></b>
        </a>
        <ul class="dropdown-menu dropdown-menu-right">
          <li><a class="dropdown-item" [routerLink]="['./settings','profile']"><i class="fa fa-user"></i> &nbsp;{{'NAVBAR.PROF' | translate:param}}</a></li>

          <li><a class="dropdown-item" [routerLink]=" ['./settings','default'] "> <i class="fa fa-wrench"></i> &nbsp;
              {{'NAVBAR.DEF_SET' | translate:param}}</a></li>

          <li>
            <a class="dropdown-item" [routerLink]=" ['./settings','change-password'] "> <i class="fa fa-lock"></i>
              &nbsp; {{'NAVBAR.CHANGE_PA' | translate:param}}</a>
          </li>

          <li class="dropdown-divider"></li>
          <li *ngIf="authGuard.getUserShift()">
            <a class="dropdown-item" href="javascript:void(0)" (click)="shiftOut()">
              <i class="fa fa-clock-o"></i> &nbsp; {{'NAVBAR.SHIFTT_OUT' | translate:param}}</a>
          </li>
          <li><a class="dropdown-item" href="javascript:void(0)" (click)="logout()"><i class="fa fa-sign-out"></i>
              &nbsp;{{'NAVBAR.LOG_OUT' | translate:param}}</a></li>
        </ul>
      </li>

    </ul>
  </div>
</div>