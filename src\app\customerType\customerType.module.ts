import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { CustomerTypeService } from './../shared/services/customerType.service';
import { DataTableModule } from 'angular2-datatable';
import { CustomerListComponent } from './+customerTypeList/customerType.component';
import { AddCustomerTypeComponent } from './+customerTypeList/+add/add.customerType.component';
import { EditCustomerTypeComponent } from './+customerTypeList/+edit/edit.customerType.component';

import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';


// all routes related to this Role module.
export const routes = [
  { path: '', component: CustomerListComponent, pathMatch: 'full' }
//   { path: 'add', component: AddCustomerTypeComponent },
//   { path: 'edit/:id', component: EditCustomerTypeComponent }
];

@NgModule({
    imports: [
        FormsModule,
        CommonModule,
        TooltipModule.forRoot(),
        DataTableModule,
        RouterModule.forChild(routes),
        ReactiveFormsModule,
        TranslateModule.forRoot({
        loader:{ 
            provide: TranslateLoader, 
            useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
            deps: [HttpClient] 
        }})
    ],
    exports: [],
    declarations: [
        CustomerListComponent,
        AddCustomerTypeComponent,
        EditCustomerTypeComponent
    ],
    providers: [CustomerTypeService],
})
export class CustomerTypeModule { }
