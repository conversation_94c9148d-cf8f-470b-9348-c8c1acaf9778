<div *ngIf="selectedReport && paymentAtcheckout" class="widget table-scroll">
    <div class="row payroll">
        <div class="col-sm-7">
            <h3>{{'REM_PAY_PEP.REM_PAY' | translate:param}}</h3>
            <form [formGroup]="paymentAtcheckout"
                (ngSubmit)="paymentAtcheckoutProcess(selectedReport, selectedReport.customer_id)">
                <table class="table table-strips">
                    <tr>
                        <td>{{'REM_PAY_PEP.NET_AMT' | translate:param}}
                            <small *ngIf="selectedReport.payments.cardSwipeCharges">
                                ({{'REM_PAY_PEP.WITH' | translate:param}}
                                <i class="fa fa-inr"></i>
                                &nbsp;{{selectedReport.payments.cardSwipeCharges}}
                                {{'REM_PAY_PEP.CAR_SWIP_CHAR' | translate:param}})
                            </small>
                        </td>
                        <td class="text-right">
                            <i class="fa fa-inr"></i>&nbsp;{{selectedReport.payments.net_amount | number:'1.2-2'}}</td>
                    </tr>
                    <tr *ngIf="selectedReport.payments.guest_stay_Amount">
                        <td>
                            {{'REM_PAY_PEP.EXT_GUE_CHAR' | translate:param}}
                        </td>
                        <td class="text-right">
                            <i
                                class="fa fa-inr"></i>&nbsp;{{selectedReport.payments.guest_stay_Amount | number:'1.2-2'}}
                        </td>
                    </tr>
                    <tr
                        *ngIf="selectedReport.payments.is_early_checkout && selectedReport.payments.prev_can_charge && (selectedReport.payments.prev_can_charge > 0)">
                        <td>
                            {{'REM_PAY_PEP.PRE_CAN_CHAR' | translate:param}}
                        </td>
                        <td class="text-right">
                            <i class="fa fa-inr"></i>&nbsp;{{selectedReport.payments.prev_can_charge | number:'1.2-2'}}
                        </td>
                    </tr>
                    <tr *ngIf="selectedReport.payments.is_early_checkout">
                        <td>
                            {{'REM_PAY_PEP.CAN_CHARGE' | translate:param}} (
                            {{selectedReport.payments.cancellation_charge}}% )
                        </td>
                        <td class="text-right">
                            <i
                                class="fa fa-inr"></i>&nbsp;{{selectedReport.payments.cancellation_payable_amount | number:'1.2-2'}}
                        </td>
                    </tr>
                    <tr *ngIf="selectedReport.payments.is_early_checkout">
                        <td>
                            {{'REM_PAY_PEP.PAY_AMT_CAN' | translate:param}}
                        </td>
                        <td class="text-right">
                            <span style="color: green;">(+)
                            <i
                                class="fa fa-inr"></i>&nbsp;{{(selectedReport.payments.total_payable_amount) | number:'1.2-2'}}
                             </span>
                        </td>
                    </tr>
                    <tr *ngIf="selectedReport.payments.early_checkin_Charge">
                        <td>
                            {{'REM_PAY_PEP.EA_CHEK_CHAR' | translate:param}}
                        </td>
                        <td class="text-right">
                            <span style="color: green;">(+)
                            <i
                                class="fa fa-inr"></i>&nbsp;{{selectedReport.payments.early_checkin_Charge | number:'1.2-2'}}
                            </span>
                        </td>
                    </tr>
                    <tr *ngFor="let payment of selectedReport.payments.bookingPayment">
                        <td *ngIf="payment.bill_no != null">
                            {{payment.is_advance ? 'Advance Payment' : 'Payment'}}
                            <small>
                                <i *ngIf="payment">({{payment ? paymentTypeList[payment.payment_mode].text : ''}}
                                    <span *ngIf="payment?.payment_mode == 1">
                                        - #{{payment.payment_reciept_number}}</span> on {{payment.payment_date | date}}
                                    )
                                </i>
                                <span *ngIf="payment.card_charge">
                                    with
                                    <i class="fa fa-inr"></i>
                                    {{payment.amount_with_card_charge}} Card Charges.</span>
                            </small>
                        </td>
                        <td class="text-right" *ngIf="payment.bill_no != null">
                            <span style="color: red;">(-)
                            <i class="fa fa-inr"></i>&nbsp;
                            {{payment.amount_with_card_charge + payment.amount | number:'1.2-2'}}
                            </span>
                        </td>
                    </tr>
                    <tr *ngIf="selectedReport.payments.bookingCustomDiscounts1">
                        <td>
                            {{'REM_PAY_PEP.CUST_DISCS' | translate:param}}
                        </td>
                        <td class="text-right">
                             <span style="color: red;"> (-)
                            <i
                                class="fa fa-inr"></i>&nbsp;{{selectedReport.payments.bookingCustomDiscounts1 | number:'1.2-2'}}
                            </span>
                        </td>
                    </tr>
                    <tr *ngIf="selectedReport.payments.returnAmount">
                        <td>
                            {{'REM_PAY_PEP.RETED_AMT' | translate:param}}
                        </td>
                        <td class="text-right">
                            <span style="color: green;"> (+)
                            <i class="fa fa-inr"></i>&nbsp;{{selectedReport.payments.returnAmount | number:'1.2-2'}}
                            </span>
                        </td>
                    </tr>
                    <tr *ngFor="let fund of selectedReport.payments.bookingFund">
                        <td>
                            {{'REM_PAY_PEP.FUNDS' | translate:param}}
                            <small>
                                <i *ngIf="fund">
                                    ({{fund ? fund['fund.name'] : ''}})
                                </i>
                            </small>
                        </td>
                        <td class="text-right">
                            <i class="fa fa-inr"></i>&nbsp;{{(fund ? fund.amount : 0) | number:'1.2-2'}}
                        </td>
                    </tr>
                    <tr>
                        <td>{{'REM_PAY_PEP.TOT_PAY' | translate:param}}</td>
                        <!-- <td class="text-right"
                            [ngStyle]="{'display': paymentAtcheckout?.value.payment_mode == '1' ? 'block' : 'none'}">
                            <span style="color: blue;">
                            <i class="fa fa-inr"></i>
                            {{selectedReport.cardPaymentTotal ? selectedReport.cardPaymentTotal : 0}}
                            </span>
                        </td> -->
                        <!-- [ngStyle]="{'display': paymentAtcheckout?.value.payment_mode == '1' ? 'none' : 'block'}" -->
                        <td class="text-right">
                            <span style="color: blue;">
                            <i class="fa fa-inr"></i>
                            <span #totalPayableAmount>
                                 <!-- + (selectedReport.payments.cardSwipeCharges ? selectedReport.payments.cardSwipeCharges : 0) -->
                                {{ ((selectedReport.payments.total_payable_amount - (selectedReport.payments.paidAmount ? selectedReport.payments.paidAmount
                                : 0) + (selectedReport.payments.returnAmount ? selectedReport.payments.returnAmount : 0)
                                - paymentAtcheckout?.value.payment_amount + (selectedReport.payments.early_checkin_Charge
                                ? selectedReport.payments.early_checkin_Charge : 0) + (selectedReport.payments.fundAmount
                                ? selectedReport.payments.fundAmount : 0) - paymentAtcheckout?.value.custom_discount)
                                < 0) ? 0 : selectedReport.payments.total_payable_amount - (selectedReport.payments.paidAmount ? selectedReport.payments.paidAmount
                                    : 0) + (selectedReport.payments.returnAmount ? selectedReport.payments.returnAmount : 0)
                                    - paymentAtcheckout?.value.payment_amount + (selectedReport.payments.early_checkin_Charge
                                    ? selectedReport.payments.early_checkin_Charge : 0) + (selectedReport.payments.fundAmount
                                    ? selectedReport.payments.fundAmount : 0) - paymentAtcheckout?.value.custom_discount
                                    | number: '1.2-2' }} </span>
                                </span>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="relative row" style="margin-left: 0px;">
                                <ng-select [items]="paymentTypeList"
                                    [clearable]="false"
                                    [searchable]="false"
                                    bindLabel="text"
                                    bindValue="id"
                                    [(ngModel)]="paymentTypeSelection"
                                    [ngModelOptions]="{standalone: true}"
                                    (change)="PaymentAtcheckoutSelectionChanged($event, selectedReport, selectedReport.process_type)"
                                    class="advancePaymentSelection"
                                    [placeholder]="'REM_PAY_PEP.PAY_MODE' | translate:param">
                                </ng-select>
                            </div>
                        </td>
                        <td>
                            <div *ngIf="paymentTypeSelection == '1'">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="fa fa-file-text-o"></i>
                                    </span>
                                    <input type="text" class="form-control" formControlName="payment_reciept_number"
                                        placeholder="{{'REM_PAY_PEP.RECE_NUM' | translate:param}}">
                                </div>
                                <br>
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="fa fa-percent"></i>
                                    </span>
                                    <input type="text" formControlName="card_charge" class="form-control"
                                        placeholder="{{'REM_PAY_PEP.CARD_SWI' | translate:param}}"
                                        (keyup)="updateCheckoutCardSwipeCharges(selectedReport)">
                                </div>
                                <br>
                            </div>
                            <div *ngIf="paymentTypeSelection == '2'">
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="fa fa-file-text-o"></i>
                                    </span>
                                    <input type="text" formControlName="bank_name" class="form-control"
                                        placeholder="{{'REM_PAY_PEP.BANK_NAME' | translate:param}}">
                                </div>
                                <br>
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="fa fa-file-text-o"></i>
                                    </span>
                                    <input type="text" formControlName="bank_cheque_no" class="form-control"
                                        placeholder="{{'REM_PAY_PEP.CHECK_NO' | translate:param}}">
                                </div>
                                <br>
                            </div>
                            <div *ngIf="paymentTypeSelection == '3'">
                                <div class="input-group">
                                  <span class="input-group-addon">
                                    <i class="fa fa-file-text-o"></i>
                                  </span>
                                  <input type="text" formControlName="bank_name" class="form-control" placeholder="Bank Name">
                                </div>
                                <br>
                                <div class="input-group">
                                  <span class="input-group-addon">
                                    <i class="fa fa-file-text-o"></i>
                                  </span>
                                  <!-- <input type="text" formControlName="bank_cheque_no" class="form-control"
                                    placeholder="Reference No."> -->
                                  <input type="text" formControlName="bank_cheque_no" class="form-control"
                                    placeholder="{{'RESERVATIONS.CHECK_NO' | translate:param}}">
                                </div>
                                <br>
                              </div>
                            <div class="input-group">
                                <span class="input-group-addon">
                                    <i class="fa fa-inr"></i>
                                </span>
                                <input type="text" formControlName="payment_amount"
                                    placeholder="{{'REM_PAY_PEP.AMOUNT' | translate:param}}"
                                    (keyup)="updateFunds(selectedReport, selectedReport.process_type)"
                                    class="form-control text-right">
                            </div>
                        </td>
                    </tr>
                    <!-- <tr [ngStyle]="{'display': paymentAtcheckout.value.payment_mode == '1' ? 'none' : 'table-row'}"> -->
                    <tr>
                         <td>
                            {{'REM_PAY_PEP.RET_AMT_DOT' | translate:param}}

                        </td>
                        <td>
                            <div class="row">
                                <div class="col-xs-6">
                                    <div class="input-group">
                                        <span class="input-group-addon fundCheck abc-checkbox abc-checkbox-success"
                                            style="padding-left: 0px">
                                            <input type="checkbox" #fundtypeID id="fundtypeID"
                                                [disabled]="selectedFundValue ? false : true"
                                                (change)="fundTypeEnable($event)">
                                            <label for="fundtypeID" class="capitalized">
                                            </label>
                                        </span>
                                       <ng-select id="default-select" [items]="fndType" [readonly]="disableFund" [ngModel]="selectedFundValue" formControlName="fund_type" (change)="FundTypeChanges($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Fund Type"></ng-select>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-control text-right">
                                        <i class="fa fa-inr"></i>&nbsp;
                                        <span #returnAmountValue>
                                             <!-- + (selectedReport.payments.cardSwipeCharges ? selectedReport.payments.cardSwipeCharges : 0) -->
                                            {{(((selectedReport.payments.total_payable_amount - (selectedReport.payments.paidAmount ? selectedReport.payments.paidAmount
                                            : 0) + (selectedReport.payments.returnAmount ? selectedReport.payments.returnAmount
                                            : 0) - paymentAtcheckout?.value.payment_amount + (selectedReport.payments.early_checkin_Charge
                                            ? selectedReport.payments.early_checkin_Charge : 0) + (selectedReport.payments.fundAmount
                                            ? selectedReport.payments.fundAmount : 0) - paymentAtcheckout?.value.custom_discount)
                                            < 0) ? makePositive((selectedReport.payments.total_payable_amount - (selectedReport.payments.paidAmount ? selectedReport.payments.paidAmount
                                                : 0) + (selectedReport.payments.returnAmount ? selectedReport.payments.returnAmount
                                                : 0) - paymentAtcheckout?.value.payment_amount + (selectedReport.payments.early_checkin_Charge
                                                ? selectedReport.payments.early_checkin_Charge : 0) + (selectedReport.payments.fundAmount
                                                ? selectedReport.payments.fundAmount : 0) - paymentAtcheckout?.value.custom_discount))
                                                : paymentAtcheckout?.value.payment_amount>
                                                0 ? returnZero() : 0)}}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr *ngIf="(paymentTypeSelection == '1')">
                        <td>
                            {{'REM_PAY_PEP.CARD_SWI' | translate:param}}
                        </td>
                        <td class="text-right">
                            <span style="color: green;"> (+)
                            <i class="fa fa-inr"></i>&nbsp;{{selectedReport.payments.cardSwipeCharges ? selectedReport.payments.cardSwipeCharges
                            : 0}}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td>{{'REM_PAY_PEP.REM_AMOUNT' | translate:param}}</td>
                        <!-- <td class="text-right"
                            [ngStyle]="{'display': paymentAtcheckout?.value.payment_mode == '1' ? 'block' : 'none'}">
                            <span style="color: blue;">
                            <i class="fa fa-inr"></i>
                            {{selectedReport.cardPaymentTotal ? selectedReport.cardPaymentTotal : 0}}
                            </span>
                        </td> -->
                        <!-- [ngStyle]="{'display': paymentAtcheckout?.value.payment_mode == '1' ? 'none' : 'block'}" -->
                        <td class="text-right">
                            <span style="color: blue;">
                            <i class="fa fa-inr"></i>
                            <!-- + (selectedReport.payments.cardSwipeCharges ? selectedReport.payments.cardSwipeCharges : 0) -->
                            <span #totalPayableAmount>
                                 {{ ((selectedReport.payments.total_payable_amount - (selectedReport.payments.paidAmount ? selectedReport.payments.paidAmount
                                : 0) + (selectedReport.payments.returnAmount ? selectedReport.payments.returnAmount : 0)
                                - paymentAtcheckout?.value.payment_amount + (selectedReport.payments.early_checkin_Charge
                                ? selectedReport.payments.early_checkin_Charge : 0) + (selectedReport.payments.fundAmount
                                ? selectedReport.payments.fundAmount : 0) - paymentAtcheckout?.value.custom_discount)
                                < 0) ? 0 : selectedReport.payments.total_payable_amount - (selectedReport.payments.paidAmount ? selectedReport.payments.paidAmount
                                    : 0) + (selectedReport.payments.returnAmount ? selectedReport.payments.returnAmount : 0)
                                     - paymentAtcheckout?.value.payment_amount + (selectedReport.payments.early_checkin_Charge
                                    ? selectedReport.payments.early_checkin_Charge : 0) + (selectedReport.payments.fundAmount
                                    ? selectedReport.payments.fundAmount : 0) - paymentAtcheckout?.value.custom_discount
                                    | number: '1.2-2' }}
                                </span>
                            </span>
                        </td>
                    </tr>
                    <!-- <tr *ngIf="authGuard.isAdmin()">
                        <td>{{'REM_PAY_PEP.CUST_DISC' | translate:param}}</td>
                        <td class="text-right">
                            <div class="input-group">
                                <span class="input-group-addon">
                                    <i class="fa fa-inr"></i>
                                </span>
                                <input class="form-control" formControlName="custom_discount" type="text"
                                    placeholder="{{'REM_PAY_PEP.CUST_DISC_PLACE' | translate:param}}"
                                    (keyup)="updateFunds(selectedReport, selectedReport.process_type)">
                            </div>
                        </td>
                    </tr> -->
                    <!-- *ngIf="selectedReport.process_type == 'checkout'" -->
                    <tr>
                        <td>{{'REM_PAY_PEP.PAVATI_NO' | translate:param}}</td>
                        <td class="text-right">
                            <div class="input-group">
                                <input class="form-control" formControlName="pavati_no" pattern="[\d|,|\/]*" type="text"
                                    placeholder="{{'REM_PAY_PEP.PAVATI_NO_PLACE' | translate:param}}">
                            </div>
                            <div *ngIf="paymentAtcheckout.controls['pavati_no'].dirty && paymentAtcheckout.controls['pavati_no'].invalid"
                                class="alert alert-danger">
                                <div *ngIf="paymentAtcheckout.controls['pavati_no'].errors?.required">
                                    {{'REM_PAY_PEP.PAVATI_REQ' | translate:param}}</div>
                            </div>
                        </td>
                    </tr>
                    <tr
                        *ngIf="selectedReport.process_type == 'checkout' && paymentAtcheckout.controls['reference_user']">
                        <td>{{'REM_PAY_PEP.REF' | translate:param}}</td>
                        <td class="text-right">
                            <div class="input-group" style="margin-top: .5rem;">
                                <span class="input-group-addon" [ngClass]="{'has-border-right': !isCheckoutReference}">
                                    <div class="abc-checkbox is_reference abc-checkbox-warning float-xs-left">
                                        <input type="checkbox" id="checkbox1" [disabled]="!referenceUser"
                                            (change)="hasCheckoutReference()" value="true">
                                        <label for="checkbox1"></label>
                                    </div>
                                </span>
                                <div class="relative" style="width: 100%">
                                   <ng-select [items]="referenceUser" [readonly]="!isCheckoutReference" (change)="checkoutReferenceTypeChanged($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Reference"></ng-select>
                                </div>
                            </div>
                            <div *ngIf="paymentAtcheckout.controls['reference_user'].dirty && paymentAtcheckout.controls['reference_user'].invalid"
                                class="alert alert-danger">
                                <div *ngIf="paymentAtcheckout.controls['reference_user'].errors?.required">
                                    {{'REM_PAY_PEP.REF_REQ' | translate:param}}</div>
                            </div>
                        </td>
                    </tr>
                    <tr *ngIf="selectedReport.process_type == 'checkout' && paymentAtcheckout.controls['note']">
                        <td>{{'REM_PAY_PEP.NOTE' | translate:param}}</td>
                        <td class="text-right">
                            <div class="input-group">
                                <textarea class="form-control" formControlName="note" type="text"
                                    placeholder="{{'REM_PAY_PEP.TXT_MSG' | translate:param}}"></textarea>
                            </div>
                            <div *ngIf="paymentAtcheckout.controls['note'].dirty && paymentAtcheckout.controls['note'].invalid"
                                class="alert alert-danger">
                                <div *ngIf="paymentAtcheckout.controls['note'].errors?.required">
                                    {{'REM_PAY_PEP.NOT_REQ' | translate:param}}</div>
                            </div>
                        </td>
                    </tr>
                    <!-- panNo -->
                    <tr *ngIf="paymentAtcheckout.controls['payment_verification_id']">
                        <td>{{'RESERVATIONS.PAN_NO' | translate:param}}</td>
                        <td class="text-right">
                            <div class="input-group">
                                <input (keyup)="getPanNo()" class="form-control"
                                    formControlName="payment_verification_id" type="text"
                                    placeholder="{{'RESERVATIONS.PAN_NO_PLACE' | translate:param}}." />
                            </div>
                            <div *ngIf="paymentAtcheckout.controls['payment_verification_id'].dirty && paymentAtcheckout.controls['payment_verification_id'].invalid"
                                class="alert alert-danger">
                                <div *ngIf="paymentAtcheckout.controls['payment_verification_id'].errors?.required">
                                    {{'RESERVATIONS.VALID_MSG.PAN_REQ' | translate:param}}</div>
                            </div>
                        </td>
                    </tr>
                    <!-- panNo -->
                </table>
                <!------------ Splite Recept Name ------ v ----->
                <div *ngIf="(panNo.length > 0) || cardSelected ? false : true">
                    <div formArrayName="items" *ngFor="let item of paymentAtcheckout.get('items').controls; let i = index">
                        <div [formGroupName]="i" class="input-group">
                            <input class="form-control m-1" formControlName="name" type="text" placeholder="Name">
                            <input class="form-control m-1" formControlName="city" type="text" placeholder="City">
                            <input class="form-control m-1" formControlName="panno" type="text" placeholder="Pan No">
                        </div>
                    </div>
                </div>

                <div class="text-right">
                    <!-- && ((selectedReport.cardPaymentTotal > 0 ? selectedReport.cardPaymentTotal : 0).toString() != paymentAtcheckout.value.payment_amount)) -->
                    <button type="submit" [disabled]="((selectedReport.process_type == 'checkout') &&
                paymentAtcheckout.controls['pavati_no'].invalid) ||
                (paymentAtcheckout?.value.payment_amount <= 0 &&
                paymentAtcheckout?.value.custom_discount <= 0 &&
                ((selectedReport.payments.total_payable_amount -
                    (selectedReport.payments.paidAmount ? selectedReport.payments.paidAmount : 0) -
                    (selectedReport.payments.cardSwipeChargesOriginal ? selectedReport.payments.cardSwipeChargesOriginal : 0) -
                    paymentAtcheckout?.value.payment_amount -
                    paymentAtcheckout?.value.custom_discount -
                    (selectedReport.payments.customDiscountTotal ? selectedReport.payments.customDiscountTotal : 0) +
                    (selectedReport.payments.fundAmount ? selectedReport.payments.fundAmount : 0)) > 0))"
                        class="btn btn-ms btn-inverse">{{'REM_PAY_PEP.UPDA_PAY' | translate:param}}</button>
                    <button (click)="cancel(false)" class="btn btn-ms btn-inverse">
                        {{'REM_PAY_PEP.CANCEL' | translate:param}}
                    </button>
                </div>
            </form>
        </div>
        <div class="col-sm-5 border-sm-left">
            <h3>{{'REM_PAY_PEP.CUST_DETAILS' | translate:param}}</h3>
            <hr style="margin-top: 0;">
            <h1 class="text-capitalize">{{selectedReport.username}}</h1>
            <strong class="text-light">
                <i>{{'REM_PAY_PEP.BOOK_FROM' | translate:param}} {{selectedReport.start | date}}
                    {{'REM_PAY_PEP.TO' | translate:param}} {{selectedReport.end | date}}</i>
            </strong>
        </div>
    </div>
</div>
