import { changePasswordComponent } from './changePassword/changePassword.component';
import { DefaultSettingsComponent } from './defaultSettings/defaultSettings.component';
import { NgModule } from '@angular/core';
import { DharamshalaService } from "../shared/services/dharamshala.service";
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TimepickerModule } from 'ngx-bootstrap/timepicker';
import { NgSelectModule } from '@ng-select/ng-select';

import { ProfileComponent } from './profileView/profile.component';
import { NKDatetimeModule } from 'ng2-datetime/ng2-datetime';

import { TextMaskModule } from 'angular2-text-mask';
import { CustomFormsModule } from 'ng2-validation'

import 'ng2-datetime/src/vendor/bootstrap-timepicker/bootstrap-timepicker.min.js';
import 'ng2-datetime/src/vendor/bootstrap-datepicker/bootstrap-datepicker.min.js';
import { DharamshalaModule } from 'app/dharamshala/dharamshala.module';

import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

export const routes = [
    { path: '', component: DefaultSettingsComponent, pathMatch: 'full'},
    { path: 'default', component: DefaultSettingsComponent},
    { path: 'profile', component: ProfileComponent },
    { path: 'change-password', component: changePasswordComponent}
];

@NgModule({
    imports: [
        RouterModule.forChild(routes),
        TimepickerModule.forRoot(),
        NKDatetimeModule,
        FormsModule,
        CommonModule,
        NgSelectModule,
        ReactiveFormsModule,
        TextMaskModule,
        CustomFormsModule,
        TranslateModule.forRoot({
            loader:{ 
                provide: TranslateLoader, 
                useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
                deps: [HttpClient] 
            }})
    ],
    exports: [],
    declarations: [
        ProfileComponent,
        DefaultSettingsComponent,
        changePasswordComponent
    ],
    providers: [ DharamshalaService ],
})
export class SettingModule { }
