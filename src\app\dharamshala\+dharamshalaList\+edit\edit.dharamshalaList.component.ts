import { DharamShala } from './../../data';
import { FormControl, FormGroup, FormBuilder, FormArray, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Component, OnInit, OnDestroy, NgZone, ViewChild } from '@angular/core';

import { Select2OptionData } from 'ng2-select2';
import { FileUploader, FileUploaderOptions } from 'ng2-file-upload';
import { MapsAPILoader } from '@agm/core';
import { CustomValidators } from 'ng2-validation';

import { GoogleMapService } from './../../../shared/services/google-map.service';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { AuthGuard } from './../../../shared/guards/auth-guard.service';
import { _secretKey_auth } from './../../../shared/globals/config';
import { apiUrl } from './../../../api-env';
import { allowedFileTypes, allowedMimeTypes } from "../allowedFileTypes";
import { TranslateService } from '@ngx-translate/core';


import * as data from '../../data';
declare var google: any;
declare var jQuery: any;
declare var moment: any;
declare var Messenger: any;
import * as CryptoJS from 'crypto-js';
const URL = apiUrl + 'dharamshala/add/image';
const URLDoc = apiUrl + 'dharamshala/add/doc';

@Component({
    selector: 'edit-dharamshala',
    templateUrl: '../+add/add.dharamshalaList.component.html'
})
export class EditDharamshalaComponent implements OnInit {
    datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        todayHighlight: true,
        icon: 'fa fa-calendar',
        endDate: new Date(),
        format: 'dd/mm/yyyy'
    }
    pageName: string = "Edit";
    public uploader: FileUploader = new FileUploader({ 
        url: URL,
        allowedMimeType: allowedMimeTypes
     });
    public uploadedImages: any[] = [];
    public uploader2: FileUploader = new FileUploader({
        url: URLDoc, 
        allowedFileType: allowedFileTypes 
    });
    public uploadedDocs: any[] = [];

    pageType: string;
    select2Options: any = {
        width: '100%'
    };
    check_in_options: any = {
        defaultTime: '08:00 AM',
        minuteStep: 5
    }
    check_out_options: any = {
        defaultTime: '08:00 PM',
        minuteStep: 5
    }
    initialData: any;
    lat: number = 19.0760098;
    lng: number = 72.8780646;
    zoom: number = 15;
    location_data = { coords: { lat: this.lat, lng: this.lng } };
    public data: any;
    public company_addr: string;
    public markers: any[] = [];
    public externalLinks: any[] = [];
    public uploadedImageURLs: any[] = [];
    public uploadedDocsURLs: any[] = [];
    public initRolelist;
    public countryList: Array<Select2OptionData>;
    public citiesList: Array<Select2OptionData>;
    public city: any;
    public country: any;
    public userCountry: any;
    dharamshalaForm: FormGroup;
    id: number;

    @ViewChild('externalinkTitle') externalinkTitle: any;
    @ViewChild('externallinkBody') externallinkBody: any;
    //service variables
    private sub: any;
    private initData: any;
    private getDharamshala: any;
    private deleteImages: any;
    private deleteDocs: any;
    private saveDharamshalaService: any;

    dharamshalaList: any[] = data.DharamShala;
    constructor(
        private route: ActivatedRoute,
        private gmService: GoogleMapService,
        private _loader: MapsAPILoader,
        private _zone: NgZone,
        private _fb: FormBuilder,
        private authGuard: AuthGuard,
        private DS: DharamshalaService,
        private router: Router,
        public translate: TranslateService
    ) {
        this.sub = this.route.params.subscribe(params => {
            this.id = +params['id'];
        })
        this.buildForm();
        this.getDharamshalaDetails(this.id);
        this.getInitData();
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
		translate.setDefaultLang(currentLang);// New Change ****
        translate.get('DHARAMSHALA.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log("Page type: ",res);
            this.pageName = res;
            //=> 'hello world'
        });
    }
    
    ngOnInit() {
        this.autocomplete();
        this.initImageUploadEvents();
        this.initDocUploadEvents()
    }
    getInitData() {
        this.initData = this.DS.getInitData()
        .subscribe((res) => {
            if (res.status == "success") {
                // console.log("init Data : ",res.data);
                this.initialData = res.data.roles;
            }
        })
    }
    getDharamshalaDetails(id) {
        this.getDharamshala = this.DS.getDharamshalaDetails(id)
        .subscribe((res) => {
            if (res.status == "success") {
                // console.log("Dharamshala details : ",res);
                delete res.data.dharamshala.detail.external_link;
                this.data = res.data;
                this.initExternal_links();
                this.initImages();
                this.initDocs();
                this.citiesList = res.data.cities;
                // console.log("citiesssss : ",res.data.cities);
                this.countryList = res.data.countries;
                this.lat = res.data.dharamshala.detail.lat;
                this.lng = res.data.dharamshala.detail.lng;
                this.initRolelist = res.data.dharamshala.detail.user.role_id;
                this.city = res.data.dharamshala.detail.city;
                this.country = res.data.dharamshala.detail.country;
                this.userCountry = res.data.dharamshala.detail.user.country;
                res.data.dharamshala.detail.user.dob = new Date(res.data.dharamshala.detail.user.dob.toString())
                this.dharamshalaForm.patchValue(res.data.dharamshala.detail);
                this.dharamshalaForm.controls['check_in'].patchValue(res.data.dharamshala.detail.check_in);
                this.dharamshalaForm.controls['check_out'].patchValue(res.data.dharamshala.detail.check_out);
                } else {
                    // console.log(res);
                }
            }, (err) => {
                // console.log(err);
            })
    }
    initExternal_links() {
        let external_links = this.data.dharamshala.external_link;
        if (external_links && external_links.length) {
            external_links.forEach(element => {
                this.addExternalLink(element.title, element.body);
            });
        }
    }
    initImages() {
        if (this.data.files && this.data.files.length) {
            
            this.data.files.forEach(element => {
                // console.log(element)
                this.addUploadedImages(element);
                this.uploadedImageURLs.push(element);
            });
        }
    }
    initDocs() {
        if (this.data.docs && this.data.docs.length) {
            this.data.docs.forEach(element => {
                this.addUploadedImages(element);
                this.uploadedDocsURLs.push(element);
            });
            
        }
    }
    
    buildForm() {
        this.dharamshalaForm = this._fb.group({
            // signing_authority: ['', Validators.required],
            name: ['', Validators.required],
            total_bed: ['', [Validators.required, CustomValidators.digits]],
            email: ['', [Validators.required, CustomValidators.email]],
            contact: ['', [Validators.required, CustomValidators.digits]],
            city: [''],
            city_name: [''],
            country: ['', Validators.required],
            zip: ['', [Validators.required, CustomValidators.digits]],
            address: ['', Validators.required],
            lat: [0],
            lng: [0],
            uploadedImages: this._fb.array([]),
            external_link: this._fb.array([]),
            check_in: ['', Validators.required],
            check_out: ['', Validators.required],
            status: [true, Validators.required],
            early_checkout_charges: [null, [Validators.required,CustomValidators.number, CustomValidators.lte(100)]],
            user: this._fb.group({
                first_name: ['', [Validators.required]],
                last_name: ['', [Validators.required]],
                email: ['', [Validators.required]],
                dob: ['', [Validators.required]],
                country: ['', [Validators.required]],
                role_id: ['', Validators.required],
                id:['']
            }),
        })
    }
    getRoleList(): Select2OptionData[] {
        if (this.initialData) {
            return jQuery.map(this.initialData, function (obj) {
                return { id: obj.id, text: obj.name };
            })
        }
    }
    userRoleChanged(event) {
        let user = <FormGroup>this.dharamshalaForm.controls['user'];
        let role = <FormControl>user.controls['role_id'];
        setTimeout(function () {
        role.patchValue(event.value);
        }, 0);
    }
    countryChanged(event: any) {
        setTimeout(() => {
            this.dharamshalaForm.controls['country'].patchValue(event.value);
        }, 0);
    }
    cityChanged(event: any) {
        setTimeout(() => {
            this.dharamshalaForm.controls['city'].patchValue(event.value);
            let index = this.findIndex(parseInt(event.value), "id", this.citiesList) >= 0 ? this.findIndex(parseInt(event.value), "id", this.citiesList) : 0;
            let data = event.value
            let demo = this.citiesList.find(res => data == res.id)
            console.log("this is my city ===========>", demo.text)
            this.dharamshalaForm.controls['city_name'].patchValue(demo.text);
        }, 0);
	}
	userCountryChanged(event: any) {
		let user = <FormGroup>this.dharamshalaForm.controls['user'];
		setTimeout(() => {
			user.controls['country'].patchValue(event.value);
		}, 0);
	}
    getCordinates(data) {
        this.lat = data.coords.lat;
        this.lng = data.coords.lng;
        this.gmService.getAddrFromLtLng(this.lat, this.lng).subscribe(res => {
            this.dharamshalaForm.controls['address'].patchValue(res.results[0].formatted_address);
        });
    }
    autocomplete() {
        this._loader.load().then(() => {
            var autocomplete = new google.maps.places.Autocomplete(document.getElementById("autocompleteInput"), {});
            google.maps.event.addListener(autocomplete, 'place_changed', () => {
                this._zone.run(() => {
                    var place = autocomplete.getPlace();

                    this.markers.push({
                        lat: place.geometry.location.lat(),
                        lng: place.geometry.location.lng(),
                        label: place.name,
                    });
                    this.dharamshalaForm.controls['address'].patchValue(place.formatted_address);
                    this.lat = place.geometry.location.lat();
                    this.lng = place.geometry.location.lng();
                });
            });
        });
    }
    /**
     * when image has been uploaded but not saved, images will be list out to form control
     * @param {any} res : response - response from image upload api
     */
    addUploadedImages(res) {
        let control = <FormArray>this.dharamshalaForm.controls['uploadedImages'];
        control.push(
            this._fb.group({
                mimetype: [(res.data) ? res.data.mimetype : res.mimetype],
                extension: [(res.data) ? res.data.extension : res.extension],
                oldName: [(res.data) ? res.data.oldName : res.oldName],
                originalName: [(res.data) ? res.data.originalName : res.originalName],
                size: [(res.data) ? res.data.size : res.size],
                uploaded: [(res.data) ? res.data.uploaded : res.uploaded],
                is_deleted: [(res.data) ? res.data.is_deleted : res.is_deleted],
                is_doc: [(res.data) ? res.data.is_doc : res.is_doc]
            })
        );
    }
    /**
     * remove images that are already uploaded but not saved yet
     *
     * @param {any} data : response - api response that received by api after marked as deleted
     * @param {any} index : file index
     */
    removeUploadedImages(data) {
        let controlvalue = this.dharamshalaForm.controls['uploadedImages'].value;
        let control = <FormArray>this.dharamshalaForm.controls['uploadedImages'];
        let index = this.findIndex(controlvalue, data.originalName, "originalName")

        let newControl = this._fb.group({
            mimetype: [data.mimetype],
            extension: [data.extension],
            oldName: [data.oldName],
            originalName: [data.originalName],
            size: [data.size],
            uploaded: [data.uploaded],
            is_deleted: [data.is_deleted],
            is_doc: [data.is_doc]
        })
        control.setControl(index, newControl) // replace control is_deleted to true
    }

    initImageUploadEvents() {
        this.uploader.onWhenAddingFileFailed = (item: any, filter: any, option: any) => {
            // console.log("item selected : ",item, filter, option);
            Messenger().post({  hideAfter: 5,
                message: item.type + ' format is not supported!',
                type: 'error',
                showCloseButton: true
            });
        };
        this.uploader.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
            //things to do on completion
            if (response) {
                let res = JSON.parse(response);
                this.addUploadedImages(res);
            }
        };
        this.uploader.onBeforeUploadItem = (item: any) => {
            // image uploaded - add token of auth
            let token = this.authGuard.ud.session_id;
            let timestamp = (+ new Date()).toString();
            let generatedToken = CryptoJS.AES.encrypt(token,
                _secretKey_auth);
            this.uploader.authToken = generatedToken;
        }
    }
    initDocUploadEvents() {
        this.uploader2.onWhenAddingFileFailed = (item: any, filter: any, option: any) => {
            // console.log("item selected : ", item, filter, option);
            Messenger().post({  hideAfter: 5,
                message: item.type + ' format is not supported!',
                type: 'error',
                showCloseButton: true
            });
        };
        this.uploader2.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
            //things to do on completion
            if (response) {
                let res = JSON.parse(response);
                this.addUploadedImages(res);
            }
        };
        this.uploader2.onBeforeUploadItem = (item: any) => {
            // image uploaded - add token of auth
            let token = this.authGuard.ud.session_id;
            let timestamp = (+ new Date()).toString();
            let generatedToken = CryptoJS.AES.encrypt(token,
                _secretKey_auth);
            this.uploader2.authToken = generatedToken;
        }
    }
    deleteImage(img) {
        let index = this.findIndex(this.uploadedImageURLs, img.originalName, "originalName")

        if (confirm('Are You Sure, you want to delete ?')) {
            this.deleteImages = this.DS.deleteImage(img)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.removeUploadedImages(res.data);
                        this.uploadedImageURLs[index].is_deleted = res.data.is_deleted;
                    }
                }, (err) => {
                    Messenger().post({  hideAfter: 5,
                        message: "Can not perform Operation",
                        type: "error",
                        showCloseButton: true
                    });
                })
        }
    }
    deleteDoc(doc) {
        let index = this.findIndex(this.uploadedDocsURLs, doc.originalName, "originalName");

        if (confirm('Are You Sure, you want to delete ?')) {
            this.deleteDocs = this.DS.deleteImage(doc)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.removeUploadedImages(res.data);
                        this.uploadedDocsURLs[index].is_deleted = res.data.is_deleted;
                    }
                }, (err) => {
                    Messenger().post({  hideAfter: 5,
                        message: "Can not perform Operation",
                        type: "error",
                        showCloseButton: true
                    });
                })
        }
    }
    addExternalLink(title, body) {
        let control = <FormArray>this.dharamshalaForm.controls['external_link'];
        control.push(
            this._fb.group({
                title: [title],
                body: [body]
            }));
        let data = {
            title: title,
            body: body
        }
        this.externalLinks.push(data);
        this.externalinkTitle.nativeElement.value = "";
        this.externallinkBody.nativeElement.value = "";
    }
    deleteExternalLink(index, ele) {
        let control = <FormArray>this.dharamshalaForm.controls['external_link'];
        control.removeAt(index);
        this.externalLinks.splice(index, 1);
    }
    authorityChanged(event) {
        this.dharamshalaForm.controls['signing_authority'].patchValue(event.value);
    }
    findIndex(arr, searchTerm, property) {
        for (var i = 0, len = arr.length; i < len; i++) {
            if (arr[i][property] === searchTerm) return i;
        }
        return -1;
    }
    saveDharamshala() {
        // let timestamp1 = new Date(this.dharamshalaForm.value.check_in).toString()
        // let timestamp2 = new Date(this.dharamshalaForm.value.check_out).toString()
        // this.dharamshalaForm.value.check_in = timestamp1;
        // this.dharamshalaForm.value.check_out = timestamp2;
        // console.log(timestamp1)

        if (this.dharamshalaForm.valid) {
            // console.log("this.dharamshalaForm.value :",this.dharamshalaForm.value);
            this.saveDharamshalaService = this.DS.saveEditedDharamshala(this.id, this.dharamshalaForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.router.navigate(['admin/dharamshala']);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.dharamshalaForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }

    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
        if (this.getDharamshala) {
            this.getDharamshala.unsubscribe();
        }
        if (this.initData) {
            this.initData.unsubscribe();
        }
        if (this.saveDharamshalaService) {
            this.saveDharamshalaService.unsubscribe();
        }
        if (this.deleteImages) {
            this.deleteImages.unsubscribe();
        }
        if (this.deleteDocs) {
            this.deleteDocs.unsubscribe();
        }
    }

}