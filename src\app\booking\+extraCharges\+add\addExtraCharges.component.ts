import { BookingService } from './../../../shared/services/booking.service';
import { CustomValidators } from 'ng2-validation';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;


@Component({
    selector: 'add-extra-charges',
    templateUrl: '../extraCharges.actions.component.html'
})

export class AddExtraChargesComponent implements OnInit {
    extraChargesForm: FormGroup;
    private sub: any;
    public pageType:  string = "Add";
    @Output() sendAdded = new EventEmitter();
    @Output() closeAdd = new EventEmitter();
    constructor(
        private _fb: FormBuilder,
        private BS: BookingService,
        public translate: TranslateService
    ) { 
        translate.get('MANAGE_EXTRA_CHARGES.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }
    ngOnInit() {
        this.buildForm();
    }
    buildForm() {
        this.extraChargesForm = this._fb.group({
            name: ['',[ Validators.required]],
            charge: ['0',[Validators.required,CustomValidators.number]],
            status: [true ,Validators.required]
        })
    }
    saveExtraCharges(){
        if(this.extraChargesForm.valid){
            this.sub = this.BS.saveExtraChages(this.extraChargesForm.value)
            .subscribe((res) => {
                if(res.status == "success"){   
                    this.extraChargesForm.reset();
                    this.sendAdded.emit(res.data);
                }
            },(err) => {
                 let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.extraChargesForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
            })
        }else{
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    closeThisComp(){
       this.closeAdd.emit('hiddenAdd');
    }
}