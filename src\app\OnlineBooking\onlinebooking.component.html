<div *ngIf="!showEmptyTemplate" class="c-main-booking-wrap">
<!-- Start Header -->
<div class="c-main-booking-header-wrap" >
  <div class="container">
    <div class="row align-items-center">
      <!-- Logo Column -->
      <div class="col-12 col-md-6 mb-3 mb-md-0 d-flex justify-content-center justify-content-md-start">
        <img src="../../assets/img/logo.svg" alt="Logo" class="img-fluid" />
      </div>

      <!-- Text Column -->
      <div class="col-12 col-md-6 text-center">
        <h2 class="c-main-booking-header-text mb-0">
          Online Booking
          <br />
          <h5 class="d-block mt-0">Helpline number: +91 84250 01008</h5>
        </h2>
      </div>
    </div>
  </div>
</div>
<!-- End Header -->

  <!-- Start Body -->
  <div class="c-main-booking-body-wrap" *ngIf="!isCustomerBookingFormActive">
    <!-- Start Search Section -->
    <div class="c-main-booking-search-section-wrap">
      <div class="c-main-booking-search-section-group">
        <h2 class="c-main-booking-body-title">
          Swaminarayn mandir Mumbai Room booking
        </h2>
        <form [formGroup]="searchForm" (ngSubmit)="onSubmit()">
          <div class="c-main-booking-search-control-group row">
            <div class="c-main-booking-search-control-box col-lg-6 col-md-12 col-sm-12 col-12">
              <label for="checkInDateId">CHECK-IN <span class="text-danger">*</span></label>
              <input type="date" class="form-control" id="checkinDateId" formControlName="checkInDate" [min]="todayDate()" />
              <div *ngIf="searchForm.get('checkInDate').touched && searchForm.get('checkInDate').invalid" class="error">
                <div *ngIf="searchForm.get('checkInDate').hasError('required')" class="text-danger">please select date first</div>
              </div>
            </div>
            <div class="c-main-booking-search-control-box col-lg-6 col-md-12 col-sm-12 col-12">
              <label for="checkOutDateId">CHECK-OUT <span class="text-danger">*</span></label>
              <input type="date" class="form-control" id="checkOutDateId" formControlName="checkOutDate"
                [min]="checkoutValidation()" />
              <div *ngIf="searchForm.get('checkOutDate').touched && searchForm.get('checkOutDate').invalid"
                class="error">
                <div *ngIf="searchForm.get('checkOutDate').hasError('required')" class="text-danger">please select date
                  first</div>
              </div>
            </div>
          </div>
          <button type="submit" [ngClass]="{'btn-secondary': searchForm.invalid, 'btn-success':!searchForm.invalid}" class="btn c-main-booking-search-btn" [disabled]="searchForm.invalid">search rooms</button>
        </form>
        <!-- <div class="c-main-booking-search-section-box">
          <label for="">Check-In</label>
          <input type="date">
        </div>
        <div class="c-main-booking-search-section-box">
          <label for="">Check-Out</label>
          <input type="date">
        </div>
        <div class="c-main-booking-search-section-box">
          <label for="">Room</label>
          <input type="date">
        </div> -->
        <span class="alert alert-danger" *ngIf="searchValidError">{{searchValidError}}</span>

      </div>
    </div>
    <!-- End Search Section -->

    <!-- Start Result Section -->
    <ng-container *ngIf="getAvailableRoom">
      <div class="c-main-search-result-seaction-wrap">
        <div class="c-main-result-box-wrap">
          <h2 class="text-center c-main-result-box-title">Select Room</h2>
          <div class="container">
            <div class="row">
              <div class="col-lg-4 col-md-6 col-sm-6 col-6 my-1" *ngFor="let room of getAvailableRoom">
                <div class="card text-center c-main-search-result-seaction-card"
                  [ngClass]="{'disabled-card': !room?.data?.Available}"
                  [ngStyle]="!room?.data?.Available ? {'pointer-events': 'none', 'opacity': '0.5'} : {}">
                  <div class="card-body">
                    <h5 class="card-title">{{ room?.data?.CategoryName }}</h5>
                    <div class="card-text" [ngClass]="{'text-danger': !room?.data.Available}">
                      {{ room?.data?.Available ? "Maintenance charges:" : "Room not available" }}
                      <strong>{{ room?.data?.charges }}</strong>
                    </div>
                  </div>
                  <div class="card-footer text-muted">
                    <a href="javascript:void(0)" (click)="selectedBookingId(room?.data)"
                      [attr.data-roomId]="room?.data?.RoomId" class="btn btn-primary">Book Now</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <!-- End Result Section -->
  </div>
  <!-- End Body -->

  <!-- Start Customer Booking Form -->
  <ng-container *ngIf="isCustomerBookingFormActive">
    <div class="container mt-5">
      <div class="list-group list-group-flush">
        <div class="list-group-item" style="margin-top: 5rem;">
          <!-- Warning text -->
          <!-- <div class="col-sm-12 col-md-12">
            <marquee direction="right" class="alert alert-danger">Whatever warning to show here!</marquee>
          </div> -->
          <!-- Form heading here -->
           <div class="row">
            <div class="col-md-8 col-sm-8">
            <h4 class="mb-2 mt-2 font-weight-bold">Enter details For Online Booking </h4>
          </div>
          <!-- <div class="col-md-4 col-sm-4 bg-red">
            <h5 class="mb-2 mt-2 font-weight-bold">Helpline Number : <a href="tel:+91 8425001008" style="text-decoration: none;color: red;" >+91 8425001008</a></h5>
          </div> -->
           </div>
          
          <!-- Search user details -->
          <div class="col-md-6 col-sm-12" style="padding-left: 0!important;">
            <div class="row">
              <div class="form-group col-md-4 col-sm-4">
                <select class="form-control" (change)="selectSearchType($event)" name="Action" id="Action">
                  <option value="" disabled>Select search type</option>
                  <option *ngFor="let type of searchTypeList" [value]="type.id">{{type.name}}</option>
                </select>
              </div>

              <!-- Form search functionality -->
              <div class="col-md-8 col-sm-8">
                <input *ngIf="selectedSearchType || selectedSearchType == 0" type="text" [(ngModel)]="searchTerm"
                  (input)="searchUser()" (focus)="onFocus()" (blur)="onBlur()" placeholder="Search for users..."
                  class="search-input" />

                <!-- Dropdown list of users, only showing those that match the search -->
                <div *ngIf="userList?.length > 0 && isListEnable" class="dropdown-list">
                  <div *ngIf="setListError"> {{setListError}}</div>
                  <ul *ngIf="!setListError">
                    <li *ngFor="let user of userList" (click)="selectUser(user)">
                      {{ user.name }}
                    </li>
                  </ul>
                </div>
                <!-- Dropdown list over -->
              </div>
            </div>
          </div>
          <form [formGroup]="userInfoControl">
            <div class="row">
              <!-- Full name -->
              <div class="form-group col-sm-12 col-md-6">
                <label>Full Name <span class="text-danger">*</span></label>
                <input type="text" formControlName="name" class="form-control"
                  [ngClass]="{'is-invalid': userInfoControl.invalid}" />
                <div *ngIf="userInfoControl.get('name').touched && userInfoControl.get('name').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('name').hasError('required')"> your name is required</div>
                  <div *ngIf="userInfoControl.get('name').hasError('minlength')">please add proper full name</div>
                </div>
              </div>
              <!-- referance user selector -->
              <div class="form-group col-sm-12 col-md-6">
                <label>Referance Name</label>
                <input type="text" formControlName="referance_user" class="form-control"
                  [ngClass]="{'is-invalid': userInfoControl.get('referance_user').touched && userInfoControl.get('referance_user').invalid}" />

                <div
                  *ngIf="userInfoControl.get('referance_user').touched && userInfoControl.get('referance_user').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('referance_user').hasError('minlength')">please enter proper referance
                    name</div>
                </div>
              </div>
              <!-- Mobile number  -->
              <div class="form-group col-sm-12 col-md-6">
                <label>Mobile Number <span class="text-danger">*</span></label>
                <input type="text" formControlName="contact" class="form-control"
                  [ngClass]="{'is-invalid': userInfoControl.get('contact').touched && userInfoControl.get('contact').invalid}" />
                <div *ngIf="userInfoControl.get('contact').touched && userInfoControl.get('contact').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('contact').hasError('required')">Mobile number is required</div>
                  <div *ngIf="userInfoControl.get('contact').hasError('pattern')">Mobile number must be a valid mobile
                    number</div>
                </div>
              </div>
              <!-- email address -->
              <div class="form-group col-sm-12 col-md-6">
                <label>Email Address <span class="text-danger">*</span></label>
                <input type="text" formControlName="email" class="form-control"
                  [ngClass]="{'is-invalid': userInfoControl.get('email').touched && userInfoControl.get('email').invalid}" />
                <div *ngIf="userInfoControl.get('email').touched && userInfoControl.get('email').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('email').hasError('required')">Email is required</div>
                  <div *ngIf="userInfoControl.get('email').hasError('pattern')">Email must be a valid Email address
                  </div>
                </div>
              </div>
              <!-- address field  -->
              <div class="form-group col-sm-12 col-md-6">
                <label>Address <span class="text-danger">*</span></label>
                <textarea type="text" formControlName="address" class="form-control" rows="2"
                  [ngClass]="{'is-invalid': userInfoControl.get('address').touched && userInfoControl.get('address').invalid}"></textarea>
                <div *ngIf="userInfoControl.get('address').touched && userInfoControl.get('address').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('address').hasError('required')">Address is required</div>
                </div>
              </div>
              <!-- date of birth field -->
              <div class="form-group col-sm-12 col-md-6">
                <label>Date of birth <span class="text-danger">*</span></label>
                <input type="date" formControlName="birth_date" class="form-control"
                  [ngClass]="{'is-invalid': userInfoControl.get('birth_date').touched && userInfoControl.get('birth_date').invalid}" />
                <div *ngIf="userInfoControl.get('birth_date').touched && userInfoControl.get('birth_date').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('birth_date').hasError('required')">date of birth is required</div>
                  <!-- <div *ngIf="userInfoControl.get('birth_date').hasError('ageInvalid')">
                    {{ userInfoControl.get('birth_date').errors?.['ageInvalid'] }}</div> -->
                </div>
              </div>
              <!-- Adult Counter -->
              <div class="form-group col-sm-6 col-md-3">
                <label>Total Adults</label>
                <div class="input-group">
                  <!-- Plus Button -->
                  <button type="button" class="btn btn-outline-secondary" (click)="handleMinus('adult')">
                    -
                  </button>

                  <input type="text" class="form-control" [value]="adultCountNumber" readonly />
                  <!-- Minus Button -->
                  <button type="button" class="btn btn-outline-secondary" (click)="handlePlus('adult')">
                    +
                  </button>
                </div>
                <div *ngIf="counterError" class="invalid-feedback text-danger">{{counterError}}</div>
              </div>

              <!-- Child Counter -->
              <div class="form-group col-sm-6 col-md-3">
                <label>Total Children</label>
                <div class="input-group">
                  <!-- Plus Button -->
                  <button type="button" class="btn btn-outline-secondary" (click)="handleMinus('child')">
                    -
                  </button>

                  <input type="text" class="form-control" [value]="childrenCountNumber" readonly />
                  <!-- Minus Button -->
                  <button type="button" class="btn btn-outline-secondary" (click)="handlePlus('child')"> + </button>
                </div>
              </div>

              <!-- Aadhar card Number -->
              <div class="form-group col-sm-12 col-md-6">
                <label>Aadhar card number <span class="text-danger">*</span></label>
                <input type="text" formControlName="aadharcard_number" class="form-control"
                  [ngClass]="{'is-invalid': userInfoControl.get('aadharcard_number').touched && userInfoControl.get('aadharcard_number').invalid}" />
                <div
                  *ngIf="userInfoControl.get('aadharcard_number').touched && userInfoControl.get('aadharcard_number').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('aadharcard_number').hasError('required')">aadhar card number is
                    required</div>
                  <div *ngIf="userInfoControl.get('aadharcard_number').hasError('pattern')">please add valid aadhar
                    number</div>
                </div>
              </div>
              <!-- Pan card number -->
              <div class="form-group col-sm-12 col-md-6">
                <label>Pan card number</label>
                <input type="text" formControlName="pancard_number" class="form-control"
                  [ngClass]="{'is-invalid': userInfoControl.get('pancard_number').touched && userInfoControl.get('pancard_number').invalid}" />
                <div
                  *ngIf="userInfoControl.get('pancard_number').touched && userInfoControl.get('pancard_number').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('pancard_number').hasError('pattern')">please add valid pan card
                    number</div>
                </div>
              </div>
              <!-- Aadhar card photo -->
              <div class="form-group col-sm-6 col-md-3">
                <label>Aadhar card photo <span class="text-danger">*</span></label>
                <input type="file" #aadharFileInput (change)="HandleDocumentUploads($event, 'AadharCardPhoto')"
                  class="form-control" formControlName="AadharCardPhoto"
                  [ngClass]="{'is-invalid':userInfoControl.get('AadharCardPhoto').touched && userInfoControl.get('AadharCardPhoto').invalid}" />

                <!-- <div class="mt-1" *ngIf="selectedAadharFile && !disableAadharUpload">
                  <span><i class="glyphicon glyphicon-cloud-upload doc-upload-success"
                      (click)="uploadIdProof('aadhar')"></i></span>
                  <span><i class="glyphicon glyphicon-remove-circle doc-upload-cancel"
                      (click)="cancelUploadPhoto('aadhar', aadharFileInput)"></i></span>
                </div> -->
                <div
                  *ngIf="userInfoControl.get('AadharCardPhoto').touched && userInfoControl.get('AadharCardPhoto').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('AadharCardPhoto').hasError('required')">{{userInfoControl.get('AadharCardPhoto').errors?.required}}</div>
                  <div *ngIf="userInfoControl.get('AadharCardPhoto').hasError('invalidFileType')">
                    {{userInfoControl.get('AadharCardPhoto').errors?.invalidFileType}}</div>
                  <div *ngIf="userInfoControl.get('AadharCardPhoto').hasError('invalidFileSize')">
                    {{userInfoControl.get('AadharCardPhoto').errors?.invalidFileSize}}</div>
                </div>
              </div>
              <!-- Pan card photo -->
              <div class="form-group col-sm-6 col-md-3">
                <label>Pan card photo</label>
                <input type="file" #panFileInput (change)="HandleDocumentUploads($event, 'PanCardPhoto')"
                  class="form-control" formControlName="PanCardPhoto"
                  [ngClass]="{'is-invalid':userInfoControl.get('PanCardPhoto').touched && userInfoControl.get('PanCardPhoto').invalid}" />
                <!-- <div class="mt-1" *ngIf="selectedPanFile && !disablePanUpload">
                  <span><i class="glyphicon glyphicon-cloud-upload doc-upload-success"
                      (click)="uploadIdProof('pan')"></i></span>
                  <span><i class="glyphicon glyphicon-remove-circle doc-upload-cancel"
                      (click)="cancelUploadPhoto('pan', panFileInput)"></i></span>
                </div> -->
                <div *ngIf="userInfoControl.get('PanCardPhoto').touched && userInfoControl.get('PanCardPhoto').invalid"
                  class="invalid-feedback text-danger">
                  <div *ngIf="userInfoControl.get('PanCardPhoto').hasError('invalidFileType')">
                    {{userInfoControl.get('PanCardPhoto').errors?.invalidFileType}}</div>
                  <div *ngIf="userInfoControl.get('PanCardPhoto').hasError('invalidFileSize')">
                    {{userInfoControl.get('PanCardPhoto').errors?.invalidFileSize}}</div>
                </div>
              </div>
            </div>

            <div class="row" style="justify-content: space-around;">
              <div class="col-md-5 notes">
                <ul>
                  <li><b>नोट 1:</b> कृपया हमारे आधिकारिक भुगतान क्यूआर कोड या लिंक के अलावा किसी अन्य माध्यम से भुगतान न करें। अन्य किसी भी भुगतान के लिए हम जिम्मेदार नहीं होंगे। यदि आपके कोई प्रश्न हों, तो हमारी हेल्पलाइन नंबर पर संपर्क करें।</li>
                  <li><b>नोट 2:</b>जब तक हमारी तरफ से बुकिंग पुष्टि का संदेश नहीं आता, तब तक आपकी बुकिंग कन्फर्म नहीं मानी जाएगी।</li>
                  <li><b>नोट 3:</b>ऊपर दिया गया मेंटेनेंस चार्ज केवल एक दिन के लिए है।</li>
                  <li><b>नोट 4:</b>जब आप यह बुकिंग फॉर्म सबमिट कर देंगे, तब हम सभी विवरणों की जांच करके आपको भुगतान लिंक भेजेंगे।</li>
                </ul>
            </div>
            <div class="col-md-5 notes">
            <ul>
              <li><b>Note 1:</b> Please do not make any payments through any method other than our official payment QR code or link. We will not be responsible for any other payments. If you have any questions, please contact our helpline number.</li>
              <li><b>Note 2:</b> Your booking will not be confirmed until you receive a confirmation message from our side.</li>
              <li><b>Note 3:</b> The maintenance charge mentioned above is for one day only.</li>
              <li><b>Note 4:</b>After you submit this booking form, we will verify all details from our side and send you a payment link.</li>
            </ul>
            </div>
            </div>
            <div  style="    display: flex ; align-items: center;">
              <div >
                <input type="submit" (click)="submitForm()" class="btn btn-primary" value="Submit" />
              </div>
              <div style="margin-left: 10px;">
                <button (click)="CloseSubmitForm()" class="btn btn-danger">Close</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </ng-container>
  <!-- End Customer Booking Form -->
</div>

<!-- this modal is shown after the successful sending data to user -->
<div class="modal fade" *ngIf="!showEmptyTemplate" bsModal #successMessageModal="bs-modal" role="dialog"
  aria-labelledby="successMessageModal" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Booking request sent</h5>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <div class="alert alert-success">
              <h4>
                The request for room booking has been sent! <br /> <br />You will get the sms if your booking is
                accepted
              </h4>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <a href="https://www.swaminarayangadi.com/"><button type="button" class="btn btn-success btn-block" (click)="closeModal()">Visit Site</button></a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<!-- this modal is shown if any error got while saving the data -->
<div class="modal fade" *ngIf="!showEmptyTemplate" bsModal #errorMessageModal="bs-modal" role="dialog"
  aria-labelledby="errorMessageModal" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Booking request sent</h5>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <div class="alert alert-danger">
              <h4> {{showFormError}}</h4>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-success btn-block" (click)="closeMessageModel()">Go back</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- this template is shown if the token is not valid or expired -->
<div *ngIf="showEmptyTemplate" class="error-container">
  <div class="error-content">
    <!-- <img src="assets/404-error.svg" alt="404 Not Found" class="error-image" /> -->
    <h1 class="error-title">Oops! Page Not Found</h1>
    <p class="error-message">
      {{showTokenErrorMsg}}
    </p>
  </div>
</div>