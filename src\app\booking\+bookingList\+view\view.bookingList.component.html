<section class="widget">
  <header>
    <div class="row">
      <div class="col-sm-10">
        <h4>
          <span class="capitalized">
            <i class="fa fa-clipboard"></i>&nbsp;&nbsp;View Booking Details</span>
        </h4>
      </div>
      <div class="col-sm-1" style="padding-right: 0px;">
        <button *ngIf="canAdd" type="button" class="btn btn-sm btn-danger float-sm-right" tooltip="Add Alternate Billing Guest" placement="bottom"
          (click)="addAlternateBG()">
          <i class="fa fa-plus"></i>
          &nbsp;&nbsp;Add
        </button>
      </div>
      <div class="col-sm-1">
        <button type="button" class="btn btn-sm btn-danger float-sm-right" (click)="gobacksimon()">
          <i class="fa fa-angle-left"></i>&nbsp;&nbsp;Back</button>
      </div>
    </div>
  </header>
  <hr class="large-hr">
  <div class="clearfix"></div>
  <div class="widget-body" *ngIf="data">
    <div class="mt">
      <div class="row">
        <div class="col-md-6">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4>Booking Details</h4>
            </div>
            <div class="panel-body">
              <table class="table table-no-mar">
                <tr>
                  <!-- Need Dynamic Data interpolation for Unique Booking ID ///////// Please donot commit without it -->
                  <td>Booking ID:</td>
                  <td>
                    <strong>{{selectedBooking.unique_booking_id}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>Booking Type:</td>
                  <td>
                    <strong>{{bookingType[selectedBooking.booking_type].text}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>Booking Status:</td>
                  <td>
                    <strong class="capitalize">{{selectedBooking.current_status}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>Booking Date:</td>
                  <td>
                    <strong>{{(selectedBooking.booking_date ? selectedBooking.booking_date : '') | date}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>Expected Check-In:</td>
                  <td>
                    <strong class="text-success">{{(selectedBooking.detail[0].expected_check_in ? selectedBooking.detail[0].expected_check_in : '' ) |
                      date:'h:mm a'}}&nbsp;Hrs.</strong>
                  </td>
                </tr>
                <tr>
                  <td>Expected Check-Out:</td>
                  <td>
                    <strong class="text-success">{{(selectedBooking.detail[0].expected_check_out ? formatCheckoutDate(selectedBooking.detail[0].expected_check_out)
                      : '') | date:'h:mm a'}}&nbsp;Hrs.</strong>
                  </td>
                </tr>
                <tr>
                  <td>Actual Check In:</td>
                  <td>
                    <strong class="text-success">{{selectedBooking.detail[0].check_in}}&nbsp;Hrs.</strong>
                  </td>
                </tr>
                <tr>
                  <td>Actual Check out:</td>
                  <td>
                    <strong class="text-danger">{{selectedBooking.detail[0].check_out}}&nbsp;Hrs.</strong>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <div *ngIf="selectedBooking.alternate_billing_guest && !showForm" class="col-md-6">
            <div class="panel panel-default">
              <div class="panel-heading">
                  <div class="row">
                      <div class="col-sm-10">
                        <h4>Alternate Billing Guest</h4>
                      </div>
                      <div class="col-sm-2">
                        <button class="btn btn-sm btn-primary float-sm-right" (click)="editAlternateBG()">Edit</button>
                      </div>
                  </div>
              </div>
              <div class="panel-body">
                <table class="table table-no-mar">
                  <tr>
                    <td>
                      Name
                    </td>
                    <td>
                      <strong>
                        {{selectedBooking.alternate_billing_guest}}
                      </strong>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      Status
                    </td>
                    <td>
                      <strong [ngStyle]="{'color': selectedBooking.is_alternate_bg_active ? '#4CAF50' : '#F44336'}">
                        {{selectedBooking.is_alternate_bg_active ? 'Active' : 'In-active'}}
                      </strong>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        <div *ngIf="showForm" class="col-md-6">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4>Alternate Billing Guest</h4>
            </div>
            <div class="panel-body">
              <form [formGroup]="alternateBG_Form" (ngSubmit)="saveAlternateBG()">
                <div class="form-group row">
                  <label for="normal-field" class="col-md-3  col-form-label text-md-right">Name*</label>
                  <div class="col-md-8 ">
                    <span *ngIf="alternateBG_Form.controls.name.errors?.backend" class="errMsg __fromBackend">{{alternateBG_Form.controls.name.errors?.backend}}</span>
                    <input type="text" class="form-control" formControlName="name" name="name" placeholder="">
                    <span class="errMsg" *ngIf="!alternateBG_Form.controls.name.valid && !alternateBG_Form.controls.name.untouched">
                      <span [hidden]="!alternateBG_Form.controls.name.errors.required">Name is required</span>
                    </span>
                  </div>
                </div>
                <div class="form-group row">
                  <label class="col-md-3 col-form-label text-md-right" for="default-select">Status</label>
                  <div class="col-md-8 ">
                    <div class="radio-horizontal">
                      <div class="abc-radio">
                        <input type="radio" formControlName="status" id="radio-1" [value]="true">
                        <label for="radio-1">
                          Active
                        </label>
                      </div>
                      <div class="abc-radio">
                        <input type="radio" formControlName="status" id="radio-2" [value]="false">
                        <label for="radio-2">
                          Inactive
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-8 offset-md-3">
                    <div class="">
                      <button type="submit" class="btn btn-sm btn-inverse capitalized">
                        <i class="fa fa-check"></i>Save</button>
                      <button (click)="hideAlternateBG()" class="btn btn-sm btn-secondary">Cancel</button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4" *ngFor="let booking of selectedBooking.detail">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4>Room Details</h4>
            </div>
            <div class="panel-body">
              <table class="table table-no-mar">
                <tr>
                  <td>Name:</td>
                  <td class="capitalize">
                    <strong>{{booking.title}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>Guest Name:</td>
                  <td style="text-transform:uppercase;">
                    <strong>{{booking.guest_name}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>Type:</td>
                  <td class="capitalize">
                    <strong>{{booking.name}}</strong>
                  </td>
                </tr>
                <tr>
                  <td>Rate:</td>
                  <td>
                    <strong>
                      <span *ngIf="booking.net_amount">
                        <i class="fa fa-inr"></i>&nbsp;{{booking.net_amount}}
                      </span>
                      <span *ngIf="!booking.net_amount">
                        Free
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr *ngIf="booking.extra_charges">
                  <td>Extra Guest Charge:</td>
                  <td>
                    <strong>
                      <span>
                        <i class="fa fa-inr"></i>&nbsp;{{booking.extra_charges}}
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr>
                  <td>Check In date:</td>
                  <td>
                    <strong>
                      <span>
                        {{(booking.start ? booking.start : '') | date:'MMM d, y'}}&nbsp;
                      </span>
                    </strong>
                  </td>
                </tr>
                <tr>
                  <td>Check Out date:</td>
                  <td>
                    <strong>
                      <span>
                        {{(booking.end ? formatCheckoutDate(booking.end) : '') | date:'MMM d, y'}}&nbsp;
                      </span>
                    </strong>
                  </td>
                </tr>
              </table>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</section>