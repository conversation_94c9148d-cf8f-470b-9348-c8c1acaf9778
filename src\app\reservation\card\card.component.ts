import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Modal } from 'app/shared/directive/modal/modal';
import { ReservationServices } from 'app/shared/services/reservation.services';
import { Subscription } from 'rxjs';

/** Check-In component popup rendering through this card component */
@Component({
  selector: 'card',
  templateUrl: './card.component.html',
})
export class CardComponent extends Modal implements OnInit, OnDestroy {

  //Pre-retrieved data
  checkInData: any = {
    "currentOperation":"CheckIN",
    "startDateAndTime":"2209252323",
    "endDateAndTime":"2209272323",
    "roomId":"RM0001",
    "doorId":"DR01"
  };
  
  showData: boolean = false;
  showError: boolean = false;
  @ViewChild('clsPopup') clsPopup:any;
  respJson: any = {};

  constructor(private reservation_S: ReservationServices) {
    super();
  }

  countDown:Subscription;
  counter = 120;
  tick = 1000;

  ngOn<PERSON><PERSON>roy(){
    this.countDown=null;
  }

  ngOnInit(): void {
    this.showData = false;
    this.showError = false;
    this.getCheckInData();
  }

  getCheckInData() {
    //Backend API call
    let payload = this.checkInData;
    this.showData = false;
    this.reservation_S.getCheckInData(payload).subscribe((data) => {

      //Success
      console.log('Get CheckIN data API Response: ' + JSON.stringify(data));
      if(data['status'] == true || data['status'] == 'true'){
        this.showData = true;
        this.showError = false;
        //TODO : close popup and proceed with next step on success.
  
        if(this.clsPopup.nativeElement){
          this.clsPopup.nativeElement.click();
        }
      } else {
        this.showData = false;
        this.showError = true;
        if(this.clsPopup.nativeElement){
          this.clsPopup.nativeElement.click();
        }
      }
      this.respJson = data;
    }, (error) => {
      //Failure -> TODO : Show respective error to the user and do not close popup.
      this.showData = false;
      this.showError = true;
      console.log('ERROR:: ' + error);
    });

  }

  showErrorMsg(){
    this.showData = false;
    this.showError = true;
  }

}
