<style>
  .form {
    padding: 25px;
    background: #fff;
    border-radius: 10px;
  }

</style>
<div class="container">
  <div class="jumbotron">
    <div class="form col-sm-6 offset-sm-3 col-md-4 offset-md-4">
      <form [formGroup]="setPasswordForm" (ngSubmit)="setPassword()">
        <div class="row">
          <div class="col-sm-12">
            <input type="password" placeholder="{{'SET_PASSWORD.NEW_PASS' | translate:param}}" formControlName="new_password" class="form-control">
            <span class="errMsg" *ngIf="!setPasswordForm.controls.new_password.valid && setPasswordForm.controls.new_password.touched">
                <span [hidden]="!setPasswordForm.controls.new_password.errors?.required">{{'SET_PASSWORD.PASSS_REQ' | translate:param}}</span>
              </span>
          </div>
        </div>
        <br>
        <div class="row">
          <div class="col-sm-12">
            <input type="password" placeholder="{{'SET_PASSWORD.RE_TYP' | translate:param}}" formControlName="cfm_password" class="form-control">
            <span class="errMsg" *ngIf="!setPasswordForm.controls.cfm_password.valid && setPasswordForm.controls.cfm_password.touched">
                <span [hidden]="!setPasswordForm.controls.cfm_password.errors?.required">{{'SET_PASSWORD.CONF_PASS_REQ' | translate:param}}</span>
                <span [hidden]="!setPasswordForm.controls.cfm_password.errors?.MatchPassword">{{'SET_PASSWORD.PASS_NO_MATCH' | translate:param}} </span>
              </span>
          </div>
        </div>
        <br>
        <div class="row">
          <div class="col-sm-12">
              <input type="submit" [disabled]="!setPasswordForm.valid" value="{{'SET_PASSWORD.SET_PASS' | translate:param}}" class="btn btn-sm btn-inverse btn-block">
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
