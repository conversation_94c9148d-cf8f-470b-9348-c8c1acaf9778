// Polyfills

// import 'ie-shim'; // Internet Explorer 9 support

// Import core-js features
// import 'core-js/es/symbol';
// import 'core-js/es/object';
// import 'core-js/es/function';
// import 'core-js/es/parse-int';
// import 'core-js/es/parse-float';
// import 'core-js/es/number';
// import 'core-js/es/math';
// import 'core-js/es/string';
// import 'core-js/es/date';
// import 'core-js/es/array';
// import 'core-js/es/regexp';
// import 'core-js/es/map';
// import 'core-js/es/set';
// import 'core-js/es/weak-map';
// import 'core-js/es/weak-set';
// import 'core-js/es/typed-array';
// import 'core-js/es/reflect';

// Import reflect metadata
import 'reflect-metadata';

// Import zone.js
import 'zone.js/dist/zone';

// Typescript emit helpers polyfill
import 'ts-helpers';

if ('production' === ENV) {
  // Production
} else {
  // Development
  Error.stackTraceLimit = Infinity;
  require('zone.js/dist/long-stack-trace-zone');
}
