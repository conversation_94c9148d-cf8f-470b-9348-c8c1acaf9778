import { Component, OnInit, OnDestroy } from '@angular/core';
import { BuildingService } from './../shared/services/building.service';
import { AuthGuard } from "../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
declare var Messenger: any;
@Component({
    selector: 'building-list',
    templateUrl: './building.component.html'
})

export class BuildingComponent implements OnInit {
    config: any;// New Change ****
    buildings: any[];
    searchQuery: string;
    originalBuildings: any[];
    dharamshalas: any[] = [];
    public selectedBuilding: any;
    public canViewRecords: boolean;
    
    hiddenAddBuilding : boolean = true;
    hiddenEditBuilding : boolean = true;

    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****

    // service variables
    private sub: any;
    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
        private BS: BuildingService
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() {
        this.canViewRecords = true;            
        this.sub = this.BS.getAllBuildings()
            .subscribe((res) => {
                if (res.status == "success") {
                    this.buildings = res.data.buildings;
                    // console.log("this.buildings : ",this.buildings);
                    this.originalBuildings = res.data.buildings;
                    this.dharamshalas = res.data.dharamshalas;
                    // console.log("this.dharamshalas : ", this.dharamshalas);
                    if(this.dharamshalas.length == 0){
                        Messenger().post({  hideAfter: 5,
                            message: "No dharamshala found. Please add Dharamshala before adding building.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.buildings;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }
    showEditbuilding(ele) {
        this.selectedBuilding = ele;
        this.hiddenEditBuilding = !this.hiddenEditBuilding;
    }
    // add component
    showAddBuilding(){
        this.hiddenAddBuilding = !this.hiddenAddBuilding;
    }
    handleAddBuildingEvent(event) {
        this.hiddenAddBuilding = event.gethiddenAddBuilding;
        if(this.canViewRecords && event.data){
            this.originalBuildings.push(event.data);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }
    // edit component
    showEditBuilding(){
        this.hiddenEditBuilding = !this.hiddenEditBuilding;
    }
    handleEditBuildingEvent(event) {
        this.hiddenEditBuilding = event.gethiddenEditBuilding;
        if(this.canViewRecords && event.data){
            this.buildings[this.findIndex(event.data.id, "id") - 1] = event.data;
            this.originalBuildings[this.findIndex(event.data.id, "id",this.originalBuildings) - 1] = event.data;
        }
    }
    searchEvent() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery.trim() != '') {
            this.buildings = this.buildings.filter( data => {
                return ((<string>data.name).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    initializeData() {
        this.buildings = this.originalBuildings;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    ngOnDestroy(){
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if(this.sub){
            this.sub.unsubscribe();
        }

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}