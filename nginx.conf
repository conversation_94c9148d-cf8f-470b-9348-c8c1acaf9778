events{}
http {
    include /etc/nginx/mime.types;
    server {
        listen 8080;
        return 301 https://$host$request_uri:8080;
    }
#    server {
#         listen 443 ssl;
#         server_name bookings.sgadi.com;
#         ssl_certificate /etc/nginx/bookings.sgadi.com.p7b;
#         ssl_certificate_key /etc/nginx/bookings.sgadi.com_key.key;
#         root /usr/share/nginx/html;
#         location / { 
#             try_files $uri $uri/ /index.html;
#         }
#     }
server {
    listen 443 ssl;
    server_name bookings.swaminarayangadi.com;
    ssl_certificate /etc/nginx/bookings.swaminarayangadi.com.crt;
    ssl_certificate_key /etc/nginx/bookings.swaminarayangadi.com_key.txt;
    root /usr/share/nginx/html;
    location / { 
        try_files $uri $uri/ /index.html;
    }
}

}