{"name": "Dharamshala", "version": "3.4.0", "description": "Dharamshala is a hotel management system", "scripts": {"install": "napa", "build:dev": "webpack --config config/webpack.dev.js --progress --profile", "build:prod": "webpack --config config/webpack.prod.js  --progress --profile --bail", "build": "npm run build:dev", "ci": "npm run lint && npm test && npm run e2e", "clean:dist": "npm run rimraf -- dist", "clean:install": "npm set progress=false && npm install", "clean:start": "npm start", "clean": "npm cache clean && npm run rimraf -- node_modules dist", "github-deploy:dev": "webpack --config config/webpack.github-deploy.js --progress --profile --github-dev", "github-deploy:prod": "webpack --config config/webpack.github-deploy.js --progress --profile --github-prod", "lint": "npm run tslint \"src/**/*.ts\"", "postversion": "git push && git push --tags", "prebuild:dev": "npm run clean:dist", "prebuild:prod": "npm run clean:dist", "preclean:install": "npm run clean", "preclean:start": "npm run clean", "preversion": "npm test", "rimraf": "<PERSON><PERSON><PERSON>", "server:dev:hmr": "npm run server:dev -- --inline --hot", "server:dev": "webpack-dev-server --config config/webpack.dev.js --progress --profile --watch --content-base src/", "server:prod": "http-server dist --cors", "server": "npm run server:dev", "start:hmr": "npm run server:dev:hmr", "start": "npm run server:dev", "tslint": "tslint", "typedoc": "typedoc", "version": "npm run build", "watch:dev:hmr": "npm run watch:dev -- --hot", "watch:dev": "npm run build:dev -- --watch", "watch:prod": "npm run build:prod -- --watch", "watch:test": "npm run test -- --auto-watch --no-single-run", "watch": "npm run watch:dev", "webpack-dev-server": "webpack-dev-server", "webpack": "webpack"}, "dependencies": {"@agm/core": "^3.0.0-beta.0", "@angular/animations": "^10.2.5", "@angular/cdk": "^10.2.7", "@angular/common": "^10.2.5", "@angular/compiler": "^10.2.5", "@angular/core": "^10.2.5", "@angular/forms": "^10.2.5", "@angular/platform-browser": "^10.2.5", "@angular/platform-browser-dynamic": "^10.2.5", "@angular/platform-server": "^10.2.5", "@angular/router": "^10.2.5", "@angularclass/conventions-loader": "1.0.2", "@angularclass/hmr": "1.2.1", "@angularclass/hmr-loader": "3.0.2", "@ng-select/ng-select": "^7.0.1", "@ngx-translate/core": "^13.0.0", "@ngx-translate/http-loader": "^6.0.0", "angular2-autosize": "1.0.1", "angular2-datatable": "0.5.2", "angular2-text-mask": "^9.0.0", "animate.css": "3.5.1", "assets-webpack-plugin": "3.4.0", "awesome-bootstrap-checkbox": "1.0.0-alpha.4", "babel-runtime": "^6.26.0", "bootstrap": "4.0.0-alpha.5", "bootstrap-colorpicker": "2.3.6", "bootstrap-datepicker": "1.6.4", "bootstrap-markdown": "2.10.0", "bootstrap-select": "1.11.2", "bootstrap-slider": "9.2.0", "core-js": "^2.6.12", "crypto-js": "^3.1.9-1", "d3": "3.5.17", "dropzone": "4.3.0", "easy-pie-chart": "2.1.7", "font-awesome": "4.7.0", "fontawesome-iconpicker": "^1.3.0", "fullcalendar": "3.0.1", "glyphicons-halflings": "1.9.0", "hammerjs": "2.0.8", "http-server": "0.9.0", "ie-shim": "0.1.0", "jasny-bootstrap": "3.1.3", "jquery": "3.1.0", "jquery-flot": "0.8.3", "jquery-slimscroll": "1.3.8", "jquery-sparkline": "2.3.2", "jquery-ui": "git+https://github.com/jquery/jquery-ui.git#1.11.4", "jquery.animate-number": "0.0.13", "jvectormap": "2.0.4", "magnific-popup": "1.1.0", "markdown": "0.5.0", "messenger": "git+https://github.com/HubSpot/messenger.git#v1.4.2", "moment": "2.15.1", "moment-range": "^3.0.3", "morris.js": "0.5.0", "ng2-datetime": "1.2.2", "ng2-file-upload": "^1.4.0", "ng2-modal": "0.0.21", "ng2-pdf-viewer": "^7.0.1", "ng2-right-click-menu": "0.0.11", "ng2-select2": "^1.0.0-beta.16", "ng2-table": "1.3.2", "ng2-validation": "^4.1.0", "ng4-intl-phone": "^1.2.0", "ngx-bootstrap": "^6.2.0", "ngx-contextmenu": "^6.0.0", "ngx-icon-picker": "^1.0.0", "nvd3": "1.8.4", "pace": "git+https://github.com/HubSpot/pace.git#v0.7.7", "parsleyjs": "2.4.4", "reflect-metadata": "0.1.9", "rickshaw": "1.6.0-rc.0", "rxjs": "^6.6.7", "rxjs-compat": "^6.6.7", "select2": "^4.0.3", "shufflejs": "4.0.2", "webpack-raphael": "2.1.4", "widgster": "0.0.3", "zone.js": "~0.10.3"}, "napa": {"bootstrap-application-wizard": "git://github.com/amoffat/bootstrap-application-wizard.git#3768da3142f43428c5f63284407a2481a9e283d3", "twitter-bootstrap-wizard": "git://github.com/VinceG/twitter-bootstrap-wizard.git#62a9aca8cc61fdb496c4bf7b92e1ff70c698667e", "bootstrap_calendar": "https://github.com/xero/bootstrap_calendar.git#1.0.1", "jquery.nestable": "https://github.com/dbushell/Nestable.git#4f93032cfafe2002f24ed6bd7dc1510931498503", "jquery.flot.animator": "https://github.com/Codicode/flotanimator.git#3c256c0183d713fd3bf41d04417873928eb1a751", "flot-orderBars": "https://github.com/emmerich/flot-orderBars.git#98081459571f60f7d95cc79a848c4f558d077486", "jQuery-Mapael": "https://github.com/neveldo/jQuery-Mapael.git#0.7.1", "skycons": "https://github.com/darkskyapp/skycons.git#7095ecf5f653dbcadbddb0d317b42e65fe091eae"}, "devDependencies": {"@angular/compiler-cli": "^10.2.5", "@types/hammerjs": "2.0.33", "@types/jasmine": "~3.5.0", "@types/node": "^12.11.1", "@types/source-map": "0.1.27", "@types/uglify-js": "2.0.27", "@types/webpack": "2.0.0", "angular-router-loader": "0.4.0", "angular2-template-loader": "^0.6.2", "awesome-typescript-loader": "^5.2.1", "codelyzer": "^6.0.0", "copy-webpack-plugin": "^6.3.2", "css-loader": "^3.6.0", "exports-loader": "0.6.3", "expose-loader": "0.7.1", "file-loader": "0.9.0", "html-webpack-plugin": "^4.5.0", "imports-loader": "0.6.5", "istanbul-instrumenter-loader": "0.2.0", "json-loader": "0.5.4", "metrojs": "0.9.77", "napa": "2.3.0", "npm-run-all": "3.1.2", "parse5": "2.2.2", "raw-loader": "0.5.1", "rimraf": "^2.5.4", "sass": "^1.32.13", "sass-loader": "^7.3.1", "source-map-loader": "0.1.5", "string-replace-loader": "1.0.5", "style-loader": "^1.3.0", "to-string-loader": "1.1.4", "ts-helpers": "1.1.2", "ts-node": "1.7.0", "tslint": "^5.9.1", "tslint-loader": "2.1.5", "typedoc": "0.5.0", "typescript": "4.0.8", "url-loader": "0.5.7", "webpack": "^4.44.2", "webpack-cli": "^3.3.12", "webpack-dev-middleware": "1.6.1", "webpack-dev-server": "^3.11.0", "webpack-md5-hash": "0.0.5", "webpack-merge": "1.0.1"}, "overrides": {"rickshaw": {"dependencies": {"d3": "3.5.5"}}}, "engines": {"node": ">= 4.2.1", "npm": ">= 3"}, "resolutions": {"core-js": "^3.8.0"}}