import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { NgSelectModule } from '@ng-select/ng-select';
import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';

import { BuildingComponent } from './building.component';
import { AddBuildingComponent } from './+add/addBuilding.component';
import { EditBuildingComponent } from './+edit/editBuilding.component';
import { BuildingService } from './../shared/services/building.service';

import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
// all routes related to this Role module.
export const routes = [
  { path: '', component: BuildingComponent, pathMatch: 'full' }
//   { path: 'add', component: AddAmenitiesComponent },
//   { path: 'edit/:id', component: EditAmenitiesComponent }
];

@NgModule({
    imports: [
        FormsModule,
        CommonModule,
        TooltipModule.forRoot(),
        NgSelectModule,
        DataTableModule,
        ReactiveFormsModule,
        RouterModule.forChild(routes),
        TranslateModule.forRoot({
           loader: { 
              provide: TranslateLoader, 
              useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
            deps: [HttpClient] 
          }}),
    ],
    exports: [],
    declarations: [
        BuildingComponent,
        AddBuildingComponent,
        EditBuildingComponent
    ],
    providers: [BuildingService],
})
export class BuildingModule { }