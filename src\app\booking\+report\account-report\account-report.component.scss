.panel-heading h4 {
    margin: 0;
  }
  .red-header{
    color: red;
  }
  .panel-default {
    border: 1px solid #ddd;
    border-radius: 3px;
  }
mark {
  background-color: #ff0;
}
.table-scroll {
  overflow: auto;
}
table.no-bottom-border:last-child {
  border-bottom: none !important;
}
.with-labels {
  .label {
    font-size: 12px;
    font-weight: bold;
    padding: 3px 7px;
    min-width: 100px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    text-transform: uppercase;
    filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, .352));
    -webkit-filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, .352));
    &.label-success {
      background: #4CAF50;
    }
    &.label-danger {
      background: #F44336;
    }
    &.label-warning {
      background: #CDDC39;
      color: #333 !important
    }
    &.label-primary {
      background: #2196F3;
    }
    &.label-light {
      background: rgb(138, 138, 138);
    }
  }
  .uppercase {
    text-transform: uppercase;
  }

.reference-level {
      cursor:pointer;
  }
.guest-type-level {
      // cursor:pointer;
      background-color: #faebd7;
      color: #795548;
      font-weight: bold;
      td {
          padding-left: 15px;
      }
}
  .guest-level {
      color: #FFFFFF;
      font-weight: bold;
      background-color: #8e7452;
      td {
          padding-left: 30px;
          span.revenue-report-note {
              white-space: normal;
          }
      }
      td:last-child {
          padding-left: 15px;
      }
  }
}
.panel-default {
  border: 1px solid #ddd;
  border-radius: 3px;
}
div.amount-summaries {
    margin-top: 15px;
    margin-bottom: 15px;
    .row.filter-row {
        margin-top: 5px;
      //   color: #5d8fc2;
    }
}
