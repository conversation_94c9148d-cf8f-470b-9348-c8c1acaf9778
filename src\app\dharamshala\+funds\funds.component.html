<div class="row">
  <div [ngClass]="{'col-sm-6': (!hideAdd || !hideEdit),'col-sm-12': (hideAdd && hideEdit)}">
    <section class="widget">
      <header>
        <h4><span class="" style="color: red;"><i class="fa fa-inr"></i>&nbsp;&nbsp;{{'FUNDS.FUNDS_MANAGE' | translate:param}}</span></h4>
      </header>
      <hr class="large-hr">
      <div class="float-sm-right text-right col-sm-12">
        <button type="button" *ngIf="auth.roleAccessPermission('fund','add')" [disabled]="!hideAdd" (click)="showAdd()"  class="display-inline-block btn btn-sm btn-inverse" tooltip="{{'FUNDS.ADD_NEW_FOUND' | translate:param}}" placement="top"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{'FUNDS.ADD' | translate:param}}</button>
        <div class="form-group display-inline-block __search">
          <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'FUNDS.SEARCH' | translate:param}}">
          <span class="form-group-addon"><i class="fa fa-search"></i></span>
          <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
        </div>
      </div>
      <div class="clearfix"></div>
      <div class="widget-body">
        <div class="mt">
          <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
            <thead>
              <tr>
                <th>
                  <mfDefaultSorter by="id">#</mfDefaultSorter>
                </th>
                <th>
                  <mfDefaultSorter by="name">{{'FUNDS.FUNT_TIT' | translate:param}}</mfDefaultSorter>
                </th>
                <th class="no-sort">
                  <mfDefaultSorter by="status">{{'FUNDS.STATUS' | translate:param}}</mfDefaultSorter>
                </th>
                <th *ngIf="auth.roleAccessPermission('fund','edit')" class="no-sort">
                  <mfDefaultSorter by="status">{{'FUNDS.DEFAULT' | translate:param}}</mfDefaultSorter>
                </th>
                <th *ngIf="auth.roleAccessPermission('fund','edit')" class="no-sort">
                  <mfDefaultSorter by="status">{{'FUNDS.ACTION' | translate:param}}</mfDefaultSorter>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let ds of mf.data">
                <td>{{ds.id}}</td>
                <td><span class="uppercase fw-semi-bold"> {{ds.name}}</span></td>
                <td class=" ">
                  <span class="text-success" *ngIf="ds.status">{{'FUNDS.ACTIVE' | translate:param}}</span>
                  <span class="text-danger" *ngIf="!ds.status">{{'FUNDS.INACTIVE' | translate:param}}</span>
                </td>
                <td *ngIf="auth.roleAccessPermission('fund','edit')">
                  <div style="position: relative">
                    <input type="radio" id="{{ds.id}}_is_default_radio" class="is_default_radio" name="is_default_radio" (change)="changeDefault(ds)" [checked]="ds.is_default">
                    <label for="{{ds.id}}_is_default_radio"></label>
                  </div>
                </td>
                <td *ngIf="auth.roleAccessPermission('fund','edit')" class="width-100">
                  <button type="button" *ngIf="auth.roleAccessPermission('fund','edit')" (click)="showEdit(ds)" class="btn btn-xs btn-default" tooltip="{{'FUNDS.EDIT_FUND_DETA' | translate:param}}" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{'FUNDS.EDIT' | translate:param}}</button>
                </td>
              </tr>
              <tr *ngIf="canViewRecords && mf.data.length === 0">
                <td colspan="100">
                  {{'FUNDS.NO MATCHES' | translate:param}}
                </td>
              </tr>
              <tr *ngIf="!canViewRecords">
                <td class="text-danger" colspan="100">
                  {{'FUNDS.PERMISSION_DENIED' | translate:param}}
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="12">
                  <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </section>
  </div>
  <add-funds class="col-sm-6" *ngIf="!hideAdd" (hideAddEvent)="handleAdd()" (addToList)="addTolistEvent($event)"></add-funds>
  <edit-funds class="col-sm-6" *ngIf="!hideEdit" [selectedFund]="selectedFund" (hideEditEvent)="handleEdit($event)" (editToList)="editTolistEvent($event)"></edit-funds>
</div>
