import { _secretKey } from './../../shared/globals/config';
import { Component, EventEmitter, OnInit, ElementRef, Output, HostListener
} from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from './../../shared/services/user.service';
import { AuthGuard } from './../../shared/guards/auth-guard.service';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
import { AppConfig } from '../../app.config';
import * as CryptoJS from 'crypto-js';

@Component({
  selector: '[navbar]',
  templateUrl: './navbar.template.html'
})
export class Navbar implements OnInit {
  @Output() toggleSidebarEvent: EventEmitter<any> = new EventEmitter();
  public ud: any;
  public isDropdownOpen: boolean = false;
  private _secretKey: String = _secretKey;
  public $destroy = new Subject(); // New Change ****
  private langChangeSub: Subscription; // New Change ****
  constructor(
    public translate: TranslateService,// New Change ****
    el: ElementRef,
    private router: Router,
    private authGuard: AuthGuard,
    private uService: UserService,
    private TS: TranslateEventService,
    private config: AppConfig
  ) {
    const currentLang = localStorage.getItem('currentLang');
    translate.setDefaultLang(currentLang || 'en');
    // New Change ****
    this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
      this.changeLang(res);
    });
  }
  changeLang(lang: string) {
    // New Change ****
    this.translate.use(lang);
  }

  toggleSidebar(state): void {
    this.toggleSidebarEvent.emit(state);
  }

  ngOnInit(): void {
  const userData = localStorage.getItem('0');
  if (userData) {
    const bytes = CryptoJS.AES.decrypt(userData, this._secretKey);
    this.ud = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
  }
}
  toggleDropdown(event: Event): void {
    event.preventDefault();
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  @HostListener('document:click', ['$event'])
  onOutsideClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.nav-item.dropdown')) {
      this.isDropdownOpen = false;
    }
  }
  shiftOut() {
    this.router.navigate(['admin/shift/out']);
  }
  logout() {
    this.uService.userLogOut().subscribe(res => {
      if (res.status === 'success') {
        console.log("123123123132");
        localStorage.removeItem('avialablebill')
        this.authGuard.removeUser();
        this.router.navigate(['']);
      }
    });
  }
  ngOnDestroy() {
    this.$destroy.next(); // New Change ****
    this.$destroy.complete(); // New Change ****
    
    // New Change ****
    if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
}
}
