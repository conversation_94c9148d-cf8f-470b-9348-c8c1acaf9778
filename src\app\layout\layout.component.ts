import { UserService } from './../shared/services/user.service';
import { Component, ViewEncapsulation, ElementRef, ViewChild, OnInit, OnDestroy, AfterViewInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { AppConfig } from '../app.config';
import { TranslateEventService } from 'app/shared/services/translation.service';
import { Subject, Subscription } from 'rxjs-compat';// New Change ****
import { TranslateService } from '@ngx-translate/core';
import * as CryptoJS from 'crypto-js';
import { _secretKey } from './../shared/globals/config';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { ReservationServices } from 'app/shared/services/reservation.services';
import { BookingService } from 'app/shared/services/booking.service';
import { Sidebar } from './sidebar/sidebar.component';

declare var jQuery: any;
declare var Hammer: any;
declare var Raphael: any;
@Component({
  selector: 'layout',
  encapsulation: ViewEncapsulation.None,
  templateUrl: './layout.template.html',
  host: {
    '[class.nav-static]': 'config.state["nav-static"]',
    '[class.chat-sidebar-opened]': 'chatOpened',
    '[class.app]': 'true',
    id: 'app'
  },
  providers: [UserService],
})
export class Layout implements OnInit, OnDestroy, AfterViewInit {
  config: any;
  configFn: any;
  $sidebar: any;
  el: ElementRef;
  router: Router;
  chatOpened: boolean = false;
  translateText: string;
  public $destroy = new Subject(); // New Change ****
  private langChangeSub: Subscription; // New Change ****
  public bookingTypeOptions = {
    width: '100%',
  };
  pageUrl = '';
  phone_number: Number;
  PhoneErrorMsg: any;
  PhoneErrorMsgStyle: any;
  phoneButtonLoader: boolean = false;
  defaultMenu: any;
  StartDate :any;
  StartDatetime:any;
  EndDate:any;
  EndDatetime:any;
  menus: any;
  menuListJson: any;
  pageArray = [];
  private _secretKey: String = _secretKey;
  @ViewChild('checkInDynamicVerifyModal') public checkInDynamicVerifyModal: ModalDirective;
  @ViewChild('getPhoneNumberModal') public getPhoneNumberModal: ModalDirective;
  @ViewChild('sidebarComponent') sidebarComponent: Sidebar;

  getCardInfo: any;
  displayTime: any;
  getCardInfoValid: boolean = false;
  dynamicTimer: any;
  errMsg: any;
  tableData:any;
  cardRoomName: any;
  // StartDate:any;
  constructor(
    router: Router,
    el: ElementRef,
    config: AppConfig,
    private TS: TranslateEventService,
    public translate: TranslateService,
    private reservation_S: ReservationServices,
    private BS: BookingService,
  ) {
    this.config = config.getConfig();// New Change ****
    let currentLang = localStorage.getItem('currentLang'); // New Change ****
    translate.setDefaultLang(currentLang);// New Change ****

    Raphael.prototype.safari = function (): any { return; };
    this.el = el;
    this.config = config.getConfig();
    this.configFn = config;
    this.router = router;

    // New Change ****
    this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
      this.changeLang(res);
    });

    // get default menu
    let defaultMenu = localStorage.getItem('dm');
    let DMdata = CryptoJS.AES.decrypt(defaultMenu, this._secretKey);
    let DMJson = JSON.parse(DMdata.toString(CryptoJS.enc.Utf8));
    this.defaultMenu = DMJson;
    // get user given menu
    let userMenu = localStorage.getItem('m');
    let UMdata = CryptoJS.AES.decrypt(userMenu, this._secretKey);
    let UMJson = JSON.parse(UMdata.toString(CryptoJS.enc.Utf8));
    this.menus = UMJson
    // fill menu items
    if (this.menus.length > 0) {
      this.menuListJson = this.inputRouterLink(this.menus[1]);
      this.pageArray = this.menuListJson.children.map((obj) => {
        return { id: obj.routerLink, text: obj.title };
      });

    } else {
      // this.isSuperAdmin = true;
      // this.menuListJson = menuList // this means user is super admin
    }
  }
  changeLang(lang: string) {
    // New Change ****
    this.translate.use(lang);
  }
  toggleSidebarListener(state): void {
    let toggleNavigation = state === 'static'
      ? this.toggleNavigationState
      : this.toggleNavigationCollapseState;
    toggleNavigation.apply(this);
    localStorage.setItem('nav-static', this.config.state['nav-static']);
  }

  toggleChatListener(): void {
    jQuery(this.el.nativeElement).find('.chat-notification-sing').remove();
    this.chatOpened = !this.chatOpened;

    setTimeout(() => {
      // demo: add class & badge to indicate incoming messages from contact
      // .js-notification-added ensures notification added only once
      jQuery('.chat-sidebar-user-group:first-of-type ' +
        '.list-group-item:first-child:not(.js-notification-added)')
        .addClass('active js-notification-added')
        .find('.fa-circle')
        .after('<span class="badge tag-danger ' +
          'pull-right animated bounceInDown">3</span>');
    }, 1000);
  }

  toggleNavigationState(): void {
    this.config.state['nav-static'] = !this.config.state['nav-static'];
    if (!this.config.state['nav-static']) {
      this.collapseNavigation();
    } else {
      // When switching to static mode, ensure proper menu state
      this.expandNavigation();
      // Reset all menu states to collapsed when entering static mode
      if (this.sidebarComponent) {
        setTimeout(() => {
          this.sidebarComponent.closeAllMenus();
        }, 100);
      }
    }
    
    // Notify sidebar component about navigation state change
    if (this.sidebarComponent) {
      this.sidebarComponent.onNavigationStateChange();
    }
  }

  resetFlagForCheckIn(){
    this.reservation_S.resetFlagForCheckIn().subscribe((data) => {

    }, (error) => {
      console.log('ERROR:: ' + error);
    });
}

  getVerifyCard() {
    let roomsData = [];
    this.BS.getRoomInfo().subscribe((res) => {
      res.data.roomDetails.forEach((item) => {
        item.rooms.forEach((e) => {
          roomsData.push(e);
        });
      });
    });
    this.StartDatetime = '';
    this.EndDatetime='';
    this.getCardInfoValid = false;
    //Backend API call
    this.checkInDynamicVerifyModal.show();
    this.dynamicTimerLayout(2)
    let payload = {
      "currentOperation": "Verify",
      "startDateAndTime": null,
      "endDateAndTime": null,
      "roomId": null,
      "doorId": null,
    };
    this.errMsg = '';
    this.getCardInfo = { message: 'Put your card on reader device to start verify card process.' }
    this.reservation_S.verifyProcess(payload).subscribe((data) => {
      if(data.status == true || data.status == 'true'){
        this.getCardInfoValid = true;
        this.getCardInfo = data;
        this.tableData = data.message;
        this.cardRoomName = roomsData.filter(x => x.id == data.roomId)[0];
        
        this.errMsg = '';
      }else{
        this.getCardInfoValid = false;
        this.errMsg = data.message;
      } 
      //Divyesh
      // Show respective error

      var startTime =data.startDateAndTime;
      var EndTime = data.endDateAndTime
      
      //Get Start Date
      this.StartDatetime = '';
      this.EndDatetime='';
      var stringStartDate = startTime ? startTime.toString() : '';
      this.StartDate = stringStartDate.match(/.{1,2}/g)
      
      this.StartDatetime = this.StartDate[2]+"/"+ this.StartDate[1]+"/20"+this.StartDate[0]+" "+this.StartDate[3]+":"+this.StartDate[4];

      //Get End Date
      
      var stringEndtDate = EndTime? EndTime.toString() : '';
      this.EndDate = stringEndtDate.match(/.{1,2}/g)
      
      this.EndDatetime = this.EndDate[2]+"/"+ this.EndDate[1]+"/20"+this.EndDate[0]+" "+this.EndDate[3]+":"+this.EndDate[4];

     
      //TODO : close popup  and  message .
    }, (error) => {
      //Failure -> TODO : Show respective error to the user and do not close popup.

      console.log('ERROR:: ' + error);
    });
  }

  openSendBookingModal() {
    this.getPhoneNumberModal.show();
  }

  sendOnlineReservationRequest() {
    if (!this.phone_number) {
      this.PhoneErrorMsg = 'Phone Number is Required!';
      this.PhoneErrorMsgStyle = 'alert alert-danger';
      return;
    }

    this.phoneButtonLoader = true;
    this.reservation_S.SendonlineBookingUrl({ phone_number: this.phone_number }).subscribe(
      (response) => {
        console.log('Response from the server:', response);
        if (response.status === "success" || response.status == 'true') {
          this.PhoneErrorMsg = response.message;
          this.PhoneErrorMsgStyle = 'alert alert-success';
          this.phoneButtonLoader = false;
          setTimeout(() => {
            this.getPhoneNumberModal.hide();
            this.PhoneErrorMsg = null
            this.phone_number = null
            this.phoneButtonLoader = false;
          }, 1500);
        } else {
          this.PhoneErrorMsg = response.errors.msg || 'Please check if number is valid 10 digit.';
          this.PhoneErrorMsgStyle = 'alert alert-danger';
          this.phoneButtonLoader = false;
        }
      },
      (error) => {
        error =error.json()
        console.log('Error while calling API:', error);
        this.PhoneErrorMsg = error.errors.msg || error.message || 'Please check if number is valid 10 digit.';
        this.phoneButtonLoader = false;
        this.PhoneErrorMsgStyle = 'alert alert-danger';
      }
    );
  }

  expandNavigation(): void {
    // this method only makes sense for non-static navigation state
    if (this.isNavigationStatic()
      && (this.configFn.isScreen('lg') || this.configFn.isScreen('xl'))) { return; }

    jQuery('layout').removeClass('nav-collapsed');
    // Find all active collapse elements
    const $activeCollapse = this.$sidebar.find('.active .active').closest('.collapse');
    if ($activeCollapse.length) {
      $activeCollapse.each((index, element) => {
        const $element = jQuery(element);
        $element.addClass('in');
        $element.css('height', 'auto');
        $element.css('display', 'block');
      });
      $activeCollapse.siblings('[data-toggle=collapse]').removeClass('collapsed');
    }
    
    // Ensure all menu items are properly reset
    const $menuItems = this.$sidebar.find('.sidebar-nav li');
    $menuItems.each((index, element) => {
      const $element = jQuery(element);
      const $collapse = $element.find('.collapse');
      const $toggleIcon = $element.find('.toggle.fa');
      
      // Reset all collapsed menus to proper state
      if ($collapse.length && !$collapse.hasClass('in')) {
        $collapse.css('height', '0px');
        $collapse.css('display', 'none');
        $element.removeClass('open');
        if ($toggleIcon.length) {
          $toggleIcon.removeClass('fa-angle-up').addClass('fa-angle-down');
        }
      }
    });
  }

  collapseNavigation(): void {
    // this method only makes sense for non-static navigation state
    if (this.isNavigationStatic()
      && (this.configFn.isScreen('lg') || this.configFn.isScreen('xl'))) { return; }

    jQuery('layout').addClass('nav-collapsed');
    // Find all expanded collapse elements
    const $expandedCollapse = this.$sidebar.find('.collapse.in');
    if ($expandedCollapse.length) {
      $expandedCollapse.each((index, element) => {
        const $element = jQuery(element);
        $element.removeClass('in');
        $element.css('height', '0px');
        $element.css('display', 'none');
      });
      
      // Reset all parent menu items
      const $menuItems = this.$sidebar.find('.sidebar-nav li');
      $menuItems.each((index, element) => {
        const $element = jQuery(element);
        $element.removeClass('open');
        
        // Reset toggle icons
        const $toggleIcon = $element.find('.toggle.fa');
        if ($toggleIcon.length) {
          $toggleIcon.removeClass('fa-angle-up').addClass('fa-angle-down');
        }
      });
      
      // Reset collapsed state for trigger links
      $expandedCollapse.siblings('[data-toggle=collapse]').addClass('collapsed');
    }
    
    // Close all opened submenus using the sidebar component
    if (this.sidebarComponent) {
      this.sidebarComponent.closeAllMenus();
    }
  }

  /**
   * Check and set navigation collapse according to screen size and navigation state
   */
  checkNavigationState(): void {
    if (this.isNavigationStatic()) {
      if (this.configFn.isScreen('sm')
        || this.configFn.isScreen('xs') || this.configFn.isScreen('md')) {
        this.collapseNavigation();
      }
    } else {
      if (this.configFn.isScreen('lg') || this.configFn.isScreen('xl')) {
        setTimeout(() => {
          this.collapseNavigation();
        }, this.config.settings.navCollapseTimeout);
      } else {
        this.collapseNavigation();
      }
    }
  }

  isNavigationStatic(): boolean {
    return this.config.state['nav-static'] === true;
  }

  toggleNavigationCollapseState(): void {
    if (jQuery('layout').is('.nav-collapsed')) {
      this.expandNavigation();
    } else {
      this.collapseNavigation();
    }
  }

  _sidebarMouseEnter(): void {
    if (this.configFn.isScreen('lg') || this.configFn.isScreen('xl')) {
      // Only expand navigation if NOT in static mode
      if (!this.isNavigationStatic()) {
        this.expandNavigation();
      }
    }
  }
  _sidebarMouseLeave(): void {
    if (this.configFn.isScreen('lg') || this.configFn.isScreen('xl')) {
      // Only collapse navigation if NOT in static mode
      if (!this.isNavigationStatic()) {
        this.collapseNavigation();
        // Also close all submenus when mouse leaves (only for non-static mode)
        if (this.sidebarComponent) {
          this.sidebarComponent.closeAllMenus();
        }
      }
    }
  }

  enableSwipeCollapsing(): void {
    let swipe = new Hammer(document.getElementById('content-wrap'));
    let d = this;

    swipe.on('swipeleft', () => {
      setTimeout(() => {
        if (d.configFn.isScreen('md')) { return; }

        if (!jQuery('layout').is('.nav-collapsed')) {
          d.collapseNavigation();
        }
      });
    });

    swipe.on('swiperight', () => {
      if (d.configFn.isScreen('md')) { return; }

      if (jQuery('layout').is('.chat-sidebar-opened')) { return; }

      if (jQuery('layout').is('.nav-collapsed')) {
        d.expandNavigation();
      }
    });
  }

  collapseNavIfSmallScreen(): void {
    if (this.configFn.isScreen('xs')
      || this.configFn.isScreen('sm') || this.configFn.isScreen('md')) {
      this.collapseNavigation();
    }
  }
  changeLanguage() {
    let lang: 'en' | 'hi' = this.translateText == 'English' ? 'en' : 'hi';
    this.translateText = this.translateText == 'English' ? 'Hindi' : 'English';
    this.TS.emitLangChange(lang);
    window.location.reload();
  }

  ngOnInit(): void {
   
    // var str = 2210201010;
    // var string = str.toString()
    // this.StartDate = string.match(/.{1,2}/g)


    
    // var date = data[0]
    // var date = data[0] + data[1]
    this.translateText = this.TS.getCurrentLang() == 'en' ? 'Hindi' : 'English';
    if (localStorage.getItem('nav-static') === 'true') {
      this.config.state['nav-static'] = true;
    }
    if (this.checkInDynamicVerifyModal) {
    this.checkInDynamicVerifyModal.onHide.subscribe((res) => {
      clearInterval(this.dynamicTimer);
      this.resetFlagForCheckIn();
    });
  }
    let $el = jQuery(this.el.nativeElement);
    this.$sidebar = $el.find('[sidebar]');

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        setTimeout(() => {
          this.collapseNavIfSmallScreen();
          window.scrollTo(0, 0);

          $el.find('a[href="#"]').on('click', (e) => {
            e.preventDefault();
          });
        });
      }
    });

    this.$sidebar.on('mouseenter', this._sidebarMouseEnter.bind(this));
    this.$sidebar.on('mouseleave', this._sidebarMouseLeave.bind(this));

    this.checkNavigationState();

    this.$sidebar.on('click', () => {
      if (jQuery('layout').is('.nav-collapsed')) {
        this.expandNavigation();
        // Also ensure sidebar component state is properly reset (only for non-static mode)
        if (this.sidebarComponent && !this.isNavigationStatic()) {
          // Force a small delay to ensure DOM is updated
          setTimeout(() => {
            this.sidebarComponent.closeAllMenus();
          }, 50);
        }
      }
    });

    this.router.events.subscribe(() => {
      this.collapseNavIfSmallScreen();
      window.scrollTo(0, 0);
    });

    if ('ontouchstart' in window) {
      this.enableSwipeCollapsing();
    }

    this.$sidebar.find('.collapse').on('show.bs.collapse', function (e): void {
      // execute only if we're actually the .collapse element initiated event
      // return for bubbled events
      if (e.target !== e.currentTarget) { return; }

      let $triggerLink = jQuery(this).prev('[data-toggle=collapse]');
      jQuery($triggerLink.data('parent'))
        .find('.collapse.in').not(jQuery(this)).collapse('hide');
    })
      /* adding additional classes to navigation link li-parent
       for several purposes. see navigation styles */
      .on('show.bs.collapse', function (e): void {
        // execute only if we're actually the .collapse element initiated event
        // return for bubbled events
        if (e.target !== e.currentTarget) { return; }

        jQuery(this).closest('li').addClass('open');
      }).on('hide.bs.collapse', function (e): void {
        // execute only if we're actually the .collapse element initiated event
        // return for bubbled events
        if (e.target !== e.currentTarget) { return; }

        jQuery(this).closest('li').removeClass('open');
      });
  }

  ngAfterViewInit(): void {
    // Move modal subscription here where view is guaranteed to be initialized
    if (this.checkInDynamicVerifyModal) {
      this.checkInDynamicVerifyModal.onHide.subscribe((res) => {
        clearInterval(this.dynamicTimer);
        this.resetFlagForCheckIn();
      });
    }

    // After layout is initialized, check if we need to show the online booking popup
    const shouldShow = localStorage.getItem('show_online_booking_popup') === '1';
    if (shouldShow) {
      // Trigger the popup component to check counts itself, then open if needed
      const modalCmp = document.querySelector('app-online-booking-popup');
      if (modalCmp) {
        modalCmp.dispatchEvent(new CustomEvent('trigger-check-online-booking-popup', { bubbles: true }));
      }
    }
  }

  pageTypeChanged(event) {
    this.pageUrl = event.id;
    this.router.navigateByUrl(`admin/${event.id}`);
  }

  inputRouterLink(menu: any): Promise<any> {
    let ele = menu;
    this.defaultMenu.forEach(element => {
      if (ele.children) {
        ele.children.forEach(element => {
          let child = element;
          this.defaultMenu.forEach(element => {
            if (element.id == child.router_link) {
              child['routerLink'] = element.url;
            }
          });
        });
      }
    });
    return menu;
  }
  ngOnDestroy() {
    this.$destroy.next(); // New Change ****
    this.$destroy.complete(); // New Change ****

    // New Change ****
    if (this.langChangeSub)
      this.langChangeSub.unsubscribe();
  }
  dynamicTimerLayout(minute) {
    // let minute = 1;
    let seconds: number = minute * 60;
    let textSec: any = "0";
    let statSec: number = 60;

    const prefix = minute < 10 ? "0" : "";

    this.dynamicTimer = setInterval(() => {
      seconds--;
      if (statSec != 0) statSec--;
      else statSec = 59;

      if (statSec < 10) {
        textSec = "0" + statSec;
      } else textSec = statSec;

      this.displayTime = `${prefix}${Math.floor(seconds / 60)}:${textSec}`;

      if (seconds == 0) {
        this.checkInDynamicVerifyModal.hide();
        clearInterval(this.dynamicTimer);
      }
    }, 1000);
  }
}
