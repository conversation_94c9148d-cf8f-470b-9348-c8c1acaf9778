<section class="widget">
  <header>
    <h4><span class=""><i class="fa fa-building"></i>&nbsp;&nbsp;{{'DHARAMSHALA.DHARAM_MANAGE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right text-right col-sm-6">
     <!-- *ngIf="auth.roleAccessPermission('dharamshala','add')" -->
    <a *ngIf="auth.roleAccessPermission('dharamshala','add')" [routerLink]="['add']" class="display-inline-block btn btn-sm btn-inverse" tooltip="{{'DHARAMSHALA.ADD_NEW_DHARAM' | translate:param}}" placement="left"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{'DHARAMSHALA.ADD' | translate:param}}</a>
    <div class="form-group display-inline-block __search">
       <!-- canViewRecords ? searchEvent() : null -->
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{'DHARAMSHALA.SEARCH' | translate:param}}">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body">
    <div class="mt">

         <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
        <tr>
          <th>
            <mfDefaultSorter by="id">#</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="name">{{'DHARAMSHALA.DHARAM_NAME' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="">
            <mfDefaultSorter by="info">{{'DHARAMSHALA.SIGN_AUTHO' | translate:param}}</mfDefaultSorter>
          </th>

          <th class=" ">
            <mfDefaultSorter by="date">{{'DHARAMSHALA.ADMIN_EMAIL' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="no-sort">
            <mfDefaultSorter by="status">{{'DHARAMSHALA.STATUS' | translate:param}}</mfDefaultSorter>
          </th>
           <!-- *ngIf="auth.roleAccessPermission('dharamshala','edit') || auth.roleAccessPermission('dharamshala','view')" -->
          <th *ngIf="auth.roleAccessPermission('dharamshala','edit') || auth.roleAccessPermission('dharamshala','view')" class="no-sort">
            <mfDefaultSorter by="status">{{'DHARAMSHALA.ACTION' | translate:param}}</mfDefaultSorter>
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let ds of mf.data">
          <td>{{ds.id}}</td>
          <td>
            <span class="uppercase fw-semi-bold">
               <!-- *ngIf="auth.roleAccessPermission('dharamshala','view')" -->
              <a *ngIf="auth.roleAccessPermission('dharamshala','view')" [routerLink]="['view',ds.id]">{{ds.name}}</a>
               <!-- *ngIf="!auth.roleAccessPermission('dharamshala','view')" -->
              <span *ngIf="!auth.roleAccessPermission('dharamshala','view')">{{ds.name}}</span>             
            </span>
          </td>
          <td class="">{{ds.user_name}}</td>

          <td>{{ds.email}}</td>
          <td class=" ">
            <span class="text-success" *ngIf="ds.status">{{'DHARAMSHALA.ACTIVE' | translate:param}}</span>
            <span class="text-danger" *ngIf="!ds.status">{{'DHARAMSHALA.INACTIVE' | translate:param}}</span>
          </td>
           <!-- *ngIf="auth.roleAccessPermission('dharamshala','edit') || auth.roleAccessPermission('dharamshala','view')" -->
          <td *ngIf="auth.roleAccessPermission('dharamshala','edit') || auth.roleAccessPermission('dharamshala','view')">
            <div role="group">
               <!-- *ngIf="auth.roleAccessPermission('dharamshala','edit')" -->
              <a *ngIf="auth.roleAccessPermission('dharamshala','edit')" [routerLink]="['edit',ds.id]" class="btn btn-sm btn-default" tooltip="{{'DHARAMSHALA.EDIT_NEW_TOOLTIP' | translate:param}}" placement="left"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{'DHARAMSHALA.EDIT' | translate:param}}</a>&nbsp;
                <!-- *ngIf="auth.roleAccessPermission('dharamshala','view')" -->
              <a *ngIf="auth.roleAccessPermission('dharamshala','view')" [routerLink]="['view',ds.id]" class="btn btn-sm btn-default" tooltip="{{'DHARAMSHALA.VIEW_NEW_TOOLTIP' | translate:param}}" placement="left"><i class="fa fa-question"></i>&nbsp;&nbsp;{{'DHARAMSHALA.VIEW' | translate:param}}</a>
            </div>
          </td>
        </tr>
        <tr *ngIf="canViewRecords && mf.data.length === 0">
          <td colspan="100">
            {{'DHARAMSHALA.NO MATCHES' | translate:param}}
          </td>
        </tr>
        <tr *ngIf="!canViewRecords">
          <td class="text-danger" colspan="100">
            {{'DHARAMSHALA.PERMISSION_DENIED' | translate:param}}
          </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="12">
            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
          </td>
        </tr>
        </tfoot>
      </table>


    </div>
  </div>
</section>