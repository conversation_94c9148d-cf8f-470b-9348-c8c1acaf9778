import { FormControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { MenuGroupService } from './../../../shared/services/menuGroup.service';
// import { Menu, menuList } from './../../../layout/sidebar/sidebar.data';
import { _secretKey } from './../../../shared/globals/config';
import { ActivatedRoute, Router } from '@angular/router';
// import { CustomValidators } from "ng2-validation";
import { Select2OptionData } from 'ng2-select2';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

// import * as CryptoJS from 'crypto-js';
// declare var Messenger: any;
declare var jQuery: any;

@Component({
    selector: 'manage-menu',
    templateUrl: './manage.menuGroup.component.html',
    styleUrls: ['./manage.menuGroup.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class ManageMenuComponent implements OnInit {
    private _secretKey: String = _secretKey;
    config: any;// New Change ****
    id: number;
    menus: any;
    menuIcon: any;
    defaultMenu: any;
    editmenu: FormGroup;
    isMenu: boolean = false;
    public isTypeAdd: boolean;
    public initParentValue: any;
    public initRouterValue: any;
    public initialDataParent: any;
    public initialDataRouter: any;
    public shouldResetParent: boolean;
    public initialDataParentOriginal: any;

    // service variables
    private sub: any;
    private getMenu: any;
    private saveMenu: any;
    private deleteMenuItem: any;
    private saveEditedMenu: any;
    private addMenuItemPost: any;
    private getRouterAndParent: any;

    select2Options: any = {
        width: '100%'
    };

    nest1Options: Object = {
        maxDepth: 2
    };
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private route: ActivatedRoute,
        private MGS: MenuGroupService,
        private router: Router,
        private _fb: FormBuilder,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.sub = this.route.params.subscribe(params => {
            this.id = params['id']; // get id from URL
            this.getMenu = this.MGS.getMenus(this.id).subscribe(res => {
                // get menu for this menu group
                if (res.status == "success") {
                        /** fill menu items for current menus*/
                        this.menus = res.data.menuGroup;
                        this.initialDataRouter = res.data.routerLinks;
                        this.initialDataParent = JSON.parse(JSON.stringify(res.data.parentMenus));
                        this.initialDataParentOriginal = JSON.parse(JSON.stringify(res.data.parentMenus));
                        /** */
                    // success ends here
                } else {
                    // console.log(res);
                }
            }, err => {
                // console.log(err);
                // this.menus = menuList;
            })
        });
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    buildForm() {
        // build edit menu form here
        this.editmenu = this._fb.group({
            id: [''],
            router_link: [''],
            icon: ['', Validators.required],
            title: ['', Validators.required],
            parent_id: ['', Validators.required],
            is_enabled: ['', Validators.required],
            custom_url: ['', Validators.pattern(/^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,3}(:[0-9]{1,5})?(\/.*)?$/)],
        })
    }
    parentIdChanged(event) {
        let parent = <FormControl>this.editmenu.controls['parent_id'];
        parent.patchValue(event.value);
    }
    routerIdChanged(event) {
        let router = <FormControl>this.editmenu.controls['router_link'];
        router.patchValue(event.value);
        this.clearRouterErrors();
    }
    clearRouterErrors() {
        let router = <FormControl>this.editmenu.controls['router_link'];
        let custom_url = <FormControl>this.editmenu.controls['custom_url'];
        if (router.hasError('backend')) {
            router.setErrors({ backend: null });
            router.updateValueAndValidity();
        }
        if (custom_url.hasError('backend')) {
            custom_url.setErrors({ backend: null });
            custom_url.updateValueAndValidity();
        }
    }
    getParentList(): Select2OptionData[] {
        // --- initialData is to included in the API response --- //
        if (this.initialDataParent) {
            return jQuery.map(this.initialDataParent, function (obj) {
                return { id: obj.id, text: obj.title };
            })
        }
    }
    getRouterList(): Select2OptionData[] {
        // --- initialData is to included in the API response --- //
        if (this.initialDataRouter) {
            return jQuery.map(this.initialDataRouter, function (obj) {
                return { id: obj.id, text: obj.alias };
            })
        }
    }
    ngOnInit() {
        jQuery('#nestable1').nestable(this.nest1Options);
       
    }

    deleteMenu(id) {
        // delete from current menu and added to deleted menu
        this.deleteMenuItem = this.MGS.deleteMenuItem(id).subscribe(res => {
            console.log("Deleted Menu Response : ", res);
            this.menus = res.data.menuGroup;
        });
    }
    addMenuItem() {
        if (this.editmenu) {
            this.initializeData();
        }
        this.isTypeAdd = true;
        this.buildForm();
        this.isMenu = true;
        setTimeout(() => {
            this.resetParentAndRouter();
            this.initializeIconPicker();
        }, 50); // workaround for jQuery initialising issue.
    }

    findeIndex(arr,property,value) {
        for(let i = 0; i < arr.length; i++) {
            if(arr[i][property] == value){
                return i;
            }
        }
    }
    /**
     * show menu edit form
     * 
     * @param {any} id - id of menu selected to be edited
     * 
     * @memberof ManageMenuComponent
     */
    editMenuName(menu) {
        //update menu
        this.initializeData();
        if (!this.editmenu) {
            this.buildForm();
        }
        if(this.shouldResetParent) {
            this.initialDataParent = this.initialDataParentOriginal;
        }
        if(menu.parent_id == 0) {
            this.initialDataParent.splice(this.findeIndex(this.initialDataParent, "id", menu.id), 1);
            this.shouldResetParent = true;
        }
        this.isTypeAdd = false;
        this.isMenu = true;
        setTimeout(() => {
            this.initParentValue = menu.parent_id;
            this.initRouterValue = menu.router_link;
            this.editmenu.patchValue(menu);
            this.menuIcon = menu.icon;
            this.initializeIconPicker();
        }, 50); // workaround for jQuery initialising issue.
    }

    initializeIconPicker() {
        // --- jquery icon picker destroy if already init --- //
        jQuery.iconpicker.batch('.icp-dd', 'destroy');
        jQuery('.icp-dd').iconpicker({
            hideOnSelect: true
        }).on('iconpickerSelected', (e) => {
            // --- patch hidden input value --- //
            this.editmenu.patchValue({ 'icon': (e.iconpickerValue).slice(3, (e.iconpickerValue.length)) })
        });
    }
    resetParentAndRouter() {
        this.initParentValue = 0;
        this.initRouterValue = 0;
    }
    /**
     * save menu edited details (Menu title and menu icon)
     * 
     * 
     * @memberof ManageMenuComponent
     */
    saveEditMenu() {
        // menu/edit/manage
        if (this.editmenu.valid) {
            if ((!this.editmenu.value.router_link || this.editmenu.value.router_link == 0 || this.editmenu.value.router_link == "0") &&
                (!this.editmenu.value.custom_url || this.editmenu.value.custom_url == "")) {
                this.editmenu.controls['router_link'].setErrors({
                    backend: 'Atleast one of router or custom url is required.'
                });
                this.editmenu.controls['custom_url'].setErrors({
                    backend: 'Atleast one of router or custom url is required.'
                });
            }
            else {
                let link = (<string>this.editmenu.value.custom_url);
                if (this.editmenu.value.custom_url) {
                    this.editmenu.value.custom_url = link.includes('http') ? link : 'http://'.concat(link);
                }
                if (this.isTypeAdd) {
                    this.addMenuItemPost = this.MGS.addMenuItem(this.id, this.editmenu.value).subscribe(res => {
                        if (res.status == "success") {
                            this.initializeData();
                            if (!this.menus) {
                                this.menus = [];
                            }
                            this.menus = res.data.menuGroup;
                            this.initialDataParent = JSON.parse(JSON.stringify(res.data.parentMenus));
                            this.initialDataParentOriginal = JSON.parse(JSON.stringify(res.data.parentMenus));
                            setTimeout(() => {
                                this.resetParentAndRouter();
                                this.initializeIconPicker();
                            }, 0);
                        }
                    }, (err) => {
                        let errBody = JSON.parse(err._body);
                        let errors = errBody.data;
                        if (errors.length > 0) {
                            errors.forEach(element => {
                                let control = this.editmenu.controls[element.fieldname];
                                control.setErrors({
                                    backend: element.error
                                });
                            });
                        }
                    })
                }
                else {
                    this.saveEditedMenu = this.MGS.saveMenuEdit(this.editmenu.value, this.editmenu.value.id).subscribe(res => {
                        if (res.status == "success") {
                            this.menus = res.data;
                            this.hideEditMenu();
                            this.menus = res.data.menuGroup;
                            this.initialDataParent = JSON.parse(JSON.stringify(res.data.parentMenus));
                            this.initialDataParentOriginal = JSON.parse(JSON.stringify(res.data.parentMenus));
                            this.shouldResetParent = false;
                            setTimeout(() => {
                                this.resetParentAndRouter();
                                this.initializeIconPicker();
                            }, 0);
                            // change default menu properties
                            // this.changeInDefaultMenu(this.editmenu.value);
                            // set localstorage according to new default menu
                            // localStorage.setItem('dm', CryptoJS.AES.encrypt(JSON.stringify(this.defaultMenu), this._secretKey));
                            // Messenger().post({  hideAfter: 5,
                            //         message: "Changes will be reflected from your next session",
                            //         type: "error",
                            //         showCloseButton: true
                            // });
                        }
                    }, (err) => {
                        let errBody = JSON.parse(err._body);
                        let errors = errBody.data;
                        if (errors.length > 0) {
                            errors.forEach(element => {
                                let control = this.editmenu.controls[element.fieldname];
                                control.setErrors({
                                    backend: element.error
                                });
                            });
                        }
                    });
                }
            }
        }
    }
    /**
     * make changes to default menu item details which was updated
     * 
     * @param {any} ele - updated elements
     * 
     * @memberof ManageMenuComponent
     */
    changeInDefaultMenu(ele) {
        let id = ele.id;
        let title = ele.title;
        let icon = ele.icon;
        this.defaultMenu.forEach(element => {
            if (element.id == id) {
                element.title = title;
                element.icon = icon;
            } else {
                // loop through childrens
                if (element.children) {
                    element.children.forEach(element => {
                        if (element.id == id) {
                            element.title = title;
                            element.icon = icon;
                        }
                    });
                }
            }
        });

    }
    /**
     * save menus for that menu group
     * 
     * 
     * @memberof ManageMenuComponent
     */
    updateMenu() {
        // save menus to group
        let value = jQuery('#nestable1').nestable('serialize');
        let defaultMenus = this.convertMenuGroupToDefault(JSON.parse(JSON.stringify(this.menus)));
        value = this.fillMenu(value, defaultMenus);
        if (value) {
            this.saveMenu = this.MGS.saveMenu(this.id, value).subscribe(res => {
                if (res.status == "success") {
                    this.router.navigate(['/admin/menu/']);
                }
            });
        }
    }
    /**
     * convert given menu to a flattened menu to avail all unique children at first depth
     * 
     * @param {any} menuGroup - menu to be converted into a flattened menu
     * 
     * @memberof ManageMenuComponent
     */
    convertMenuGroupToDefault(menuGroup: any[]) {
        let menu = [];
        menuGroup.forEach(element => {
            menu.push(element);
            if (element.children) {
                element.children.forEach(ele => {
                    menu.push(ele);
                });
            }
        });
        return menu;
    }
    /**
     * 
     * use to remove elements from menu list and push to another menu list
     * 
     * @param {any} arr  - from which array element should be deleted
     * @param {any} attr - which attribute to match
     * @param {any} value - value to match
     * @param {any} target - after deleting where to add
     * 
     * @memberof ManageMenuComponent
     */
    removeByAttr(arr, attr, value, target) {
        for (let i = 0; i <= arr.length - 1; i++) {
            if (arr[i]
                && arr[i].hasOwnProperty(attr)
                && (arguments.length > 2 && arr[i][attr] === value)) {

                target.unshift(arr[i]); // upshift will add to top of the array
                arr.splice(i, 1);

            } else {
                if (arr[i].children) {
                    // delete from children array
                    for (let j = 0; j <= arr[i].children.length; j++) {
                        if (arr[i].children[j]
                            && arr[i].children[j].hasOwnProperty(attr)
                            && (arguments.length > 2 && arr[i].children[j][attr] === value)) {

                            target.unshift(arr[i].children[j]);
                            arr[i].children.splice(j, 1);

                        }
                    } // endfor
                }
            }
        } // endfor
    }
    /**
     * 
     * fill menus items(attributes) from default menu items
     * 
     * @param {any} arr - array of menu items to be filled
     * @returns 
     * 
     * @memberof ManageMenuComponent
     */
    fillMenu(arr, menu?) {
        let menuData = [];
        let defaultMenu = menu ? menu : this.defaultMenu;

        arr.forEach(element => {
            let pushData = {};
            let ele = element;

            defaultMenu.forEach(element => {
                let defaultEle = element;
                if (defaultEle.id == ele.id) {
                    // menuData.push(element)
                    pushData = element;
                }
                if (ele.children) {
                    // loop through if has children
                    pushData["children"] = [];
                    ele.children.forEach(element => {
                        let child = element;
                        defaultMenu.forEach(element => {
                            if (element.id == child.id) {
                                pushData["children"].push(element);
                            }
                        });
                    });
                }
            });
            menuData.push(pushData);
        });
        return menuData;
    }
    hideEditMenu() {
        this.initializeData();
        this.isMenu = false;
    }
    initializeData() {
        if (this.editmenu) {
            this.editmenu.reset();
        }
        if (!this.isTypeAdd) {
            this.isTypeAdd = undefined;
        }
        this.menuIcon = undefined;
        this.initParentValue = undefined;
        this.initRouterValue = undefined;
        // this.initialDataParent = undefined;
        // this.initialDataRouter = undefined;
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
        if (this.getMenu) {
            this.getMenu.unsubscribe();
        }
        if (this.saveMenu) {
            this.saveMenu.unsubscribe();
        }
        if (this.saveEditedMenu) {
            this.saveEditedMenu.unsubscribe();
        }
        if (this.deleteMenuItem) {
            this.deleteMenuItem.unsubscribe();
        }
        if (this.addMenuItemPost) {
            this.addMenuItemPost.unsubscribe();
        }
        if (this.getRouterAndParent) {
            this.getRouterAndParent.unsubscribe()
        }

        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}