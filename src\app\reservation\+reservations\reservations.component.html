<section class="widget reservation__page my-pagination">
  <div class="widget-body">
    <tabset class="__custom_tab" #staticTabs>
      <tab>
        <!-- ************************************************************************************ -->
        <!-- Reservation tab -->
        <ng-template tabHeading>
          <i class="fa fa-calendar"></i>&nbsp;&nbsp;{{'RESERVATIONS.RESERVATIONS' | translate:param}}
        </ng-template>
        <div class="row" *ngIf="(DateRangesWithBookingArray && DateRangesWithBookingArray.length == 0)">
          <div class="col-md-12 text-center">
            <br>
            <br>
            <br>
            <i class="fa fa-circle-o-notch fa-pulse fa-2x"></i>
            <br>
            <br>
            <br>
          </div>
        </div>
        <div *ngIf="showLoader" class="loader-reservation"
          [ngClass]="{'with-white-background': categoryTypeLoading ? true : false}">
          <div *ngIf="!categoryTypeLoading" class="loader-base">
            <i class="fa fa-spinner fa-pulse fa-3x"></i>
          </div>
          <span *ngIf="categoryTypeLoading" class="center-align capitalize">
            <h3>Loading {{roomCategoryType[findIndex(selectedRoomCategory,"id",roomCategoryType) - 1].text}}
              category...</h3>
            <i class="fa fa-spinner fa-pulse fa-2x"></i>
          </span>
        </div>
        <div class="mt" style="margin-top: 0px;"
          *ngIf="DateRangesWithBookingArray && DateRangesWithBookingArray.length > 0">
          <div class="row" style="justify-content: space-between;">
            <div class="col-sm-6">
              <ul class="pagination _2">
                <li>
                  <a class="today-button-calendar" href="javascript:void(0)" tooltip="{{(currMoment | date)}}"
                    placement="right" (click)="navigateToCurrentDay(true)">{{'RESERVATIONS.TODAY' |
                    translate:param}}</a>
                </li>
              </ul>
            </div>

            <!-- Select for category filter -->
            <div class="col-sm-3" *ngIf="roomCategoryType.length > 1">
              <ng-select
                [items]="roomCategoryType"
                bindLabel="text"
                bindValue="id"
                [(ngModel)]="selectedRoomCategory"
                (change)="categoryFilterChanged($event)"
                [clearable]="false"
                placeholder="Select category"
                class="room-categorytype-selection">
              </ng-select>
            </div>
            <!-- Select for category filter -->

          </div>
          <br>
          <div class="row">
            <div [ngClass]="{'col-md-5': isDateDisplayTypeWeek,'col-md-4': !isDateDisplayTypeWeek}">
              <ul class="pagination _2">
                <li>
                  <a href="javascript:void(0)" (click)="isDateDisplayTypeWeek ? prevWeek(true) : prevMonth(true)"
                    tooltip="{{'RESERVATIONS.PREV' | translate:param}} {{isDateDisplayTypeWeek ? 'week' : 'month'}}"
                    placement="bottom">
                    <i class="fa fa-angle-left"></i>
                  </a>
                </li>
                <li *ngIf="isDateDisplayTypeWeek">
                  <a href="javascript:void(0)" (click)="isDateDisplayTypeWeek ? prevDay(true) : null"
                    tooltip="{{'RESERVATIONS.PREV_DAY' | translate:param}}" placement="bottom">
                    <i class="fa fa-angle-left"></i>
                  </a>
                </li>
                <li>
                  <a href="javascript:void(0)" (click)="changeDateDisplayType(true)"
                    style="min-width: 150px;text-align: center;">{{isDateDisplayTypeWeek
                    ? 'Week' : 'Month'}} {{isDateDisplayTypeWeek ? weekCount + 1 : currMonth + 1}}</a>
                </li>
                <li *ngIf="isDateDisplayTypeWeek">
                  <a href="javascript:void(0)" (click)="isDateDisplayTypeWeek ? nextDay(true) : null"
                    tooltip="{{'RESERVATIONS.NEX_DAY' | translate:param}}" placement="bottom">
                    <i class="fa fa-angle-right"></i>
                  </a>
                </li>
                <li>
                  <a href="javascript:void(0)" (click)="isDateDisplayTypeWeek ? nextWeek(true) : nextMonth(true)"
                    tooltip="{{'RESERVATIONS.NEXT' | translate:param}} {{isDateDisplayTypeWeek ? 'week' : 'month'}}"
                    placement="bottom">
                    <i class="fa fa-angle-right"></i>
                  </a>
                </li>
              </ul>
            </div>
            <div [ngClass]="{'col-md-2': isDateDisplayTypeWeek,'col-md-3': !isDateDisplayTypeWeek}">
              <ng-select
                [items]="bookingType"
                bindLabel="text"
                bindValue="id"
                [(ngModel)]="bookingTypeSelection"
                (change)="bookingTypeChanged($event)"
                [clearable]="false"
                placeholder="Select booking type">
              </ng-select>
            </div>
            <div class="col-md-5">
              <ul class="pagination">
                <li>
                  <a href="javascript:void(0)" (click)="prevYear(true)"
                    tooltip="{{'RESERVATIONS.PREV_YEAR' | translate:param}}" placement="bottom">
                    <i class="fa fa-angle-double-left"></i>
                  </a>
                </li>
                <li>
                  <a href="javascript:void(0)" (click)="prevMonth(true)"
                    tooltip="{{'RESERVATIONS.PREV_MONTH' | translate:param}}" placement="bottom">
                    <i class="fa fa-angle-left"></i>
                  </a>
                </li>
                <li class="active">
                  <a href="javascript:void(0)"
                    style="min-width: 150px;text-align: center;">{{monthNames[currMonth]}}&nbsp;/&nbsp;{{currYear}}</a>
                </li>
                <li>
                  <a href="javascript:void(0)" (click)="nextMonth(true)"
                    tooltip="{{'RESERVATIONS.NEX_MONTH' | translate:param}}" placement="bottom">
                    <i class="fa fa-angle-right"></i>
                  </a>
                </li>
                <li>
                  <a href="javascript:void(0)" (click)="nextYear(true)"
                    tooltip="{{'RESERVATIONS.NEXT_YEAR' | translate:param}}" placement="bottom">
                    <i class="fa fa-angle-double-right"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <br>
          <div class="row">
            <div class="col-md-12">
              <div style="overflow: auto; padding: 15px;">
                <div [class.isMonth]="!isDateDisplayTypeWeek"
                  [ngStyle]="{'min-width': isDateDisplayTypeWeek ? '1120px' : 'inherit'}">
                  <div class="table-div reservation_table">
                    <div class="table-div-row">
                      <div class="table-div-cell">Room</div>
                      <div *ngFor="let item of weekRange" class="table-div-cell"
                        [ngClass]="{'__disabled-booking': item.is_disabled,'__curr_month': item.monthnumber == (currMonth + 1), '__next_month': (item.monthnumber > (currMonth + 1)), '__prev_month': item.monthnumber < (currMonth + 1) && !item.is_disabled,'current-date-highlight': isCurrentDay(item),'same-day':sameDay}">
                        <span>{{item.date}}</span>
                        <span class="__week_name">{{item.day}}</span>
                      </div>

                    </div>
                  </div>

                  <div>
                    <div *ngFor="let room of DateRangesWithBookingArray; let i = index;">
                      <div class="table-div reservation_table __date_range selection_area"
                        *ngIf="((room.category_id == selectedRoomCategory) || (selectedRoomCategory === '000000'))"
                        [ngStyle]="{'border-bottom': ((i == DateRangesWithBookingArray.length - 1) || (room.category_id == selectedRoomCategory)) ? '1px solid #dedede' : 'none'}">
                        <div class="table-div-row ">
                          <div class="table-div-cell capitalize">{{getCatName(room.category_id)}}</div>
                          <!-- __booking-selectables -->
                          <div class="__date_range table-div-cell" *ngFor="let item of weekRange">

                            <input type="checkbox">
                            <!-- __availabel-for-booking -->
                            <label [contextMenu]="OverBooking" [contextMenuSubject]="[item,room.category_id]"
                              class="__already_booked __overbooking_cell"
                              [ngClass]="{'current-date-highlight': isCurrentDay(item),'same-day':sameDay}">
                              <div *ngIf="hasOverBooking(room.category_id,item)" style="cursor:pointer;"
                                (click)="openOverBookingNote([item,room.category_id])">
                                <span class="badge note overbooking-count-right"
                                  tooltip="{{'RESERVATIONS.OVER_BOOK' | translate:param}}"
                                  placement="left">{{getOverBookingCount(room.category_id,item)}}</span>
                                <i class="float-right-calendar fa fa-calendar-o"></i>
                              </div>
                            </label>
                          </div>
                        </div>
                        <!-- Room Lists -->
                        <div class="__date_range table-div-row" *ngFor="let dates of room.room; let roomIndex = index;">

                          <div class="table-div-cell capitalize">{{dates.room_title}}</div>
                          <div class="table-div-cell" *ngFor="let item of dates.dates; let j = index"
                            [attr.data-isbooked]="item.is_booked && !item.is_ending"
                            [ngClass]="{'__booking-selectables': !item.is_disabled,'current-date-highlight': isCurrentDay(item),'same-day':sameDay}">
                            <input type="checkbox" #dateObj class="checkbox_date" disabled
                              attr.data-timestamp="{{dates.room_id}}_{{item.timestamp}}"
                              [attr.data-categoryid]="dates.room_category_id" [attr.data-roomid]="dates.room_id"
                              [attr.data-fulldate]="item.fulldate" [attr.data-date]="item.date"
                              [attr.data-isdisabled]="item.is_disabled" [attr.data-isbooked]="item.is_booked"
                              [attr.data-iscommonroom]="item.isCommonRoom">
                            <label [attr.for]="item.date" class="__availabel-for-booking"
                              *ngIf="!item.is_disabled && !item.is_booked  && item.booking_status != 'split'"
                              [contextMenu]="NewBooking" [contextMenuSubject]="item"></label>

                            <label [attr.for]="item.date" class="__cannotselect"
                              *ngIf="item.is_disabled && !item.is_booked && item.booking_status != 'split'"></label>
                            <!-- attr.data-bookingstatus="{{!item.all_guest_details_added && item.booking_status !== 'reserved' ? 'all_guest_details_added' : item.isCommonRoom ? 'reserved' : item.booking_status}}" -->
                            <label [attr.for]="item.date" style="cursor: pointer;"
                              [tooltip]="item.isCommonRoom ? 'Common room booking' : getBookingCustomerType(item)"
                              placement="top"
                              [attr.data-username]="!item.isCommonRoom ? item.is_ending ? truncateString(item.main_person,10) : item.main_person : ''"
                              *ngIf="item.is_booked && !item.is_disabled && item.booking_status != 'split'"
                              [attr.data-starting]="item.is_starting" [attr.data-ending]="item.is_ending"
                              class=" __already_booked"
                              [ngClass]="{'has-extra-booking': item.extraBooking ? true : false}"
                              attr.data-bookingstatus="{{
                                  item.booking_status === 'maintenance' ? 'maintenance' :
                                  !item.all_guest_details_added && item.booking_status !== 'reserved' ?
                                  'all_guest_details_added' :
                                  item.isCommonRoom ? 'reserved' : item.booking_status}}"
                              attr.data-extrabookingstatus="{{item.extraBooking ? (!item.extraBooking.all_guest_details_added && item.extraBooking.booking_status !== 'reserved' ? 'all_guest_details_added' : item.extraBooking.isCommonRoom ? 'reserved' : item.extraBooking.booking_status) : ''}}"
                              [contextMenu]="CurrentBooking" [contextMenuSubject]="[item,room.category_id]">
                              <div class="relative-class">
                                <span *ngIf="(item.customer_id == 1  && item.is_starting && !item.isCommonRoom)"
                                  class="badge note satsangi">
                                  <img src="assets/img/satsangi-xs.png">
                                </span>
                                <span *ngIf="(item.stay_type && item.is_starting && !item.isCommonRoom)"
                                  class="badge note stay-type" [ngClass]="{'with-satsangi': ((item.customer_id == 1 || item.customer_id == 4) && !item.isCommonRoom),
                                      'with-note': item.booking_note && item.booking_note != '' && item.is_starting}">
                                  <i class="fa fa-{{getStayTypeIcon(item.stay_type)}}"></i>
                                </span>
                                <span *ngIf="(item.customer_id == 4  && item.is_starting && !item.isCommonRoom)"
                                  class="badge note satsangi">
                                  <div class="free-customer"></div>
                                </span>
                              </div>
                              <div class="relative">
                                <span class="badge note"
                                  [ngClass]="{'with-satsangi':((item.customer_id == 1 || item.customer_id == 4) && !item.isCommonRoom)}"
                                  (click)="addEditBookingNote(item)"
                                  *ngIf="(item.booking_note && item.booking_note != '' && item.is_starting)">
                                  <i class="fa fa-info-circle"></i>
                                </span>
                              </div>
                              <div *ngIf="item.isCommonRoom">
                                <span class="badge note overbooking-count-left">{{item.guestCount}}</span>
                                <i class="float-left-calendar fa fa-calendar-o fa-2x"></i>
                                <div class="badge note overbooking-count-left text-label">
                                  {{'RESERVATIONS.BEDS_RESEV' | translate:param}}</div>
                              </div>
                              <span *ngIf="showCheckoutAlert(item)" class="badge special">
                              </span>
                            </label>
                            <!-- attr.data-bookingstatus="{{!item.all_guest_details_added && item.booking_status !== 'reserved' ? 'all_guest_details_added' : item.isCommonRoom ? 'reserved' : item.booking_status}}" -->
                            <label [attr.for]="item.date"
                              [tooltip]="item.isCommonRoom ? 'Common room booking' : item.customer_name" placement="top"
                              [attr.data-username]="!item.isCommonRoom ? item.is_ending ? truncateString(item.main_person,10) : item.main_person : ''"
                              *ngIf="item.is_booked && item.is_disabled && item.booking_status != 'split'"
                              [attr.data-starting]="item.is_starting" [attr.data-ending]="item.is_ending"
                              class=" __already_booked"
                              [ngClass]="{'has-extra-booking': item.extraBooking ? true : false}"
                              attr.data-bookingstatus="{{
                                  item.booking_status === 'maintenance' ? 'maintenance' :
                                  !item.all_guest_details_added && item.booking_status !== 'reserved' ? 'all_guest_details_added' :
                                  item.isCommonRoom ? 'reserved' : item.booking_status}}"
                              [contextMenu]="CurrentBooking" [contextMenuSubject]="[item,room.category_id]">
                              <div class="relative-class">
                                <span *ngIf="(item.customer_id == 1  && item.is_starting && !item.isCommonRoom)"
                                  class="badge note satsangi">
                                  <img src="assets/img/satsangi-xs.png">
                                </span>
                                <span *ngIf="(item.stay_type && item.is_starting && !item.isCommonRoom)"
                                  class="badge note stay-type" [ngClass]="{'with-satsangi': ((item.customer_id == 1 || item.customer_id == 4) && !item.isCommonRoom),
                                    'with-note': item.booking_note && item.booking_note != '' && item.is_starting}">
                                  <i class="fa fa-{{getStayTypeIcon(item.stay_type)}}"></i>
                                </span>
                                <span *ngIf="(item.customer_id == 4  && item.is_starting && !item.isCommonRoom)"
                                  class="badge note satsangi">
                                  <div class="free-customer"></div>
                                </span>
                              </div>
                              <div class="relative">
                                <span class="badge note"
                                  [ngClass]="{'with-satsangi':((item.customer_id == 1 || item.customer_id == 4) && !item.isCommonRoom)}"
                                  (click)="addEditBookingNote(item)"
                                  *ngIf="(item.booking_note && item.booking_note != '' && item.is_starting)">
                                  <i class="fa fa-info-circle"></i>
                                </span>
                              </div>
                              <div *ngIf="item.isCommonRoom">
                                <span class="badge note overbooking-count-left">{{item.guestCount}}</span>
                                <i class="float-left-calendar fa fa-calendar-o fa-2x"></i>
                                <div class="badge note overbooking-count-left text-label">Beds Reserved</div>
                              </div>
                              <span *ngIf="showCheckoutAlert(item)" class="badge special">
                              </span>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <pre>{{DateRangesWithBookingArray | json}}</pre> -->
            <br>
          </div>
        </div>
      </tab>
      <tab *ngFor="let tabz of tabs; let tabIndex = index;" [heading]="tabz.title" [active]="tabz.active"
        [removable]="true" (removed)="removeTabHandler(tabz)">
        <!-- ************************************************************************************ -->
        <!-- view tab -->
        <!-- <div #billPrint *ngIf="tabz?.content?.action_type == 'view'">
          <div *ngFor="let content of tabz.content.data">
            <div class="row">
              <div class="col-sm-4">
                <label for="" class="text-capitalize" style="border: none;">
                  {{'RESERVATIONS.UNIQ_BOOK_ID' | translate:param}} :&nbsp;
                  <strong>{{content?.booking_details.booking.unique_booking_id}}</strong>
                </label>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-4">
                <label for="" class="form-control text-capitalize">
                  Room :&nbsp;
                  <strong>{{content?.booking_details.bookingRoom['room.title']}}</strong>
                </label>
              </div>
              <div class="col-sm-4">
                <label for="" class="form-control bg-checkin">
                  {{'RESERVATIONS.CHECK_IN_DATE' | translate:param}} :&nbsp;
                  <strong>{{content?.booking_details.bookingRoom.start | date}}</strong>
                </label>
              </div>
              <div class="col-sm-4" style="padding-left: 0px;">
                <label for="" class="form-control bg-checkout">
                  {{'RESERVATIONS.CHECK_OUT_DATE' | translate:param}} :&nbsp;
                  <strong>{{formatCheckoutDate(content?.booking_details.bookingRoom.end, content?.booking_details) |
                    date}}</strong>
                </label>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-4 border-sm-left">
                <h1 class="capitalize">{{content?.booking_details.bookingRoom['guest.name']}}</h1>

                <small *ngIf="content?.booking_details.booking['guest.contact']">
                  <i
                    class="fa fa-phone-square "></i>&nbsp;&nbsp;{{content?.booking_details.bookingRoom['guest.contact']}}&nbsp;&nbsp;</small>
                <small *ngIf="content?.booking_details.booking['guest.email']">
                  <i
                    class="fa fa-envelope "></i>&nbsp;&nbsp;{{content?.booking_details.bookingRoom['guest.email']}}</small>
                <br>
                <br>

                <div
                  *ngIf="content.booking_details.bookingPayment.length >=1 &&  content.booking_details.bookingPayment[0].bill_no != null">
                  <button (click)="ViewBillPopUpPDF(content.booking_details)">Download receipt</button>
                </div>
                <div
                  *ngIf="content.booking_details.bookingPayment.length == 0 || content.booking_details.bookingPayment[0].bill_no == null">
                  <button [disabled]="PaymentReceipt" (click)="ViewBillPopUpPDF(content.booking_details)">Download
                    receipt</button>
                </div>
              </div>
              <div class="col-sm-8">
                <div class="view-tab row" style="margin-right:0px;margin-left:0px;">
                  <div class="col-sm-6">
                    <label for="" class="form-control text-capitalize">
                      {{'RESERVATIONS.REG_ON' | translate:param}} :&nbsp;
                      <strong>{{content?.booking_details.booking.booking_date | date}}</strong>
                    </label>
                  </div>
                  <div class="col-sm-6">
                    <label for="" class="form-control text-capitalize">
                      {{'RESERVATIONS.CURR_STATUS' | translate:param}} :&nbsp;
                      <strong
                        [ngStyle]="{'color': content?.booking_details.bookingRoom.current_status == 'checkin' ? '#64bd63' :
                        content?.booking_details.bookingRoom.current_status == 'reserved' ? '#5d8fc2' :
                        content?.booking_details.bookingRoom.current_status == 'checkout' ? '#d80000' : 'inherit'}">{{content?.booking_details.bookingRoom.current_status
                        == 'checkin' ? 'Check In' : content?.booking_details.bookingRoom.current_status == 'reserved' ?
                        'Reserved'
                        : content?.booking_details.bookingRoom.current_status == 'checkout' ? 'Check Out' :
                        '-'}}
                        <span *ngIf="content?.booking_details.bookingRoom.createdBy !== null && content?.booking_details.bookingRoom.createdBy !== undefined">
                          by {{ content?.booking_details.bookingRoom.createdBy }}
                        </span>
                      </strong>
                    </label>
                  </div>
                  <div class="col-sm-6">
                    <label for="" class="form-control text-capitalize">
                      {{'RESERVATIONS.BOOK_TYPE' | translate:param}} :&nbsp;
                      <strong>{{bookingType[content?.booking_details.booking.booking_type].text}}</strong>
                    </label>
                  </div>
                  <div class="col-sm-6">
                    <label for="" class="form-control">
                      {{'RESERVATIONS.TOT_PER' | translate:param}} :&nbsp;
                      <strong>
                        {{content?.booking_details.bookingRoom.adult + content?.booking_details.bookingRoom.child
                        }}&nbsp; ( A: {{content?.booking_details.bookingRoom.adult}}/
                        C: {{content?.booking_details.bookingRoom.child}} )
                      </strong>
                    </label>
                  </div>
                  <div class="col-sm-6">
                    <label for="" class="form-control text-capitalize">
                      {{'RESERVATIONS.CUST_TYPE' | translate:param}} :&nbsp;
                      <strong>{{content?.booking_details.booking['customer.name']}}</strong>
                    </label>
                  </div>
                  <div class="col-sm-6">
                    <label for="" class="form-control text-capitalize">
                      {{'RESERVATIONS.ROOM_CAT' | translate:param}} :&nbsp;
                      <strong>{{content?.booking_details.bookingRoom['room.roomcategory.name']}}</strong>
                    </label>
                  </div>
                  <div class="col-sm-6">
                    <label for="" class="form-control text-capitalize">
                      {{'RESERVATIONS.STAY_TYPE' | translate:param}} :&nbsp;
                      <strong>{{content?.booking_details.booking['stay_type.name']}}</strong>
                    </label>
                  </div>

                  <div class="col-sm-6">
                    <label for="" class="form-control text-capitalize" style="background-color: rgba(0, 0, 0, 0.15);">
                      {{ (content?.booking_details.bookingRoom.current_status == 'reserved' ?
                      'RESERVATIONS.EXPEC_CHECK_IN' : 'RESERVATIONS.ACHECK_IN') | translate:param }} :&nbsp;
                      <strong>{{content ? (content?.booking_details.bookingRoom.current_status == 'reserved' ?
                        (content.booking_details.bookingRoom['expected_check_in'] | date:'shortTime') :
                        (content.booking_details.bookingRoom['check_in'] | date:'h:mm a')) : '-'}}</strong>
                    </label>
                  </div>
                  <div *ngFor="let item of proofTypeList" class="col-sm-6">
                    <label *ngIf="item.id === content?.booking_details.bookingRoom['guest.proof_type']" for=""
                      class="form-control text-capitalize">
                      {{item.text}}:&nbsp;
                      <strong>{{content?.booking_details.bookingRoom['guest.proof_value']}}</strong>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="tabz.content.data">
            <h5>{{'RESERVATIONS.PAY_HIST' | translate:param}}</h5>
            <table class="table payroll" style="margin-bottom: 0;">
              <tr *ngIf="tabz.content.data[0]?.booking_details.booking.is_agent" style="color:#aaa;">
                <td>{{'RESERVATIONS.AGEN_NAME' | translate:param}}</td>
                <td class="text-right">
                  {{tabz.content.data[0]?.booking_details.booking['agent.name']}}
                </td>
              </tr>
              <tr *ngIf="tabz.content.data[0]?.booking_details.booking.is_agent" style="color:#aaa;">
                <td>{{'RESERVATIONS.AGEN_RECEIP_NO' | translate:param}}</td>
                <td class="text-right">
                  {{tabz.content.data[0]?.booking_details.booking.agent_receipt_no}}
                </td>
              </tr>
              <tr *ngIf="tabz.content.data[0]?.booking_details.booking.is_agent" style="color:#aaa;">
                <td>{{'RESERVATIONS.AGENT_LOCATION' | translate:param}}</td>
                <td class="text-right">
                  {{agentLocationList[this.findIndex(parseIntCustom(tabz.content.data[0]?.booking_details.booking['agent.location']),"id",agentLocationList)
                  - 1].text}}
                </td>
              </tr>
              <tr (click)="isActive = !isActive" class="text-primary">
                <td>{{'RESERVATIONS.TOT_AMT' | translate:param}}
                  <span *ngIf="tabz.content.data[0]?.booking_details.booking['stay_type.name']">
                    <small>
                      (<strong>
                        {{tabz.content.data[0]?.booking_details.booking['stay_type.name']}}
                      </strong><i> {{'RESERVATIONS.CHARGE_APPLIED' | translate:param}}</i>)
                    </small>
                  </span>
                </td>
                <td class="text-right">
                  <i class="fa fa-inr"></i>&nbsp;{{tabz.content.data[0]?.booking_details.booking.total_amount}}
                </td>
              </tr>
              <tr class="text-primary" *ngIf="tabz.content.data[0]?.booking_details.cardSwipeCharges > 0">
                <td>{{'RESERVATIONS.TOT_CARD_SWI' | translate:param}}</td>
                <td class="text-right">
                  <i class="fa fa-inr"></i>&nbsp;{{tabz.content.data[0]?.booking_details.cardSwipeCharges}}
                </td>
              </tr>
              <tr *ngFor="let extraGuest of tabz.content.data[0]?.booking_details.ExtraGuest" class="text-primary">
                <td>{{'RESERVATIONS.EXT-GUE_CHAR' | translate:param}}
                  <small>
                    in ({{extraGuest['booking_rooms.room.title']}})
                    &nbsp;&nbsp;{{'RESERVATIONS.FROM' | translate:param}}&nbsp;&nbsp;
                    ({{extraGuest['booking_rooms.start'] | date}}&nbsp;&nbsp;
                    <i class="fa fa-long-arrow-right"></i> &nbsp;&nbsp;{{extraGuest['booking_rooms.end'] | date}})
                  </small>
                </td>
                <td class="text-right ">
                  +
                  <i class="fa fa-inr"></i>&nbsp; {{extraGuest['booking_rooms.net_amount']}}
                </td>
              </tr>
              <tr *ngFor="let early_checkIn of tabz.content.data" class="text-danger">
                <td *ngIf="early_checkIn.booking_details.bookingRoom['early_checkin_charge']">
                  {{'RESERVATIONS.EARLY_CHECK_IN' | translate:param}}
                  <i>
                    <small>
                      ({{early_checkIn.booking_details.bookingRoom['room.title']}} -
                      {{early_checkIn.booking_details.bookingRoom['room.roomcategory.name']}}
                      &nbsp;at&nbsp; {{early_checkIn.booking_details.bookingRoom['check_in'] | date:'h:mm a'}})
                    </small>
                  </i>
                </td>
                <td *ngIf="early_checkIn.booking_details.bookingRoom['early_checkin_charge']" class="text-right">
                  +
                  <i class="fa fa-inr"></i>&nbsp;{{early_checkIn.booking_details.bookingRoom['early_checkin_charge']}}
                </td>
              </tr>
              <tr *ngIf="tabz.content.data[0]?.booking_details.bookingCancellationAmount" class="text-danger">
                <td>{{'RESERVATIONS.CAN_CHARGE' | translate:param}}</td>
                <td class="text-right ">
                  +
                  <i class="fa fa-inr"></i>&nbsp; {{tabz.content.data[0]?.booking_details.bookingCancellationAmount}}
                </td>
              </tr>
              <tr class="text-danger">
                <td>{{'RESERVATIONS.CUST_DIS' | translate:param}}</td>
                <td class="text-right ">-
                  <i class="fa fa-inr"></i>&nbsp;{{tabz.content.data[0]?.booking_details.booking.discount}}
                </td>
              </tr>
              <tr *ngIf="tabz.content.data[0]?.booking_details.booking.is_reference" class="text-danger">
                <td>{{'RESERVATIONS.REF_NAME' | translate:param}}</td>
                <td class="text-right ">{{tabz.content.data[0]?.booking_details.booking['reference_user.name']}}</td>
              </tr>
              <tr *ngIf="tabz.content.data[0]?.booking_details.booking.is_reference" class="text-danger">
                <td>{{'RESERVATIONS.REF_DIS' | translate:param}}</td>
                <td class="text-right ">-
                  <i
                    class="fa fa-inr"></i>&nbsp;{{(tabz.content.data[0]?.booking_details.booking.referal_discount)?tabz.content.data[0]?.booking_details.booking.referal_discount
                  : '0'}}
                </td>
              </tr>
              <tr *ngFor="let payment of tabz.content.data[0]?.booking_details.bookingPayment" class="text-danger">
                <td>
                  <span>
                    {{payment.is_advance ? 'Advance ' : ''}}
                  </span>Payments
                  <small>
                    <i *ngIf="payment">({{paymentType[payment?.payment_mode]?.text}} &nbsp;on&nbsp;
                      {{payment?.payment_date
                      | date}}
                      <span *ngIf="payment?.payment_reciept_number">
                        {{'RESERVATIONS.BY' | translate:param}} #{{payment.payment_reciept_number}}
                      </span>) &nbsp;&nbsp;  - ( {{payment['user.first_name']}}&nbsp;{{payment['user.last_name']}} )
                      <span *ngIf="payment?.bank_cheque_no">
                        (Cheque No: {{payment.bank_cheque_no}})
                      </span>
                    </i>
                    <span *ngIf="payment.card_charge">
                      {{'RESERVATIONS.WITH' | translate:param}}
                      <i class="fa fa-inr"></i>
                      {{payment.amount_with_card_charge}}
                      {{'RESERVATIONS.CARD_CHARGE' | translate:param}}</span>
                    <span *ngIf="payment.return_amount && payment.return_amount > 0">
                      <strong>
                        (with <i class="fa fa-inr"></i>
                        {{payment.return_amount}} {{'RESERVATIONS.WITH' | translate:param}})
                      </strong>
                    </span>
                  </small>
                </td>
                <td class="text-right ">-
                  <i class="fa fa-inr"></i>&nbsp;{{payment.amount_with_card_charge + payment.amount}}
                </td>
              </tr>
              <tr *ngFor="let fund of tabz.content.data[0]?.booking_details.bookingFund" class="text-primary">
                <td>{{'RESERVATIONS.FUNDS_ADDED' | translate:param}} ({{fund['fund.name']}})</td>
                <td class="text-right ">
                  +
                  <i class="fa fa-inr"></i>&nbsp; {{fund.amount}}
                </td>
              </tr>
              <tr *ngIf="tabz.content.data[0]?.booking_details.booking.custom_discount" class="text-danger">
                <td>{{'RESERVATIONS.CUST_DISC' | translate:param}} &nbsp;&nbsp;( {{tabz.content.data[0]?.booking_details.booking.coustome_discount.first_name}} &nbsp; {{tabz.content.data[0]?.booking_details.booking.coustome_discount.last_name}} )</td>
                <td class="text-right ">
                  -
                  <i class="fa fa-inr"></i>&nbsp; {{tabz.content.data[0]?.booking_details.booking.custom_discount}}
                </td>
              </tr>
              <tr
                *ngIf="(tabz.content.data[0]?.booking_details.returnAmount && (tabz.content.data[0]?.booking_details.returnAmount > 0))"
                class="text-danger">
                <td>{{'RESERVATIONS.RETD_AMT' | translate:param}}</td>
                <td class="text-right ">
                  <i class="fa fa-inr"></i>&nbsp; {{tabz.content.data[0]?.booking_details.returnAmount}}
                </td>
              </tr>
              <tr class="text-success">
                <td>{{'RESERVATIONS.PEND_PAY' | translate:param}}</td>
                <td class="text-right ">
                  <i class="fa fa-inr"></i>&nbsp;
                  {{(tabz.content.data[0]?.booking_details.booking.cancellation_payable_amount
                  ? tabz.content.data[0]?.booking_details.booking.cancellation_payable_amount : 0) +
                  tabz.content.data[0]?.booking_details.booking.net_amount
                  + (tabz.content.data[0]?.booking_details.ExtraGuestCharge ?
                  tabz.content.data[0]?.booking_details.ExtraGuestCharge
                  : 0) - (tabz.content.data[0]?.booking_details.paidAmount ?
                  tabz.content.data[0]?.booking_details.paidAmount
                  : 0) + (tabz.content.data[0]?.booking_details.cardSwipeCharges ?
                  tabz.content.data[0]?.booking_details.cardSwipeCharges
                  : 0)+ (tabz.content.data[0]?.booking_details.fundAmount ?
                  tabz.content.data[0]?.booking_details.fundAmount
                  : 0) + (tabz.content.data[0]?.totalEarlyCheckInCharge) -
                  (tabz.content.data[0]?.booking_details.booking.custom_discount
                  ? tabz.content.data[0]?.booking_details.booking.custom_discount : 0) +
                  (tabz.content.data[0]?.booking_details.returnAmount
                  ? tabz.content.data[0]?.booking_details.returnAmount : 0)}}
                </td>
              </tr>
            </table>
          </div>
        </div> -->
        <!-- ************************************************************************************ -->
         

        <!-- New View Details Tab -->
         <div #billprint *ngIf="tabz?.content?.action_type == 'view'">
          <div *ngFor="let content of tabz.content.data">
            <div class="view_top">
              <div class="view_top_1">
                <h1>{{content?.booking_details.bookingRoom['guest.name']}}</h1>
                <label for="" class="text-capitalize" style="border: none;">
                  {{'RESERVATIONS.UNIQ_BOOK_ID' | translate:param}} :&nbsp;
                  <strong>{{content?.booking_details.booking.unique_booking_id}}</strong>
                </label>
              </div>
              <div class="view_top_2">
                <div class="view_top_2_left">
                  <div class="view_top_2_left_first">
                    <i class="fa fa-phone-square "></i>
                    &nbsp;&nbsp;{{content?.booking_details.bookingRoom['guest.contact']}}
                  </div>
                  <div class="view_top_2_left_second">
                    <i class="fa fa-envelope "></i>
                    &nbsp;&nbsp;{{content?.booking_details.bookingRoom['guest.email']}}
                  </div>
                </div>
                <div class="view_top_2_right">
                  <p>Room :&nbsp;
                    <strong>{{content?.booking_details.bookingRoom['room.title']}}</strong>
                  </p>
                </div>
              </div>
              <div class="view_top_3">
                <div class="view_top_3_left">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="#5d6679" d="M215.7 499.2C267 435 384 279.4 384 192C384 86 298 0 192 0S0 86 0 192c0 87.4 117 243 168.3 307.2c12.3 15.3 35.1 15.3 47.4 0zM192 128a64 64 0 1 1 0 128 64 64 0 1 1 0-128z"/></svg>&nbsp;&nbsp;&nbsp;&nbsp;
                  <p> {{content?.booking_details.getGuest[0]?.address}} </p>
                </div>
                <div class="view_top_3_right">
                  <div *ngIf="content.booking_details.bookingPayment.length >=1 &&  content.booking_details.bookingPayment[0].bill_no != null">
                    <button (click)="ViewBillPopUpPDF(content.booking_details)" class="enable_Download_Receipt"
                      [disabled]="acceptButtonLoader">
                      <span class="loader-parent-style" *ngIf="acceptButtonLoader"><i *ngIf="acceptButtonLoader"
                          class="fa fa-circle-o-notch fa-pulse fa-2x"></i></span>
                      <span *ngIf="!acceptButtonLoader">Download
                        receipt</span>
                    </button>
                  </div>
                  <div *ngIf="content.booking_details.bookingPayment.length == 0 || content.booking_details.bookingPayment[0].bill_no == null">
                    <button [disabled]="PaymentReceipt" (click)="ViewBillPopUpPDF(content.booking_details)" class="disable_Download_Receipt">Download receipt</button>
                  </div>
                </div>
              </div>
            </div>
            <div class="view_content">
              <div class="view_content_left">
                <p>Check-In Date</p>
                <b>{{content?.booking_details.bookingRoom.start | date}}</b>
                <div class="view_content_left_bottum">
                  <div class="view_content_left_first">
                    <p>Registration Date</p>
                    <p>Booking Type</p>
                    <p>Customer Type</p>
                    <p>Stay Type</p>
                  </div>
                  <div class="view_content_left_second">
                    <p>{{content?.booking_details.booking.booking_date | date}}</p>
                    <p>{{bookingType[content?.booking_details.booking.booking_type].text}}</p>
                    <p>{{content?.booking_details.booking['customer.name']}}</p>
                    <p>{{content?.booking_details.booking['stay_type.name']}}</p>
                  </div>
                </div>
                <img class="view_content_left_image" src="../../../assets/img/checkin.png" alt="Check In Logo">
              </div>
              <div class="view_content_right">
                <p>Check-Out Date</p>
                <b>{{formatCheckoutDate(content?.booking_details.bookingRoom.end, content?.booking_details) | date}}</b>
                <div class="view_content_right_bottum">
                  <div class="view_content_right_first">
                    <p>Current Status</p>
                    <p>Total Person</p>
                    <p>Room Category</p>
                    <p>Check-In</p>
                  </div>
                  <div class="view_content_right_second">
                    <p>
                      {{content?.booking_details.bookingRoom.current_status == 'checkin' ? 'Check In' 
                      : content?.booking_details.bookingRoom.current_status == 'reserved' ? 'Reserved'
                      : content?.booking_details.bookingRoom.current_status == 'checkout' ? 'Check Out' 
                      : '-'}}
                    </p>
                    <p>
                      {{content?.booking_details.bookingRoom.adult + content?.booking_details.bookingRoom.child }}&nbsp; 
                      ( A: {{content?.booking_details.bookingRoom.adult}}/ C: {{content?.booking_details.bookingRoom.child}} )
                    </p>
                    <p>{{content?.booking_details.bookingRoom['room.roomcategory.name']}}</p>
                    <p>
                      {{content ? (content?.booking_details.bookingRoom.current_status == 'reserved' ?
                      (content.booking_details.bookingRoom['expected_check_in'] | date:'shortTime') :
                      (content.booking_details.bookingRoom['check_in'] | date:'h:mm a')) : '-'}}
                    </p>
                  </div>
                </div>
                <img class="view_content_right_image" src="../../../assets/img/checkout.png" alt="">
              </div>
            </div>

            <!-- Payment History -->
            <div class="view_data" *ngIf="ViewData == 'payment'">
              <div class="view_button">
                <div class="view_button_left">
                  <button class="button_Active" (click)="paymentDetails()"><i class="fa-solid fa-credit-card"></i>&nbsp;&nbsp; Payment</button>
                  <button class="button_inactive" style="margin-left: 10px;" (click)="guestDetails()"><i class="fa-solid fa-user"></i>&nbsp;&nbsp; Guest</button>
                </div>
                <div class="view_button_right">
                  <button class="button_Active" (click)="updatePayment(eventData)"><i class="fa-solid fa-arrows-rotate" ></i>&nbsp;&nbsp; Update Payment</button>
                  <select (change)="onActionChange($event)" class="button_Active dropdown" style="margin-left: 10px;" name="Action" id="Action">
                    <option value="Action">Action</option>
                    <option value="Check In">Check In</option>
                    <option value="Transfer Room">Transfer Room</option>
                    <option value="Extend CheckOut">Extend CheckOut</option>
                    <option value="Check Out">Check Out</option>
                  </select>
                  <i class="fa-solid fa-angle-down drop" style="color: #ffffff;"></i>
                </div>
              </div>
              <div class="view_data_content">
                <h1>Payment History</h1>
                <table class="table payroll" >
                  <tr>
                    <th>Description</th>
                    <th>Type</th>
                    <th>Details</th>
                    <th>Date</th>
                    <th>Amount</th>
                  </tr>
                  <tr >
                    <td>Total Amount</td>
                    <td>Full Day Charge</td>
                    <td></td>
                    <td></td>
                    <td class="text-primary"><i class="fa fa-inr"></i>&nbsp;{{tabz.content.data[0]?.booking_details.booking.total_amount}}</td>
                  </tr>
                  <tr *ngFor="let extraGuest of tabz.content.data[0]?.booking_details.ExtraGuest">
                    <td>Extra Guest</td>
                    <td>Charge</td>
                    <td></td>
                    <td>
                      ({{extraGuest['BookingRooms.start'] | date}}&nbsp;&nbsp;
                      <i class="fa fa-long-arrow-right"></i> &nbsp;&nbsp;{{extraGuest['BookingRooms.end'] | date}})</td>
                    <td class="text-danger"><i class="fa fa-inr"></i>&nbsp;+ {{extraGuest['BookingRooms.net_amount']}}</td>
                  </tr>
                  <tr *ngIf="tabz.content.data[0]?.booking_details.bookingCancellationAmount">
                    <td>Cancellation</td>
                    <td>Charge</td>
                    <td></td>
                    <td></td>
                    <td class="text-danger"><i class="fa fa-inr"></i>&nbsp;+ {{tabz.content.data[0]?.booking_details.bookingCancellationAmount}}</td>
                  </tr>
                  <tr>
                    <td>Discount</td>
                    <td>Customer</td>
                    <td></td>
                    <td></td>
                    <td class="text-danger">
                      <i class="fa fa-inr"></i>&nbsp;- {{tabz.content.data[0]?.booking_details.booking.discount}}
                    </td>
                  </tr>
                  <tr *ngFor="let payment of tabz.content.data[0]?.booking_details.bookingPayment">
                    <td> {{payment.is_advance ? 'Advance ' : ''}} Payments ( {{payment["user.first_name"]}} {{payment["user.last_name"]}} )</td>
                    <td>{{paymentType[payment?.payment_mode]?.text.split(' ')[0]}}</td>
                    <td *ngIf="payment.bank_name && payment.bank_cheque_no">{{payment.bank_name}} ( {{payment.bank_cheque_no}} )</td>
                    <td *ngIf="payment.payment_reciept_number">{{payment.payment_reciept_number}}</td>
                    <td *ngIf="!payment.bank_name && !payment.bank_cheque_no && !payment.payment_reciept_number"></td>
                    <td>{{payment?.payment_date | date}}</td>
                    <td class="text-success"><i class="fa fa-inr"></i>&nbsp;- {{payment.amount_with_card_charge + payment.amount}}</td>
                  </tr>
                  <tr *ngFor="let fund of tabz.content.data[0]?.booking_details.bookingFund">
                    <td>Funds</td>
                    <td>{{fund["fund.name"]}}</td>
                    <td></td>
                    <td></td>
                    <td class="text-primary"><i class="fa fa-inr"></i>&nbsp;+ {{fund.amount}}</td>
                  </tr>
                  <tr *ngIf="tabz.content.data[0]?.booking_details.booking.custom_discount">
                    <td>Discount</td>
                    <td>Custom</td>
                    <td></td>
                    <td></td>
                    <td class="text-danger"><i class="fa fa-inr"></i>&nbsp; {{tabz.content.data[0]?.booking_details.booking.custom_discount}}</td>
                  </tr>
                  <tr>
                    <td>Pending Payment</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="text-success">
                      <i class="fa fa-inr"></i>&nbsp;
                      {{(tabz.content.data[0]?.booking_details.booking.cancellation_payable_amount
                      ? tabz.content.data[0]?.booking_details.booking.cancellation_payable_amount : 0) +
                      tabz.content.data[0]?.booking_details.booking.net_amount
                      + (tabz.content.data[0]?.booking_details.ExtraGuestCharge ?
                      tabz.content.data[0]?.booking_details.ExtraGuestCharge
                      : 0) - (tabz.content.data[0]?.booking_details.paidAmount ?
                      tabz.content.data[0]?.booking_details.paidAmount
                      : 0) + (tabz.content.data[0]?.booking_details.cardSwipeCharges ?
                      tabz.content.data[0]?.booking_details.cardSwipeCharges
                      : 0)+ (tabz.content.data[0]?.booking_details.fundAmount ?
                      tabz.content.data[0]?.booking_details.fundAmount
                      : 0) + (tabz.content.data[0]?.totalEarlyCheckInCharge) -
                      (tabz.content.data[0]?.booking_details.booking.custom_discount
                      ? tabz.content.data[0]?.booking_details.booking.custom_discount : 0) +
                      (tabz.content.data[0]?.booking_details.returnAmount
                      ? tabz.content.data[0]?.booking_details.returnAmount : 0)}}
                    </td>
                  </tr>
                </table>
              </div>
            </div>

            <!-- Guest Details  -->
            <div class="view_data" *ngIf="ViewData == 'guest'">
              <div class="view_button">
                <div class="view_button_left">
                  <button class="button_inactive" (click)="paymentDetails()"><i class="fa-solid fa-credit-card"></i>&nbsp;&nbsp; Payment</button>
                  <button class="button_Active" style="margin-left: 10px;" (click)="guestDetails()"><i class="fa-solid fa-user"></i>&nbsp;&nbsp; Guest</button>
                </div>
                <div class="view_button_right">
                  <button class="button_Active" [hidden]="enableAddGuests(content?.booking_details.bookingRoom.current_status)"  (click)="addGuest(eventData, roomIndex)"><i class="fa-solid fa-plus"></i>&nbsp;&nbsp; Add Guest</button>
                  <select (change)="onActionChange($event)" class="button_Active dropdown" style="margin-left: 10px;" name="Action" id="Action">
                    <option value="Action">Action</option>
                    <option value="Check In">Check In</option>
                    <option value="Transfer Room">Transfer Room</option>
                    <option value="Extend CheckOut">Extend CheckOut</option>
                    <option value="Check Out">Check Out</option>
                  </select>
                  <i class="fa-solid fa-angle-down drop" style="color: #ffffff;"></i>
                </div>
              </div>
              <div class="view_data_content">
                <h1>Guest List</h1>
                <table class="table payroll">
                  <tr>
                    <th>No.</th>
                    <th>Full Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Aadhaar Card</th>
                    <th>Pan Card</th>
                    <th>Other Id</th>
                    <th>Action</th>
                  </tr>
                  <tr *ngFor="let guest of tabz.content.data[0]?.booking_details.getGuest">
                    <td>{{tabz.content.data[0]?.booking_details.getGuest.indexOf(guest) + 1}}</td>
                    <td>{{guest.name ? guest.name : '-'}}</td>
                    <td>{{guest.email ? guest.email : '-'}}</td>
                    <td>{{guest.contact ? guest.contact : '-'}}</td>
                    <td>{{guest.aadharcard_number ? guest.aadharcard_number : '-'}}</td>
                    <td>{{guest.pancard_number ? guest.pancard_number : '-'}}</td>
                    <td>{{guest.other_proof_type ? guest.other_proof_type : '-'}}</td>
                    <td class="ViewEditButtons">
                      <!-- <button (click)="viewGuestDetail(guest)"><i class="fa-regular fa-eye" style="color: rgba(86, 86, 86, 1);"></i></button> -->
                      <button (click)="preAddGuestInfo(eventData)"><i class="fa-regular fa-pen-to-square" style="color: rgba(86, 86, 86, 1)"></i></button>
                      <button *ngIf="guest.documents" (click)="viewDocumentProof(guest.documents)" style="padding: 7px 8.7px;"><i class="fa-regular fa-file-image" style="color: rgba(86, 86, 86, 1)"></i></button>
                      <button *ngIf="!guest.documents" style="padding: 7px 8.7px; cursor: not-allowed;"><i class="fa-regular fa-file-image" style="color: rgba(86, 86, 86, 1)"></i></button>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Document Viewer -->
        <div bsModal #viewDocumentModal="bs-modal" id="viewDocumentModal" class="modal fade" tabindex="-1" role="dialog"
        [config]="{backdrop: false, keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <ng-container *ngFor="let imageUrl of imageUrls">
                <img   class="image-modal-view px-1 pb-1 pt-1" [src]="imageUrl" alt="">
              </ng-container>
              <div class="modal-footer">
                <div class="btn-group">
                  <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="closeModal()">
                    {{'VIEW_BOOKI_DETAI.CLOSE' | translate:param}}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- <div bsModal #viewGuestDetailsModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
          [config]="{backdrop: 'static', keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <view-guest-details *ngIf="viewComp" [isPoliceInquiry]="isPoliceInquiry" [guestData]="guestDetailss" (goBack)="closeComp($event)">
              </view-guest-details>
            </div>
          </div>
        </div> -->

        <!-- check out tab -->
        <div *ngIf="tabz?.content.action_type == 'checkout'" class="widget-body table-scroll-res">
          <div class="row payroll">
            <div class="col-sm-7">
              <h3>{{'RESERVATIONS.REM_PAY' | translate:param}}</h3>
              <form [formGroup]="paymentAtcheckout"
                (ngSubmit)="confirmModal(tabz?.content, tabz?.content.customer_id,tabz?.content.process_type, tabz)">
                <table class="table table-strips">
                  <tr>
                    <td>{{'RESERVATIONS.NET_AMT' | translate:param}}
                      <small *ngIf="tabz?.content.payments.cardSwipeCharges">
                        ({{'RESERVATIONS.WITH' | translate:param}}
                        <i class="fa fa-inr"></i>
                        &nbsp;{{tabz?.content.payments.cardSwipeCharges}}
                        {{'RESERVATIONS.CAR_SWIP_CHAR' | translate:param}})
                      </small>
                    </td>
                    <td class="text-right">
                      <i class="fa fa-inr"></i>&nbsp;{{tabz?.content.payments.net_amount | number:'1.2-2'}}
                    </td>
                  </tr>
                  <tr *ngIf="tabz?.content.payments.bookingCustomDiscounts1">
                    <td>
                      {{'RESERVATIONS.CUST_DISCS' | translate:param}}
                    </td>
                    <td class="text-right">
                      <span class="sub-transction">(-)
                        <i class="fa fa-inr"></i>&nbsp;{{tabz?.content.payments.bookingCustomDiscounts1 |
                        number:'1.2-2'}}
                      </span>
                    </td>
                  </tr>
                  <tr *ngIf="tabz?.content.payments.guest_stay_Amount">
                    <td>
                      {{'RESERVATIONS.EXT_GUE_CHAR' | translate:param}}
                    </td>
                    <td class="text-right">
                      <i class="fa fa-inr"></i>&nbsp;{{tabz?.content.payments.guest_stay_Amount | number:'1.2-2'}}
                    </td>
                  </tr>
                  <tr
                    *ngIf="tabz?.content.payments.is_early_checkout && tabz?.content.payments.prev_can_charge && (tabz?.content.payments.prev_can_charge > 0)">
                    <td>
                      {{'RESERVATIONS.PRE_CAN_CHAR' | translate:param}}
                    </td>
                    <td class="text-right">
                      <i class="fa fa-inr"></i>&nbsp;{{tabz?.content.payments.prev_can_charge | number:'1.2-2'}}
                    </td>
                  </tr>
                  <tr *ngIf="tabz?.content.payments.is_early_checkout">
                    <td>
                      {{'RESERVATIONS.CAN_CHARGE' | translate:param}} ( {{tabz?.content.payments.cancellation_charge}}%
                      )
                    </td>
                    <td class="text-right">
                      <i class="fa fa-inr"></i>&nbsp;{{tabz?.content.payments.cancellation_payable_amount |
                      number:'1.2-2'}}
                    </td>
                  </tr>
                  <tr *ngIf="tabz?.content.payments.is_early_checkout">
                    <td>
                      {{'RESERVATIONS.PAY_AMT_CAN' | translate:param}}
                    </td>
                    <td class="text-right">
                      <span class="multiplcation">(+)
                        <i class="fa fa-inr"></i>&nbsp;{{(tabz?.content.payments.total_payable_amount) |
                        number:'1.2-2'}}
                      </span>
                    </td>
                  </tr>
                  <tr *ngIf="tabz?.content.payments.early_checkin_Charge">
                    <td>
                      {{'RESERVATIONS.EA_CHEK_CHAR' | translate:param}}
                    </td>
                    <td class="text-right">
                      <span class="multiplcation">(+)
                        <i class="fa fa-inr"></i>&nbsp;{{tabz?.content.payments.early_checkin_Charge | number:'1.2-2'}}
                      </span>
                    </td>
                  </tr>
                  <tr *ngFor="let payment of tabz?.content.payments.bookingPayment">
                    <td>
                      {{payment.is_advance ? 'Advance Payment' : 'Payment'}}
                      <small>
                        <i *ngIf="payment">({{payment  && paymentType[payment.payment_mode] ? paymentType[payment.payment_mode].text : ''}}
                          <span *ngIf="payment?.payment_mode == 1">
                            - #{{payment.payment_reciept_number}}</span> on {{payment.payment_date | date}} )
                          <span *ngIf="payment?.payment_mode == 2 && payment?.bank_cheque_no">
                            (Cheque No: {{payment.bank_cheque_no}})</span>
                          <span *ngIf="payment?.payment_mode == 3 && payment?.bank_cheque_no">
                            (Cheque No: {{payment.bank_cheque_no}})</span> </i>
                        <span *ngIf="payment.card_charge">
                          {{'RESERVATIONS.WITH' | translate:param}}
                          <i class="fa fa-inr"></i>
                          {{payment.amount_with_card_charge}}
                          {{'RESERVATIONS.CARD_CHARGE' | translate:param}}</span>
                        <span *ngIf="payment.return_amount">
                          <i> {{'RESERVATIONS.WITH_RET_AMT' | translate:param}} <i
                              class="fa fa-inr"></i>{{payment.return_amount}}</i>
                        </span>
                      </small>
                    </td>
                    <td class="text-right">
                      <span class="sub-transction">(-)
                        <i class="fa fa-inr"></i>&nbsp;
                        <!-- {{(payment.amount_with_card_charge > 0 ?
                        payment.amount_with_card_charge: payment ? payment.amount : 0) | number:'1.2-2'}} -->
                        {{payment.amount_with_card_charge + payment.amount | number:'1.2-2'}}
                      </span>
                    </td>
                  </tr>
                  <tr *ngIf="tabz?.content.payments.returnAmount">
                    <td>
                      {{'RESERVATIONS.RETED_AMT' | translate:param}}
                    </td>
                    <td class="text-right">
                      <span class="multiplcation">(+)
                        <i class="fa fa-inr"></i>&nbsp;{{tabz?.content.payments.returnAmount | number:'1.2-2'}}
                        `</span>
                    </td>
                  </tr>
                  <tr *ngFor="let fund of tabz?.content.payments.bookingFund">
                    <td>
                      {{'RESERVATIONS.FUNDS' | translate:param}}
                      <small>
                        <i *ngIf="fund">
                          ({{fund ? fund['fund.name'] : ''}})
                        </i>
                      </small>
                    </td>
                    <td class="text-right">
                      <i class="fa fa-inr"></i>&nbsp;{{(fund ? fund.amount : 0) | number:'1.2-2'}}
                    </td>
                  </tr>

                  <tr>
                    <td>{{'RESERVATIONS.TOT_PAY' | translate:param}}</td>
                    <!-- <td class="text-right"
                        [ngStyle]="{'display': paymentAtcheckout?.value.payment_mode == '1'? 'block' : 'none'}">
                        <span style="color: blue;">
                        <i class="fa fa-inr"></i>
                        {{tabz?.content.cardPaymentTotal ? tabz?.content.cardPaymentTotal : 0}}
                        </span>
                      </td> -->
                    <!-- [ngStyle]="{'display': paymentAtcheckout?.value.payment_mode == '1'? 'none' : 'block'}" -->
                    <td class="text-right">
                      <span style="color: blue;">
                        <i class="fa fa-inr"></i>
                        <span #totalPayableAmount>
                          {{ ((tabz?.content.payments.total_payable_amount - (tabz?.content.payments.paidAmount ?
                          tabz?.content.payments.paidAmount
                          : 0) + (tabz?.content.payments.returnAmount ? tabz?.content.payments.returnAmount : 0) -
                          paymentAtcheckout?.value.payment_amount +
                          (tabz?.content.payments.early_checkin_Charge
                          ? tabz?.content.payments.early_checkin_Charge : 0) + (tabz?.content.payments.fundAmount ?
                          tabz?.content.payments.fundAmount
                          : 0) - paymentAtcheckout?.value.custom_discount)
                          < 0) ? 0 : tabz?.content.payments.total_payable_amount - (tabz?.content.payments.paidAmount ?
                            tabz?.content.payments.paidAmount : 0) + (tabz?.content.payments.returnAmount ?
                            tabz?.content.payments.returnAmount : 0) - paymentAtcheckout?.value.payment_amount +
                            (tabz?.content.payments.early_checkin_Charge ? tabz?.content.payments.early_checkin_Charge :
                            0) + (tabz?.content.payments.fundAmount ? tabz?.content.payments.fundAmount : 0) -
                            paymentAtcheckout?.value.custom_discount | number: '1.2-2' }} </span>
                        </span>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <div class="paymentMode" style="margin-left: 0px;">
                        <label for="">
                          {{'RESERVATIONS.PAY_MODE' | translate:param}}
                        </label>
                        <ng-select
                          class="advancePaymentSelection"
                          [items]="paymentType"
                          bindLabel="text"
                          bindValue="id"
                          [clearable]="false"
                          [searchable]="true"
                          [dropdownPosition]="auto"
                          [ngModel]="selectedPaymentType"
                          formControlName="payment_mode"
                          (change)="PaymentAtcheckoutSelectionChanged($event, tabz?.content, tabz?.content.process_type)"
                        >
                        </ng-select>
                      </div>
                    </td>                    
                    <td>
                      <div *ngIf="paymentTypeSelection == '1'">
                        <div class="input-group">
                          <span class="input-group-addon">
                            <i class="fa fa-file-text-o"></i>
                          </span>
                          <input type="text" class="form-control" formControlName="payment_reciept_number"
                            placeholder="{{'RESERVATIONS.RECE_NUM' | translate:param}}">
                        </div>
                        <br>
                        <div class="input-group">
                          <span class="input-group-addon">
                            <i class="fa fa-percent"></i>
                          </span>
                          <input type="text" formControlName="card_charge" class="form-control"
                            placeholder="{{'RESERVATIONS.CARD_SWI' | translate:param}}"
                            (keyup)="updateCheckoutCardSwipeCharges(tabz?.content)">
                        </div>
                        <br>
                      </div>
                      <div *ngIf="paymentTypeSelection == '2'">
                        <div class="input-group">
                          <span class="input-group-addon">
                            <i class="fa fa-file-text-o"></i>
                          </span>
                          <input type="text" formControlName="bank_name" class="form-control"
                            placeholder="{{'RESERVATIONS.BANK_NAME' | translate:param}}">
                        </div>
                        <br>
                        <div class="input-group">
                          <span class="input-group-addon">
                            <i class="fa fa-file-text-o"></i>
                          </span>
                          <input type="text" formControlName="bank_cheque_no" class="form-control"
                            placeholder="Cheque No.">
                        </div>
                        <br>
                      </div>
                      <div *ngIf="paymentTypeSelection == '3'">
                        <div class="input-group">
                          <span class="input-group-addon">
                            <i class="fa fa-file-text-o"></i>
                          </span>
                          <input type="text" formControlName="bank_name" class="form-control" placeholder="Bank Name">
                        </div>
                        <br>
                        <div class="input-group">
                          <span class="input-group-addon">
                            <i class="fa fa-file-text-o"></i>
                          </span>
                          <!-- <input type="text" formControlName="bank_cheque_no" class="form-control"
                              placeholder="Reference No."> -->
                          <input type="text" formControlName="bank_cheque_no" class="form-control"
                            placeholder="{{'RESERVATIONS.CHECK_NO' | translate:param}}">
                        </div>
                        <br>
                      </div>
                      <div class="input-group">
                        <span class="input-group-addon">
                          <i class="fa fa-inr"></i>
                        </span>
                        <input type="text" formControlName="payment_amount"
                          placeholder="{{'RESERVATIONS.AMOUNT' | translate:param}}"
                          (keyup)="updateFunds('splite' ,tabz?.content, tabz?.content.process_type)"
                          class="form-control text-right">
                      </div>
                      <span class="text-right text-success">
                        <strong class="text-danger"
                            *ngIf="this.paymentAtcheckout.value.payment_mode == '0' && isAdvancePayment && paymentAtcheckout.get('payment_amount').value > (perDayPayment - paidAdvanceAmount)">
                            {{ "We only accept 1 day payment as advance in cash " + ' , ' + "Per Day Amount:" + ' ' + perDayPayment}}
                        </strong>
                        <strong class="text-danger"
                        *ngIf="(this.paymentAtcheckout.value.payment_mode != '0' && isAdvancePayment && paymentAtcheckout.get('payment_amount').value > (bankTotalPayable))">
                        {{ "We only accept total_amount  as advance" + ' , ' + "Remaining Amount:" + ' ' + bankTotalPayable }}
                    </strong>
                        
                    </span>
                    </td>
                   
                  </tr>
                  <!-- [ngStyle]="{'display': paymentAtcheckout.value.payment_mode == '1' ? 'none' : 'table-row'}" -->
                  <tr>
                    <td>
                      {{'RESERVATIONS.RET_AMT_DOT' | translate:param}}
                    </td>
                    <td>
                      <div class="row">
                        <div class="col-xs-6">
                          <div class="input-group">
                            <span class="input-group-addon fundCheck abc-checkbox abc-checkbox-success"
                              style="padding-left: 0px">
                              <input type="checkbox" #fundtypeID id="fundtypeID"
                                [disabled]="selectedFundValue ? false : true" (change)="fundTypeEnable($event)">
                              <label for="fundtypeID" class="capitalized">
                              </label>
                            </span>
                            <ng-select
                            id="default-select"
                            [disabled]="disableFund"
                            class="custom-ng-select return_amount_select"
                            [items]="fndType"
                            bindLabel="text"
                            bindValue="id"
                            [clearable]="false"
                            [searchable]="true"
                            [dropdownPosition]="auto"
                            [ngModel]="selectedFundValue"
                            formControlName="fund_type"
                            (change)="FundTypeChanges($event)">
                          </ng-select>                          
                          </div>
                        </div>
                        <div class="col-xs-6">
                          <div class="form-control text-right">
                            <i class="fa fa-inr"></i>&nbsp;
                            <span #returnAmountValue>
                              <!-- + (tabz?.content.payments.cardSwipeCharges ? tabz?.content.payments.cardSwipeCharges : 0) -->
                              {{(((tabz?.content.payments.total_payable_amount - (tabz?.content.payments.paidAmount ?
                              tabz?.content.payments.paidAmount
                              : 0) + (tabz?.content.payments.returnAmount ? tabz?.content.payments.returnAmount : 0) -
                              paymentAtcheckout?.value.payment_amount
                              +
                              (tabz?.content.payments.early_checkin_Charge ?
                              tabz?.content.payments.early_checkin_Charge
                              : 0) + (tabz?.content.payments.fundAmount ? tabz?.content.payments.fundAmount : 0) -
                              paymentAtcheckout?.value.custom_discount)
                              < 0) ? makePositive(tabz?.content,(tabz?.content.payments.total_payable_amount -
                                (tabz?.content.payments.paidAmount ? tabz?.content.payments.paidAmount : 0) +
                                (tabz?.content.payments.returnAmount ? tabz?.content.payments.returnAmount : 0) -
                                paymentAtcheckout?.value.payment_amount + (tabz?.content.payments.early_checkin_Charge ?
                                tabz?.content.payments.early_checkin_Charge : 0) + (tabz?.content.payments.fundAmount ?
                                tabz?.content.payments.fundAmount : 0) - paymentAtcheckout?.value.custom_discount)) :
                                paymentAtcheckout?.value.payment_amount>
                                0 ? returnZero() : 0)}}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr *ngIf="(paymentTypeSelection == '1')">
                    <td>
                      {{'RESERVATIONS.CARD_SWI' | translate:param}}
                    </td>
                    <td class="text-right">
                      <span class="multiplcation">(+)
                        <i class="fa fa-inr"></i>&nbsp;{{tabz?.content.payments.cardSwipeCharges ?
                        tabz?.content.payments.cardSwipeCharges
                        : 0}}
                      </span>
                    </td>
                  </tr>
                  <!-- <tr *ngIf="authGuard.isAdmin() && tabz?.content.process_type == 'checkout'">
                      <td>{{'RESERVATIONS.CUST_DISC' | translate:param}}</td>
                      <td class="text-right">
                        <div class="input-group">
                          <span class="input-group-addon">
                            <i class="fa fa-inr"></i>
                          </span>
                          <input class="form-control" formControlName="custom_discount" type="text"
                            placeholder="{{'RESERVATIONS.CUST_DISC_PLACE' | translate:param}}"
                            (keyup)="updateFunds('no',tabz?.content, tabz?.content.process_type)">
                        </div>
                      </td>
                    </tr> -->

                  <tr *ngIf="tabz?.content.process_type == 'checkout' && paymentAtcheckout.controls['reference_user']">
                    <td>{{'RESERVATIONS.REF' | translate:param}}</td>
                    <td class="text-right">
                      <div class="input-group" style="margin-top: .5rem;">
                        <span class="input-group-addon" [ngClass]="{'has-border-right': !isCheckoutReference}">
                          <div class="abc-checkbox is_reference abc-checkbox-warning float-xs-left">
                            <input type="checkbox" id="checkbox1" [disabled]="!refereceUser"
                              (change)="hasCheckoutReference()" value="true">
                            <label for="checkbox1"></label>
                          </div>
                        </span>
                        <div class="relative" style="width: 100%">
                          <ng-select
                            [items]="refereceUser"
                            class="referenceSelections"
                            bindLabel="text"
                            bindValue="id"
                            [clearable]="false"
                            [searchable]="true"
                            [dropdownPosition]="'auto'"
                            (change)="checkoutReferenceTypeChanged($event)">
                          </ng-select>
                        </div>                        
                      </div>
                      <div
                        *ngIf="paymentAtcheckout.controls['reference_user'].dirty && paymentAtcheckout.controls['reference_user'].invalid"
                        class="alert alert-danger">
                        <div *ngIf="paymentAtcheckout.controls['reference_user'].errors?.required">
                          {{'RESERVATIONS.VALID_MSG.REF_REQ' | translate:param}}</div>
                      </div>
                    </td>
                  </tr>
                  <tr *ngIf="tabz?.content.process_type == 'checkout' && paymentAtcheckout.controls['note']">
                    <td>{{'RESERVATIONS.NOTE' | translate:param}}</td>
                    <td class="text-right">
                      <div class="input-group">
                        <textarea class="form-control" formControlName="note" type="text"
                          placeholder="{{'RESERVATIONS.TXT_MSG' | translate:param}}"></textarea>
                      </div>
                      <div
                        *ngIf="paymentAtcheckout.controls['note'].dirty && paymentAtcheckout.controls['note'].invalid"
                        class="alert alert-danger">
                        <div *ngIf="paymentAtcheckout.controls['note'].errors?.required">
                          {{'RESERVATIONS.VALID_MSG.NOT_REQ' | translate:param}}</div>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>{{'RESERVATIONS.REM_AMOUNT' | translate:param}}</td>
                    <!-- <td class="text-right"
                        [ngStyle]="{'display': paymentAtcheckout?.value.payment_mode == '1' ? 'block' : 'none'}">
                        <span style="color: blue;">
                        <i class="fa fa-inr"></i>
                        {{tabz?.content.cardPaymentTotal ? tabz?.content.cardPaymentTotal : 0}}
                        </span>
                      </td> -->
                    <!-- [ngStyle]="{'display': paymentAtcheckout?.value.payment_mode == '1' ? 'none' : 'block'}" -->
                    <td class="text-right">
                      <span style="color: blue;">
                        <i class="fa fa-inr"></i>
                        <span #totalPayableAmount>
                          <!-- + (tabz?.content.payments.returnAmount ? tabz?.content.payments.returnAmount : 0) -->
                          {{ ((tabz?.content.payments.total_payable_amount - (tabz?.content.payments.paidAmount ?
                          tabz?.content.payments.paidAmount
                          : 0) + (tabz?.content.payments.returnAmount ? tabz?.content.payments.returnAmount : 0)
                          - paymentAtcheckout?.value.payment_amount +
                          (tabz?.content.payments.early_checkin_Charge
                          ? tabz?.content.payments.early_checkin_Charge : 0) + (tabz?.content.payments.fundAmount ?
                          tabz?.content.payments.fundAmount
                          : 0) - paymentAtcheckout?.value.custom_discount)
                          < 0) ? 0 : tabz?.content.payments.total_payable_amount - (tabz?.content.payments.paidAmount ?
                            tabz?.content.payments.paidAmount : 0) + (tabz?.content.payments.returnAmount ?
                            tabz?.content.payments.returnAmount : 0) - paymentAtcheckout?.value.payment_amount +
                            (tabz?.content.payments.early_checkin_Charge ? tabz?.content.payments.early_checkin_Charge :
                            0) + (tabz?.content.payments.fundAmount ? tabz?.content.payments.fundAmount : 0) -
                            paymentAtcheckout?.value.custom_discount | number: '1.2-2' }} </span>
                        </span>
                    </td>
                  </tr>
                  <tr *ngIf="paymentAtcheckout.controls['payment_verification_id']">
                    <td>{{'RESERVATIONS.PAN_NO' | translate:param}}</td>
                    <td class="text-right">
                      <div class="input-group">
                        <input (keyup)="getPanNo('b')" class="form-control" formControlName="payment_verification_id"
                          type="text" placeholder="{{'RESERVATIONS.PAN_NO_PLACE' | translate:param}}." />
                      </div>
                      <div
                        *ngIf="paymentAtcheckout.controls['payment_verification_id'].dirty && paymentAtcheckout.controls['payment_verification_id'].invalid"
                        class="alert alert-danger">
                        <div *ngIf="paymentAtcheckout.controls['payment_verification_id'].errors?.required">
                          {{'RESERVATIONS.VALID_MSG.PAN_REQ' | translate:param}}</div>
                      </div>
                    </td>
                  </tr>
                </table>
                <!------------ Splite Recept Name ----------->
                <div
                  *ngIf="cardSelected == true? true : false || ( paymentTypeSelection == '0' && ( tabz?.content.booking_status == 'reserved' || tabz?.content.action_type == 'checkout'))">
                  <div formArrayName="items"
                    *ngFor="let item of paymentAtcheckout.get('items').controls; let i = index">
                    <div [formGroupName]="i" class="input-group">
                      <input class="form-control m-1" formControlName="name" type="text" placeholder="Name">
                      <input class="form-control m-1" formControlName="city" type="text" placeholder="City">
                      <input class="form-control m-1" formControlName="panno" type="text" placeholder="Pan No">
                    </div>
                  </div>
                  <div *ngIf="paymentAtcheckout.controls['items'].dirty && paymentAtcheckout.controls['items'].invalid"
                    class="alert alert-danger">
                    <!-- <div *ngIf="paymentAtcheckout.controls['items'].errors?.required"> -->
                    All field,s are required.
                    <!-- </div> -->
                  </div>
                </div>
                <!--------- End Splite Recept Name ------------->
                <div class="text-right">
                  <!-- amountAccept ||  -->
                  <button type="submit" [disabled]="is_disable || ((((tabz?.content.process_type == 'checkout') &&
                        paymentAtcheckout.controls['pavati_no'].invalid) || paymentAtcheckout.controls['payment_amount'].invalid ||
                        ((this.paymentAtcheckout.value.payment_mode == '1') && paymentAtcheckout.controls['payment_verification_id'].invalid &&
                        ((tabz?.content.cardPaymentTotal > 0 ? tabz?.content.cardPaymentTotal : 0).toString() >= paymentAtcheckout.value.payment_amount)) ||
                        ((paymentAtcheckout?.value.payment_amount <= 0 && tabz?.content.process_type != 'checkout') &&
                          ((tabz?.content.payments.total_payable_amount -
                            (tabz?.content.payments.paidAmount ? tabz?.content.payments.paidAmount : 0) -
                            (tabz?.content.payments.cardSwipeChargesOriginal ? tabz?.content.payments.cardSwipeChargesOriginal : 0) -
                            paymentAtcheckout?.value.payment_amount -
                            paymentAtcheckout?.value.custom_discount +
                            (tabz?.content.payments.fundAmount ? tabz?.content.payments.fundAmount : 0)) > 0))) ||  (this.paymentAtcheckout.value.payment_mode == '0' && isAdvancePayment && paymentAtcheckout.get('payment_amount').value > (perDayPayment - paidAdvanceAmount)) || (this.paymentAtcheckout.value.payment_mode != '0' && isAdvancePayment && paymentAtcheckout.get('payment_amount').value > (bankTotalPayable)))"
                    class="btn btn-ms btn-inverse">{{tabz?.content.process_type
                    == 'payment' ? 'Update Payment' : 'CheckOut'}}</button>
                </div>
              </form>
            </div>
            <div class="col-sm-5 border-sm-left">
              <div>
                <h3>{{'RESERVATIONS.CUST_DETAILS' | translate:param}}</h3>
                <hr style="margin-top: 0;">
                <h1 class="text-capitalize">{{tabz?.content.username}}</h1>
                <strong class="text-light">
                  <i>{{'RESERVATIONS.BOOK_FROM' | translate:param}} {{tabz?.content.start}} to {{tabz?.content.end}}</i>
                </strong>
              </div>
              <br>
              <div *ngIf="tabz?.content.payments.immidiate_return_amount" class="card"
                style="border: 1px solid rgba(0, 0, 0, 0.125);">
                <div class="card-body">
                  <strong>
                    <h3>{{'RESERVATIONS.IMME_RETU' | translate:param}}</h3>
                  </strong>
                  <hr style="margin-top: 0;">
                  <strong style="color: red;">{{'RESERVATIONS.REF_AMT' | translate:param}} &nbsp;&nbsp;<i
                      class="fa fa-inr"></i>{{tabz?.content.payments.immidiate_return_amount}}</strong>
                  <div style="margin-top: 15px;">
                    <small>
                      <code>*</code>
                      <i>
                        {{'RESERVATIONS.ADV_PREV' | translate:param}}
                        <strong>
                          {{'RESERVATIONS.GUE_TYPE' | translate:param}}
                        </strong>
                        {{'RESERVATIONS.REF_AMT_NEED' | translate:param}}
                      </i>
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- ************************************************************************************ -->


        <!-- *************************** Bill Print ***************************** -->
        <!-- assets/img/Receipt Latter 02.jpg -->
        <div class="widget-body" *ngIf="tabz?.content.action_type == 'invoice'">
          <!-- <h5 style="text-align:center;">INVOICE</h5> -->
          <div>
            <img src="assets/img/Receipt Latter 02.jpg" style="max-width: -webkit-fill-available;">
          </div>
          <div class="mt billing-guest">
            <div class="row">
              <div class="col-sm-12">
                <div class="row">
                  <div class="col-sm-6">
                    <p style="margin-bottom: 1em;">
                      <strong>{{'RESERVATIONS.BILL_NO' | translate:param}} :
                        {{tabz?.content.booking_details.booking.bill_no}}</strong>
                    </p>
                    <h3 style="margin-bottom: 0px;">{{'RESERVATIONS.MR' | translate:param}}
                      <span class="text-capitalize">
                        {{tabz?.content.booking_details.booking['is_alternate_bg_active'] ?
                        tabz?.content.booking_details.booking['alternate_billing_guest']
                        : tabz?.content.booking_details.booking['guest.name']}}
                      </span>
                    </h3>
                  </div>
                  <div class="col-sm-6">
                    <p>
                      <span style="float:right;">
                        <strong>
                          {{'RESERVATIONS.DATE' | translate:param}} : {{currMoment | date:'dd/MM/y'}}
                        </strong>
                      </span>
                    </p>
                  </div>
                </div>
                <hr>
                <div class="row">
                  <div class="col-sm-12">
                    <p style="margin-bottom: 0px;">{{'RESERVATIONS.ADDRESS' | translate:param}} :
                      <span *ngIf="tabz?.content.booking_details.booking['guest.address']">
                        {{tabz?.content.booking_details.booking['guest.address']}}
                      </span>
                      <span *ngIf="tabz?.content.booking_details.booking['guest.city']">
                        , {{tabz?.content.booking_details.booking['guest.city']}}
                      </span>
                    </p>
                    <p>{{'RESERVATIONS.TOT_PAX' | translate:param}}: {{tabz?.content.adult}} Adult,
                      {{tabz?.content.child}} Child</p>
                    <p>
                      {{'RESERVATIONS.ARRIVE' | translate:param}} :
                      {{tabz?.content.booking_details.bookingRoom[0].check_in | date:'MMM d, y, h:mm a'}}
                    </p>
                    <p>
                      {{'RESERVATIONS.DEPARTURE' | translate:param}} :
                      {{tabz?.content.booking_details.bookingRoom[0].check_out | date:'MMM d, y, h:mm a'}}
                    </p>
                  </div>
                </div>
                <hr>
                <!-- START: payments section -->
                <div class="mt" *ngIf="tabz?.content.booking_details.bookingPayment.length > 0">
                  <table class="table table-condence no-m-b">
                    <thead>
                      <tr>
                        <th style="width: 10%;">
                          {{'RESERVATIONS.SR_NO' | translate:param}}
                        </th>
                        <th style="width: 30%;">
                          {{'RESERVATIONS.PAY' | translate:param}}
                        </th>
                        <th style="width: 20%;">
                          {{'RESERVATIONS.PAY_MODE' | translate:param}}
                        </th>
                        <th style="width: 20%;">
                          {{'RESERVATIONS.BOOK_DATE' | translate:param}}
                        </th>
                        <th class="text-right" style="width: 20%;">
                          {{'RESERVATIONS.NET_AMT' | translate:param}}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let payment of tabz?.content.booking_details.bookingPayment; let j = index;">
                        <td style="width: 10%">
                          {{j + 1}}
                        </td>
                        <td style="width: 30%;">
                          {{payment.is_advance ? "Advance payment" : "Post check-in payment"}}
                        </td>
                        <td style="width: 20%;">
                          {{paymentType[findIndex(payment.payment_mode,"id",paymentType) - 1].text}}
                        </td>
                        <td style="width: 20%;">
                          {{payment.payment_date ? (payment.payment_date | date) : '-'}}
                        </td>
                        <td class="text-right" style="width: 20%;">
                          <i class="fa fa-inr"></i>
                          {{payment.amount}}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!-- END: payments section -->
                <div class="row">
                  <div class="col-sm-12">
                    <p style="text-align: right;">
                      <span style="
                          font-size: 2rem;
                          width: 100%;
                          display: block;">
                        {{'RESERVATIONS.BHET_FU_RE' | translate:param}}
                        <span>
                          <small>
                            <i class="fa fa-inr"></i>
                            {{tabz?.content.booking_details.paidAmount}}
                          </small>
                        </span>
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="btn btn-sm btn-primary" style="margin-top: 0px;" (click)="takeBillPrint()">Print</div>
        </div>
        <!-- **************************** Bill Print ***************************** -->


        <!-- *************************** Add/Edit Booking Note ***************************** -->
        <div *ngIf="tabz?.content.action_type == 'booking_note'" class="widget-body">
          <div class="row">
            <div class="col-sm-6">
              <h1>{{'RESERVATIONS.NOTE' | translate:param}}</h1>
            </div>
            <div class="col-sm-6">
              <button class="btn btn-default pull-right" (click)="addNote(notesArray)">{{'RESERVATIONS.ADD' |
                translate:param}}</button>
            </div>
          </div>
          <form [formGroup]="addEditNoteForm" (ngSubmit)="saveBookingNote(tabz?.content,tabIndex)">
            <div class="mt" formArrayName="note">
              <div class="row">
                <div class="form-group col-sm-3" *ngFor="let note of notesArray; let i = index;" [formGroupName]="i">
                  <div class="row">
                    <div class="col-sm-11" style="padding-right: 10px;">
                      <textarea [attr.readonly]="(note.message && (!authGuard.isAdmin())) ? true : null"
                        class="form-control" formControlName="message" rows="2"></textarea>
                    </div>
                    <div class="col-sm-1" *ngIf="i > 0 && authGuard.isAdmin()" style="padding: 0px; color: red;">
                      <i class="fa fa-trash" (click)="deleteNote(note)"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button *ngIf="notesArray && notesArray.length > 0" [disabled]="addEditNoteForm.invalid"
              class="btn btn-default">{{'RESERVATIONS.SAVE' | translate:param}}</button>
          </form>
        </div>
        <!-- **************************** Add/Edit Booking Note ***************************** -->


        <!-- *************************** Over - Booking Note ***************************** -->

        <div *ngIf="tabz?.content.action_type == 'over_booking'" class="widget-body">
          <div class="row">
            <div class="col-sm-6">
              <h1>{{'RESERVATIONS.OVER_BOOK' | translate:param}}</h1>
            </div>
          </div>
          <!-- Over-Booking LIST -->
          <div *ngIf="overBookingList.length > 0" class="mt">
            <table class="table table-condence no-m-b">
              <thead>
                <tr>
                  <th>{{'RESERVATIONS.SR_NO' | translate:param}}</th>
                  <th>{{'RESERVATIONS.NAME' | translate:param}}</th>
                  <th>{{'RESERVATIONS.CONT' | translate:param}}</th>
                  <th>{{'RESERVATIONS.EMAIL' | translate:param}}</th>
                  <th>{{'RESERVATIONS.ROOM_NO' | translate:param}}</th>
                  <th>{{'RESERVATIONS.BOOK_FROM' | translate:param}}</th>
                  <th>{{'RESERVATIONS.BOOK_TO' | translate:param}}</th>
                  <th class="text-center">{{'RESERVATIONS.ACTION' | translate:param}}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let overBooking of overBookingList; let i = index;">
                  <td>{{i + 1}}</td>
                  <td>{{overBooking.g_name ? overBooking.g_name : '-'}}</td>
                  <td>{{overBooking.contact ? overBooking.contact : '-'}}</td>
                  <td>{{overBooking.email ? overBooking.email : '-'}}</td>
                  <td>{{overBooking.title ? overBooking.title : '-'}}</td>
                  <td>{{overBooking.start ? (overBooking.start | date) : '-'}}</td>
                  <td>{{overBooking.end ? (overBooking.end | date) : '-'}}</td>
                  <td class="text-center">
                    <i class="fa fa-check text-primary" tooltip="{{'RESERVATIONS.CONF_OVE_BOOK' | translate:param}}"
                      placement="bottom" style="cursor: pointer;"
                      (click)="comfirmOverBooking(overBooking)"></i>&nbsp;&nbsp;
                    <i class="fa fa-trash-o text-primary" tooltip="{{'RESERVATIONS.DEL_OVE_BOOK' | translate:param}}"
                      placement="bottom" style="cursor: pointer;" (click)="deleteOverBooking(overBooking)"></i>
                  </td>
                </tr>
              </tbody>
            </table>
            <div *ngIf="overBookingList.length == 0 && !overBookingForm" class="body-center-content"
              (click)="addNewOverBooking(tabz?.content.cat_id,tabz?.content.note_date)">
              <i class="fa fa-plus"></i>&nbsp;
              <span>
                {{'RESERVATIONS.CLICK_HERE' | translate:param}}
                <strong>
                  {{'RESERVATIONS.OVER_BOOK' | translate:param}}
                </strong>
              </span>
            </div>
          </div>
        </div>

        <!-- **************************** Over - Booking Note ***************************** -->


        <!-- *************************** Common Room Booking ***************************** -->

        <div *ngIf="tabz?.content.action_type == 'common_room'" class="widget-body common-room-booking-element">
          <div class="row">
            <div class="col-sm-6">
              <h1>{{tabz?.content.booking_details[0].room_name}}</h1>
              ({{'RESERVATIONS.COMM_ROOM_BOOK' | translate:param}})
            </div>
            <div class="col-sm-6">
              <div *ngIf="tabz?.content.booking_details" class="text-right">
                <small>
                  <strong>{{'RESERVATIONS.TOT_BED' | translate:param}} :
                    &nbsp;&nbsp;{{tabz?.content.booking_details[0].max_bed | number:'2.0-0'}}</strong>
                </small>
              </div>
              <div class="pull-right" class="text-right">
                <small>
                  <strong>{{'RESERVATIONS.RES_BED' | translate:param}}:
                    &nbsp;&nbsp;{{tabz?.content.guestCount | number:'2.0-0'}}</strong>
                </small>
              </div>
            </div>
          </div>
          <div class="mt">
            <!-- *ngIf="overBookingList.length > 0" -->
            <table class="table with-labels table-condence no-m-b">
              <thead>
                <tr>
                  <th>{{'RESERVATIONS.SR_NO' | translate:param}}</th>
                  <th>{{'RESERVATIONS.NAME' | translate:param}}</th>
                  <th>{{'RESERVATIONS.CONT' | translate:param}}</th>
                  <th>{{'RESERVATIONS.ROOM' | translate:param}}</th>
                  <th>{{'RESERVATIONS.BOOK_FROM' | translate:param}}</th>
                  <th>{{'RESERVATIONS.BOOK_TO' | translate:param}}</th>
                  <th class="text-center">{{'RESERVATIONS.TOT_BEDS' | translate:param}}</th>
                  <th class="text-center">{{'RESERVATIONS.STATUS' | translate:param}}</th>
                </tr>
              </thead>
              <tbody #commonRoomBody id="commonRoomBody">
                <tr class="common-room" [contextMenu]="CommonRoomBooking" [contextMenuSubject]="booking"
                  (contextmenu)="selectRow($event)"
                  *ngFor="let booking of tabz?.content.booking_details; let i = index;">
                  <td>{{i + 1}}</td>
                  <td>{{booking.guest_name}}</td>
                  <td>{{booking.guest_contact}}</td>
                  <td>{{booking.room_name}}</td>
                  <td>{{booking.startFullDate | date}}</td>
                  <td>{{booking.endFullDate | date}}</td>
                  <td class="text-center">
                    <span class="badge" style="background: #5d8fc2;">
                      {{booking.total_bed}}
                    </span>
                  </td>
                  <td class="capitalize text-center">
                    <label for="" class="label" [ngClass]="{
                            'label-warning': booking.current_status == 'checkout',
                            'label-success': booking.current_status == 'checkin',
                            'label-primary': booking.current_status == 'reserved',
                            'label-danger': booking.current_status == 'cancelled'}">
                      {{booking.current_status}}
                    </label>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- **************************** Common Room Booking ***************************** -->

        <!-- check in tab -->

        <div *ngIf="tabz?.content.action_type == 'checkin'">
          <form [formGroup]="checkInProcessform" (ngSubmit)="saveProofs(tabz?.content.user_data.id)">
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label for="" class="control-label">{{'RESERVATIONS.FULL_NAME' | translate:param}}</label>
                  <div>
                    <input type="text" formControlName="name" class="form-control">
                  </div>
                </div>
                <div class="form-group">
                  <label for="" class="control-label">{{'RESERVATIONS.CONC_NO' | translate:param}}</label>
                  <div>
                    <input type="text" formControlName="contact" class="form-control">
                  </div>
                </div>
                <div class="form-group">
                  <label for="" class="control-label">{{'RESERVATIONS.EMAIL' | translate:param}}</label>
                  <div>
                    <input type="text" formControlName="email" class="form-control">
                  </div>
                </div>
                <div class="form-group">
                  <button type="submit" [disabled]="checkInProcessform.controls.abletocheckin.value"
                    class="btn btn-md btn-success">{{'RESERVATIONS.SAVE_DOC' | translate:param}}
                  </button>
                  {{checkInProcessform}}
                  <button type="button" [disabled]="!checkInProcessform.controls.abletocheckin.value"
                    (click)="processCheckInAgain()" class="btn btn-md btn-primary">{{'RESERVATIONS.CHECK_IN' |
                    translate:param}}
                  </button>
                </div>
              </div>
              <div class="col-sm-6">
                <div class="relative form-group">
                  <label for="" class="control-label">
                    {{'RESERVATIONS.PROF_TYPE' | translate:param}} :
                  </label>
                  <ng-select
                    [items]="proofTypeList"
                    (change)="proofTypeChanged($event)"
                    bindLabel="text"
                    bindValue="id"
                    [clearable]="false"
                    [searchable]="true"
                    [dropdownPosition]="'auto'">
                  </ng-select>
                </div>                
                <div class="form-group">
                  <label for="" class="control-label">
                    {{'RESERVATIONS.FILE_UPLOAD' | translate:param}} :
                  </label>
                  <input type="file" name="avatar" class="form-control" ng2FileSelect [uploader]="uploader" multiple />
                  <br />
                </div>
                <div class="files" *ngIf="uploader.queue.length" class="well well-sm">
                  <table class="table">
                    <thead>
                      <tr>
                        <th width="50%">{{'RESERVATIONS.NAME' | translate:param}}</th>
                        <th width="230px">&nbsp;</th>
                        <th style="text-align: right">{{'RESERVATIONS.ACTION' | translate:param}}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let item of uploader.queue">
                        <td>
                          <strong>{{ item?.file?.name }}</strong>
                        </td>
                        <td *ngIf="uploader.isHTML5">
                          <div class="progress" style="margin-bottom: 0;">
                            <div class="progress-bar" role="progressbar" [ngStyle]="{ 'width': item.progress + '%' }">
                            </div>
                          </div>
                        </td>
                        <td class="text-center">
                          <span *ngIf="item.isSuccess">
                            <i class="glyphicon glyphicon-ok"></i>
                          </span>
                          <span *ngIf="item.isCancel">
                            <i class="glyphicon glyphicon-ban-circle"></i>
                          </span>
                          <span *ngIf="item.isError">
                            <i class="glyphicon glyphicon-remove"></i>
                          </span>
                        </td>
                        <td nowrap>

                          <button type="button" class="btn btn-success btn-xs" (click)="item.upload()"
                            [disabled]="item.isReady || item.isUploading || item.isSuccess">
                            <span class="glyphicon glyphicon-upload"></span>
                            {{'RESERVATIONS.FILE_UPLOAD' | translate:param}}
                          </button>
                          <button type="button" class="btn btn-danger btn-xs" (click)="item.remove()">
                            <span class="glyphicon glyphicon-trash"></span>{{'RESERVATIONS.FILE_REMOVE' |
                            translate:param}}
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <hr>
                </div>
              </div>
            </div>
          </form>
        </div>
        <!-- ************************************************************************************ -->
        <!-- Guest tab -->
        <div *ngIf="tabz?.content.action_type == 'guest'">
          <form [formGroup]="guestForm" (ngSubmit)="saveGuestDetails(tabz?.content)">
            <div formArrayName="guests">
              <div *ngFor="let item of guestForm.controls.guests.controls;let i = index" [formGroupName]="i">
                <div class="row">
                  <div class="col-sm-6">
                    <h4 class="capitalize text-primary">{{'RESERVATIONS.DETAIL_FOR' | translate:param}} -
                      {{item.value.name}}
                    </h4>
                    <strong>
                      <small>
                        <span>{{'RESERVATIONS.ROOM_NO' | translate:param}}: {{item.value.room_name}}</span>
                      </small>
                    </strong>
                  </div>
                </div>
                <div>
                  <!-- <div class="col-sm-6"> -->
                  <fieldset class="__fieldset">
                    <legend>{{'RESERVATIONS.PERS_INFO' | translate:param}} :</legend>
                    <div class="row">
                      <div class="col-sm-4">
                        <div class="input-group">
                          <span for="" class="input-group-addon">
                            {{'RESERVATIONS.NAME' | translate:param}}*
                          </span>
                          <input type="text" class="form-control" formControlName="name">
                        </div>
                        <div *ngIf="guestForm.controls.guests.controls[i].controls['name'].dirty &&
                                guestForm.controls.guests.controls[i].controls['name'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['name'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.NAME_REQ' | translate:param}}</div>
                          <div *ngIf="!guestForm.controls.guests.controls[i].controls['name'].errors?.required &&
                                  guestForm.controls.guests.controls[i].controls['name'].errors?.spaceCharCheck">
                            {{'RESERVATIONS.VALID_MSG.NAME_LEAVE' | translate:param}}</div>
                          <div *ngIf="!guestForm.controls.guests.controls[i].controls['name'].errors?.required &&
                                  !guestForm.controls.guests.controls[i].controls['name'].errors?.spaceCharCheck &&
                                  guestForm.controls.guests.controls[i].controls['name'].errors?.defaultNameCheck">
                            {{'RESERVATIONS.VALID_MSG.INV_NAME' | translate:param}}
                          </div>
                        </div>
                      </div>

                      <div class="col-sm-4">
                        <div class="input-group">

                          <int-phone-prefix type="text" class="form-control" formControlName="contact">
                          </int-phone-prefix>
                        </div>
                        <div *ngIf="guestForm.controls.guests.controls[i].controls['contact'].dirty &&
                                guestForm.controls.guests.controls[i].controls['contact'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['contact'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.CON_REQ' | translate:param}}</div>
                        </div>
                      </div>
                      <div class="col-sm-4">
                        <div class="input-group">

                          <span class="input-group-addon">
                            {{'RESERVATIONS.EMAIL' | translate:param}}
                          </span>
                          <input type="text" class="form-control" formControlName="email">
                        </div>
                        <div
                          *ngIf="guestForm.controls.guests.controls[i].controls['email'].dirty && guestForm.controls.guests.controls[i].controls['email'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['email'].errors?.email ">
                            {{'RESERVATIONS.VALID_MSG.VALID_EMAIL' | translate:param}}</div>
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['email'].errors?.required ">
                            {{'RESERVATIONS.VALID_MSG.EMAIL_REQ' | translate:param}}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="row" style="margin-top: 0.8rem;">
                      <div class="col-sm-4" style="margin-top:0.5rem;">
                        <div class="input-group">
                          <label for="" class="control-label">
                            {{'RESERVATIONS.TYPE' | translate:param}}
                          </label>
                          <div class="radio-horizontal">
                            <div class="abc-radio">
                              <input type="radio" formControlName="guest_maturity_type" id="guest_maturity_type-{{i}}-1"
                                value="adult" (change)="adultChanged(i)">
                              <label for="guest_maturity_type-{{i}}-1">
                                {{'RESERVATIONS.ADULT' | translate:param}}
                              </label>
                            </div>
                            <div class="abc-radio">
                              <input type="radio" formControlName="guest_maturity_type" id="guest_maturity_type-{{i}}-2"
                                value="child" (change)="childChanged(i)">
                              <label for="guest_maturity_type-{{i}}-2">
                                {{'RESERVATIONS.CHILD' | translate:param}}
                              </label>
                            </div>
                          </div>
                        </div>
                        <div
                          *ngIf="guestForm.controls.guests.controls[i].controls['guest_maturity_type'].dirty && guestForm.controls.guests.controls[i].controls['guest_maturity_type'].invalid"
                          class="alert alert-danger">
                          <div
                            *ngIf="guestForm.controls.guests.controls[i].controls['guest_maturity_type'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.GUEST_TYPE_REQ' | translate:param}}</div>
                        </div>
                      </div>
                      <div class="col-sm-2" style="padding-right:0px;">
                        <div class="input-group">
                          <datetime [formControl]="guestForm.controls.guests.controls[i].controls['dob']"
                            (ngModelChange)="updateAge(i)" [timepicker]="false" [datepicker]="datepickerOpts">
                          </datetime>
                        </div>
                        <div
                          *ngIf="guestForm.controls.guests.controls[i].controls['dob'].dirty && guestForm.controls.guests.controls[i].controls['dob'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['dob'].errors?.required ">
                            {{'RESERVATIONS.VALID_MSG.DOB_REQ' | translate:param}}</div>
                        </div>
                      </div>
                      <div class="col-sm-2">
                        <div class="input-group">
                          <input type="text" class="form-control" formControlName="age" (keyup)="updateDob(i)"
                            placeholder="{{'RESERVATIONS.AGE' | translate:param}}">
                          <span class="input-group-addon">yrs</span>
                        </div>
                        <div
                          *ngIf="guestForm.controls.guests.controls[i].controls['age'].dirty && guestForm.controls.guests.controls[i].controls['age'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['age'].errors?.digits ">
                            {{'RESERVATIONS.VALID_MSG.ONLY_DIGIT_REQ' | translate:param}}</div>
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['age'].errors?.required ">
                            {{'RESERVATIONS.VALID_MSG.AGE_REQ' | translate:param}}</div>
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['age'].errors?.lte ||
                              guestForm.controls.guests.controls[i].controls['age'].errors?.gt">
                            {{'RESERVATIONS.VALID_MSG.INV_AGE' | translate:param}}
                          </div>
                        </div>
                      </div>

                      <div class="col-sm-4" style="margin-top:0.5rem;">
                        <div class="input-group">
                          <label for="" class="control-label">
                            {{'RESERVATIONS.GENDER' | translate:param}}
                          </label>
                          <div class="radio-horizontal">
                            <div class="abc-radio">
                              <input type="radio" formControlName="gender" id="Gender-{{i}}-1" value="male">
                              <label for="Gender-{{i}}-1">
                                {{'RESERVATIONS.MALE' | translate:param}}
                              </label>
                            </div>
                            <div class="abc-radio">
                              <input type="radio" formControlName="gender" id="Gender-{{i}}-2" value="female">
                              <label for="Gender-{{i}}-2">
                                {{'RESERVATIONS.FEMALE' | translate:param}}
                              </label>
                            </div>
                          </div>
                        </div>
                        <div
                          *ngIf="guestForm.controls.guests.controls[i].controls['gender'].dirty && guestForm.controls.guests.controls[i].controls['gender'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['gender'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.GEN_REQ' | translate:param}}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row" style="margin-top: 0.8rem;">
                      <div class="col-sm-4">
                        <div class="btn btn-primary" style="background: #000000;border-color: #000000;"
                          (click)="showMoreOn(i)">
                          Show {{!item.value.show ? 'More' : 'Less'}}
                          <i *ngIf="!item.value.show" class="fa fa-angle-down"></i>
                          <i *ngIf="item.value.show" class="fa fa-angle-up"></i>
                        </div>
                        <div *ngIf="item.value.can_delete" class="btn btn-primary"
                          (click)="deleteGuest(tabz?.content.booking_id, item, i)"
                          style="background-color: #d3d3d3; border-color: #d3d3d3;color: #000;">
                          Delete
                        </div>
                      </div>
                    </div>
                  </fieldset>
                  <!-- <br> -->

                  <fieldset class="__fieldset" *ngIf="item.value.show">
                    <legend>{{'RESERVATIONS.RES_INFO' | translate:param}} :</legend>
                    <div class="row">
                      <div class="col-sm-6">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.ADDRESS' | translate:param}}
                          </label>
                          <textarea type="text" class="form-control" rows="3" formControlName="address"></textarea>
                        </div>
                        <div
                          *ngIf="guestForm.controls.guests.controls[i].controls['address'].dirty && guestForm.controls.guests.controls[i].controls['address'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['address'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.ADDRE_REQ' | translate:param}}</div>
                        </div>
                      </div>

                      <div class="col-sm-6">
                        <div class="row">
                          <div class="col-sm-6" style="margin-bottom:0.7rem;">
                            <div class="input-group">
                              <label for="" class="input-group-addon">
                                {{'RESERVATIONS.CITY' | translate:param}}
                              </label>
                              <input type="text" class="form-control" formControlName="city">
                            </div>
                            <div
                              *ngIf="guestForm.controls.guests.controls[i].controls['city'].dirty && guestForm.controls.guests.controls[i].controls['city'].invalid"
                              class="alert alert-danger" style="margin-bottom: 0px;">
                              <div *ngIf="guestForm.controls.guests.controls[i].controls['city'].errors?.required">
                                {{'RESERVATIONS.VALID_MSG.CITY_REQ' | translate:param}}
                              </div>
                            </div>
                          </div>

                          <div class="col-sm-6" style="margin-bottom:0.7rem;">
                            <div class="input-group">
                              <label for="" class="input-group-addon">
                                {{ 'RESERVATIONS.COUNTRY' | translate:param }}
                              </label>
                              <ng-select
                                style="width: -webkit-fill-available;"
                                [items]="allGuestObject.countries"
                                [bindLabel]="'text'"
                                [bindValue]="'id'"
                                [clearable]="false"
                                [searchable]="true"
                                [dropdownPosition]="'auto'"
                                (change)="guestCountryChanged($event, i)"
                                [ngModel]="guestForm.value.guests[i].country ? guestForm.value.guests[i].country : 103">
                              </ng-select>
                            </div>                            
                            <div
                              *ngIf="guestForm.controls.guests.controls[i].controls['country'].dirty && guestForm.controls.guests.controls[i].controls['country'].invalid"
                              class="alert alert-danger" style="margin-bottom: 0px;">
                              <div *ngIf="guestForm.controls.guests.controls[i].controls['country'].errors?.required">
                                {{'RESERVATIONS.VALID_MSG.COUNTRY_REQ' | translate:param}}</div>
                            </div>
                          </div>
                        </div>

                        <div class="row">
                          <div class="col-sm-6">
                            <div class="input-group">
                              <label for="" class="input-group-addon">
                                Zip
                              </label>
                              <input type="text" class="form-control" maxlength="6" formControlName="zip">
                            </div>
                            <div
                              *ngIf="guestForm.controls.guests.controls[i].controls['zip'].dirty && guestForm.controls.guests.controls[i].controls['zip'].invalid"
                              class="alert alert-danger" style="margin-bottom: 0px;">
                              <div *ngIf="guestForm.controls.guests.controls[i].controls['zip'].errors?.required">
                                {{'RESERVATIONS.VALID_MSG.ZIP_REQ' | translate:param}}</div>
                              <div *ngIf="guestForm.controls.guests.controls[i].controls['zip'].errors?.digits">
                                {{'RESERVATIONS.VALID_MSG.ONLY_DIGIT_REQ' | translate:param}}</div>
                            </div>
                          </div>
                          <div class="col-sm-6">
                            <button *ngIf="item.value.is_billingUser && (guestForm.controls.guests.controls.length > 1)"
                              type="button" class="btn btn-primary pull-right"
                              style="background: #000000;border-color: #000000;"
                              (click)="copyToAllGuestAtCheckIn(item.value)">
                              {{'RESERVATIONS.VALID_MSG.SET_IN_ALL' | translate:param}}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </fieldset>
                  <fieldset class="__fieldset" *ngIf="item.value.show">
                    <legend>{{'RESERVATIONS.DOC' | translate:param}} :</legend>
                    <div class="row">
                      <div class="col-sm-4">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.DOC' | translate:param}}
                          </label>
                          Pan Card
                          <!-- <ng-select style="width:-webkit-fill-available;" [items]="proofTypeList"
                              (change)="guestProofTypeChanged($event,i)"
                              [value]="(guestForm.value.guests[i].proof_type) ? (guestForm.value.guests[i].proof_type) : '0' "
                              >
                            </ng-select> -->
                        </div>
                      </div>

                      <div class="col-sm-4">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.NUM' | translate:param}}
                          </label>
                          <input type="text" class="form-control" formControlName="pancard_number">
                        </div>

                        <div
                          *ngIf="guestForm.controls.guests.controls[i].controls['pancard_number'].dirty && guestForm.controls.guests.controls[i].controls['pancard_number'].invalid"
                          class="alert alert-danger">
                          <!-- <div
                            *ngIf="guestForm.controls.guests.controls[i].controls['pancard_number'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.NUM_REQ' | translate:param}}</div> -->
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['pancard_number'].errors?.validateInput?.pattern">
                            <!-- {{'RESERVATIONS.VALID_MSG.NUM_REQ' | translate:param}} -->
                            {{'RESERVATIONS.VALID_MSG.WRONG_NUM' | translate:param}}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-sm-4" style="margin-top: 0.7rem;">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.FILE_UPLOAD' | translate:param}}
                          </label>
                          <input type="file" name="PanCard" class="form-control" ng2FileSelect
                            [uploader]="PanCardFileuploaders[i]"/>
                        </div>
                        <!-- <div *ngIf="guestForm.controls.guests.controls[i].controls['PC_proof'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['PC_proof'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.PROOF_REQ' | translate:param}}</div>
                        </div> -->
                      </div>
                      <div class="col-sm-4" style="margin-top: 0.7rem;">
                        <div class="Docs" *ngIf="item.value.PC_proof && item.value.PC_proof.url">
                          <span class="delete_image" (click)="deleteUploadedImg(item, i)">
                            <i class="fa fa-times"></i>
                          </span>
                          <span class="view_image" tooltip="{{'RESERVATIONS.CLICK_VIEW' | translate:param}}"
                            placement="bottom" (click)="viewUploadedImg(item.value.PC_proof)">
                            <i class="fa fa-image fa-2x"></i>
                            {{item.value.PC_proof.displayname ? item.value.PC_proof.displayname : item.value.PC_proof.oldName}}
                          </span>
                        </div>
                      </div>
                      <div class="files" *ngIf="PanCardFileuploaders[i].queue.length" class="well well-sm col-sm-4"
                        style="margin-top: 0.7rem;">
                        <table class="table">
                          <thead>
                            <tr>
                              <th width="50%">{{'RESERVATIONS.NAME' | translate:param}}</th>
                              <th>{{'RESERVATIONS.FILE_SIZE' | translate:param}}</th>
                              <th width="230px">Progress</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let item of PanCardFileuploaders[i].queue">
                              <td>
                                <strong>{{ item?.file?.name }}</strong>
                              </td>
                              <td *ngIf="PanCardFileuploaders[i].isHTML5" nowrap>{{ item?.file?.size/1024/1024 |
                                number:'.2'
                                }}
                                MB
                              </td>
                              <td *ngIf="PanCardFileuploaders[i].isHTML5">
                                <div class="progress" style="margin-bottom: 0;">
                                  <div class="progress-bar" role="progressbar"
                                    [ngStyle]="{ 'width': item.progress + '%' }"></div>
                                </div>
                              </td>
                              <td class="text-center">
                                <span *ngIf="item.isSuccess">
                                  <i class="glyphicon glyphicon-ok"></i>
                                </span>
                                <span *ngIf="item.isCancel">
                                  <i class="glyphicon glyphicon-ban-circle"></i>
                                </span>
                                <span *ngIf="item.isError">
                                  <i class="glyphicon glyphicon-remove"></i>
                                </span>
                              </td>
                              <td nowrap>
                                <button tooltip="{{'RESERVATIONS.UPLOAD' | translate:param}}" placement="left"
                                  type="button" class="btn btn-success btn-xs" (click)="item.upload()"
                                  [disabled]="item.isReady || item.isUploading || item.isSuccess">
                                  <span class="glyphicon glyphicon-cloud-upload"></span>
                                </button>
                                <button tooltip="{{'RESERVATIONS.REMOVE' | translate:param}}" placement="right"
                                  type="button" class="btn btn-danger btn-xs" (click)="item.remove()">
                                  <span class="glyphicon glyphicon-trash"></span>
                                </button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                    <!-- second -->
                    <div class="row">
                      <div class="col-sm-4">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.DOC' | translate:param}}
                          </label>
                          Aadhaar Card
                          <!-- <ng-select style="width:-webkit-fill-available;" [items]="proofTypeList"
                              (change)="guestProofTypeChanged($event,i)"
                              [value]="(guestForm.value.guests[i].proof_type) ? (guestForm.value.guests[i].proof_type) : '0' "
                              >
                            </ng-select> -->
                        </div>
                      </div>
                      <div class="col-sm-4">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.NUM' | translate:param}}
                          </label>
                          <input type="text" class="form-control" formControlName="aadharcard_number">
                        </div>
                        <div
                          *ngIf="guestForm.controls.guests.controls[i].controls['aadharcard_number'].dirty && guestForm.controls.guests.controls[i].controls['aadharcard_number'].invalid"
                          class="alert alert-danger">
                          <!-- <div
                            *ngIf="guestForm.controls.guests.controls[i].controls['aadharcard_number'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.NUM_REQ' | translate:param}}</div> -->
                          <div
                            *ngIf="guestForm.controls.guests.controls[i].controls['aadharcard_number'].errors?.pattern">
                            <!-- {{'RESERVATIONS.VALID_MSG.NUM_REQ' | translate:param}} -->
                            {{'RESERVATIONS.VALID_MSG.WRONG_NUM' | translate:param}}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-sm-4" style="margin-top: 0.7rem;">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.FILE_UPLOAD' | translate:param}}
                          </label>
                          <input type="file" name="AadharCardF" class="form-control" ng2FileSelect
                            [uploader]="AadharCardFileuploaders[i]" />
                        </div>
                        <!-- <div *ngIf="guestForm.controls.guests.controls[i].controls['AC_proof'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['AC_proof'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.PROOF_REQ' | translate:param}}</div>
                        </div> -->
                      </div>
                      <div class="col-sm-4" style="margin-top: 0.7rem;">
                        <div class="Docs" *ngIf="item.value.AC_proof && item.value.AC_proof.url">
                          <span class="delete_image" (click)="deleteUploadedImg(item, i)">
                            <i class="fa fa-times"></i>
                          </span>
                          <span class="view_image" tooltip="{{'RESERVATIONS.CLICK_VIEW' | translate:param}}"
                            placement="bottom" (click)="viewUploadedImg(item.value.AC_proof)">
                            <i class="fa fa-image fa-2x"></i>
                            {{item.value.AC_proof.displayname ? item.value.AC_proof.displayname : item.value.AC_proof.oldName}}
                          </span>
                        </div>
                      </div>
                      <div class="files" *ngIf="AadharCardFileuploaders[i].queue.length" class="well well-sm col-sm-4"
                        style="margin-top: 0.7rem;">
                        <table class="table">
                          <thead>
                            <tr>
                              <th width="50%">{{'RESERVATIONS.NAME' | translate:param}}</th>
                              <th>{{'RESERVATIONS.FILE_SIZE' | translate:param}}</th>
                              <th width="230px">Progress</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let item of AadharCardFileuploaders[i].queue">
                              <td>
                                <strong>{{ item?.file?.name }}</strong>
                              </td>
                              <td *ngIf="AadharCardFileuploaders[i].isHTML5" nowrap>{{ item?.file?.size/1024/1024 |
                                number:'.2'
                                }}
                                MB
                              </td>
                              <td *ngIf="AadharCardFileuploaders[i].isHTML5">
                                <div class="progress" style="margin-bottom: 0;">
                                  <div class="progress-bar" role="progressbar"
                                    [ngStyle]="{ 'width': item.progress + '%' }"></div>
                                </div>
                              </td>
                              <td class="text-center">
                                <span *ngIf="item.isSuccess">
                                  <i class="glyphicon glyphicon-ok"></i>
                                </span>
                                <span *ngIf="item.isCancel">
                                  <i class="glyphicon glyphicon-ban-circle"></i>
                                </span>
                                <span *ngIf="item.isError">
                                  <i class="glyphicon glyphicon-remove"></i>
                                </span>
                              </td>
                              <td nowrap>
                                <button tooltip="{{'RESERVATIONS.UPLOAD' | translate:param}}" placement="left"
                                  type="button" class="btn btn-success btn-xs" (click)="item.upload()"
                                  [disabled]="item.isReady || item.isUploading || item.isSuccess">
                                  <span class="glyphicon glyphicon-cloud-upload"></span>
                                </button>
                                <button tooltip="{{'RESERVATIONS.REMOVE' | translate:param}}" placement="right"
                                  type="button" class="btn btn-danger btn-xs" (click)="item.remove()">
                                  <span class="glyphicon glyphicon-trash"></span>
                                </button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                    <!-- third -->
                    <div class="row">
                      <div class="col-sm-4">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{ 'RESERVATIONS.DOC' | translate:param }}
                          </label>
                          <ng-select
                            style="width: -webkit-fill-available;"
                            [items]="guestProofTypeList"
                            [bindLabel]="'text'"
                            [bindValue]="'id'"
                            [clearable]="false"
                            [searchable]="true"
                            [dropdownPosition]="'auto'"
                            (change)="guestProofTypeChanged($event, i)"
                            [ngModel]="guestForm.value.guests[i].proof_type ? guestForm.value.guests[i].proof_type : '0'">
                          </ng-select>
                        </div>                        
                      </div>
                      <div class="col-sm-4" *ngIf="guestForm.controls.guests.controls[i].controls?.other_proof_type">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.NAME' | translate:param}}
                          </label>
                          <input type="text" class="form-control" formControlName="other_proof_type">
                        </div>
                        <div
                          *ngIf="guestForm.controls.guests.controls[i].controls['other_proof_type'].dirty && guestForm.controls.guests.controls[i].controls['other_proof_type'].invalid"
                          class="alert alert-danger">
                          <div
                            *ngIf="guestForm.controls.guests.controls[i].controls['other_proof_type'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.ID_NAME' | translate:param}}</div>
                        </div>
                      </div>
                      <div class="col-sm-4">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.NUM' | translate:param}}
                          </label>
                          <input type="text" class="form-control" formControlName="proof_value">
                        </div>

                      </div>
                    </div>
                    <div class="row">
                      <div class="col-sm-4" style="margin-top: 0.7rem;">
                        <div class="input-group">
                          <label for="" class="input-group-addon">
                            {{'RESERVATIONS.FILE_UPLOAD' | translate:param}}
                          </label>
                          <input type="file" name="avatar" class="form-control" ng2FileSelect
                            [uploader]="Fileuploaders[i]" />
                        </div>
                        <div *ngIf="guestForm.controls.guests.controls[i].controls['proof'].invalid"
                          class="alert alert-danger">
                          <div *ngIf="guestForm.controls.guests.controls[i].controls['proof'].errors?.required">
                            {{'RESERVATIONS.VALID_MSG.PROOF_REQ' | translate:param}}</div>
                        </div>
                      </div>
                      <div class="col-sm-4" style="margin-top: 0.7rem;">
                        <div class="Docs" *ngIf="item.value.proof && item.value.proof.url">
                          <span class="delete_image" (click)="deleteUploadedImg(item, i)">
                            <i class="fa fa-times"></i>
                          </span>
                          <span class="view_image" tooltip="{{'RESERVATIONS.CLICK_VIEW' | translate:param}}"
                            placement="bottom" (click)="viewUploadedImg(item.value.proof)">
                            <i class="fa fa-image fa-2x"></i>
                            {{item.value.proof.displayname ? item.value.proof.displayname : item.value.proof.oldName}}
                          </span>
                        </div>
                      </div>
                      <div class="files" *ngIf="Fileuploaders[i].queue.length" class="well well-sm col-sm-4"
                        style="margin-top: 0.7rem;">
                        <table class="table">
                          <thead>
                            <tr>
                              <th width="50%">{{'RESERVATIONS.NAME' | translate:param}}</th>
                              <th>{{'RESERVATIONS.FILE_SIZE' | translate:param}}</th>
                              <th width="230px">Progress</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let item of Fileuploaders[i].queue">
                              <td>
                                <strong>{{ item?.file?.name }}</strong>
                              </td>
                              <td *ngIf="Fileuploaders[i].isHTML5" nowrap>{{ item?.file?.size/1024/1024 | number:'.2'
                                }}
                                MB
                              </td>
                              <td *ngIf="Fileuploaders[i].isHTML5">
                                <div class="progress" style="margin-bottom: 0;">
                                  <div class="progress-bar" role="progressbar"
                                    [ngStyle]="{ 'width': item.progress + '%' }"></div>
                                </div>
                              </td>
                              <td class="text-center">
                                <span *ngIf="item.isSuccess">
                                  <i class="glyphicon glyphicon-ok"></i>
                                </span>
                                <span *ngIf="item.isCancel">
                                  <i class="glyphicon glyphicon-ban-circle"></i>
                                </span>
                                <span *ngIf="item.isError">
                                  <i class="glyphicon glyphicon-remove"></i>
                                </span>
                              </td>
                              <td nowrap>
                                <button tooltip="{{'RESERVATIONS.UPLOAD' | translate:param}}" placement="left"
                                  type="button" class="btn btn-success btn-xs" (click)="item.upload()"
                                  [disabled]="item.isReady || item.isUploading || item.isSuccess">
                                  <span class="glyphicon glyphicon-cloud-upload"></span>
                                </button>
                                <button tooltip="{{'RESERVATIONS.REMOVE' | translate:param}}" placement="right"
                                  type="button" class="btn btn-danger btn-xs" (click)="item.remove()">
                                  <span class="glyphicon glyphicon-trash"></span>
                                </button>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </fieldset>
                </div>
                <hr *ngIf="(i + 1) < guestForm.controls.guests.controls.length" style="background:#9E9E9E;">
              </div>
            </div>
            <!-- other guest details ends ================== -->

            <!-- Check in button popup process - start -->
            <!-- [modal]="'cardComponent'" -->

            <div class="inline-flex">

              <!-- <button class="btn btn-warning mr-2" style="margin: auto;margin-top: 15px;display: table; width: 180px;"
                (click)="checkInDynamicModalAction(tabz?.content)" type="button">Generate Card</button> -->
              <!-- Check in button popup process - end -->

              <button class="btn btn-success mr-2" style="margin: auto;margin-top: 15px;display: table; width: 180px;"
                (click)="allowCardValidFlag ? checkInDynamicModalAction(tabz?.content) : doNothing()"
                [disabled]="guestForm.value.abletocheckin" id="reservationsSaveDetails" type="submit">Save
                Details{{tabz?.content.type == 'checkIn' ?
                ' & Check In' : ''}}</button>

              <!-- <button class="btn btn-info mr-2" style="margin: auto;margin-top: 15px;display: table; width: 180px;"
                (click)="verifyCard($event)" type="button">Verify Card</button> -->
            </div>
            <!-- <button type="button" *ngIf="tabz?.content.type == 'checkIn'" [disabled]="!guestForm.value.abletocheckin" class="btn btn-sm btn-success" (click)="checkInAfterGuestSaved(tabz?.content)">Check In</button> -->
          </form>


        </div>
        <!-- ************************************************************************************ -->

        <!-- Split booking tab -->
        <div *ngIf="tabz?.content.action_type == 'split'">
          <form [formGroup]="splitBookingForm">
            <!-- (ngSubmit)="makeSplitBookingUsingCard(tabz?.content.booking_id,tabz?.content.room_id)"> -->
            <div formArrayName="dates">
              <div class="row" *ngFor="let booking of splitBookingForm.controls.dates.controls; let i = index"
                [formGroupName]="i">
                <div class="col-sm-4">
                  <bs-datepicker-inline #splitDatePickerStart class="full-table"
                    [formControl]="splitBookingForm.controls.dates.controls[i].controls['start']"
                     [datesDisabled]="dateDisabled" [bsValue]="extendStartDate"
                     [bsConfig]="{containerClass:'theme-dark-blue'}"
                    (bsValueChange)="checkSameDate($event, i, booking, 'start')">
                  </bs-datepicker-inline>
                  <label for="" class="form-control bg-checkin">
                    {{'RESERVATIONS.CHECK_IN_DATE' | translate:param}} :&nbsp;
                    <strong>{{booking.value.start | date}}</strong>
                  </label>
                  <div class="form-group">
                    <label for="" class="form-control" *ngIf="booking.value.room_id">
                      {{'RESERVATIONS.ROOM' | translate:param}} : {{booking.value.room_title}}
                    </label>
                  </div>
                </div>
                <div class="col-sm-4">
                  <bs-datepicker-inline #splitDatePickerEnd class="full-table"
                    [formControl]="splitBookingForm.controls.dates.controls[i].controls['end']" [minDate]="minDate"
                     [datesDisabled]="dateDisabled" [bsValue]="extendEndDate"
                     [bsConfig]="{containerClass:'theme-orange'}"
                    (bsValueChange)="checkSameDate($event, i, booking, 'end')">
                  </bs-datepicker-inline>
                  <label for="" class="form-control bg-checkout">
                    {{'RESERVATIONS.CHECK_OUT_DATE' | translate:param}} : {{booking.value.room_title}} : &nbsp;
                    <strong>{{formatCheckoutDate(booking.value.end) | date}}</strong>
                  </label>
                  <div class="form-group">
                    <label for="" class="form-control" *ngIf="booking.value.room_cat_name">
                      {{'RESERVATIONS.ROOM_CAT' | translate:param}} : {{booking.value.room_cat_name}}
                    </label>
                  </div>
                </div>
                <div class="col-sm-4">
                  <input type="hidden" class="form-control" formControlName="guest_id">
                  <div class="form-group">
                    <label for="" style="margin-bottom: 0px">
                      <strong>{{'RESERVATIONS.GUEST_NAME' | translate:param}}</strong>
                    </label>
                    <input type="text" class="form-control" formControlName="guest_name">
                  </div>
                  <div class="form-group">
                    <label for="" style="margin-bottom: 0px">
                      <strong>{{'RESERVATIONS.GUEST_CONT' | translate:param}}</strong>
                    </label>
                    <int-phone-prefix type="text" formControlName="guest_contact" [defaultCountry]="'in'"
                      (keyup)="getCustomerList(i,'split')"></int-phone-prefix>
                    <div *ngIf="showCustomerList && selectedCustormerFormIndex == i" class="customer-list-dropdown">
                      <div class="col" *ngFor="let customer of customerList; let p = index;"
                        (click)="selectCustomer(customer,i,p,'split')">
                        {{customer.name}}
                      </div>
                    </div>

                  </div>
                  <div class="form-group">
                    <label for="" style="margin-bottom: 0px">
                      <strong>{{'RESERVATIONS.GUE_EMAIL' | translate:param}}</strong>
                    </label>
                    <input type="text" class="form-control" formControlName="guest_email">
                  </div>
                  <!-- <pre>{{booking.value.availibility | json}}</pre> -->
                  <div class="form-group" *ngIf="!booking.value.availibility">
                    <!-- <button type="button" [disabled]="booking.value.start == '' || booking.value.end == ''"
                        class="btn btn-xs btn-inverse" (click)="searchAvailibility(booking, i)">
                        <i class="fa fa-search"></i>&nbsp;{{'RESERVATIONS.SEARCH_AVAIL' | translate:param}}</button> -->
                  </div>

                  <!-- Get checkout Date  -->

                  <div class="row">
                    <div class="timepicker">
                      <div class="form-group row" style="align-items: center;margin-bottom: 0px;margin-top: 0.5rem;">
                        <div class="col-sm-4">
                          <label for="normal-field" class="">{{'RESERVATIONS.CHECK_OUT_TIME' | translate:param}}</label>
                        </div>
                        <!-- need to check formControlName name -->
                        <div class="col-sm-8">
                          <datetime formControlName="CheckOutTime" [timepicker]="{ icon: 'fa fa-clock-o' }"
                            [datepicker]=false>
                            >
                          </datetime>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- ENd checkout DAte code -->
                  <!-- <div class="timepicker">
                      <div class="form-group row" style="align-items: center;margin-bottom: 0px;margin-top: 0.5rem;">
                        <div class="col-sm-4">
                          <label for="normal-field" class="">{{'RESERVATIONS.CHECK_IN' | translate:param}}</label>
                        </div>
                        <div class="col-sm-8">
                          <datetime
                          formControlName="start"
                          [timepicker]="true" [datepicker]="datepickerOpts"
                            >
                          </datetime>
                        </div>

                      </div>
                    </div>                 -->



                  <div class="form-group splitBookingRoomSelect_div"
                    *ngIf="selectFormIndex == i && splitAvailableRoom.length && !booking.value.availibility">
                    <table class="table">
                      <tr *ngFor="let room of splitAvailableRoom">
                        <td>
                          <span class="splitBookingRoomSelect" (click)="selectAvailableRoom(room,booking,tabz)">
                            <i class="fa fa-check"></i>&nbsp;{{room.room_title}}&nbsp;
                            <i class="fa fa-long-arrow-right"></i>&nbsp;{{room.roomCategory_name}}</span>
                        </td>
                      </tr>
                    </table>
                  </div>
                  <hr>
                  <div class="btn-group btn-justify">
                    <button type="button" class="btn btn-sm btn-default"
                      *ngIf="!booking.valid && !booking.value.availibility"
                      (click)="patchMain(booking)">{{'RESERVATIONS.SET_AS_MAIN' | translate:param}}</button>
                    <button type="button" *ngIf="!booking.value.mainGuest " (click)="deleteDates(i)"
                      class="btn btn-danger btn-sm">{{'RESERVATIONS.FILE_REMOVE' | translate:param}}</button>
                    <button type="button" (click)="setSplitDates()"
                      *ngIf="i == (splitBookingForm.controls.dates.controls.length - 1)"
                      class="btn btn-success btn-sm">{{'RESERVATIONS.ADD_NEW' | translate:param}}
                    </button>
                  </div>
                </div>
              </div>
              <hr>
              <div style="text-align:right">
                <button type="button" [disabled]="!splitBookingForm.valid"
                  (click)="makeSplitBooking(tabz?.content.booking_id,tabz?.content.room_id,tabz)"
                  class="btn btn-warning btn-sm">{{'RESERVATIONS.EXTEND_CHECKOUT' | translate:param}}</button>
              </div>
            </div>
          </form>
        </div>

        <div *ngIf="tabz?.content.action_type == 'TransferRoom'">
          <form [formGroup]="splitBookingForm">
            <!-- (ngSubmit)="makeSplitBookingUsingCard(tabz?.content.booking_id,tabz?.content.room_id)"> -->
            <div formArrayName="dates">
              <div class="row" *ngFor="let booking of splitBookingForm.controls.dates.controls; let i = index"
                [formGroupName]="i">
                <div class="col-sm-4">

                  <div class="form-group">
                    <!-- get room id  -->
                    <label for="" class="form-control" *ngIf="booking.value.room_id">
                      {{'RESERVATIONS.ROOM' | translate:param}} : {{booking.value.room_title}}
                    </label>
                  </div>
                </div>
                <div class="col-sm-4">
                  <!-- <bs-datepicker-inline #splitDatePickerEnd class="full-table"
                      [formControl]="splitBookingForm.controls.dates.controls[i].controls['end']" [minDate]="minDate"
                       [datesDisabled]="dateDisabled"
                      (bsValueChange)="checkSameDate($event, i, booking, 'end')">
                    </bs-datepicker-inline> -->
                  <!-- <label for="" class="form-control bg-checkout">
                      {{'RESERVATIONS.CHECK_OUT_DATE' | translate:param}} : {{booking.value.room_title}} : &nbsp;
                      <strong>{{formatCheckoutDate(booking.value.end) | date}}</strong>
                    </label> -->
                  <div class="form-group">
                    <label for="" class="form-control" *ngIf="booking.value.room_cat_name">
                      {{'RESERVATIONS.ROOM_CAT' | translate:param}} : {{booking.value.room_cat_name}}
                    </label>
                  </div>
                </div>
                <div class="col-sm-4">
                  <input type="hidden" class="form-control" formControlName="guest_id">
                  <div class="form-group">
                    <label for="" style="margin-bottom: 0px">
                      <strong>{{'RESERVATIONS.GUEST_NAME' | translate:param}}</strong>
                    </label>
                    <input type="text" class="form-control" formControlName="guest_name">
                  </div>
                  <div class="form-group">
                    <label for="" style="margin-bottom: 0px">
                      <strong>{{'RESERVATIONS.GUEST_CONT' | translate:param}}</strong>
                    </label>
                    <int-phone-prefix type="text" formControlName="guest_contact" [defaultCountry]="'in'"
                      (keyup)="getCustomerList(i,'split')"></int-phone-prefix>
                    <div *ngIf="showCustomerList && selectedCustormerFormIndex == i" class="customer-list-dropdown">
                      <div class="col" *ngFor="let customer of customerList; let p = index;"
                        (click)="selectCustomer(customer,i,p,'split')">
                        {{customer.name}}
                      </div>
                    </div>

                  </div>
                  <div class="form-group">
                    <label for="" style="margin-bottom: 0px">
                      <strong>{{'RESERVATIONS.GUE_EMAIL' | translate:param}}</strong>
                    </label>
                    <input type="text" class="form-control" formControlName="guest_email">
                  </div>
                  <!-- <pre>{{booking.value.availibility | json}}</pre> -->
                  <div class="form-group" *ngIf="!booking.value.availibility">
                    <button type="button" [disabled]="booking.value.start == '' || booking.value.end == ''"
                      class="btn btn-xs btn-inverse" (click)="searchAvailibility(booking, i)">
                      <i class="fa fa-search"></i>&nbsp;{{'RESERVATIONS.SEARCH_AVAIL' | translate:param}}</button>
                  </div>

                  <!-- Get checkout Date  -->

                  <div class="row">
                    <div class="timepicker">
                      <div class="form-group row" style="align-items: center;margin-bottom: 0px;margin-top: 0.5rem;">

                      </div>
                    </div>
                  </div>
                  <div class="form-group splitBookingRoomSelect_div"
                    *ngIf="selectFormIndex == i && splitAvailableRoom.length && !booking.value.availibility">
                    <table class="table">
                      <tr *ngFor="let room of splitAvailableRoom">
                        <td>
                          <span class="splitBookingRoomSelect" (click)="selectAvailableRoom(room,booking , tabz)">
                            <i class="fa fa-check"></i>&nbsp;{{room.room_title}}&nbsp;
                            <i class="fa fa-long-arrow-right"></i>&nbsp;{{room.roomCategory_name}}</span>
                        </td>
                      </tr>
                    </table>
                  </div>
                  <hr>
                  <div class="btn-group btn-justify">
                    <button type="button" class="btn btn-sm btn-default"
                      *ngIf="!booking.valid && !booking.value.availibility"
                      (click)="patchMain(booking)">{{'RESERVATIONS.SET_AS_MAIN' | translate:param}}</button>
                    <button type="button" *ngIf="!booking.value.mainGuest " (click)="deleteDates(i)"
                      class="btn btn-danger btn-sm">{{'RESERVATIONS.FILE_REMOVE' | translate:param}}</button>
                    <button type="button" (click)="setSplitDates()"
                      *ngIf="i == (splitBookingForm.controls.dates.controls.length - 1)"
                      class="btn btn-success btn-sm">{{'RESERVATIONS.ADD_NEW' | translate:param}}
                    </button>
                  </div>
                </div>
              </div>
              <hr>
              <div style="text-align:right">
                <button type="button" [disabled]="!splitBookingForm.valid"
                  (click)="maketransferRoom(tabz?.content.booking_id,tabz?.content.room_id,tabz)"
                  class="btn btn-warning btn-sm">{{'RESERVATIONS.MAKE_TRANSFER_ROOM' | translate:param}}</button>
              </div>
            </div>
          </form>
        </div>
      </tab>
    </tabset>

  </div>

</section>
<context-menu #OverBooking>
  <ng-template contextMenuItem (execute)="openOverBookingNote($event.item)" [enabled]="overBooking">
    {{'RESERVATIONS.OVER_BOOKING' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem>
    {{'RESERVATIONS.CLOSE' | translate:param}}
  </ng-template>
</context-menu>
<context-menu #NewBooking>
  <ng-template contextMenuItem [enabled]="checkDateSelectes" (execute)="openReservation($event.item)">
    {{'RESERVATIONS.RESERVE' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="roomMaintenanceModel($event.item)" [enabled]="checkDateSelectes">
    {{'RESERVATIONS.UNDER_MAIN' | translate:param}}
  </ng-template>
  <!-- <ng-template contextMenuItem [visible]="canBlockRooms" (execute)="blockRooms($event.item)">
      Block Room
    </ng-template> -->
  <ng-template contextMenuItem [enabled]="checkDateSelectes" (execute)="removeSelection($event.item)">
    {{'RESERVATIONS.CLEAR' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem divider="true"></ng-template>
  <ng-template contextMenuItem>
    {{'RESERVATIONS.CLOSE' | translate:param}}
  </ng-template>
</context-menu>
<context-menu #CurrentBooking class="wide-list" [menuClass]="'org-menu'">
  <ng-template contextMenuItem (execute)="openReservation($event.item[0], true)" [visible]="overBooking">
    {{'RESERVATIONS.OVER_NEW_BOOKING' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="openReservation($event.item[0], false, true)" [enabled]="enableExtraBooking"
    [visible]="enableExtraBooking">
    {{'RESERVATIONS.ADD_EXT_BOOK' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="preShowBookingDetails($event.item[0], $event.item[1], $event)" [visible]="isNormalRoom">
    {{'RESERVATIONS.VIEW' | translate:param}}
  </ng-template>
  <!-- <ng-template contextMenuItem [enabled]="checkedEdit">
      Edit
    </ng-template> -->
  <!-- (execute)="processCheckIn($event.item[0])" -->
  <ng-template contextMenuItem (execute)="preGuestCheckIn($event.item[0], 'checkIn')" [enabled]="checkedInEnabled"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.CHECKIN' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="preProcessCheckOut($event.item[0], 'checkout')" [enabled]="checkedOutEnabled"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.CHECKOUT' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="preProcessRoomCheckOut($event.item[0])" [enabled]="roomCheckedOutEnabled"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.ROOM_CHECK' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="preRequestBookingGuestTypeChange($event.item[0])"
    [enabled]="canChangeCustomerType" [visible]="isNormalRoom">
    {{'RESERVATIONS.CHA_GUE_TYPE' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="markRoomAsActive($event.item[0])" [visible]="canMarkRoomActive"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.MARK_ROOM_ACTI' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="printBill($event.item[0].booking_id)" [enabled]="enableBillPrint"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.PRINT_BILL' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="updatePayment($event.item[0])" [enabled]="paymentUpdateEnabled"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.UPDA_PAY' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="preAddEditBookingNote($event.item[0])" [visible]="isNormalRoom">
    {{'RESERVATIONS.ADD_VIEW_NOTE' | translate:param}}
  </ng-template>

  <ng-template contextMenuItem [enabled]="enableGuest" (execute)="preAddGuestInfo($event.item[0])"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.EDIT_GUES' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem [enabled]="enableAddGuest" (execute)="addGuest($event.item[0], $event.item[1])"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.ADD_GUES' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem [enabled]="enableSplitBooking" (execute)="doSplitBooking($event.item[0])"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.EXTEND_CHECKOUT' | translate:param}}
  </ng-template>

  <!-- //Add TRANSFER_ROOM -->
  <ng-template contextMenuItem [enabled]="enableTransferBooking" (execute)="TransferRoom($event.item[0]  , $event)"
    [visible]="isNormalRoom">
    {{'RESERVATIONS.TRANSFER_ROOM' | translate:param}}
  </ng-template>
  <div *ngIf="tempBookingObjArray">
    <ng-template contextMenuItem (execute)="roomMaintenanceModel($event.item)" [enabled]="enableRoomCleaning">
      {{'RESERVATIONS.UNDER_MAIN' | translate:param}}
    </ng-template>
  </div>

  <ng-template contextMenuItem (execute)="processNoShowGuest($event.item[0])" [enabled]="enableNoShow"
    [visible]="isNormalRoom">

  </ng-template>
  <ng-template contextMenuItem (execute)="preProcessCancel($event.item[0])" [enabled]="enableCancel"
    [visible]="displayCancel">
    {{'RESERVATIONS.CANCEL' | translate:param}}
  </ng-template>
  <!-- <ng-template contextMenuItem divider="true"></ng-template> -->
  <ng-template contextMenuItem [visible]="commonRoomBooking" (execute)="openReservation($event.item[0])">
    {{'RESERVATIONS.ADD_NEW_BOOKS' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem [visible]="showViewCommonRoomBookings" (execute)="openCommonRoomBookings($event.item[0])">
    {{'RESERVATIONS.VIEW_BOOKI' | translate:param}}
  </ng-template>
  <!-- <ng-template contextMenuItem [visible]="shouldBeVisible" (execute)="addEditBookingNote($event.item[0])">
      Add/Edit Note
    </ng-template>   -->
  <ng-template contextMenuItem [visible]="shouldBeVisible" (execute)="removeSelection($event.item[0])">
    {{'RESERVATIONS.CLEAR' | translate:param}}
  </ng-template>
  <!-- <ng-template contextMenuItem divider="true" [visible]="shouldBeVisible"></ng-template> -->
  <ng-template contextMenuItem>
    {{'RESERVATIONS.CLOSE' | translate:param}}
  </ng-template>
  <!-- <ng-template contextMenuItem>
      Print Bill
    </ng-template> -->
</context-menu>

<context-menu #CommonRoomBooking>
  <ng-template contextMenuItem (execute)="showBookingDetails($event.item)">
    {{'RESERVATIONS.VIEW' | translate:param}}
  </ng-template>
  <!-- "addGuestInfo($event.item, 'checkIn')" -->
  <ng-template contextMenuItem (execute)="addGuestInfo($event.item, 'checkIn')" [enabled]="checkedInEnabledCommonRoom">
    {{'RESERVATIONS.CHECKIN' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="processCheckOut($event.item, 'checkout')" [enabled]="commonRoomCheckOutEnabled">
    {{'RESERVATIONS.CHECKOUT' | translate:param}}
  </ng-template>
  <ng-template contextMenuItem (execute)="printBill($event.item.booking_id)" [enabled]="enableBillPrintCommonRoom">
    {{'RESERVATIONS.PRINT_BILL' | translate:param}}
  </ng-template>
  <!-- <ng-template contextMenuItem (execute)="updatePayment($event.item)" [enabled]="paymentUpdateEnabled">
      Update Payment
    </ng-template> -->
  <!-- enableGuest -->
  <ng-template contextMenuItem [enabled]="enableGuestCommonRoom" (execute)="addGuestInfo($event.item)">
    {{'RESERVATIONS.ADD_EDIT_GUE' | translate:param}}
  </ng-template>
  <!-- <ng-template contextMenuItem (execute)="processCancel($event.item)" [enabled]="enableCancel">
      Cancel
    </ng-template> -->
  <ng-template contextMenuItem divider="true"></ng-template>
  <ng-template contextMenuItem (execute)="unSelectRow()">
    {{'RESERVATIONS.CLOSE' | translate:param}}
  </ng-template>
</context-menu>

<!-- reservation wizard -->

<div class="modal fade" bsModal #staticModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}" tabindex="-1"
  role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <!--<div class="modal-header">
          <h4 class="modal-title pull-left">Static modal</h4>
        </div>-->
      <div class="modal-body" style="padding: 0">

        <form [formGroup]="bookingForm" (ngSubmit)="saveBooking($event);">

          <tabset #reservationTabs [justified]="true" class="__booking_wizard">
            <tab formGroupName="booking" (select)="checkTabIndex(0)">
              <ng-template tabHeading>
                <span class="__validation_indicator" [ngClass]="{'is-not-valid': !bookingForm.controls.booking.valid}">
                  <i class="fa fa-exclamation"></i>&nbsp;&nbsp;</span>Booking
              </ng-template>
              <!--  - booking tab - -->
              <div formArrayName="dates" *ngIf="bookingForm.controls.booking.controls.dates.controls">
                <div class="row" *ngFor="let date of bookingForm.controls.booking.controls.dates.controls; let i = index" [formGroupName]="i">
                  <div class="col-sm-4" *ngIf="i == 0">
                    <div>
                      <label for="" class="text-primary form-control">
                        {{'RESERVATIONS.RES_ON' | translate:param}} :&nbsp;
                        <strong>{{bookingForm.value.booking.reservation_date | date}}</strong>
                      </label>
                    </div>
                    <bs-datepicker-inline
                    #datepickerupdate1
                    class="full-table"
                    [minDate]="minDate"
                    [maxDate]="maxDate"
                    [bsValue]="date.value.start"
                    formControlName="start"
                    [bsConfig]="{containerClass:'theme-dark-blue'}"
                    [datesDisabled]="dateDisabled"
                    (bsValueChange)="bookingDateChanged($event,'start',i)">
                  </bs-datepicker-inline>
                    <label for="" class="form-control bg-checkin">
                      {{'RESERVATIONS.CHECK_IN_DATE' | translate:param}} :&nbsp;
                      <strong>{{date.value.start | date}}</strong>
                    </label>
                  </div>
                  <div class="col-sm-4 " *ngIf="i == 0">
                    <div>
                      <label for="" class="text-primary form-control">
                        {{'RESERVATIONS.BOOK_TYPE' | translate:param}} :&nbsp;
                        <strong>{{ bookingType[bookingForm.value.booking.booking_type].text }}</strong>
                      </label>
                    </div>
                    <!-- <bs-datepicker-inline 
                    #datepickerupdate2 
                    class="full-table" 
                    [minDate]="minEndDate"
                    [maxDate]="maxDate"
                    [bsConfig]="{containerClass:'theme-default'}"
                    (bsValueChange)="bookingDateChanged($event,'end',i)"
                    [datesDisabled]="dateDisabled">
                  </bs-datepicker-inline> -->
                  <bs-datepicker-inline
                  #datepickerupdate2
                  class="full-table"
                  [minDate]="minDate"
                  [maxDate]="maxDate"
                  [bsValue]="date.value.end"
                  formControlName="end"
                  [bsConfig]="{containerClass:'theme-orange'}"
                  [datesDisabled]="dateDisabled"
                  (bsValueChange)="bookingDateChanged($event,'end',i)">
                </bs-datepicker-inline>
                  <label for="" class="form-control bg-checkout">
                      {{'RESERVATIONS.CHECK_OUT_DATE' | translate:param}} : &nbsp;
                      <strong> {{date.value.end | date}}</strong>
                      <!-- <pre>{{date.value.end}}</pre> -->
                    </label>
                  </div>
                  <div class="col-sm-4" *ngIf="i == 0">
                    <!-- Un-comment code after code upload on server -->
                    <div class="relative">
                      <ng-select
                        [items]="stayTypeList"
                        bindLabel="text"
                        bindValue="id"
                        [formControl]="bookingForm.get(['booking', 'stay_type'])"
                        class="customerTypeSelections"
                        placeholder="select Stay Type"
                        dropdownPosition="auto"
                        (change)="stayTypeChanged($event)" required>
                      </ng-select>
                      <label for="" class="absolute">
                        {{ 'RESERVATIONS.STAY_TYPE' | translate:param }} :
                      </label>
                    </div>                    

                    <div class="relative" style="margin-top: .5rem;">
                      <ng-select
                        [items]="customerTypeList"
                        bindLabel="text"
                        bindValue="id"
                        [formControl]="bookingForm.get(['booking', 'customer_type'])"

                        class="customerTypeSelections"
                        placeholder="Select Customer Type"
                        [dropdownPosition]="'auto'"
                        (change)="customerTypeChanged($event)">
                      </ng-select>
                      <label for="" class="absolute">
                        {{ 'RESERVATIONS.CUST_TYPE' | translate:param }} :
                      </label>
                    </div>                    
                    <div class="input-group" style="margin-top: .5rem;">
                      <span class="input-group-addon" [ngClass]="{'has-border-right': !is_reference}">
                        <div class="abc-checkbox is_reference abc-checkbox-warning float-xs-left">
                          <input type="checkbox" id="checkbox1" [disabled]="!refereceUser" (change)="hasReference()"
                            value="true">
                          <label for="checkbox1"></label>
                        </div>
                      </span>
                      <div class="relative" style="width: 100%">
                        <ng-select 
                        [items]="refereceUser" 
                        [readonly]="!is_reference" 
                        placeholder="Select Referance User"
                        [formControl]="bookingForm.get(['booking', 'reference_user'])"
                        bindValue="id" 
                        bindLabel="text" 
                        class="referenceSelectionsmain"
                        (change)="referenceTypeChanged($event)" ></ng-select>
                        <label for="" class="absolute is_reference_label" [ngClass]="{'is_disabled': !is_reference}">
                          {{'RESERVATIONS.REF_' | translate:param}} :
                        </label>
                      </div>
                    </div>
                    <div
                      *ngIf="bookingForm.controls['booking'].controls['reference_user'].dirty && bookingForm.controls['booking'].controls['reference_user'].invalid"
                      class="alert alert-danger">
                      <div *ngIf="bookingForm.controls['booking'].controls['reference_user'].errors?.required">
                        {{'RESERVATIONS.VALID_MSG.REF_REQ' | translate:param}}</div>
                    </div>
                    <div *ngIf="customerTypeList && is_comment_necessary" class="form-group" style="margin-top: .7rem;">
                      <textarea type="text" class="form-control" formControlName="note"
                        placeholder="{{'RESERVATIONS.ENT_TXT_MSG' | translate:param}}"></textarea>
                      <div *ngIf="bookingForm.controls['booking'].controls['dates'].controls[i].controls['note']?.dirty &&
                            bookingForm.controls['booking'].controls['dates'].controls[i].controls['note']?.invalid"
                        class="alert alert-danger">
                        <div
                          *ngIf="bookingForm.controls['booking'].controls['dates'].controls[i].controls['note']?.errors?.required">
                          {{'RESERVATIONS.VALID_MSG.NOT_REQ' | translate:param}}</div>
                      </div>
                    </div>
                    <!-- *********************************************************** -->
                    <div class="timepicker">
                      <div class="form-group row" style="align-items: center;margin-bottom: 0px;margin-top: 0.5rem;">

                        <div class="col-sm-4">
                          <label for="normal-field" class="">{{'RESERVATIONS.CHECK_IN' | translate:param}}</label>
                        </div>
                        <div class="col-sm-8">
                          <datetime
                            [formControl]="bookingForm.controls.booking.controls.dates.controls[i].controls['check_in']"
                            [timepicker]="{ icon: 'fa fa-clock-o' }"
                            [datepicker]="{autoclose: true,todayHighlight: true,icon: 'fa fa-calendar',format: 'dd/mm/yyyy',startDate: date.value.start, endDate: date.value.end}">
                          </datetime>
                        </div>
                        <div *ngIf="bookingForm.controls['booking'].controls['dates'].controls[i].controls['check_in']?.dirty &&
                          bookingForm.controls['booking'].controls['dates'].controls[i].controls['check_in']?.invalid"
                          class="alert alert-danger no-block">
                          <div
                            *ngIf="bookingForm.controls['booking'].controls['dates'].controls[i].controls['check_in']?.errors?.required">
                            {{'RESERVATIONS.VALID_MSG.CHECK_IN_REQ' | translate:param}}</div>
                        </div>
                      </div>
                    </div>
                    <div class="timepicker">
                      <div class="form-group row" style="align-items: center;margin-bottom: 0px;margin-top: 0.5rem;">
                        <div class="col-sm-4">
                          <label for="normal-field" class="">{{'RESERVATIONS.CHECK_OUT' | translate:param}}</label>
                        </div>
                        <div class="col-sm-8">
                          <datetime
                            [formControl]="bookingForm.controls.booking.controls.dates.controls[i].controls['check_out']"
                            [timepicker]="{ icon: 'fa fa-clock-o' }"
                            [datepicker]="{autoclose: true,todayHighlight: true,icon: 'fa fa-calendar',format: 'dd/mm/yyyy',startDate: date.value.start}">
                          </datetime>
                        </div>
                        <div *ngIf="bookingForm.controls['booking'].controls['dates'].controls[i].controls['check_out']?.dirty &&
                          bookingForm.controls['booking'].controls['dates'].controls[i].controls['check_out']?.invalid"
                          class="alert alert-danger no-block">
                          <div
                            *ngIf="bookingForm.controls['booking'].controls['dates'].controls[i].controls['check_out']?.errors?.required">
                            {{'RESERVATIONS.VALID_MSG.CHECK_OUT_REQ' | translate:param}}</div>
                        </div>
                      </div>
                    </div>
                    <!-- *********************************************************** -->

                    <!-- <div class="input-group" style="margin-top: .7rem;">
                      <span class="input-group-addon" [ngClass]="{'has-border-right': !is_agent_location}">
                        <div class="abc-checkbox is_reference abc-checkbox-warning float-xs-left">
                          <input type="checkbox" id="checkbox1is_agent_location" [disabled]="!agentLocationList"
                            (change)="hasAgentLocation(i)" value="true">
                          <label for="checkbox1is_agent_location"></label>
                        </div>
                      </span>
                      <div class="relative" style="width: 100%">
                        <ng-select [items]="agentLocationList" [disabled]="!is_agent_location"
                          class="agentLocationSelections" (change)="agentLocationChanged($event)"
                          ></ng-select>
                        <label for="" class="absolute is_reference_label"
                          [ngClass]="{'is_disabled': !is_agent_location}">
                          {{'RESERVATIONS.AGENT_LOCATION' | translate:param}} :
                        </label>
                      </div>
                    </div> -->

                    <!-- ********************************************************* -->

                    <div *ngIf="agentList && is_agent" class="input-group" style="margin-top: .7rem;">
                      <div class="relative" style="width: 100%">
                        <ng-select [items]="agentList" [readonly]="!is_agent" class="agentSelections"
                          (change)="AgentTypeChanged($event)" ></ng-select>
                        <label for="" class="absolute is_reference_label" [ngClass]="{'is_disabled': !is_agent}">
                          {{'RESERVATIONS.AGENT' | translate:param}} :
                        </label>
                      </div>
                    </div>
                    <div *ngIf="agentList && is_agent" class="form-group" style="margin-top: .7rem;">
                      <input type="text" class="form-control" formControlName="agent_receipt_no"
                        placeholder="{{'RESERVATIONS.AGE_REC_NO' | translate:param}}.">
                      <div
                        *ngIf="bookingForm.controls['booking'].controls['dates'].controls[i].controls['agent_receipt_no']?.dirty &&
                          bookingForm.controls['booking'].controls['dates'].controls[i].controls['agent_receipt_no']?.invalid"
                        class="alert alert-danger">
                        <div
                          *ngIf="bookingForm.controls['booking'].controls['dates'].controls[i].controls['agent_receipt_no'].errors?.required">
                          {{'RESERVATIONS.VALID_MSG.AGEN_REC_REQ' | translate:param}}</div>
                      </div>
                    </div>
                  </div>
                  <div class="room-details room-details-with-margin col-sm-8">
                    <div>
                      <div class="form-control __flxed__rooms">
                        <div class="__room-name">{{date.value.room_title}}</div>

                        <div class="input-group" [class.child-has-no-border]="bookingForm.controls['booking'].controls['dates'].controls[i].controls['adult'].valid &&
                            bookingForm.controls['booking'].controls['dates'].controls[i].controls['child'].valid">
                          <span class="input-group-addon">
                            {{'RESERVATIONS.ADULT' | translate:param}}*
                          </span>
                          <input type="text" formControlName="adult" (change)="valueChanged()" class="form-control">
                          <span class="input-group-addon second-addon">
                            {{'RESERVATIONS.CHILD' | translate:param}}*
                          </span>
                          <input type="text" formControlName="child" (change)="valueChanged()" class="form-control">
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>

            </tab>
            <tab (select)="checkTabIndex(1)">
              <ng-template tabHeading>
                <span class="__validation_indicator" [ngClass]="{'is-not-valid': !bookingForm.controls.guest.valid}">
                  <i class="fa fa-exclamation"></i>&nbsp;&nbsp;</span>{{'RESERVATIONS.GUEST_INFO' | translate:param}}
              </ng-template>

              <div formArrayName="guest">
                <div *ngFor="let customer of bookingForm.controls.guest.controls; let j = index;" [formGroupName]="j">
                  <div class="row">
                    <div class="col-sm-8">
                      <h5>{{customer.value.room_title + ' Room : Main Guest Details'}}</h5>
                    </div>
                    <div class="abc-radio abc-radio-success col-sm-4">
                      <input type="radio" class="radioReset" name="customerModel" id="{{'radio_' + j}}"
                        [value]="customer.value.isBillingGuest" [checked]="customer.value.isBillingGuest"
                        (change)="changeCustomerModel(customer)">
                      <label for="{{'radio_' + j}}" class="capitalized">
                        {{'RESERVATIONS.IS_BILL_GUE' | translate:param}}?</label>
                    </div>
                    <!-- <pre>{{customer.value.isBillingGuest}}</pre> -->
                  </div>
                  <div class="row mb-2 mt-1">
                    <div class="col-sm-3">
                      <label class="d-block" for="{{'name_' + j}}">
                        <!-- <strong>{{'RESERVATIONS.FULL_NAME' | translate:param}}</strong> -->
                        <strong>Search Type</strong>
                      </label>
                      <ng-select [items]="searchTypeList" bindValue="id" bindLabel="text" (change)="searchTypeChanged($event,j)"
                        ></ng-select>
                    </div>
                    <div class="col-sm-9">
                      <label for="{{'name_' + j}}">
                        <!-- <strong>{{'RESERVATIONS.FULL_NAME' | translate:param}}</strong> -->
                        <strong>Search here</strong>
                      </label>
                      <!-- <int-phone-prefix  type="text" id="{{'contact_' + j}}" [defaultCountry]="'in'"
                            class="form-control" (keyup)="getCustomerList(j)">
                          </int-phone-prefix> -->
                      <focus-input *ngIf="reservationTabs.tabs[1].active == true && j == 0">
                        <int-phone-prefix *ngIf="bookingForm.controls['guest'].value[j].search_type == 0" type="text"
                          id="{{'search_type' + j}}" [defaultCountry]="'in'" (keyup)="getList($event,j)"
                          class="form-control"></int-phone-prefix>
                        <input *ngIf="bookingForm.controls['guest'].value[j].search_type != 0" type="text"
                          id="{{'search_type' + j}}" class="form-control" (keyup)="getList($event,j)">
                      </focus-input>
                      <div *ngIf="showCustomerList && selectedCustormerFormIndex == j" class="customer-list-dropdown">
                        <div class="col" *ngFor="let person of customerList; let p = index;"
                          (click)="selectCustomer(person,j,p)">
                          {{person.name}}
                        </div>
                      </div>
                    </div>
                  </div>
                  <hr />
                  <div class="row">
                    <div class="col-sm-4">
                      <label for="{{'name_' + j}}">
                        <strong>{{'RESERVATIONS.FULL_NAME' | translate:param}}</strong>
                      </label>
                      <input type="text" id="{{'name_' + j}}" formControlName="name" class="form-control">
                    </div>
                    <div class="col-sm-4">
                      <label for="{{'contact_' + j}}">
                        <strong>{{'RESERVATIONS.CON_NUM' | translate:param}}</strong>
                      </label>
                      <!-- <focus-input *ngIf="reservationTabs.tabs[1].active == true && j == 0"> -->
                      <int-phone-prefix type="text" id="{{'contact_' + j}}" [defaultCountry]="'in'"
                        formControlName="contact" class="form-control">
                        <!-- <i class="fa fa-sort-desc"></i> -->
                      </int-phone-prefix>
                      <!-- </focus-input> -->
                      <!-- <div *ngIf="j > 0">
                          <int-phone-prefix type="text" id="{{'contact_' + j}}" [defaultCountry]="'in'"
                            formControlName="contact" class="form-control" (keyup)="getCustomerList(j)"> -->
                      <!-- <i class="fa fa-sort-desc"></i> -->
                      <!-- </int-phone-prefix> -->
                      <!-- </div> -->
                      <!-- </focus-input> -->

                    </div>
                    <div class="col-sm-4">
                      <label for="{{'email_' + j}}">
                        <strong>{{'RESERVATIONS.EMAIL' | translate:param}}</strong>
                      </label>
                      <div *ngIf="bookingForm.controls.guest.controls.length > 1  && customer.value.isBillingGuest"
                        class="btn btn-xs btn-default pull-right" style="margin-bottom: .3rem;"
                        (click)="fillUserInAll(customer)">{{'RESERVATIONS.VALID_MSG.SET_IN_ALL' | translate:param}}
                      </div>
                      <input type="text" id="{{'email_' + j}}" formControlName="email" class="form-control">
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-4">
                      <label for="{{'aadharCard_' + j}}">
                        <strong>{{'RESERVATIONS.AADHAR_CARD' | translate:param}}</strong>
                      </label>
                      <input type="text" id="{{'aadharCard_' + j}}" formControlName="aadharcard_number"
                        class="form-control">
                         <div
                        *ngIf="customer.get('aadharcard_number').dirty && customer.get('aadharcard_number').invalid"
                        class="alert alert-danger">
                        <!-- <div *ngIf="customer.get('aadharcard_number').errors?.required">
                          {{'RESERVATIONS.VALID_MSG.AADHAR_REQ' | translate:param}}</div> -->
                          <div *ngIf="customer.get('aadharcard_number').errors?.pattern">
                            {{'RESERVATIONS.VALID_MSG.WRONG_NUM' | translate:param}}</div>
                      </div>


                    </div>

                    <div class="col-sm-4">
                      <label for="{{'panCard_' + j}}">
                        <strong>{{'RESERVATIONS.PANCARD' | translate:param}}</strong>
                      </label>
                      <input type="text" id="{{'panCard_' + j}}" formControlName="pancard_number"
                        class="form-control">
                        <div
                        *ngIf="customer.get('pancard_number').dirty && customer.get('pancard_number').invalid"
                        class="alert alert-danger">
                        <!-- <div *ngIf="customer.get('pancard_number').errors?.required">
                          {{'RESERVATIONS.VALID_MSG.PAN_REQ' | translate:param}}</div> -->
                          <div *ngIf="customer.get('pancard_number').errors?.validateInput?.pattern">
                            {{'RESERVATIONS.VALID_MSG.WRONG_NUM' | translate:param}}</div>
                      </div>


                    </div>

                    <div class="col-sm-4">
                      <label for="{{'document' + j}}">
                        <strong>{{'RESERVATIONS.DOCUMENT' | translate:param}}</strong>
                      </label>
                      <div *ngIf="bookingForm.controls.guest.controls.length > 1  && customer.value.isBillingGuest"
                        class="btn btn-xs btn-default pull-right" style="margin-bottom: .3rem;"
                        (click)="fillUserInAll(customer)">{{'RESERVATIONS.VALID_MSG.SET_IN_ALL' | translate:param}}
                      </div>
                      <!-- <div *ngFor="let guestFiles of customer.value?.guest[0]?.guestFiles">
                        <span></span>
                      </div> -->
                      <div *ngFor="let guestFiles of bookingForm.value?.guest[j]?.guestFiles">
                        <div class="row">
                          <span class="col-2" (click)="viewDocImg(guestFiles.guest_document)">{{guestFiles.displayname}}</span>
                          <div class="col-2"></div>
                        </div>
                      </div>
                      <!-- <input type="text" id="{{'email_' + j}}" formControlName="document" class="form-control"> -->
                    </div>
                  </div>
                  <!-- <div class="col-2">{{customer.value?.guestFiles[0]?.displayname}}</div> -->
                  <!-- <div *ngFor="let guestDocument of customer.value.guestFiles">
                    <div class="row">
                      <div class="col-2">{{customer.value?.guestFiles[0]?.displayname}}</div>
                      <div class="col-2">{{guestDocument?.displayname}}</div>
                    </div>
                  </div> -->
                  <hr>
                </div>
              </div>

            </tab>
            <tab (select)="checkTabIndex(2)">
              <ng-template tabHeading>
                <span class="__validation_indicator">
                  <i class="fa fa-exclamation"></i>&nbsp;&nbsp;</span>{{'RESERVATIONS.PAYM' | translate:param}}
              </ng-template>

              <div formGroupName="payments" class="row" *ngIf="roomChargesDetails && roomChargesDetails.length">
                <div class="col-sm-4">
                  <div class="relative">
                    <ng-select [items]="sourceType" bindValue="id" bindLabel="text" class="sourceTypeSelection"
                      (change)="sourceTypeSelectionChanged($event)" ></ng-select>
                    <label for="" class="absolute ">
                      {{'RESERVATIONS.SOURCE' | translate:param}} :
                    </label>
                  </div>
                  <hr>
                  <h5 class="text-center">{{'RESERVATIONS.ADV_PAY' | translate:param}}</h5>
                  <hr>
                  <div class="relative">
                    <ng-select [items]="paymentType" bindValue="id" bindLabel="text" class="advancePaymentSelection"
                      (change)="advancePaymentSelectionChanged($event)" ></ng-select>
                    <label for="" class="absolute">
                      {{'RESERVATIONS.PAY_MODE' | translate:param}} :
                    </label>
                  </div>
                  <br>
                  <div *ngIf="paymentTypeSelection == '1'">
                    <div class="input-group">
                      <span class="input-group-addon">
                        <i class="fa fa-file-text-o"></i>
                      </span>
                      <input type="text" formControlName="payment_reciept_number" class="form-control"
                        placeholder="{{'RESERVATIONS.RECE_NUM' | translate:param}}">
                    </div>
                    <br>
                    <div class="input-group">
                      <span class="input-group-addon">
                        <i class="fa fa-percent"></i>
                      </span>
                      <input type="text" formControlName="card_charge" class="form-control"
                        (keyup)="cardSwipeChargesChanged()" placeholder="{{'RESERVATIONS.CARD_SWI' | translate:param}}">
                    </div>
                    <br>
                  </div>
                  <div *ngIf="paymentTypeSelection == '2' || paymentTypeSelection == '3'">
                    <div class="input-group">
                      <span class="input-group-addon">
                        <i class="fa fa-file-text-o"></i>
                      </span>
                      <input type="text" formControlName="bank_name" class="form-control"
                        placeholder="{{'RESERVATIONS.BANK_NAME' | translate:param}}">
                    </div>
                    <br>
                    <div class="input-group">
                      <span class="input-group-addon">
                        <i class="fa fa-file-text-o"></i>
                      </span>
                      <input type="text" formControlName="bank_cheque_no" class="form-control"
                        placeholder="{{'RESERVATIONS.CHECK_NO' | translate:param}}">
                    </div>
                    <br>
                  </div>
                  <!-- PAN-NUMBER  -->

                  <div *ngIf="bookingForm.controls['payments'].get('payment_verification_id')">
                    <div class="text-right">
                      <div class="input-group">
                        <input (keyup)="getPanNo('a')" class="form-control" formControlName="payment_verification_id"
                          type="text" placeholder="{{'RESERVATIONS.PAN_NO_PLACE' | translate:param}}." />
                      </div>
                      <div
                        *ngIf="bookingForm.controls['payments'].get('payment_verification_id').dirty && bookingForm.controls['payments'].get('payment_verification_id').invalid"
                        class="alert alert-danger">
                        <div *ngIf="bookingForm.controls['payments'].get('payment_verification_id').errors?.required">
                          {{'RESERVATIONS.VALID_MSG.PAN_REQ' | translate:param}}</div>
                      </div>
                    </div>
                  </div>
                  <br>
                  <div class="input-group">
                    <span class="input-group-addon">
                      <i class="fa fa-inr"></i>
                    </span>
                    <input type="text" formControlName="payment_amount" (blur)="addtoAdvancePment()"
                      (keyup)="applyAdvancePayment()" class="form-control"
                      placeholder="{{'RESERVATIONS.AMOUNT' | translate:param}}">
                  </div>
                  <hr>
                  <button *ngIf="!isSameDayBooking() || isOverBooking" type="submit"
                    [disabled]="!bookingForm.valid || ((paymentTypeSelection == '0' && totalPayments > perDayCharge) && !canShowCardSwipeAmount) || (paymentTypeSelection != '0' && totalPayments > bankAdvanceAmount)"
                    class="btn btn-md btn-block btn-warning">
                    <i *ngIf="bookingSaveOnly" class="fa fa-spinner fa-pulse fa-fw"></i>
                    {{'RESERVATIONS.RESERVE' | translate:param}}</button>
                  <div class="row" *ngIf="isSameDayBooking() && !isOverBooking">
                    <div class="col-sm-5">
                      <!-- [disabled]="!bookingForm.valid || (bankAdvanceAmount < 0 && !canShowCardSwipeAmount)" -->
                      <button type="button" style="margin-top:0px; margin-left: auto;"
                        [disabled]="!bookingForm.valid || ((paymentTypeSelection == '0' && totalPayments > perDayCharge) && !canShowCardSwipeAmount) || (paymentTypeSelection != '0' && totalPayments > bankAdvanceAmount)"
                        class="btn btn-md btn-block btn-warning" (click)="saveBooking($event)">
                        <i *ngIf="bookingSaveOnly" class="fa fa-spinner fa-pulse fa-fw"></i>
                        {{'RESERVATIONS.RESERVE' | translate:param}}
                      </button>
                    </div>
                    <div class="col-sm-7">
                      <!-- [disabled]="!bookingForm.valid || (bankAdvanceAmount < 0 && !canShowCardSwipeAmount)" -->
                      <button type="button" style="margin-top:0px; margin-left: auto;"
                        [disabled]="!bookingForm.valid || ((paymentTypeSelection == '0' && totalPayments > perDayCharge) && !canShowCardSwipeAmount) || (paymentTypeSelection != '0' && totalPayments > bankAdvanceAmount)"
                        class="btn btn-md btn-block btn-warning" (click)="saveBooking($event,'check-in')">
                        <i *ngIf="bookingSaveAndCheckin" class="fa fa-spinner fa-pulse fa-fw"></i>
                        {{'RESERVATIONS.RESER_CHECK_IN' | translate:param}}
                      </button>
                    </div>
                  </div>
                </div>
                <div class="col-sm-8">
                  <div class="payroll">

                    <table class="table table-strips">
                      <thead>
                        <tr>
                          <th>{{'RESERVATIONS.ROOM' | translate:param}}</th>
                          <th class="text-right">{{'RESERVATIONS.RENT' | translate:param}}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let room of bookingForm.value.booking.dates;let i = index;">
                          <td>{{room.room_title}}</td>
                          <td class="text-right">
                            <div *ngIf="expandDiv" class="payment_expander">
                              <table class="table-no-border">
                                <tr *ngIf="!isCommonRoomCategory">
                                  <td>
                                    {{'RESERVATIONS.ROOM_RENT' | translate:param}} :
                                  </td>
                                  <td>
                                    {{(room.total_days * roomChargesDetails[i].charges) }}
                                  </td>
                                </tr>
                                <tr>
                                  <td>{{isCommonRoomCategory ? 'Adult' : 'Extra Adult'}} ({{isCommonRoomCategory ?
                                    room.adult
                                    : (room.adult - roomChargesDetails[i].std_occupancy >= 0 ) ? (room.adult -
                                    roomChargesDetails[i].std_occupancy)
                                    : 0 }}) * {{isCommonRoomCategory ? 'Adult Charges' : 'Extra Adult Charges'}}
                                    ({{isCommonRoomCategory
                                    ? roomChargesDetails[i].charges : roomChargesDetails[i].extra_adult_charges}}) :
                                  </td>
                                  <td>{{isCommonRoomCategory ? (room.total_days * roomChargesDetails[i].charges *
                                    room.adult)
                                    : (room.total_days * ((( room.adult - roomChargesDetails[i].std_occupancy ) >= 0) ?
                                    (room.adult
                                    - roomChargesDetails[i].std_occupancy) * roomChargesDetails[i].extra_adult_charges
                                    :
                                    0 ))}}</td>
                                </tr>
                                <tr>
                                  <td>{{isCommonRoomCategory ? 'Child' : 'Extra Child'}} ({{isCommonRoomCategory ?
                                    room.child
                                    : (room.adult - roomChargesDetails[i].std_occupancy) >= 0 ? room.child :
                                    (room.child
                                    - (roomChargesDetails[i].std_occupancy - room.adult)) >= 0 ? (room.child -
                                    (roomChargesDetails[i].std_occupancy
                                    - room.adult)) : 0}}) * {{isCommonRoomCategory ? 'Child Cost' : 'Extra Child
                                    Cost'}}
                                    ({{isCommonRoomCategory ? roomChargesDetails[i].charges :
                                    roomChargesDetails[i].extra_child_charges}})
                                    : </td>
                                  <td>{{isCommonRoomCategory ? (room.total_days * roomChargesDetails[i].charges *
                                    room.child)
                                    : (room.total_days * (((room.adult - roomChargesDetails[i].std_occupancy) >= 0) ?
                                    (room.child)
                                    * roomChargesDetails[i].extra_child_charges : (room.child -
                                    (roomChargesDetails[i].std_occupancy
                                    - room.adult)) >= 0 ? (room.child - (roomChargesDetails[i].std_occupancy -
                                    room.adult))
                                    * roomChargesDetails[i].extra_child_charges : 0))}}</td>
                                </tr>
                              </table>
                              <hr>
                            </div>
                            <p class="payments" [ngClass]="{'is_expanded_trigger': expandDiv}"
                              (click)="expandDiv = !expandDiv">
                              <i class="fa fa-inr"></i>&nbsp; {{(room.total_days * (isCommonRoomCategory ?
                              (roomChargesDetails[i].charges
                              * (parseIntCustom(room.adult) + parseIntCustom(room.child))) :
                              (roomChargesDetails[i].charges
                              + ((room.adult - roomChargesDetails[i].std_occupancy) >= 0 ? ((room.adult -
                              roomChargesDetails[i].std_occupancy)
                              * roomChargesDetails[i].extra_adult_charges + (room.child) *
                              roomChargesDetails[i].extra_child_charges)
                              : ((room.child - (roomChargesDetails[i].std_occupancy - room.adult)) >= 0 ? (room.child -
                              (roomChargesDetails[i].std_occupancy
                              - room.adult)) * roomChargesDetails[i].extra_child_charges : 0)))))}}
                            </p>
                          </td>
                        </tr>
                        <tr style="background: #eee;">
                          <td>
                            <strong>Subtotal :</strong>
                          </td>
                          <td class="text-right">
                            <p class="totalpayments">
                              <strong>
                                <i class="fa fa-inr"></i>&nbsp;{{totalPayments}}
                              </strong>
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <br>
                    <div class="well well-sm">
                      <table class="table table-strips">
                        <tr *ngFor="let ap of advancePayments">
                          <td>
                            {{'RESERVATIONS.ADV_PAY' | translate:param}} ({{ap.date}})
                          </td>
                          <td class="text-right">
                            <i class="fa fa-inr"></i>&nbsp;{{ap.amount}}
                          </td>
                        </tr>
                        <tr *ngIf="totalCustomerDiscount > 0">
                          <td>
                            {{'RESERVATIONS.CUST_DIS' | translate:param}}
                          </td>
                          <td class="text-right">
                            <i class="fa fa-inr"></i>&nbsp;{{totalCustomerDiscount}}
                          </td>
                        </tr>
                        <tr *ngIf="(advancePayments.length > 0) && (paymentTypeSelection == '1')">
                          <td>
                            {{'RESERVATIONS.CARD_SWI' | translate:param}}
                          </td>
                          <td class="text-right">
                            <i class="fa fa-inr"></i>&nbsp;{{advancePayments.length > 0 ? ((totalPayments *
                            bookingForm.value.payments.card_charge)/100)
                            : 0}}
                          </td>
                        </tr>
                        <tr style="background: #eee;">
                          <td>
                            <strong>{{'RESERVATIONS.TOT_PAY' | translate:param}} :</strong>
                          </td>
                          <td class="text-right text-success">
                            <strong *ngIf="(totalPayments < perDayCharge) || canShowCardSwipeAmount">
                              <i class="fa fa-inr"></i>&nbsp;{{ totalPayments + ((advancePayments.length > 0) &&
                              (paymentTypeSelection
                              == '1') ? ((totalPayments * bookingForm.value.payments.card_charge)/100) : 0)}}
                            </strong>
                            <strong class="text-danger"
                              *ngIf="(paymentTypeSelection == '0' && totalPayments > perDayCharge) && !canShowCardSwipeAmount">{{ "We only accept 1 day payment as advance" +  ' ' + perDayCharge }}
                            </strong>
                            <strong class="text-danger"
                            *ngIf="(paymentTypeSelection != '0' && totalPayments > bankAdvanceAmount)">{{ "We only accept total_payment as advance" +  ' ' + bankAdvanceAmount}}
                            </strong>
                          </td>
                        </tr>
                      </table>
                    </div>

                  </div>
                </div>
              </div>
              <div formArrayName="items" *ngFor="let item of bookingForm.get('items').controls; let i = index">
                <div [formGroupName]="i" class="input-group">
                  <input class="form-control m-1" formControlName="name" type="text" placeholder="Name">
                  <input class="form-control m-1" formControlName="city" type="text" placeholder="City">
                  <input class="form-control m-1" formControlName="panno" type="text" placeholder="Pan No">
                </div>
                <!-- <div class="text-right" *ngIf="cardSelected ? false : true">
                    <div formArrayName="items"
                      *ngFor="let item of bookingForm.controls['payments'].get('items').controls; let i = index">
                      <div [formGroupName]="i" class="input-group">
                        <input class="form-control m-1" formControlName="name" type="text" placeholder="Name">
                        <input class="form-control m-1" formControlName="city" type="text" placeholder="City">
                        <input class="form-control m-1" formControlName="panno" type="text" placeholder="Pan No">
                      </div>
                    </div>
                  </div> -->
              </div>
            </tab>
          </tabset>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="close pull-right" aria-label="Close" (click)="cancelReservationProcess()">
          {{'RESERVATIONS.CANCEL' | translate:param}}
        </button>
        <button *ngIf="!isLastReservationTab" type="button" style="opacity:0.7;margin-right:10px;" aria-label="Close"
          class="close" (click)="nextReservationTab()">
          {{'RESERVATIONS.NEXT' | translate:param}}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Add Guest Form -->
<div class="modal fade" bsModal #addGuestModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}" tabindex="-1"
  role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <!--<div class="modal-header">
          <h4 class="modal-title pull-left">Static modal</h4>
        </div>-->
      <div class="modal-body" style="padding: 0">
        <!-- (ngSubmit)="saveBooking($event)" -->
        <form [formGroup]="addGuestForm" (ngSubmit)="addGuestToRoom()">
          <tabset #addGuestTabs [justified]="true" class="__booking_wizard">
            <!-- formGroupName="booking" -->
            <!-- (select)="checkTabIndex(0)" -->
            <tab (select)="checkAddGuestTabIndex(0)">
              <ng-template tabHeading>
                <span class="__validation_indicator"
                  [ngClass]="{'is-not-valid': addGuestForm.controls['adult'].invalid || addGuestForm.controls['child'].invalid || addGuestForm.controls['start'].invalid || addGuestForm.controls['end'].invalid}">
                  <i class="fa fa-exclamation"></i>&nbsp;&nbsp;</span>{{'RESERVATIONS.BOOKING' | translate:param}}
              </ng-template>
              <!--  - booking tab - -->
              <!-- formArrayName="dates" -->
              <div class="basic-guest-info">
                <div class="row">
                  <div class="col-sm-4">
                    <!-- #datepickerupdate1 -->
                    <bs-datepicker-inline class="full-table" [bsValue]="minAddGuestDate" [formControl]="addGuestForm.controls['start']" #addGuestDatePicker1
                      [bsConfig]="{containerClass:'theme-dark-blue'}"
                      (bsValueChange)="addGuestBookingDateChanged($event,'start')" [minDate]="minAddGuestDate"
                      [maxDate]="maxAddGuestDate" >
                    </bs-datepicker-inline>
                    <label for="" class="form-control bg-checkin">
                      {{'RESERVATIONS.CHECK_IN_DATE' | translate:param}} :&nbsp;
                      <strong>{{addGuestForm.value.start | date}}</strong>
                    </label>
                  </div>
                  <div class="col-sm-4">
                    <!-- #datepickerupdate2 -->
                    <bs-datepicker-inline class="full-table" [bsValue]="maxAddGuestDate" [formControl]="addGuestForm.controls['end']" #addGuestDatePicker2
                      (bsValueChange)="addGuestBookingDateChanged($event,'end')" [minDate]="minAddGuestDate" [bsConfig]="{containerClass:'theme-orange'}"
                      [maxDate]="maxAddGuestDate" >
                    </bs-datepicker-inline>
                    <label for="" class="form-control bg-checkout">
                      {{'RESERVATIONS.CHECK_OUT_DATE' | translate:param}} : &nbsp;
                      <strong>{{formatCheckoutDate(addGuestForm.value.end) | date}}</strong>
                    </label>
                  </div>
                  <div class="col-sm-4">
                    <div>
                      <label for="" class="text-primary form-control">
                        {{'RESERVATIONS.RES_ON' | translate:param}} :&nbsp;
                        <strong>{{addGuestForm.value.reservation_date | date}}</strong>
                      </label>
                    </div>
                    <div>
                      <label for="" class="text-primary form-control">
                        {{'RESERVATIONS.BOOK_TYPE' | translate:param}} :&nbsp;
                        <strong>{{bookingType[findIndex(addGuestParams?.booking_type,"id",bookingType) -
                          1]?.text}}</strong>
                      </label>
                    </div>
                    <div>
                      <label for="" class="text-primary form-control">
                        {{'RESERVATIONS.GUE_IN_ROOM' | translate:param}} :&nbsp;
                        <strong>{{addGuestParams?.booked_occupancy}}</strong>
                      </label>
                    </div>
                    <!-- class="input-group" -->
                    <div class="row" style="margin-top: 15px;">
                      <div class="col-sm-2">
                        <label>
                          {{'RESERVATIONS.ADULT' | translate:param}}*
                        </label>
                      </div>
                      <input type="hidden" formControlName="adult">
                      <div class="col-sm-9">
                        <i class="fa fa-plus increment-decrement" (click)="updateGuestArray('adult','add')"></i>
                        <span class="badge">{{addGuestForm.value.adult}}</span>
                        <i class="fa fa-minus increment-decrement" (click)="updateGuestArray('adult','subtract')"></i>
                      </div>
                    </div>
                    <div class="row" style="margin-top: 15px;">
                      <div class="col-sm-2">
                        <label>
                          {{'RESERVATIONS.CHILD' | translate:param}}*
                        </label>
                      </div>
                      <input type="hidden" formControlName="child">
                      <div class="col-sm-9">
                        <i class="fa fa-plus increment-decrement" (click)="updateGuestArray('child','add')"></i>
                        <span class="badge">{{addGuestForm.value.child}}</span>
                        <i class="fa fa-minus increment-decrement" (click)="updateGuestArray('child','subtract')"></i>
                      </div>
                      <div
                        *ngIf="addGuestForm.controls['adult'].dirty && (addGuestForm.controls['adult'].value + addGuestForm.controls['child'].value) == 0"
                        class="alert alert-danger">
                        <div>{{'RESERVATIONS.VALID_MSG.ADU_CHIL_REQ' | translate:param}}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </tab>
            <tab (select)="checkAddGuestTabIndex(1)">
              <ng-template tabHeading>
                <span class="__validation_indicator" [ngClass]="{'is-not-valid': addGuestForm.controls.guests.invalid}">
                  <i class="fa fa-exclamation"></i>&nbsp;&nbsp;</span>{{'RESERVATIONS.GUEST_INFO' | translate:param}}
              </ng-template>
              <div formArrayName="guests">
                <div *ngFor="let customer of addGuestForm.controls.guests.controls; let j = index;" [formGroupName]="j">
                  <div class="row">
                    <div class="col-sm-8">
                      <h5>{{'RESERVATIONS.GUE_DETA' | translate:param}} : </h5>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-3">
                      <label for="{{'name_' + j}}">
                        <strong>{{'RESERVATIONS.FULL_NAME' | translate:param}}*</strong>
                      </label>
                      <input type="text" id="{{'name_' + j}}" class="form-control" formControlName="name">
                      <div
                        *ngIf="addGuestForm.controls.guests.controls[j].controls['name'].dirty && addGuestForm.controls.guests.controls[j].controls['name'].invalid"
                        class="alert alert-danger">
                        <div *ngIf="addGuestForm.controls.guests.controls[j].controls['name'].errors?.required">
                          {{'RESERVATIONS.VALID_MSG.NAME_REQ' | translate:param}}</div>
                      </div>
                    </div>
                    <div class="col-sm-3">
                      <label for="{{'dob_' + j}}">
                        <strong>
                          {{'RESERVATIONS.DOB' | translate:param}}*
                        </strong>
                      </label>
                      <div class="input-group">
                        <!-- (ngModelChange)="updateDOB(i)" -->
                        <datetime class="guest-dob" formControlName="dob" (ngModelChange)="updateAddGuestAge(j)"
                          [timepicker]="false" [datepicker]="datepickerOpts"></datetime>
                      </div>
                      <div
                        *ngIf="addGuestForm.controls.guests.controls[j].controls['dob'].dirty && addGuestForm.controls.guests.controls[j].controls['dob'].invalid"
                        class="alert alert-danger">
                        <div *ngIf="addGuestForm.controls.guests.controls[j].controls['dob'].errors?.required">
                          {{'RESERVATIONS.VALID_MSG.DOB_REQ' | translate:param}}</div>
                        <div *ngIf="addGuestForm.controls.guests.controls[j].controls['dob'].errors?.minDate ||
                            addGuestForm.controls.guests.controls[j].controls['dob'].errors?.maxDate">
                          {{'RESERVATIONS.VALID_MSG.IN_VAL_DOB' | translate:param}}</div>
                      </div>
                    </div>
                    <div class="col-sm-2">
                      <label for="{{'age_' + j}}">
                        <strong>
                          {{'RESERVATIONS.AGE' | translate:param}}*
                        </strong>
                      </label>
                      <input class="guest-dob" class="form-control" formControlName="age"
                        (keyup)="updateAddGuestDOB(j)" />
                      <div
                        *ngIf="addGuestForm.controls.guests.controls[j].controls['age'].dirty && addGuestForm.controls.guests.controls[j].controls['age'].invalid"
                        class="alert alert-danger">
                        <div *ngIf="addGuestForm.controls.guests.controls[j].controls['age'].errors?.required">
                          {{'RESERVATIONS.VALID_MSG.AGE_REQ' | translate:param}}</div>
                        <div *ngIf="addGuestForm.controls.guests.controls[j].controls['age'].errors?.lte ||
                            addGuestForm.controls.guests.controls[j].controls['age'].errors?.gt">
                          {{'RESERVATIONS.VALID_MSG.INV_AGE' | translate:param}}</div>
                      </div>
                    </div>
                    <div class="col-sm-2">
                      <label for="">
                        <strong>
                          {{'RESERVATIONS.TYPE' | translate:param}}*
                        </strong>
                      </label>
                      <div class="radio-horizontal" style="padding: 3px 0;">
                        <div class="abc-radio" style="padding-left: 0px;">
                          <input type="radio" formControlName="guest_maturity_type" id="guest_maturity_type-{{j}}-1"
                            value="adult" (change)="addGuestAdultChanged(j)">
                          <label for="guest_maturity_type-{{j}}-1">
                            {{'RESERVATIONS.ADULT' | translate:param}}
                          </label>
                        </div>
                        <div class="abc-radio">
                          <input type="radio" formControlName="guest_maturity_type" id="guest_maturity_type-{{j}}-2"
                            value="child" (change)="addGuestChildChanged(j)">
                          <label for="guest_maturity_type-{{j}}-2">
                            {{'RESERVATIONS.CHILD' | translate:param}}
                          </label>
                        </div>
                      </div>
                      <!-- </div> -->
                      <div
                        *ngIf="addGuestForm.controls.guests.controls[j].controls['guest_maturity_type'].dirty && addGuestForm.controls.guests.controls[j].controls['guest_maturity_type'].invalid"
                        class="alert alert-danger">
                        <div
                          *ngIf="addGuestForm.controls.guests.controls[j].controls['guest_maturity_type'].errors?.required">
                          {{'RESERVATIONS.VALID_MSG.GUEST_TYPE_REQ' | translate:param}}</div>
                      </div>
                    </div>
                    <div class="col-sm-2">
                      <label for="">
                        <strong>
                          {{'RESERVATIONS.GENDER' | translate:param}}*
                        </strong>
                      </label>
                      <div class="radio-horizontal" style="padding: 3px 0;">
                        <div class="abc-radio" style="padding-left: 0px;">
                          <input type="radio" formControlName="gender" id="Gender-{{j}}-1" value="male">
                          <label for="Gender-{{j}}-1">
                            {{'RESERVATIONS.MALE' | translate:param}}
                          </label>
                        </div>
                        <div class="abc-radio">
                          <input type="radio" formControlName="gender" id="Gender-{{j}}-2" value="female">
                          <label for="Gender-{{j}}-2">
                            {{'RESERVATIONS.FEMALE' | translate:param}}
                          </label>
                        </div>
                      </div>
                      <!-- </div> -->
                      <div
                        *ngIf="addGuestForm.controls.guests.controls[j].controls['gender'].dirty && addGuestForm.controls.guests.controls[j].controls['gender'].invalid"
                        class="alert alert-danger">
                        <div *ngIf="addGuestForm.controls.guests.controls[j].controls['gender'].errors?.required">
                          {{'RESERVATIONS.VALID_MSG.GEN_REQ' | translate:param}}</div>
                      </div>
                    </div>
                  </div>
                  <hr>
                </div>
                <div *ngIf="addGuestForm.controls.guests.controls.length == 0" class="not-guest-detail-info"
                  (click)="previousTab()">
                  <i class="fa fa-plus"></i>
                  {{'RESERVATIONS.ADD_AN_ADU' | translate:param}}
                </div>
              </div>
            </tab>
            <tab (select)="getAddGuestRoomChargesandExtracharges(2)">
              <ng-template tabHeading>
                <span class="__validation_indicator">
                  <i class="fa fa-exclamation"></i>&nbsp;&nbsp;</span>{{'RESERVATIONS.PAYM' | translate:param}}
              </ng-template>
              <!-- formGroupName="payments" ========================= *ngIf="roomChargesDetails && roomChargesDetails.length" -->
              <div class="row">
                <div class="col-sm-12">
                  <div class="payroll">
                    <table class="table table-strips">
                      <thead>
                        <tr>
                          <th>{{'RESERVATIONS.TYPE' | translate:param}}</th>
                          <th class="text-right">{{'RESERVATIONS.RENT' | translate:param}}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <!-- *ngFor="let room of bookingForm.value.booking.dates;let i = index;" -->
                        <tr>
                          <td>Extra Adult({{(addGuestParams?.extraAdult || 0)}}) x Extra Adult
                            charge({{getIndividualPaxCharge('extra_adult_charges')}})
                            x Days({{addGuestParams?.totalDays || 0}}) :</td>
                          <td class="text-right">
                            <i class="fa fa-inr"></i>
                            {{ getTotalPaxTypeCharges('extra_adult_charges', 'extraAdult') }}
                          </td>
                        </tr>
                        <tr>
                          <td>Extra Child({{(addGuestParams?.extraChild || 0)}}) x Extra Child
                            charge({{getIndividualPaxCharge('extra_child_charges')}})
                            x Days({{addGuestParams?.totalDays || 0}}) :</td>
                          <td class="text-right">
                            <i class="fa fa-inr"></i>
                            {{ getTotalPaxTypeCharges('extra_child_charges', 'extraChild')}}
                          </td>
                        </tr>
                        <tr style="background: #eee;">
                          <td>
                            <strong>{{'RESERVATIONS.TOTAL' | translate:param}} :</strong>
                          </td>
                          <td class="text-right">
                            <p class="totalpayments">
                              <strong>
                                <i class="fa fa-inr"></i>&nbsp;
                                {{getTotalPaxTypeCharges('extra_adult_charges', 'extraAdult') +
                                getTotalPaxTypeCharges('extra_child_charges', 'extraChild')}}
                              </strong>
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div class="col-sm-12">
                  <button type="submit" class="btn btn-warning" style="min-width: 150px;">{{'RESERVATIONS.ADD' |
                    translate:param}}</button>
                </div>
              </div>
            </tab>
          </tabset>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="close pull-right" aria-label="Close" (click)="cancelReservationProcess()">
          {{'RESERVATIONS.CANCEL' | translate:param}}
        </button>
        <!-- *ngIf="!isLastReservationTab" -->
        <button *ngIf="!isLastGuestAddTab" type="button" style="opacity:0.7;margin-right:10px;" aria-label="Close"
          class="close" (click)="nextGuestAddTab()">
          {{'RESERVATIONS.NEXT' | translate:param}}
        </button>
      </div>
    </div>
  </div>
</div>
<!-- Add Guest Form -->

<!-- Booking Cancellation Modal -->
<div bsModal #smModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{backdrop: 'static',keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h2>
          {{'RESERVATIONS.BOOK_CANCEL' | translate:param}}
        </h2>
      </div>
      <div class="modal-body">
        <div class="row extra-padding" style="padding-bottom: 32px;">
          <div class="col-sm-4 text-right" style="margin-top: 8px;">
            {{'RESERVATIONS.SEL_POLICY' | translate:param}}:</div>
          <div class="col-sm-8" style="font-weight: bold;">
            <ng-select [items]="activeCancellationPolicies" bindLabel="text" bindValue="id" [(ngModel)]="selectedActiveCancellationPolicies"
              (change)="getCancellationPolicyCharges($event)" ></ng-select>
          </div>
        </div>
        <div *ngIf="refunds?.cancellationPolicyCalculation" style="padding-bottom: 32px;">
          <form [formGroup]="cancelBookingForm">
            <div class="row extra-padding" *ngIf="isReferenceNeccessaryCancel">
              <div class="col-sm-4 text-right">{{'RESERVATIONS.REF_TYPE' | translate:param}}:</div>
              <div class="col-sm-8">
                <ng-select [items]="referenceUserCancelBooking" [(ngModel)]="selectedReferenceUserCancelBooking" formControlName="fund_type"
                  (change)="cancelBookingReferenceTypeChanged($event)" bindLabel="text" bindValue="id"></ng-select>
                <div
                  *ngIf="cancelBookingForm.controls['reference_id']?.dirty && cancelBookingForm.controls['reference_id']?.invalid"
                  class="alert alert-danger">
                  <div
                    *ngIf="cancelBookingForm.controls['reference_id']?.errors?.required || cancelBookingForm.controls['reference_id']?.errors?.notEqual">
                    {{'RESERVATIONS.VALID_MSG.REF_IS_REQ' | translate:param}}</div>
                </div>
              </div>
            </div>
            <br>
            <div class="row extra-padding">
              <div class="col-sm-4 text-right">{{'RESERVATIONS.PASSCODE' | translate:param}}:</div>
              <div class="col-sm-8">
                <input type="password" class="form-control" placeholder="{{'RESERVATIONS.PASSCODE' | translate:param}}"
                  formControlName="passcode">
                <div
                  *ngIf="cancelBookingForm.controls['passcode']?.dirty && cancelBookingForm.controls['passcode']?.invalid"
                  class="alert alert-danger">
                  <div *ngIf="cancelBookingForm.controls['passcode']?.errors?.required">
                    {{'RESERVATIONS.VALID_MSG.PASSWORD_REQUIRED' | translate:param}}</div>
                </div>
              </div>
            </div>
            <br>
            <div class="row extra-padding" *ngIf="isNoteNeccessaryCancel">
              <div class="col-sm-4 text-right">{{'RESERVATIONS.NOTE' | translate:param}}</div>
              <div class="col-sm-8">
                <textarea type="text" class="form-control" placeholder="{{'RESERVATIONS.TXT_MSG' | translate:param}}"
                  formControlName="note"></textarea>
                <div *ngIf="cancelBookingForm.controls['note']?.dirty && cancelBookingForm.controls['note']?.invalid"
                  class="alert alert-danger">
                  <div *ngIf="cancelBookingForm.controls['note']?.errors?.required">
                    {{'RESERVATIONS.VALID_MSG.NOT_REQ' | translate:param}}</div>
                </div>
              </div>
            </div>
            <br>
          </form>
        </div>
        <div class="booking-cancellation" *ngIf="refunds?.cancellationPolicyCalculation">
          <p>{{'RESERVATIONS.TOT_BOOK_CHARGE' | translate:param}}:
            <strong>
              <i class="fa fa-inr"></i>&nbsp;{{refunds?.cancellationPolicyCalculation?.total_payable}}&nbsp;
            </strong>
          </p>
          <p>
            {{'RESERVATIONS.ADV_PAY' | translate:param}}:
            <strong>
              <i class="fa fa-inr"></i>&nbsp;{{refunds?.cancellationPolicyCalculation?.total_paid}}&nbsp;
            </strong>.
          </p>
          <p>
            {{'RESERVATIONS.APP_CAN_CHAR' | translate:param}}
            <strong>
              <i
                class="fa fa-inr"></i>&nbsp;{{refunds?.cancellationPolicyCalculation?.deducatable_amount}}&nbsp;({{refunds?.cancellationPolicyCalculation?.refund_deducation}}%
              /
              {{refunds?.cancellationPolicyCalculation?.policy}} policy)
            </strong>
          </p>
          <p class="text-success">
            <strong>{{'RESERVATIONS.TOT_REF_AMT' | translate:param}}:
              <i
                class="fa fa-inr"></i>&nbsp;{{refunds?.cancellationPolicyCalculation?.actual_refundable_amount}}&nbsp;</strong>
          </p>
          <p class="text-success">
            <strong>{{'RESERVATIONS.TOT_PAY_AMT' | translate:param}}:
              <i
                class="fa fa-inr"></i>&nbsp;{{refunds?.cancellationPolicyCalculation?.cancellation_payable_amount}}&nbsp;</strong>
          </p>
        </div>
      </div>
      <div class="modal-footer">
        <div class="btn-group">
          <button type="button" class="btn btn-md btn-success" [disabled]="!refunds?.cancellationPolicyCalculation"
            (click)="willnowCancel()">{{'RESERVATIONS.CONF' | translate:param}}</button>
          <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="smModal.hide()">
            {{'RESERVATIONS.CANCEL' | translate:param}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div bsModal #imageModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{backdrop: 'static',keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <img *ngIf="imageData?.isImage" class="image-modal-view" src="{{imageData?.url}}" alt="">
      <div *ngIf="!imageData?.isImage">
        <pdf-viewer src="{{imageData?.url}}" [render-text]="true" style="display: block;">
        </pdf-viewer>
      </div>
      <div class="modal-footer">
        <div class="btn-group">
          <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="imageModal.hide()">
            {{'RESERVATIONS.OK' | translate:param}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div bsModal #docImageModal="bs-modal" class="modal fade" id="docModel" tabindex="-1" role="dialog"
  [config]="{backdrop: 'static',keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <img class="image-modal-view" src="{{imageUrl}}" alt="">
      <div class="modal-footer">
        <div class="btn-group">
          <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="hideDocModel()">
            {{'RESERVATIONS.OK' | translate:param}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>


<!-- View Details Modal -->
<div class="modal" bsModal #viewDetailsModal="bs-modal" role="dialog" [config]="{backdrop:false}" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true">

  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <section class="booking-info m-2 p-2">
        <header>
          <div class="row">
            <div class="col-sm-8">
              <h4>
                <span class="capitalized">
                  <i class="fa fa-clipboard"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.VIEW_BOOK' | translate:param}}</span>
              </h4>
            </div>
            <div class="col-sm-4 view-details-buttons">
              <button type="button" class="btn btn-sm btn-danger float-sm-right" (click)="gobackModel()">
                <i class="fa fa-angle-left"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.BACK' | translate:param}}</button>
            </div>
          </div>
        </header>
        <hr class="large-hr" />
        <div class="clearfix"></div>
        <div class="widget-body" *ngIf="selectedBooking">
          <div class="mt">
            <div class="row">
              <!-- Booking Information Section -->
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h4>{{'VIEW_BOOKI_DETAI.BOOK_DE' | translate:param}}</h4>
                  </div>
                  <div class="panel-body">
                    <table class="table table-no-mar">
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.RES_ID' | translate:param}}:</td>
                        <td><strong>{{selectedBooking.id ? selectedBooking.id : '-'}}</strong></td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.STAY_TYPE' | translate:param}}:</td>
                        <td>
                          <strong>{{selectedBooking?.stayTypeDetails?.name}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.BOOK_STAT' | translate:param}}:</td>
                        <td>
                          <strong class="capitalize">{{selectedBooking.current_status ? selectedBooking.current_status :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.BOOK_DATE' | translate:param}}:</td>
                        <td>
                          <strong>{{(selectedBooking.reservation_date ? (selectedBooking.reservation_date | date) :
                            '-')}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.RATE' | translate:param}}:</td>
                        <td>
                          <strong>
                            <span
                              *ngIf="selectedBooking.roomCategoryDetails.charges">+&nbsp;{{selectedBooking.total_amount}}
                            </span>
                          </strong>
                        </td>
                      </tr>
                      <tr *ngIf="selectedBooking.bookingPaymentData.length > 0">
                        <td>{{'VIEW_BOOKI_DETAI.PAID_AMOUNT' | translate:param}}:</td>
                        <td>
                          <strong>
                            <span
                              *ngIf="selectedBooking.roomCategoryDetails.charges">+&nbsp;{{selectedBooking.bookingPaymentData[0].amount}}
                            </span>
                          </strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_IN' | translate:param}}:</td>
                        <td>
                          <strong class="text-success">{{(selectedBooking.check_in ? (selectedBooking.check_in |
                            date:'dd-MM-yyyy') : '-' )}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_OUT' | translate:param}}:</td>
                        <td>
                          <strong class="text-success">{{(selectedBooking.check_out ? (selectedBooking.check_out |
                            date:'dd-MM-yyyy') : '-')}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>Aadhar Card:</td>
                        <td>
                          <strong class="text-danger">
                            {{selectedBooking.aadharcard_number ? selectedBooking.aadharcard_number : ''}}
                          </strong>
                        </td>
                      </tr>
                      <tr>
                        <td>PAN Card:</td>
                        <td>
                          <strong class="text-danger">
                            {{selectedBooking.pancard_number ? selectedBooking.pancard_number : 'not added'}}
                          </strong>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h4>{{'VIEW_BOOKI_DETAI.BILL_GUE_DETAI' | translate:param}}</h4>
                  </div>
                  <div class="panel-body">
                    <table class="table table-no-mar">
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.GUEST_NAME' | translate:param}}:</td>
                        <td style="text-transform:uppercase;">
                          <strong>{{selectedBooking.name ? selectedBooking.name : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.GUE_TYPE' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.customerDetails.name ? selectedBooking.customerDetails.name :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.CONT' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.contact ? selectedBooking.contact : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.EMAIL' | translate:param}}:</td>
                        <td>
                          <strong>{{selectedBooking.email ? selectedBooking.email : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.ROOM_NO' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.roomDetails.title ? selectedBooking.roomDetails.title :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'Referance User' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.reference_user_name ? selectedBooking.reference_user_name :
                            '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.ROOM_TYP' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.roomCategoryDetails.name ? selectedBooking.roomCategoryDetails.name
                            : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.TOT_ADU' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.adult ? selectedBooking.adult : '-'}}</strong>
                        </td>
                      </tr>
                      <tr>
                        <td>{{'VIEW_BOOKI_DETAI.TOT_CHIL' | translate:param}}:</td>
                        <td class="capitalize">
                          <strong>{{selectedBooking.child ? selectedBooking.child : '-'}}</strong>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6" style="margin-top: 20px;">
              <div class="row">
                <!-- Transfer Room Dropdown -->
                <div class="form-group col-md-3 col-sm-3">
                  <p class="m-0 p-0">Room No</p>
                  <select class="form-control" (change)="onRoomChange($event)">
                    <option [value]="selectedBooking.roomDetails.title" disabled selected>
                      {{selectedBooking.roomDetails.title}}</option>
                    <option *ngFor="let room of roomsList" [value]="room.id">{{room.title}}</option>
                  </select>
                </div>
                <!-- Chance Customer Type Dropdown -->
                <div class="form-group col-md-5 col-sm-5">
                  <p class="m-0 p-0">Customer Type</p>
                  <select class="form-control" (change)="changeCustomerType($event)">
                    <option [value]="selectedBooking.customer_type" disabled selected>{{selectedBooking.customer_name}}</option>
                    <ng-container *ngFor="let customer of customerTypeLists">
                      <option *ngIf="customer.id !== selectedBooking.customer_type" [value]="customer.id">
                        {{customer.customer_name}}
                      </option>
                    </ng-container>
                  </select>
                </div>
                <div class="form-group col-md-4 col-sm-4">
                  <button class="btn btn-primary" style="margin-left: 10px;" (click)="updateRoomDetails(selectedBooking)" *ngIf="transferRoomId || customerTypeId">Update</button>
                </div>
              </div>
              <div class="row">
                <div [hidden]="showReferenceUserField" class="h-[30px] ms-2col-sm-8 ms-2">
                  <input type="text" [(ngModel)]="reference_user_name" required placeholder="Enter Referance User Name" style="width: 293px;
                                                                                    margin-left: 15px;
                                                                                    border: 1px solid gainsboro;
                                                                                    height: 30px;
                                                                                    padding-left: 5px;
                                                                                    border-radius: 2px;">
                </div>
              </div>

            </div>

            <div class="col-md-6" style="margin-top: 20px;">
              <button class="btn btn-primary" (click)="viewDocumentProofs(selectedBooking.guestFiles)">View Documents</button>
              <button class="btn btn-success" (click)="acceptmodal(selectedBooking)">
                <span *ngIf="isLoading"><i class="fa fa-circle-o-notch fa-pulse fa-2x"></i></span>
                <span *ngIf="!isLoading">{{selectedBooking.bookingPaymentData.length > 0 ? 'Accept Payment' : selectedBooking.active ? 'Resend Payment Link' : 'Accept Booking'}}</span>
              </button>
              <button class="btn btn-danger" (click)="canclemodal()">{{selectedBooking.bookingPaymentData.length > 0 ? 'Reject Payment' : 'Reject Booking'}}</button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>

<!-- Model to accept Booking -->
<div class="modal " bsModal #successModal="bs-modal" role="dialog" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true" tabindex="1">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Message</h5>
      </div>
      <div class="modal-body">
        <div class="row">

          <div class="col-sm-12">
            <!-- Error Message -->
            <div *ngIf="acceptErrorMessage" [class]="successModalStyle">
              {{ acceptErrorMessage }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-secondary" (click)="closeSuccessmodel()"
              [disabled]="acceptButtonLoader">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Model For View Document -->
<div bsModal #viewDocumentModals="bs-modal" id="viewDocumentModals" class="modal fade" tabindex="-1" role="dialog"
[config]="{backdrop: false, keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div style="display: flex; justify-content: space-evenly; align-items: center;">
        <ng-container *ngFor="let imageUrl of imageUrls">
          <div>
            <img class="image-modal-view px-1 pb-1 pt-1" [src]="imageUrl.Url" alt="">
            <p class="text-center"><b>{{imageUrl.doc_name}}</b></p>
          </div>
        </ng-container>
      </div>
      <div class="modal-footer">
        <div class="btn-group">
          <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="closeDocumentModal()">
            {{'VIEW_BOOKI_DETAI.CLOSE' | translate:param}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Model to accept Booking -->
<div class="modal " bsModal #acceptBookingModal="bs-modal" role="dialog" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true" tabindex="1">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{resendPaymentLink ? 'Resend Payment Link' : 'Accept Request'}}</h5>
      </div>
      <div class="modal-body">
        <div class="row">

          <div class="col-sm-12">
            <!-- Error Message -->
            <div *ngIf="acceptErrorMessage" [class]="acceptBookingStyle">
              {{ acceptErrorMessage }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <!-- Cancel Booking Button -->
            <button type="button" class="btn btn-success" (click)="acceptBookingRequest(selectedBooking)"
              [disabled]="acceptButtonLoader">
              <span class="loader-parent-style" *ngIf="acceptButtonLoader"><i *ngIf="acceptButtonLoader"
                  class="fa fa-circle-o-notch fa-pulse fa-2x"></i></span>
              <span *ngIf="!acceptButtonLoader">{{resendPaymentLink ? 'Send' : 'Accept'}}</span>
            </button>
            <!-- Close Modal Button -->
            <button type="button" class="btn btn-secondary" (click)="closeAcceptBooking()"
              [disabled]="acceptButtonLoader">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Model to Give reason to Cancle Booking -->
<div class="modal " bsModal #cancelBookingRequest="bs-modal" role="dialog" aria-labelledby="mySmallModalLabel2"
  aria-hidden="true" tabindex="1">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Reject Request</h5>
      </div>
      <div class="modal-body">
        <div class="row">

          <div class="col-sm-12">
            <!-- Cancellation Reason Input -->
            <div class="form-group">
              <label for="cancelReason">Reason:</label>
              <textarea id="cancelReason" type="text" [(ngModel)]="cancelReason" class="form-control"
                placeholder="Enter cancellation reason"></textarea>
            </div>

            <!-- Error Message -->
            <div *ngIf="cancelErrorMessage" [class]="cancleBookingStyle">
              {{ cancelErrorMessage }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <!-- Cancel Booking Button -->
            <button type="button" class="btn btn-danger" (click)="cancelOnlinePaymentRequest()">
              <i *ngIf="cancelButtenLoader" class="fa fa-circle-o-notch fa-pulse fa-2x"></i>
              <span *ngIf="!cancelButtenLoader"> Reject</span>
            </button>
            <!-- Close Modal Button -->
            <button type="button" class="btn btn-secondary" (click)="closeRejectBooking()">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- In-voice PDF View -->
<div bsModal #invoiceModal="bs-modal" class="modal fade" tabindex="-1" role="dialog"
  [config]="{backdrop: 'static',keyboard: false}" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content" style="max-width: 842px;">
      <div *ngIf="invoiceDataForView" #invoiceHtml>
        <div class="widget-body invoice-view">
          <div class="mt billing-guest">
            <div class="invoice-header-image">
            </div>
            <div class="row invoice-view-bootstrap-convertion background-logo-image">
              <div class="col-sm-12 invoice-view-bootstrap-convertion">
                <div class="row invoice-view-bootstrap-convertion">
                  <div class="col-sm-12 invoice-view-bootstrap-convertion">
                    <p class="bill-no">
                      <strong> {{'RESERVATIONS.BILL_NO' | translate:param}} :
                        {{invoiceDataForView.bookingPayment[0] && invoiceDataForView.bookingPayment[0].bill_no}}</strong>
                      <span style="float:right;display: inline-block;">
                        <strong>
                          {{'RESERVATIONS.DATE' | translate:param}} : {{currMoment | date:'dd/MM/y'}}
                        </strong>
                      </span>
                    </p>
                    <div class="customer-name">
                      <div>
                        <span>
                          <small>
                            <strong>
                              {{'RESERVATIONS.NAME' | translate:param}} :
                            </strong>
                          </small>
                        </span>
                        <span>
                          <span class="text-capitalize">
                            {{invoiceDataForView.booking.is_alternate_bg_active ?
                            invoiceDataForView.booking.alternate_billing_guest :
                            invoiceDataForView.booking.guest.name}}
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt invoice-body">
                  <table class="table table-condence no-m-b">
                    <thead>
                      <tr>
                        <th>
                          {{'RESERVATIONS.SR_NO' | translate:param}}
                        </th>
                        <th>
                          {{'RESERVATIONS.SUB' | translate:param}}
                        </th>
                        <th>
                          {{'RESERVATIONS.AMOUNT' | translate:param}}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let fund of invoiceDataForView.fundsList; let i = index;">
                        <td>
                          {{ i + 1 }}
                        </td>
                        <td>
                          {{ fund.name }}
                        </td>
                        <td>
                          <div class="image rupee-icon-for-amount"></div>
                          {{ fund.amount }}
                        </td>
                      </tr>
                      <tr class="invoice-total-amount">
                        <td width="width: 10%;">
                        </td>
                        <td style="width: 40%;">
                          {{'RESERVATIONS.TOTAL' | translate:param}}
                        </td>
                        <td>
                          <div class="image rupee-icon-for-amount"></div>
                          {{ invoiceDataForView.paidAmount }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="dharamshala-guide-lines">
            <p class="bill-no">
              <strong>PAN NO. **********</strong>
              <span style="float:right;display: inline-block;">
                <strong>
                  {{'RESERVATIONS.RECE_SIGN' | translate:param}}
                </strong>
              </span>
            </p>
            <p>
              "As per FORM #10AC order for provisional approval of the exemption Under Section 80 G (5) of The Income
              Tax Act, 1961 bearing provisional approval # **********F20214 dated 28/05/2021 is being granted from AY
              2022 23 (FY 2021 22) to AY 2026 27 (FY 2025 26)."
              <span style="float: right; color: #4a4242 !important;font-weight: 500;">(Merchant copy*)</span>
            </p>
          </div>
          <!-- <hr style="border-top: 1px dashed rgb(0, 0, 0);margin-top: 2.6rem;">
          <div class="mt billing-guest">
            <div class="invoice-header-image">
            </div>
            <div class="row invoice-view-bootstrap-convertion background-logo-image">
              <div class="col-sm-12 invoice-view-bootstrap-convertion">
                <div class="row invoice-view-bootstrap-convertion">
                  <div class="col-sm-12 invoice-view-bootstrap-convertion">
                    <p class="bill-no">
                      <strong>{{'RESERVATIONS.BILL_NO' | translate:param}} :
                        {{invoiceDataForView.booking.bill_no}}</strong>
                      <span style="float:right;display: inline-block;">
                        <strong>
                          {{'RESERVATIONS.DATE' | translate:param}} : {{currMoment | date:'dd/MM/y'}}
                        </strong>
                      </span>
                    </p>
                    <div class="customer-name">
                      <div>
                        <span>
                          <small>
                            <strong>
                              {{'RESERVATIONS.NAME' | translate:param}} :
                            </strong>
                          </small>
                        </span>
                        <span>
                          <span class="text-capitalize">
                            {{invoiceDataForView.booking['is_alternate_bg_active'] ?
                            invoiceDataForView.booking['alternate_billing_guest'] :
                            invoiceDataForView.booking['guest.name']}}
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt invoice-body">
                  <table class="table table-condence no-m-b">
                    <thead>
                      <tr>
                        <th>
                          {{'RESERVATIONS.SR_NO' | translate:param}}
                        </th>
                        <th>
                          {{'RESERVATIONS.SUB' | translate:param}}
                        </th>
                        <th>
                          {{'RESERVATIONS.AMOUNT' | translate:param}}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let fund of invoiceDataForView.fundsList; let i = index;">
                        <td>
                          {{ i + 1 }}
                        </td>
                        <td>
                          {{ fund.name }}
                        </td>
                        <td>
                          <div class="image rupee-icon-for-amount"></div>
                          {{ fund.amount }}
                        </td>
                      </tr>
                      <tr class="invoice-total-amount">
                        <td width="width: 10%;">
                        </td>
                        <td style="width: 40%;">
                          {{'RESERVATIONS.TOTAL' | translate:param}}
                        </td>
                        <td>
                          <div class="image rupee-icon-for-amount"></div>
                          {{ invoiceDataForView.paidAmount }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="dharamshala-guide-lines">
            <p class="bill-no">
              <strong>{{'RESERVATIONS.PANNO' | translate:param}}. **********</strong>
              <span style="float:right;display: inline-block;">
                <strong>
                  {{'RESERVATIONS.RECE_SIGN' | translate:param}}
                </strong>
              </span>
            </p>
            <p>
              "As per FORM #10AC order for provisional approval of the exemption Under Section 80 G (5) of The Income
              Tax Act, 1961 bearing provisional approval # **********F20214 dated 28/05/2021 is being granted from AY
              2022 23 (FY 2021 22) to AY 2026 27 (FY 2025 26)."
              <span style="float: right; color: #4a4242 !important;font-weight: 500;">(Customer copy*)</span>
            </p>
          </div> -->
        </div>
      </div>
      <div class="modal-footer">
        <div class="btn-group">
          <button type="button" class="btn btn-md btn-inverse" aria-label="Close" (click)="invoiceModal.hide()">
            {{'RESERVATIONS.OK' | translate:param}}
          </button>
          <button class="btn btn-md btn-inverse" aria-label="Close" (click)="getHtml(invoiceDataForView.booking.id)">
            {{'RESERVATIONS.GEN_PDF' | translate:param}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- In-voice PDF View -->


<!-- Change Customer Type Modal (Before CHECKOUT) -->
<div class="modal fade" bsModal #changeCustomerTypeModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        {{bookingCheckoutDetailsType === 'customer-type-change' ? 'Change Guest Type' : 'Checkout'}}
      </div>
      <div class="modal-body">
        <form [formGroup]="customerTypeChangeForm" (ngSubmit)="checkoutAfterCustomerChange()">
          <div class="change-customer-type">
            <div class="row extra-padding">
              <div class="col-sm-4">{{'RESERVATIONS.SEL_GUE_TYP' | translate:param}}:</div>
              <div class="col-sm-8">
                <ng-select [items]="customerTypeList" formControlName="customer_id" bindLabel="text" bindValue="id" (change)="checkoutCustomerTypeChanged($event)" ></ng-select>
              </div>
            </div>
            <div class="row extra-padding">
              <div class="col-sm-4">{{'RESERVATIONS.REF_TYPE' | translate:param}}:</div>
              <div class="col-sm-8">
                <ng-select [items]="referenceUserCustomerTypeChange" formControlName="reference_id" bindLabel="text" bindValue="id" (change)="preCheckoutReferenceTypeChanged($event)" ></ng-select>
                <div
                  *ngIf="customerTypeChangeForm.controls['reference_id']?.dirty && customerTypeChangeForm.controls['reference_id']?.invalid"
                  class="alert alert-danger" style="width: 50%;">
                  <div
                    *ngIf="customerTypeChangeForm.controls['reference_id']?.errors?.required || customerTypeChangeForm.controls['reference_id']?.errors?.notEqual">
                    {{'RESERVATIONS.VALID_MSG.REF_IS_REQ' | translate:param}}</div>
                </div>
              </div>
            </div>
            <div class="row extra-padding" *ngIf="isNoteNeccessary">
              <div class="col-sm-4">{{'RESERVATIONS.NOTE' | translate:param}}:</div>
              <div class="col-sm-8">
                <textarea type="text" class="form-control" placeholder="{{'RESERVATIONS.TXT_MSG' | translate:param}}"
                  formControlName="note"></textarea>
                <div
                  *ngIf="customerTypeChangeForm.controls['note']?.dirty && customerTypeChangeForm.controls['note']?.invalid"
                  class="alert alert-danger">
                  <div *ngIf="customerTypeChangeForm.controls['note']?.errors?.required">
                    {{'RESERVATIONS.VALID_MSG.NOT_REQ' | translate:param}}</div>
                </div>
              </div>
            </div>
            <div class="row extra-padding">
              <div class="col-sm-12">
                <button type="submit" class="btn btn-sm btn-inverse">{{bookingCheckoutDetailsType ===
                  'customer-type-change' ? 'Change' : 'Continue'}}</button>
                <button type="button" class="btn btn-sm btn-inverse"
                  (click)="cancelCheckout()">{{'RESERVATIONS.VALID_MSG.CANCEL' | translate:param}}</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Change Customer Type Modal (Before CHECKOUT) -->


<!-- Change Customer Type Modal (Before CHECKOUT) -->
<div class="modal fade" bsModal #deleteGuestConfirmationTypeModal="bs-modal"
  [config]="{backdrop: 'static',keyboard: false}" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h3>
          {{modalAlertType === 'checkout' ? 'Individual Room Checkout' : 'Guest Delete'}}
        </h3>
      </div>
      <div class="modal-body">
        <div class="change-customer-type">
          <h5 *ngIf="modalAlertType === 'delete-guest'" style="line-height: 1.5;">
            {{'RESERVATIONS.CONF_DEL_GUE' | translate:param}}?
            {{'RESERVATIONS.ROOM_DEL_GUES' | translate:param}}!</h5>
          <h5 *ngIf="modalAlertType === 'checkout'" style="line-height: 1.5;">
            {{'RESERVATIONS.ARE_U_SURE_GUES' | translate:param}}?
          </h5>
          <br>
          <div class="row">
            <div class="col-sm-12">
              <button type="button" class="btn btn-sm btn-inverse"
                (click)="canDeleteRoomMainGuest = modalAlertType; deleteGuestConfirmationTypeModal.hide()">{{'RESERVATIONS.CONTINUE'
                | translate:param}}</button>
              <button type="button" class="btn btn-sm btn-inverse"
                (click)="canDeleteRoomMainGuest = false; deleteGuestConfirmationTypeModal.hide()">{{'RESERVATIONS.CANCEL'
                | translate:param}}</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Change Customer Type Modal (Before CHECKOUT) -->


<!-- Show Refund Amount Alert Modal (After Customer Type Changed) -->
<div class="modal fade" bsModal #refundAlert="bs-modal" [config]="{backdrop: 'static',keyboard: false}" tabindex="-1"
  role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h3>
          {{'RESERVATIONS.ALERT' | translate:param}}!
        </h3>
      </div>
      <div class="modal-body">
        <div class="change-customer-type">
          <div class="card-body">
            <strong>
              <h3>{{'RESERVATIONS.IMME_RETU' | translate:param}}</h3>
            </strong>
            <hr style="margin-top: 0;">
            <strong style="color: red;">{{'RESERVATIONS.REF_AMT' | translate:param}}: &nbsp;&nbsp;<i
                class="fa fa-inr"></i>{{refundAmount}}</strong>
            <div style="margin-top: 15px;">
              <small>
                <code>*</code>
                <i>
                  {{'RESERVATIONS.ADV_PREV_PAY' | translate:param}}
                  <strong *ngIf="refundAmountCause === 'customer-type-change'">
                    {{'RESERVATIONS.CHANGE_GUE_TYPE' | translate:param}}
                  </strong>
                  <strong *ngIf="refundAmountCause === 'over-booking'">
                    {{'RESERVATIONS.OVER_BOKK_CANCE' | translate:param}}
                  </strong>
                  {{'RESERVATIONS.REF_AMT_NEED' | translate:param}}.
                </i>
              </small>
            </div>
          </div>
          <br>
          <div class="row">
            <div class="col-sm-12">
              <button type="button" class="btn btn-sm btn-inverse" (click)="refundAlert.hide()">{{'RESERVATIONS.OK' |
                translate:param}}</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Show Refund Amount Alert Modal (After Customer Type Changed) -->



<!-- Booking Type GET Alert Modal -->
<div class="modal fade" bsModal #bookingBreedModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        {{'RESERVATIONS.BOOK_TYPE' | translate:param}}
      </div>
      <div class="modal-body">
        <div class="change-customer-type">
          <h5>Please select a booking type to apply the current action!</h5>
          <div class="row extra-padding">
            <div class="col-sm-4"> {{'RESERVATIONS.BOOK_TYPE' | translate:param}}:</div>
            <div class="col-sm-8">
              <ng-select [items]="bookingBreeds" [(ngModel)]="selectedBookingBreed"
                (change)="bookingBreedChanged($event)" ></ng-select>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12">
              <button type="button" class="btn btn-sm btn-inverse"
                (click)="bookingBreedModal.hide()">{{'RESERVATIONS.CONTINUE' | translate:param}}</button>
              <button type="button" class="btn btn-sm btn-inverse"
                (click)="bookinfBreedCancel = true;bookingBreedModal.hide()">{{'RESERVATIONS.CANCEL' |
                translate:param}}</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Booking Type GET Alert Modal -->

<!-- Invoice PDF Confirm modal while checkout -->
<div class="modal fade" bsModal #checkoutInvoiceModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Invoice
      </div>
      <div class="modal-body">
        <div class="row">
          <div style="padding: 15px;font-weight: 600;">
            Do you want Invoice print?
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12">
            <button type="button" class="btn btn-sm btn-inverse" (click)="checkoutPDF(true)"
              [disabled]="isActiveBtn">YES</button>
            <button type="button" class="btn btn-sm btn-inverse" (click)="checkoutPDF(false)">NO</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<!-- Checkout bypass Confirm modal -->
<div class="modal fade" bsModal #checkoutBypassModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Invoice / Checkout Bypass Confirmation
      </div>
      <div class="modal-body">
        <div class="row">
          <div style="padding: 15px;font-weight: 600;">
            Do you want to bypass checkout process?
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12">
            <button type="button" class="btn btn-sm btn-inverse"
              (click)="paymentAtcheckoutProcess(tabData, tabDataID, true)" [disabled]="isActiveBtn">YES</button>
            <button type="button" class="btn btn-sm btn-inverse"
              (click)="paymentAtcheckoutProcess(tabData, tabDataID, false)">NO</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Room Maintenance reason select model -->
<div class="modal fade" bsModal #roomMaintenanceModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Select Room Maintenance Reason
      </div>
      <div class="modal-body">
        <div class="row extra-padding">
          <div class="col-sm-5"> {{'RESERVATIONS.ROOM_MAIN_REASON' | translate:param}}:</div>
          <div class="col-sm-7">
            <ng-select [items]="maintenanceResaons" [(ngModel)]="maintenance_reason_id" bindLabel="text" bindValue="id" (change)="changeRoomMain($event)" placeholder="{{'RESERVATIONS.ROOM_MAIN_REASON' | translate:param}}" ></ng-select>
            <ng-select [items]="cleanHoursOccupied" [(ngModel)]="selectedCleanHr" *ngIf="showMntReason"(change)="changeCleanRoomHours($event)" ></ng-select>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12">
            <button type="button" class="btn btn-sm btn-inverse" (click)="markAsUnderMaintenance()"
              [disabled]="maintenance_reason_id == ''">Save</button>
            <button type="button" class="btn btn-sm btn-inverse" (click)="roomMaintenanceModal.hide()">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<!-- Checkout flow model start -->
<div class="modal fade" bsModal #checkInDynamicModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Check-in Process
      </div>
      <div class="modal-body">
        <div class="row extra-padding">
          <div class="col-sm-12" [hidden]="!dynamicCheckinSuccess">
            <h4 class="text-center my-3">
              <b>{{dynamicCheckinResponseMessage}}</b>
            </h4>
          </div>
          <div class="col-sm-12" [hidden]="dynamicCheckinSuccess">
            <div class="reservations-wrapper">
              <div id="countdown">
                <div id='tiles' class="color-full">{{display}}</div>
                <div id="left" class="countdown-label">Time Remaining</div>
              </div>
              <p class="reservations-wrapper-text">
                Put your card on reader device to start check-in process.
              </p>
            </div>



            <table class="table table-bordered">
              <tbody>
                <tr>
                  <th scope="row" width="170px">Room Number</th>
                  <td>{{dynamicCheckInData?.roomId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Door Number</th>
                  <td>{{dynamicCheckInData?.doorId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-in</th>
                  <td>{{dynamicCheckInData?.startDateAndTime}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-out</th>
                  <td>{{dynamicCheckInData?.endDateAndTime}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <!-- <button type="button" class="btn btn-sm btn-inverse" (click)="checkInDynamicModal.hide()">Save</button> -->
            <button type="button" class="btn btn-sm btn-inverse"
              (click)="checkInDynamicModal.hide();guestForm.value.abletocheckin = false;">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!--

  <h1 mat-dialog-title>Delete file</h1>
  <div mat-dialog-content>
    Would you like to delete cat.jpeg?
  </div>
  <div mat-dialog-actions>
    <button mat-button mat-dialog-close>No</button>
    <button mat-button mat-dialog-close cdkFocusInitial>Ok</button>
  </div> -->


<!-- split booking modal - start -->
<div class="modal fade" bsModal #splitbookingDynamicModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Extend Checkout Process
      </div>
      <div class="modal-body">
        <div class="row extra-padding">
          <div class="col-sm-12" [hidden]="!dynamicCheckinSuccess">
            <h4 class="text-center my-3">
              <b>{{dynamicCheckinResponseMessage}}</b>
            </h4>
          </div>
          <div class="col-sm-12" [hidden]="dynamicCheckinSuccess">
            <div class="reservations-wrapper">
              <div id="countdown">
                <div id='tiles' class="color-full">{{display}}</div>
                <div id="left" class="countdown-label">Time Remaining</div>
              </div>
              <p class="reservations-wrapper-text">
                Put your card on reader device to start extend checkout process.
              </p>
            </div>
            <table class="table table-bordered">
              <tbody>
                <tr>
                  <th scope="row" width="170px">Room Number</th>
                  <td>{{splitBookingData?.roomId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Door Number</th>
                  <td>{{splitBookingData?.doorId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-in</th>
                  <td>{{splitBookingData?.startDateAndTime}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-out</th>
                  <td>{{splitBookingData?.endDateAndTime}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-sm btn-inverse"
              (click)="splitbookingDynamicModal.hide();splitBookingForm.valid = false;">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- split booking modal - end -->

<!-- transfer booking modal - start -->
<div class="modal fade" bsModal #TransferDynamicModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Split Booking Process
      </div>
      <div class="modal-body">
        <div class="row extra-padding">
          <div class="col-sm-12" [hidden]="!dynamicCheckinSuccess">
            <h4 class="text-center my-3">
              <b>{{dynamicCheckinResponseMessage}}</b>
            </h4>
          </div>
          <div class="col-sm-12" [hidden]="dynamicCheckinSuccess">
            <div class="reservations-wrapper">
              <div id="countdown">
                <div id='tiles' class="color-full">{{display}}</div>
                <div id="left" class="countdown-label">Time Remaining</div>
              </div>
              <p class="reservations-wrapper-text">
                Put your card on reader device to start Transfer Room booking process.
              </p>
            </div>
            <table class="table table-bordered">
              <tbody>
                <tr>
                  <th scope="row" width="170px">Room Number</th>
                  <td>{{TransferRoomData?.newRoomId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Door Number</th>
                  <td>{{TransferRoomData?.newDoorId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-in</th>
                  <td>{{TransferRoomData?.startDateAndTime}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-out</th>
                  <td>{{TransferRoomData?.endDateAndTime}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-sm btn-inverse"
              (click)="TransferDynamicModal.hide();splitBookingForm.valid = false;">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- transfer booking modal - end -->



<!-- checkout flow modal - start -->
<div class="modal fade" bsModal #checkoutFlowDynamicModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Checkout Process
      </div>
      <div class="modal-body">
        <div class="row extra-padding">
          <div class="col-sm-12" [hidden]="!dynamicCheckinSuccess">
            <h4 class="text-center my-3">
              <b>{{dynamicCheckinResponseMessage}}</b>
            </h4>
          </div>
          <div class="col-sm-12" [hidden]="dynamicCheckinSuccess">
            <div class="reservations-wrapper">
              <div id="countdown">
                <div id='tiles' class="color-full">{{display}}</div>
                <div id="left" class="countdown-label">Time Remaining</div>
              </div>
              <p class="reservations-wrapper-text">
                Put your card on reader device to start checkout process.
              </p>
            </div>
            <table class="table table-bordered">
              <tbody>
                <tr>
                  <th scope="row" width="170px">Room Number</th>
                  <td>{{checkoutData?.roomId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Door Number</th>
                  <td>{{checkoutData?.doorId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-in</th>
                  <td>-</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-out</th>
                  <td>-</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-sm btn-inverse"
              (click)="checkoutFlowDynamicModal.hide();">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- checkout flow modal - end -->


<!-- clean room card process - start -->
<div class="modal fade" bsModal #cleanRoomFlowDynamicModal="bs-modal" [config]="{backdrop: 'static',keyboard: false}"
  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        Room Clean Card Maintenance Process
      </div>
      <div class="modal-body">
        <div class="row extra-padding">
          <div class="col-sm-12" [hidden]="!dynamicCheckinSuccess">
            <h4 class="text-center my-3">
              <b>{{dynamicCheckinResponseMessage}}</b>
            </h4>
          </div>
          <div class="col-sm-12" [hidden]="dynamicCheckinSuccess">
            <div class="reservations-wrapper">
              <div id="countdown">
                <div id='tiles' class="color-full">{{display}}</div>
                <div id="left" class="countdown-label">Time Remaining</div>
              </div>
              <p class="reservations-wrapper-text">
                Put your card on reader device to start Room Clean Card Maintenance process.
              </p>
            </div>
            <table class="table table-bordered">
              <tbody>
                <tr>
                  <th scope="row" width="170px">Room Number</th>
                  <td>{{cleanRoomData?.roomId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Door Number</th>
                  <td>{{cleanRoomData?.doorId}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-in</th>
                  <td>{{cleanRoomData?.startDateAndTime}}</td>
                </tr>
                <tr>
                  <th scope="row" width="170px">Check-out</th>
                  <td>{{cleanRoomData?.endDateAndTime}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <button type="button" class="btn btn-sm btn-inverse"
              (click)="cleanRoomFlowDynamicModal.hide();">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- clean room card process - end -->
