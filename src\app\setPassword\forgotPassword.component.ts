import { Router } from '@angular/router';
import { UserService } from './../shared/services/user.service';
import { CustomValidators } from 'ng2-validation';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

declare var Messenger: any;

@Component({
    selector: 'forgot-password',
    templateUrl: './forgetPassword.component.html',
    providers: [UserService,TranslateEventService]
})

export class ForgotPasswordComponent implements OnInit {
    config: any;// New Change ****
    forgetPasswordForm: FormGroup

    private sub: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        config: AppConfig,
        private _fb: FormBuilder,
        private US: UserService,
        private router: Router,
        public translate: TranslateService,// New Change ****
        private TS: TranslateEventService, // New Change ****
        
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() {
        this.forgetPasswordForm = this._fb.group({
            email: ['', [Validators.required, CustomValidators.email]]
        })
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    sendLink() {
        if (this.forgetPasswordForm.valid) {
            this.sub = this.US.sendLink(this.forgetPasswordForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.router.navigate([`login`]);
                        Messenger().post({  hideAfter: 5,
                            message: "Link has been sent to this email.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                })
        }
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}