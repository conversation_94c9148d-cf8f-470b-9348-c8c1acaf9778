import { findIndex } from 'rxjs/operator/findIndex';
import { Component, OnInit, Input, Output, EventEmitter, OnDestroy, OnChanges } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { UserService } from '../../shared/services/user.service';
import { CustomValidators } from 'ng2-validation';
import { Select2OptionData } from "ng2-select2";
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;
@Component({
    selector: 'edit-discount-reference-user',
    templateUrl: '../discountReferenceUser.actions.html'
})

export class EditDiscountReferenceUserComponent implements OnInit {

    referenceUserForm: FormGroup;
    public pageType: string = "Edit";
    public customerTypes: any[] = [];
    public select2Option: Select2.Options = {
        width: '100%',
        multiple: true,
    };
    @Input() data;
    @Input() getHiddeneditRU;
    @Input() userList: Array<Select2OptionData>;
    @Input() referenceList: Array<Select2OptionData>;
    @Output() sendHiddeneditRU = new EventEmitter();
    @Output() listeditedData = new EventEmitter();
    user = '';
    public bookingTypeOptions = {
        width: '100%',
    };
    private sub: any;
    constructor(
        private _fb: FormBuilder,
        private US: UserService,
        public translate: TranslateService
    ) { 
        translate.get('REF_USER.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.buildForm();
        this.userList = jQuery.map(this.userList, function (obj: any) {
            return { id: obj.user_id, text: obj.first_name.toUpperCase() + ' ' + obj.last_name.toUpperCase() };
        });
        this.referenceList = jQuery.map(this.referenceList, function (obj: any) {
            return { id: obj.id, text: obj.name.toUpperCase()};
        })
        this.referenceUserForm.patchValue(this.data);
        this.user = this.data.first_name.toUpperCase() + ' ' + this.data.last_name.toUpperCase();
        this.referenceUserForm.controls['reference_user_ids'].patchValue(this.data.reference_user_ids ? this.data.reference_user_ids : []);
        if (this.data.reference_user_ids && this.data.reference_user_ids.length > 0) {
            for (let i = 0; i < this.data.reference_user_ids.length; i++) {
                this.customerTypes.push(this.referenceList[this.findIndex(parseInt(this.data.reference_user_ids[i]), "id", this.referenceList)]);
            };
        }
        this.referenceList.sort(function (x, y) {
            let a = x.text.toUpperCase(),
                b = y.text.toUpperCase();
            return a == b ? 0 : a > b ? 1 : -1;
        });
    }
    // ngOnChanges(changes: any) {
    //     console.log("Making changes : ");

    //     this.buildForm(changes.data.currentValue);
    // }

    findIndex(searchTearm: any, property: any, targetArray: any[]) {
        for (let i = 0; i < targetArray.length; i++) {
            if (searchTearm === targetArray[i][property]) { return i };
        }
        return -1;
    }

    buildForm() {
        this.referenceUserForm = this._fb.group({
            user_id: ['', Validators.required],
            allow_to_update_customer_type: [false],
            allow_to_update_reference_user:[false],
            reference_user_ids: [[], Validators.required]
        })
    }
    toggleChild() {
        this.referenceUserForm.reset();
        this.getHiddeneditRU = !this.getHiddeneditRU;
        this.sendHiddeneditRU.emit(this.getHiddeneditRU);
    }
    saveReferenceUser() {
        if (this.referenceUserForm.valid) {
            // console.log("this.referenceUserForm.value : ", this.referenceUserForm.value);
            this.sub = this.US.updateDisReferenceUser(this.data.id, this.referenceUserForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        // --- Resetting the form is not required if the edit form it not Hidden after editing--- // 
                        this.referenceUserForm.reset();
                        let response = res.data;
                        this.listeditedData.emit(response);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.referenceUserForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({
                hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    customerTypeChanged(event: any) {
        let referenceType = <FormControl>this.referenceUserForm.controls['reference_user_ids'];
        let selectedCustomerIds: any[] = referenceType.value;
        selectedCustomerIds.push(event.id.toString());
        referenceType.patchValue(selectedCustomerIds);

    }

    removedCustomerType(event: any) {
        let referenceType = <FormControl>this.referenceUserForm.controls['reference_user_ids'];
        let selectedCustomerIds: any[] = referenceType.value;
        selectedCustomerIds.splice(selectedCustomerIds.indexOf(event.id.toString()), 1);
        referenceType.patchValue(selectedCustomerIds);
    }

    changeUser(event: any) {
        let user = <FormControl>this.referenceUserForm.controls['user_id'];
        this.user = event.value;
        user.patchValue(event.value);
    }

    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}