import { CustomValidators } from 'ng2-validation';
import { Location } from '@angular/common';
import { Component, OnInit, Output, EventEmitter, Input, OnChanges, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { FormControl } from '@angular/forms/src/model';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'edit-agent-location',
    templateUrl: '../agent-location.action.html'
})

export class EditAgentLocationComponent implements OnInit {
    public selectedState: any;
    public pageType: string = "Edit";
    public agentLocationForm: FormGroup;
    private sub: any;
    @Input() stateList;
    @Input() selectedAgent;
    @Output() updateToList = new EventEmitter();
    @Output() hideEditEvent = new EventEmitter();
    select2Options: any = {
        width: '100%'
    };
    constructor(
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        public translate: TranslateService;
    ) { 
        translate.get('AGENT_LOCATION.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.selectedState = this.selectedAgent.state;
        console.log("this.selectedState : ",this.selectedState);
        this.initForm();
    }
    ngOnChanges(change: any) {
        if (this.agentLocationForm) {
            console.log("ON changes : ", change);
            this.selectedState = change.selectedAgent.currentValue.state;
            this.agentLocationForm.patchValue(change.selectedAgent.currentValue);
        }
    }
    initForm() {
        this.agentLocationForm = this._fb.group({
            name: ['', Validators.required],
            state: ['', [Validators.required, CustomValidators.notEqual("000000")]],
            status: [true, Validators.required]
        });
        this.agentLocationForm.patchValue(this.selectedAgent);
        console.log("Form : ",this.agentLocationForm.value);
    }
    stateChanged(event: any) {
        let stateName = this.stateList[this.findIndex(event?.id,"id",this.stateList)]?.text;
        this.agentLocationForm.get('state').patchValue(stateName);
        console.log("Location : ",this.agentLocationForm.value, stateName);
    }
    saveAgentType() {
        if (this.agentLocationForm.valid) {
            this.sub = this.DS.updateAgentLocation(this.selectedAgent.id,this.agentLocationForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.updateToList.emit(res.data);
                        this.agentLocationForm.reset();
                        this.hideComponent();
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.agentLocationForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            for(let field in this.agentLocationForm.controls) {
                this.agentLocationForm.controls[field].markAsDirty();
                this.agentLocationForm.controls[field].markAsTouched();
            }
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    findIndex(searchTerm: any, property: any, targetArray: any[]) {
        for(let i = 0; i < targetArray.length; i++) {
            if(targetArray[i][property] === searchTerm) { return i}
        }
        return -1;
    }
    hideComponent() {
        this.hideEditEvent.emit();
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}