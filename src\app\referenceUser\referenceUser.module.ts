import { NgSelectModule } from '@ng-select/ng-select';
import { NgModule, Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { EditReferenceUserComponent } from './+edit/edit.referenceUser.component';
import { AddReferenceUserComponent } from './+add/add.referenceUser.component';
import { ReferenceUserComponent } from './referenceUser.component';

import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

// all routes related to this Role module.
export const routes = [
  { path: '', component: ReferenceUserComponent, pathMatch: 'full' }
]

@NgModule({
  imports: [
    FormsModule,
    CommonModule,
    NgSelectModule,
    TooltipModule.forRoot(),
    DataTableModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    TranslateModule.forRoot({
      loader:{ 
          provide: TranslateLoader, 
          useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
          deps: [HttpClient] 
      }})
  ],
  exports: [],
  declarations: [
    ReferenceUserComponent,
    AddReferenceUserComponent,
    EditReferenceUserComponent
  ],
  providers: [],
})
export class ReferenceUserModule { }
