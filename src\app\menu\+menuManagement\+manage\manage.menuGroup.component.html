<section class="widget">
  <header>
    <h4 class=""><span class=""><i class="fa fa-tasks"></i>&nbsp;&nbsp;{{'MENU_MANAGE.MENU_GRP' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left">
    <li class="breadcrumb-item">{{'MENU_MANAGE.MENU_MANAGE' | translate:param}}</li>
    <li class="breadcrumb-item "><a [routerLink]="['../../']">{{'MENU_MANAGE.MENU_GRP' | translate:param}}</a></li>
    <li class="breadcrumb-item active">{{'MENU_MANAGE.MANG_MENU' | translate:param}}</li>
  </ol>

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <div class="row">
        <!-- role's assigned menus -->
        <div class="col-sm-4 col-md-4">
          <div class="row">
            <div class="col-sm-8">
              <h5>{{'MENU_MANAGE.AUTHO_MENU_LIS' | translate:param}}</h5>
            </div>
            <div class="col-sm-4" style="margin-top:10px;">
              <div class="badge" (click)="addMenuItem()" tooltip="{{'MENU_MANAGE.ADD_NEW_MENU' | translate:param}}" placement="top">
                <i class="fa fa-plus"></i>
              </div>
            </div>
          </div>
          <hr>
          <div class="dd" id="nestable1">
            <div class="managemenu-list " *ngIf="!menus">
              <p class="text-center" style="margin-bottom: 0">{{'MENU_MANAGE.NO_MENU' | translate:param}}</p>
            </div>
            <ol class="managemenu-list dd-list" *ngIf="menus?.length > 0">
              <li class="dd-item" *ngFor="let menu of menus" [attr.data-id]="menu.id">
                <div class="float-xs-left dd-handle">
                  <i class="fa fa-bars"></i>
                </div>
                <span>
                  <i class="fa fa-{{menu.icon}}"></i>&nbsp;{{menu.title}}
                   </span>
                <span class="float-xs-right managemenu-list-actions">
                            <a href="javascript:void(0)" (click)="editMenuName(menu)" role="button">
                              <i class="fa fa-pencil"></i></a>
                            <a href="javascript:void(0)" (click)="deleteMenu(menu.id)" role="button">
                              <i class="fa fa-trash-o"></i></a>
                        </span>
                <div class="clearfix"></div>
                <ol class="dd-list" *ngIf="menu.children">
                  <li class="dd-item" *ngFor="let childMenu of menu.children" [attr.data-id]="childMenu.id">
                    <div class="float-xs-left dd-handle">
                      <i class="fa fa-bars"></i>
                    </div>
                    <span>
                      <i class="fa fa-{{childMenu.icon}}"></i>&nbsp;{{ childMenu.title}}
                      </span>
                    <span class=" managemenu-list-actions">
                                              <a href="javascript:void(0)" role="button">
                                                <i class="fa fa-pencil" (click)="editMenuName(childMenu)"></i></a>
                                              <a href="javascript:void(0)" (click)="deleteMenu(childMenu.id)" role="button">
                                                <i class="fa fa-trash-o"></i></a>
                                          </span>

                  </li>
                </ol>

              </li>
            </ol>
            <br>
            <button *ngIf="menus && !isMenu" (click)="updateMenu()" class="btn btn-sm btn-inverse"><i class="fa fa-check"></i>&nbsp;&nbsp;{{'MENU_MANAGE.SAVE' | translate:param}}</button>
            <button *ngIf="!menus" (click)="addMenuItem()" class="btn btn-sm btn-inverse" tooltip="{{'MENU_MANAGE.ADD_NEW_MENU' | translate:param}}" placement="top"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{'MENU_MANAGE.ADD' | translate:param}}</button>
          </div>
        </div>
        <!-- deleted menus-->
        <!-- <div class="col-sm-4 col-md-4">
          <h5>Unauthorised Menu List</h5>
          <hr>
          <div class="dd" id="nestable2">
            <div class="managemenu-list " *ngIf="menusRemoved?.length == 0">
              <p class="text-center" style="margin-bottom: 0">No Menu</p>
            </div>
            <ol class="managemenu-list dd-list" *ngIf="menusRemoved?.length > 0">
              <li class="dd-item" *ngFor="let menu of menusRemoved" [attr.data-id]="menu.id">
                <div class="float-xs-left dd-handle">
                  <i class="fa fa-bars"></i>
                </div>
                <span>
                  <i class="fa fa-{{menu.icon}}"></i>&nbsp; {{menu.title}}
                   </span>
                <span class="float-xs-right managemenu-list-actions">
                    <a href="javascript:void(0)" role="button" (click)="editMenuName(menu.id)">
                                                <i class="fa fa-pencil"></i></a>
                            <a href="javascript:void(0)" (click)="addMenuItem(menu)" role="button">
                              <i class="fa fa-plus"></i></a>
                           
                        </span>
                <div class="clearfix"></div>
                <ol class="dd-list" *ngIf="menu.children">
                  <li class="dd-item" *ngFor="let childMenuRemoved of menu.children" [attr.data-id]="childMenuRemoved.id">
                    <div class="float-xs-left dd-handle">
                      <i class="fa fa-bars"></i>
                    </div>
                    <span>
                      <i class="fa fa-{{childMenuRemoved.icon}}"></i>&nbsp;{{ childMenuRemoved.title}}
                    </span>
                    <span class=" managemenu-list-actions">
                      <a href="javascript:void(0)" role="button">
                        <i class="fa fa-pencil" (click)="editMenuName(childMenuRemoved.id)"></i>
                      </a>
                      <a href="javascript:void(0)" (click)="addMenuItem(childMenuRemoved)" role="button">
                        <i class="fa fa-plus"></i>
                      </a>
                    </span>
                
                  </li>
                </ol>
              </li>
            </ol>
          </div>
        </div> -->
        <!-- add/edit menu form -->
        <div class="col-sm-4 col-md-4" *ngIf="isMenu">
          <h5 style="margin-bottom:20px;">{{isTypeAdd ? ('MENU_MANAGE.ADD_NEW_MENU_ITE' | translate:param) : ('MENU_MANAGE.EDIT_MENU_ITEM' | translate:param) }}</h5>
          <hr>
          <div class="editForm">
            <form [formGroup]="editmenu" (ngSubmit)="saveEditMenu()">
              <div class="form-group">
                <input type="hidden" formControlName="id">
              </div>
              <div class="form-group">
                <input type="text" placeholder="Menu name" formControlName="title" class="form-control">
                  <span class="errMsg __fromBackend" *ngIf="editmenu.controls['title'].errors?.backend">{{editmenu.controls['title'].errors?.backend}}</span>
                  <span class="errMsg" *ngIf="!editmenu.controls.title.valid && !editmenu.controls.title.pristine">
                    <span [hidden]="!editmenu.controls.title.errors?.required">
                      {{'MENU_MANAGE.MENU_REQ' | translate:param}}
                    </span>
                  </span>
              </div>
              <div class="form-group row">
                <label class="col-md-3 col-form-label" for="default-select1">{{'MENU_MANAGE.PARENT' | translate:param}}</label>                
                <input type="hidden" formControlName="parent_id">
               <ng-select id="default-select1" class="col-md-9" [items]="getParentList()" [ngModel]="initParentValue" (change)="parentIdChanged($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Parent"></ng-select>
                <span class="errMsg" *ngIf="editmenu.controls['parent_id'].errors?.backend">{{editmenu.controls['parent_id'].errors?.backend}}</span>                
                <span class="errMsg" *ngIf="!editmenu.controls.parent_id.valid && !editmenu.controls.parent_id.pristine">
                  <span [hidden]="!editmenu.controls.parent_id.errors.required">{{'MENU_MANAGE.PARE_REQ' | translate:param}}</span>
                </span>
              </div>
              <div class="form-group row">
                <label class="col-md-3 col-form-label" for="default-select2">Router</label>                
                <input type="hidden" formControlName="router_link">
               <ng-select id="default-select2" class="col-md-9" [items]="getRouterList()" [ngModel]="initRouterValue" (change)="routerIdChanged($event)" bindLabel="text" bindValue="id" [searchable]="true" [clearable]="true" placeholder="Select Router"></ng-select>
                <span class="errMsg" style="padding-left: 15px;" *ngIf="editmenu.controls['router_link'].errors?.backend">{{editmenu.controls['router_link'].errors?.backend}}</span>                
                <span class="errMsg" *ngIf="!editmenu.controls.router_link.valid && !editmenu.controls.router_link.pristine">
                  <span [hidden]="!editmenu.controls.router_link.errors.required">{{'MENU_MANAGE.PARE_REQ' | translate:param}}</span>
                </span>
              </div>
              <div class="form-group">
                <input type="text" placeholder="Custom URL" formControlName="custom_url" (blur)="clearRouterErrors()" class="form-control" url>
                <span class="errMsg" *ngIf="editmenu.controls['custom_url'].errors?.backend">{{editmenu.controls['custom_url'].errors?.backend}}</span>                
                <span class="errMsg" *ngIf="!editmenu.controls.custom_url.valid && !editmenu.controls.custom_url.untouched">
                  <span [hidden]="!editmenu.controls.custom_url.errors?.pattern">
                    {{'MENU_MANAGE.INVA_URL' | translate:param}}.
                  </span>
                  <!-- <pre>{{editmenu.controls.custom_url.errors.pattern}}</pre> -->
                </span>
              </div>
              <div class="form-group row">
                <label class="col-md-3 col-form-label" for="default-select">{{'MENU_MANAGE.STATUS' | translate:param}}</label>
                <div class="col-md-8 ">
                  <div class="radio-horizontal">
                    <div class="abc-radio">
                      <input type="radio" formControlName="is_enabled" id="radio-1" [value]="true">
                      <label for="radio-1">
                        {{'MENU_MANAGE.ACTIVE' | translate:param}}
                      </label>
                    </div>
                    <div class="abc-radio">
                      <input type="radio" formControlName="is_enabled" id="radio-2" [value]="false">
                      <label for="radio-2">
                        {{'MENU_MANAGE.INACTIVE' | translate:param}}
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <br> -->
              <div class="form-group row">
                <label class="col-md-3 col-form-label" for="menu-icon">Icon</label>                
                <input id="menu-icon" type="hidden" formControlName="icon">
                <div class="btn-group">
                  <button type="button" class="btn btn-default iconpicker-component"><i class="fa fa-{{menuIcon}}"></i></button>
                  <button type="button" class="icp icp-dd btn btn-default dropdown-toggle" [attr.data-selected]="menuIcon" data-toggle="dropdown">
                    <span class="caret"></span>
                    <span class="sr-only">{{'MENU_MANAGE.TOGGEL_DROP' | translate:param}}</span>
                </button>
                  <div class="dropdown-menu"></div>
                </div>
              </div>
              <div class="btn-group">
                <button class="btn btn-sm btn-inverse" [disabled]="!editmenu.valid" type="submit">{{'MENU_MANAGE.SAV_MENU' | translate:param}}</button>
                <button class="btn btn-sm btn-default" (click)="hideEditMenu()">{{'MENU_MANAGE.CANCEL' | translate:param}}</button>
              </div>
            </form>
          </div>
        </div>
      </div>

    </div>
  </div>
</section>
