<!-- Start Header -->
<div class="c-main-booking-header-wrap">
    <div class="container">
      <div class="row align-items-center">
        <!-- Logo Column -->
        <div class="col-12 col-md-6 mb-3 mb-md-0 d-flex justify-content-center justify-content-md-start">
          <img src="../../assets/img/logo.svg" alt="Logo" class="img-fluid" />
        </div>
  
        <!-- Text Column -->
        <div class="col-12 col-md-6 text-center">
          <h2 class="c-main-booking-header-text mb-0">
            Online Payment
            <br />
            <h5 class="d-block">Helpline number: +91 8425001008</h5>
          </h2>
        </div>
      </div>
    </div>
  </div>
  <!-- End Header -->

<!-- Payment Form -->
<div class="container payment-container" *ngIf="!showEmptyTemplate">
    <h2 class="text-center font-weight-bold">Your Details</h2>

    <!-- User Details Section -->
    <div class="row mt-4" style="margin-top: 2.5rem;">
        <div class="col-md-6">
            <p class="mb-2">
                <strong>User Name: <span class="text-primary">{{ tokenData.username }}</span>
                </strong>
            </p>
        </div>
        <div class="col-md-6">
            <p class="mb-2">
                <strong>Mobile: <span class="text-primary">{{ tokenData.contact }}</span></strong>
            </p>
        </div>
        <div class="col-md-6">
            <p class="mb-2">
                <strong>Total People: <span class="text-primary">{{ tokenData.child + tokenData.adult }}</span></strong>
            </p>
        </div>
        <div class="col-md-6">
            <p class="mb-2">
                <strong>Check-In: <span class="text-primary">{{ ReturnDate(tokenData.check_in) }}</span></strong>
            </p>
        </div>
        <div class="col-md-6">
            <p class="mb-2">
                <strong>Check-Out: <span class="text-primary">{{ ReturnDate(tokenData.check_out) }}</span></strong>
            </p>
        </div>
        <div class="col-md-6">
            <p class="mb-2">
                <strong>Room Category: <span class="text-primary">{{ tokenData.room_category }}</span></strong>
            </p>
        </div>
        <div class="col-md-6">
            <p class="mb-2">
                <strong>Maintanence Charge : <span class="text-success">₹{{ tokenData.total_amount }}</span></strong>
            </p>
        </div>
    </div>

    <div class="qr-code-container text-center mt-4">
        <h4 class="font-weight-bold">Scan QR Code for Payment</h4>
        <img src="../../assets/img/online-payment-qr.jpg" alt="QR Code" class="qr-code" />
    </div>
    <!-- <div class="mb-5" (click)="copyUpi()"> -->
        <div class="mb-5">
        <span style="padding-right: 5px;">Pay Via Upi:</span>
        <span>
            <i><b [ngClass]="{'text-primary':!isUpiCopied, 'text-success':isUpiCopied}">muktajivanswamibapa@mahb </b></i>
        </span>
        <!-- <span class="copy"> {{isUpiCopied? 'Copied' : 'Copy'}} <i [ngClass]="{'fa fa-check':isUpiCopied,'fa fa-clipboard':!isUpiCopied}"></i></span> -->
    </div>

    <form [formGroup]="paymentForm" (ngSubmit)="submitPayment()">
        <div class="form-group">
            <label for="transactionId"><strong>Transaction ID</strong></label>
            <input type="text" id="transactionId" formControlName="transactionId"
                class="form-control form-control-lg" />
            <!-- Validation error for transactionId -->
            <div *ngIf="paymentForm.get('transactionId').invalid && paymentForm.get('transactionId').touched">
                <span class="text-danger" *ngIf="paymentForm.get('transactionId').hasError('required')">
                    Transaction ID is required.
                </span>
            </div>
        </div>

        <div class="form-group">
            <label for="amount"><strong>Amount</strong></label>
            <input type="number" id="amount" [value]="tokenData.total_amount" disabled class="form-control form-control-lg" />
        </div>
        <div class="form-group">
            <label for="upi_id"><strong>UPI ID</strong></label>
            <input type="text" id="upi_id" formControlName="upi_id" class="form-control form-control-lg" />
            <!-- Validation error for UPI ID -->
            <div *ngIf="paymentForm.get('upi_id').invalid && paymentForm.get('upi_id').touched">
                <span class="text-danger" *ngIf="paymentForm.get('upi_id').hasError('required')">
                    UPI ID is required.
                </span>
                <span class="text-danger" *ngIf="paymentForm.get('upi_id').hasError('pattern')">
                    Invalid UPI ID format.
                </span>
            </div>
        </div>
        <div class="form-group">
            <label for="bank_name"><strong>Bank Name</strong></label>
            <input type="text" id="bank_name" formControlName="bank_name" class="form-control form-control-lg" />
            <!-- Validation error for bank_name -->
            <div *ngIf="paymentForm.get('bank_name').invalid && paymentForm.get('bank_name').touched">
                <span class="text-danger" *ngIf="paymentForm.get('bank_name').hasError('required')">
                    Bank Name is required.
                </span>
            </div>
        </div>
        <div *ngIf="pancard_field" class="form-group">
            <label for="pancard_number"><strong>Pan Card number</strong></label>
            <input type="text" id="pancard_number" formControlName="pancard_number"
                class="form-control form-control-lg" />
            <!-- Validation error for pancard_number -->
            <div *ngIf="paymentForm.get('pancard_number').invalid && paymentForm.get('pancard_number').touched">
                <span class="text-danger" *ngIf="paymentForm.get('pancard_number').hasError('pattern')">
                    Invalid Pan Card number.
                </span>
            </div>
        </div>
        <div class="form-group">
            <label for="screenshot"><strong>Upload Payment Screenshot</strong></label>
            <input type="file" #screenshotInput id="screenshot" formControlName="screenshot"
                (change)="onFileChange($event)" class="form-control-file" />
            <!-- Validation error for screenshot -->
            <div *ngIf="paymentForm.get('screenshot').invalid && paymentForm.get('screenshot').touched">
                <span class="text-danger" *ngIf="paymentForm.get('screenshot').hasError('required')">Screenshot is
                    required.</span>
                <span class="text-danger" *ngIf="paymentForm.get('screenshot').hasError('invalidFileSize')">File size
                    must be less than 5 MB.</span>
                <span class="text-danger" *ngIf="paymentForm.get('screenshot').hasError('invalidFileType')">please
                    upload in jpeg, png, jpg, jpeg* format.</span>
            </div>
        </div>
        <div class="notes">
            <div class="hindi">
                <p><b>नोट 1:</b> कृपया हमारे आधिकारिक भुगतान क्यूआर कोड या लिंक के अलावा किसी अन्य माध्यम से भुगतान न
                    करें। अन्य किसी भी भुगतान के लिए हम जिम्मेदार नहीं होंगे। यदि आपके कोई प्रश्न हों, तो हमारी
                    हेल्पलाइन नंबर पर संपर्क करें।</p>
                <p><b>नोट 2:</b>जब तक हमारी तरफ से बुकिंग पुष्टि का संदेश नहीं आता, तब तक आपकी बुकिंग कन्फर्म नहीं मानी
                    जाएगी।</p>
                <p><b>नोट 3:</b>ऊपर दिया गया मेंटेनेंस चार्ज केवल एक दिन के लिए है।</p>
            </div>
            <hr>
            <div class="english">
                <p><b>Note 1:</b> Please do not make any payments through any method other than our official payment QR
                    code or link. We will not be responsible for any other payments. If you have any questions, please
                    contact our helpline number.</p>
                <p><b>Note 2:</b> Your booking will not be confirmed until you receive a confirmation message from our
                    side.</p>
                <p><b>Note 3:</b> The maintenance charge mentioned above is for one day only.</p>
            </div>
        </div>
        <button type="submit" class="btn btn-primary btn-lg btn-block">Submit Payment</button>
    </form>
</div>

<!-- Error Template -->
<div *ngIf="showEmptyTemplate" class="error-container">
    <div class="error-content">
        <h1 class="error-title">Oops! Page Not Found</h1>
        <p class="error-message">{{ showTokenErrorMsg }}</p>
    </div>
</div>

<!-- Modal for Success/Error Messages
<div *ngIf="showModal" class="modal">
    <div class="modal-content">
        <p [ngClass]="{'text-danger': isError, 'text-success': !isError}">{{ modalMessage }}</p>
        <button (click)="closeModal()" class="btn btn-secondary">OK</button>
    </div>
</div> -->

<!-- this modal is to show messages -->
<div class="modal fade" *ngIf="!showEmptyTemplate" bsModal #showModal="bs-modal" role="dialog" aria-labelledby="showModal" aria-hidden="true">
  <div class="modal-dialog modal-md">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Booking request sent</h5>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <div [ngClass]=" {'alert alert-danger': isError, 'alert alert-success': !isError}">
              <h4> {{modalMessage? modalMessage: ''}}</h4>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-12 text-right">
            <a href="https://www.swaminarayangadi.com/"><button type="button" class="btn btn-success btn-block" (click)="closeModal()">Visit Site</button></a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>