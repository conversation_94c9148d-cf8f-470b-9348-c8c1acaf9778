<section class="widget">
  <header>
    <h4><span class=""><i class="fa fa-cubes"></i>&nbsp;&nbsp;Inventory</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right">
    <a [routerLink]="['add']" class="display-inline-block btn btn-sm btn-inverse"><i class="fa fa-plus"></i>&nbsp;&nbsp;Add</a>
    <button class="display-inline-block btn btn-sm btn-default" (click)="showAdvance = !showAdvance"><i class="fa fa-refresh"></i>&nbsp;&nbsp;Reset</button>
    <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" placeholder="Search">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body">
    <div class="mt">
         <table class="table table-condence no-m-b small-footprint" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
        <tr>
          <th>
            <mfDefaultSorter by="id">#</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="name">Title</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="date">Type</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="roomno">Status</mfDefaultSorter>
          </th>
          <th class="no-sort">
            <mfDefaultSorter>Action</mfDefaultSorter>
          </th>
          
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let ds of mf.data">
          <td>{{ds.id}}</td>
          <td><span class="fw-semi-bold">{{ds.name}}</span></td>
         <td>{{ds.type}}</td>
         <td>
             <span *ngIf="ds.status" class="text-success">Active</span>
             <span *ngIf="!ds.status" class="text-danger">Inactive</span>
         </td>
          <td class="width-100 text-center">
            <a [routerLink]="['edit',ds.id]" class="btn btn-sm">Edit</a>
          </td>
        </tr>
        <tr *ngIf="mf.data.length === 0">
          <td colspan="100">
            No matches
          </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="12">
            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
          </td>
        </tr>
        </tfoot>
      </table>
    </div>
  </div>
</section>
