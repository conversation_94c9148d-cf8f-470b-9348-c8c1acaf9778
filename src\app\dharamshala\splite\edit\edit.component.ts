import { Component, OnInit, Input } from '@angular/core';
import { FormBuilder ,FormGroup} from '@angular/forms';
import { DharamshalaService } from 'app/shared/services/dharamshala.service';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';

@Component({
    selector: 'splite-edit',
    templateUrl: './edit.component.html'
})

export class SpliteEditComponent implements OnInit{
    public mydata: any
    public formData : FormGroup;
    // @Input('mydata') mydata:any
    constructor(
        private router : Router,
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        public translate: TranslateService
    ){
        
    }

    ngOnInit(){
        this.initform()
        this.DS.getSplite().subscribe((res) =>{
            if(res.status === "success"){

                this.mydata = res.data
                this.formData.patchValue({
                    mexamount:this.mydata[0].taxable_amount,
                    minamount:this.mydata[0].split_receipt_base_amount
                })
            }
        })
        
        // console.log("heyyyyy",this.mydata)
    }
    public initform(){
        
        this.formData = this._fb.group({
            mexamount: [],
            minamount:[]
        })
    }
    public saveSpliteData(){
        console.log(this.formData.value)
        let a = parseInt(this.formData.value.mexamount)
        let b = parseInt(this.formData.value.minamount)
        let data = {
            taxable_amount : a,
            split_receipt_base_amount : b
        }
        this.DS.editSplite(1 , data).subscribe((res) =>{
            if(res.status === "success"){
                console.log(res)
                this.cancelForm();
            }
        })
    }
    public cancelForm(){
        this.router.navigateByUrl('admin/dharamshala/split')
    }
}