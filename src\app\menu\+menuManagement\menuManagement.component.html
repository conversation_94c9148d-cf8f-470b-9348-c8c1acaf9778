
<add-menu-group *ngIf="!hideAddMG"
 [gethideAddMG]="hideAddMG" (sendhideAddMG)="handlehideAddMG($event)">
</add-menu-group>

<edit-menu-group *ngIf="!hideEditMG" [selectedMG]="selectedMG"
 [gethideEditMG]="hideEditMG" (sendhideEditMG)="handlehideEditMG($event)" >
</edit-menu-group>

<section *ngIf="hideAddMG && hideEditMG" class="widget">
  <header>
    <h4><span class=""><i class="fa fa-tasks"></i>&nbsp;&nbsp;{{'MENU_MANAGE.MENU_GROU_MANG' | translate:param }}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right text-right col-sm-6">

    <button *ngIf="auth.roleAccessPermission('menu','add')" (click)="showAddMG()" class="display-inline-block btn btn-sm btn-inverse" tooltip="{{'MENU_MANAGE.ADD_NEW_GROUP' | translate:param }}" placement="left"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{'MENU_MANAGE.ADD' | translate:param }}</button>
    <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="Search">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body">
    <div class="mt">
      <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
          <tr>
            <th>
              <mfDefaultSorter by="id">#</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="name">{{'MENU_MANAGE.MENU_GRP' | translate:param }}</mfDefaultSorter>
            </th>
            <th class="no-sort">
              <mfDefaultSorter by="status">Status</mfDefaultSorter>
            </th>
            <th *ngIf="auth.roleAccessPermission('menu','edit') || auth.roleAccessPermission('menugroup','edit')" class="no-sort">
              <mfDefaultSorter by="status">{{'MENU_MANAGE.ACTION' | translate:param }}</mfDefaultSorter>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let ds of mf.data; let i = index">
            <td>{{findIndex(ds.id)}}</td>
            <td><span class="uppercase fw-semi-bold">{{ds.name}}</span></td>

            <td class=" ">
              <span class="text-success" *ngIf="ds.status">{{'MENU_MANAGE.ACTIVE' | translate:param }}</span>
              <span class="text-danger" *ngIf="!ds.status">{{'MENU_MANAGE.INACTIVE' | translate:param }}</span>
            </td>
            <td *ngIf="auth.roleAccessPermission('menu','edit') || auth.roleAccessPermission('menugroup','edit')" class="width-200 text-center">
              <div class="btn-group">
                <button *ngIf="auth.roleAccessPermission('menu','edit')" (click)="showEdit(ds)" class="btn btn-xs btn-default" tooltip="{{'MENU_MANAGE.EDIT_NEW_GRP' | translate:param }}" placement="left"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{'MENU_MANAGE.EDIT' | translate:param }}</button>
                <a *ngIf="auth.roleAccessPermission('menugroup','edit')" class="btn btn-xs btn-default" [routerLink]="['manage',ds.id]" tooltip="{{'MENU_MANAGE.MANGE_MEN_GRP' | translate:param }}" placement="left"><i class="fa fa-tasks"></i>&nbsp;&nbsp;{{'MENU_MANAGE.MANG_MENU' | translate:param }}</a>
              </div>
            </td>
          </tr>
          <tr *ngIf="canViewRecords && mf.data.length === 0">
            <td colspan="100">
              {{'MENU_MANAGE.NO MATCHES' | translate:param }}
            </td>
          </tr>
          <tr *ngIf="!canViewRecords">
            <td class="text-danger" colspan="100">
              {{'MENU_MANAGE.PERMISSION_DENIED' | translate:param }}
            </td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="12">
              <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
            </td>
          </tr>
        </tfoot>
      </table>


    </div>
  </div>
</section>
