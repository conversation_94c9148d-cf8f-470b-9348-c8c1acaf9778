import { AmenitiesService } from './../../shared/services/amenities.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
import 'rxjs/add/operator/takeUntil';

@Component({
    selector: 'amenities-list',
    templateUrl: './amenities.component.html',
})
export class AmenitiesComponent implements OnInit {
    config: any;// New Change ****
    data: any[];
    private sub: any;
    originalData: any[];
    searchQuery: string;
    canViewRecords: any;
    selectedAmenity: any;
    // show/hide child components
    hideAddAmenity: boolean = true;
    hideEditAmenity: boolean = true;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private AS: AmenitiesService,
        private auth: AuthGuard,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        this.canViewRecords = true;
        this.sub = this.AS.getAllAmenities().subscribe((res) => {
            if (res.status === "success") {
                this.data = res.data;
                this.originalData = res.data;
            }
        }, error => {
            if (error.status == 403) {
                this.canViewRecords = false;
            }
        });

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() { }
    
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        if (this.canViewRecords) {
            this.sub.unsubscribe();
        }
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }

    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }
    // add amenity component
    showAddAminity() {
        this.hideAddAmenity = !this.hideAddAmenity;
    }
    handlehideAddAmenity(event) {
        this.hideAddAmenity = event.gethideAddAminity;
        if (this.canViewRecords && event.data) {
            this.originalData.push(event.data);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }

    //edit amenity component
    showEditAmenity(ele) {
        this.selectedAmenity = ele;
        this.hideEditAmenity = !this.hideEditAmenity;
    }
    handlehideEditAmenity(event) {
        this.hideEditAmenity = event.gethideEditAminity;
        if (this.canViewRecords && event.data) {
            this.data[this.findIndex(event.data.id, "id", this.data) - 1] = event.data;
            this.originalData[this.findIndex(event.data.id, "id", this.originalData) - 1] = event.data;
        }
    }
    searchEvent() {
        this.initializeData();
        if (this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter(data => {
                return ((<string>data.name).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    initializeData() {
        this.data = this.originalData;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
}