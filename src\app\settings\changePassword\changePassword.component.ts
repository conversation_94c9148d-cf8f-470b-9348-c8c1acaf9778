import { Router } from '@angular/router';
import { UserService } from './../../shared/services/user.service';
import { _secretKey } from './../../shared/globals/config';
import { PasswordValidation } from './../../shared/directive/confirmPassword.directive';
import { CustomValidators } from 'ng2-validation';
import { FormGroup, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

import * as CryptoJS from 'crypto-js';

@Component({
    selector: 'change-password',
    templateUrl: './changePassword.component.html'
})

export class changePasswordComponent implements OnInit {
    changePassword: FormGroup;
    config: any;// New Change ****
    private _secretKey: String = _secretKey;
    // service variable
    private sub : any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private _fb: FormBuilder,
        private US: UserService,
        private route: Router,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.buidForm();
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    buidForm() {
        this.changePassword = this._fb.group({
            old_password: ['', Validators.required],
            new_password: ['', [Validators.required]],
            cfm_password: ['', [Validators.required]]
        }, { validator: PasswordValidation.MatchPassword })

    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    ngOnInit() { }

    savechangePassword() {
        // encrypt passwords
        let current_password = 
            CryptoJS.AES.encrypt(this.changePassword.value.old_password, _secretKey);

        let new_password = 
            CryptoJS.AES.encrypt(this.changePassword.value.new_password, _secretKey);

        let confirm_password = 
            CryptoJS.AES.encrypt(this.changePassword.value.cfm_password, _secretKey);

        
        let np_bytes = CryptoJS.AES.decrypt(new_password, this._secretKey);
        let cp_bytes = CryptoJS.AES.decrypt(confirm_password, this._secretKey);

        if(np_bytes.toString(CryptoJS.enc.Utf8) === cp_bytes.toString(CryptoJS.enc.Utf8)){
        // if new password == confirm password
            let data = {
                password: new_password.toString(),
                old_password: current_password.toString()
            }

            this.sub = this.US.savechangedPassword(data).subscribe((res) => {
                if(res.status == "success"){
                    this.changePassword.reset();
                    this.route.navigate(['admin/dashboard']);
                }else{
                    // console.log(res);
                }
            },(err)=>{
                // console.log(err);
            })
            // console.log(data);
        }

    }
    passwordConfirming(c: AbstractControl) {
        return c.get('new_password').value === c.get('cfm_password').value
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}