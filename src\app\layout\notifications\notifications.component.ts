import { Component, ElementRef, OnInit } from '@angular/core';
import { AppConfig } from '../../app.config';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
declare var jQuery: any;

@Component({
  selector: '[notifications]',
  templateUrl: './notifications.template.html'
})
export class Notifications implements OnInit {
  $el: any;
  config: any;
  public $destroy = new Subject(); // New Change ****
  private langChangeSub: Subscription; // New Change ****
  constructor(
    el: ElementRef, 
    config: AppConfig,
    public translate: TranslateService,// New Change ****
    private TS: TranslateEventService, // New Change ****
    ) {
      this.config = config.getConfig();// New Change ****
      let currentLang = localStorage.getItem('currentLang'); // New Change ****
      translate.setDefaultLang(currentLang);// New Change ****  
      
      this.$el = jQuery(el.nativeElement);
      this.config = config;

      // New Change ****
    this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
      this.changeLang(res);
    });
    
  }
  changeLang(lang: string) {
    // New Change ****
    this.translate.use(lang);
  }

  moveNotificationsDropdown(): void {
    jQuery('.sidebar-status .dropdown-toggle').after(jQuery('[notifications]').detach());
  }

  moveBackNotificationsDropdown(): void {
    jQuery('#notifications-dropdown-toggle').after(jQuery('[notifications]').detach());
  }

  ngOnInit(): void {
    this.config.onScreenSize(['sm', 'xs'], this.moveNotificationsDropdown);
    this.config.onScreenSize(['sm', 'xs'], this.moveBackNotificationsDropdown, false);

    if (this.config.isScreen('sm')) { this.moveNotificationsDropdown(); }
    if (this.config.isScreen('xs')) { this.moveNotificationsDropdown(); }

    jQuery('.sidebar-status').on('show.bs.dropdown', () => {
      jQuery('#sidebar').css('z-index', 2);
    }).on('hidden.bs.dropdown', () => {
      jQuery('#sidebar').css('z-index', '');
    });

    jQuery(document).on('change', '[data-toggle="buttons"] > label', ($event) => {
      let $input = jQuery($event.target).find('input');
      $input.trigger('change');
    });
  }
  ngOnDestroy() {
    this.$destroy.next(); // New Change ****
    this.$destroy.complete(); // New Change ****
    
    // New Change ****
    if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
  }
}
