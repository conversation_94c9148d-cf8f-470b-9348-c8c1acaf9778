import { Component, OnDestroy, Output, EventEmitter, Input } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { CustomValidators } from 'ng2-validation';
import { AmenitiesService } from './../../../shared/services/amenities.service';
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;
@Component({
    selector: 'add-amenities',
    templateUrl: '../amenities.actions.html'
})
export class AddAmenitiesComponent {
    amenitiesAdd: FormGroup;
    pageType: string = "Add";
    private sub: any;
    // Input/Output
    @Input() gethideAddAminity;
    @Output() sendhideAddAmenity = new EventEmitter();
    constructor(
        private _fb: FormBuilder,
        private AS: AmenitiesService,
        public translate: TranslateService,
    ) {
        this.buildForm();
        translate.get('AMENITIES.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }
    buildForm(){
        this.amenitiesAdd = this._fb.group({
            name: ['', Validators.required],
            charge: ['0',[Validators.required, CustomValidators.number]],
            status: [true,Validators.required]
        })
    }
    saveAmenities() {
        if (this.amenitiesAdd.valid) {
            this.sub = this.AS.saveAmenities(this.amenitiesAdd.value).subscribe((res) => {
                if (res.status === "success") {
                    console.log("Added Amenities : ",res.data);
                    this.amenitiesAdd.reset();
                    this.toggleChild(res.data);
                }
            },
                (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.amenitiesAdd.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }

    toggleChild(data) {
        console.log("Breadcrum navigation");
        let result;
        this.gethideAddAminity = !this.gethideAddAminity;
        if (data) {
            result = { gethideAddAminity: this.gethideAddAminity, data: data }
        } else {
            result = { gethideAddAminity: this.gethideAddAminity }
        }
        this.sendhideAddAmenity.emit(result);
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}