import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()

export class RoomCategoryService {
    
    constructor(private chttp: CommonHttpService) {
    }

    getInitForAdd(){
        return this.chttp.get('roomCategory/add');
    }
    getAllRoomCat(){
        return this.chttp.get('roomCategory/list');
    }
    getOneRoomCat(id){
        return this.chttp.get(`roomCategory/edit/${id}`);
    }
    saveRoomCategory(data){
        return this.chttp.post(`roomCategory/add`,data, true)
    }
    deleteImage(data){
        return this.chttp.post(`roomCategory/edit/image/delete/${data.originalName}`, data);
    }
    updateRoomCategory(id,data){
        return this.chttp.post(`roomCategory/edit/${id}`, data, true)
    }
}