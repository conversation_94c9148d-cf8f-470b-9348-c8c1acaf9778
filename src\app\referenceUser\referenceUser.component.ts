import { Component, OnInit, OnDestroy } from '@angular/core';
import { UserService } from './../shared/services/user.service';

import { AuthGuard } from "../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

@Component({
    selector: 'reference',
    templateUrl: './referenceUser.component.html'
})

export class ReferenceUserComponent implements OnInit {
    config: any;// New Change ****
    data: any[];
    originalData: any[];
    customerTypeList: any[];
    searchQuery: string;
    addRUHidden: boolean = true;
    editRUHidden: boolean = true;
    private sub: any;
    public selectedRU: any;
    public canViewRecords: boolean;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private US: UserService,
        private auth: AuthGuard,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() {
        this.canViewRecords = true;
        this.sub = this.US.fetchAllReferenceUsers()
            .subscribe((res) => {
                if (res.status == "success") {
                    this.data = res.data.referenceUsers;
                    this.originalData = res.data.referenceUsers;
                    this.customerTypeList = res.data.customerTypes;
                    console.log("customer type list", this.customerTypeList);
                    
                    // console.log("Customer Type List : ", this.customerTypeList);
                    // console.log("this.data : ",this.data);
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }
    // add component
    showAddRU() {
        this.addRUHidden = false;
        this.editRUHidden = true
    }
    handleHiddenaddRU() {
        this.addRUHidden = !this.addRUHidden;
    }
    addDateTolist(event) {
        if (this.canViewRecords && event) {
            this.originalData.push(event);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }
    // edit component
    showEditRU(data) {
        this.editRUHidden = false
        this.addRUHidden = true;
        this.selectedRU = data;
    }
    handleHiddeneditRU() {
        this.editRUHidden = !this.editRUHidden;
        this.selectedRU = "";
    }
    updateTolist(event) {
        if (this.canViewRecords && event) {
            this.data[this.findIndex(event.id, "id") - 1] = event;
            this.originalData[this.findIndex(event.id, "id", this.originalData) - 1] = event;
        }
        this.editRUHidden = !this.editRUHidden;
    }
    getCustomerTypeString(item: any) {
        let customerTypeString = '';
        for (let i = 0; i < item.customer_type_ids.length; i++) {
            let index = this.findIndex(parseInt(item.customer_type_ids[i]), "id", this.customerTypeList) - 1;
            if (index > -1) {
                customerTypeString += (this.customerTypeList[index].text);
                if (i < item.customer_type_ids.length - 1) {
                    customerTypeString += ', ';
                }
            }
        }
        item['customer_types'] = customerTypeString;
        return customerTypeString;
    }
    searchEvent() {
        this.initializeData();
        if (this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter(data => {
                let searchString = (<string>data.name).concat(data.contact, data.customer_types);
                return (searchString.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    initializeData() {
        this.data = this.originalData;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if (this.sub) {
            this.sub.unsubscribe();
        }
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}