import { Component, OnInit, ElementRef } from '@angular/core';
import { FormGroup, FormBuilder, FormArray } from '@angular/forms';
import { CustomValidators } from 'ng2-validation';
import { BookingService } from './../../shared/services/booking.service';
import { reservationDetails, sample } from './../data';
import Moment from 'moment';
import { extendMoment } from 'moment-range';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

declare var jQuery: any;
const moment = extendMoment(Moment);

@Component({
    selector: 'room-booking-settings',
    templateUrl: './booking.setting.component.html',
    styleUrls: ['./booking.setting.scss']
})

export class BookingSettingComponent implements OnInit {
    config: any;// New Change ****
    bookingSettings: FormGroup;
    monthNames: string[] = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
    dayNames: string[] = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    currMoment = moment();
    currDate = this.currMoment.toDate();
    currDateDate = this.currMoment.get('date');
    currMonth = this.currMoment.get('month');
    currYear = this.currMoment.get('year');

    todayMonth = this.currMoment.get('month') + 1;
    todayYear = this.currMoment.get('year');

    currMonthFirstWeekStart: any;
    currMonthFirstWeekEnd: any;
    currMonthStartDate: any;
    currMonthEndDate: any;
    ranges: any[];
    roomCat: any[];
    bookingSettingdata: any[];
    settingprevVal: any;
    settingCurrVal: any;

    // services
    private sub: any;
    public canViewRecords: boolean;
    private canEditRecords: boolean;
    private saveBookingSettings: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****

    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private _fb: FormBuilder,
        private BS: BookingService,
        private elementRef: ElementRef,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        // console.log("this.canEditRecords : ",this.canEditRecords);
        this.getMonthDetails();
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }
    getMonthDetails() {
        this.canEditRecords = this.auth.roleAccessPermission('roombookingsetting','add');
        this.canViewRecords = true;
        this.sub = this.BS.getRoomCat(this.currMonth + 1, this.currYear)
        .subscribe((res) => {
                if (res.status == "success") {
                    this.roomCat = res.data.roomCategories;
                    this.bookingSettingdata = res.data.bookingSettings;
                    // console.log("this.bookingSettingdata : ", this.bookingSettingdata);
                    this.getMonthDateRange(this.currYear, this.currMonth);
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    ngOnInit() {
        this.buildForm();
        this.bookingSettings.valueChanges.subscribe(val => {
            // console.log(val)
        })
    }
    buildForm() {
        this.bookingSettings = this._fb.group({
            room_settings: this._fb.array([])
        })
        let i = 0;
        if (this.canViewRecords && this.roomCat) {
            this.roomCat.forEach(element => {
                let control = <FormArray>this.bookingSettings.controls['room_settings'];
                let control2 = this._fb.array([]);

                this.ranges.forEach(ele => {
                    let j = 0;
                    let online_quota_val;
                    let personal_quota_val;

                    this.bookingSettingdata.forEach(iterator => {
                        iterator.settings.forEach(val => {
                            let iterator_date = moment(val.release_date);
                            let ele_date = moment(ele.fulldate);
                            if (iterator_date.isSame(ele_date, 'days') &&
                                (element.id == val.room_category_id)) {
                                online_quota_val = val.online_quota;
                                personal_quota_val = element.total_room - online_quota_val;
                            }
                            j++;
                        })
                    })
                    let is_disabled;
                    let currDateMoment = moment(ele.year + '-' + ele.monthnumber + '-' + ele.date, "YYYY-MM-DD")
                    let todayDateMoment = this.currMoment
                    is_disabled = this.canEditRecords ? currDateMoment.isBefore(todayDateMoment, 'day') : true;
                    control2.push(this._fb.group({
                        room_category_id: [element.id],
                        release_date: [ele.fulldate],
                        total_room: [element.total_room],
                        online_quota: [{
                            value: ((online_quota_val) ? online_quota_val : element.default_online_quota),
                            disabled: is_disabled
                        }, [CustomValidators.max(element.total_room)]],
                        personal_quota: [{
                            value: ((online_quota_val) ? element.total_room - online_quota_val : element.total_room - element.default_online_quota),
                            disabled: true
                        }]
                    }))
                    online_quota_val = null
                    personal_quota_val = null
                });
                // console.log(control2.value)
                control.push(this._fb.group({
                    room: element.name,
                    settings: control2
                }));

                i++;
            });
        }
        if (this.canViewRecords && this.bookingSettingdata) {
            this.bookingSettings.patchValue(this.bookingSettingdata);
        }
    }
    getMonthDateRange(year, month): void {
        let startDate = moment([year, month]);
        let endDate = moment(startDate).endOf('month');
        // set month start and end date
        this.currMonthStartDate = startDate.toDate();
        this.currMonthEndDate = endDate.toDate();
        this.createDateArray();
    }
    createDateArray(): void {
        this.ranges = [];
        for (let i = 1; i <= this.currMonthEndDate.getDate(); i++) {
            let day = moment([this.currYear, this.currMonth, i]);
            let orignalDate = moment([this.currYear, this.currMonth, i]).format('YYYY-MM-DD');
            let todayDateMoment = this.currMoment
            //adding previous month dates
            // if (i == 1) {
            //     let weekFirst = moment([this.currYear, this.currMonth, i]).startOf('week');
            //     for (let j = 1; j <= 7; j++) {
            //         if (weekFirst.get('month') < this.currMonth) {
            //             let dateObj = {};
            //             dateObj['daynumber'] = weekFirst.get('day');
            //             dateObj['day'] = this.dayNames[weekFirst.get('day')];
            //             dateObj['date'] = weekFirst.get('date');
            //             dateObj['monthnumber'] = weekFirst.get('month') + 1;
            //             dateObj['month'] = this.monthNames[weekFirst.get('month')];
            //             dateObj['year'] = weekFirst.get('year');
            //             this.ranges.push(dateObj)
            //         }
            //         weekFirst = weekFirst.add(1, 'days');
            //     }
            // }
            // adding current month dates
            let dateObj = {};
            dateObj['fulldate'] = orignalDate;
            dateObj['daynumber'] = day.get('day');
            dateObj['day'] = this.dayNames[day.get('day')];
            dateObj['date'] = day.get('date');
            dateObj['monthnumber'] = day.get('month') + 1;
            dateObj['month'] = this.monthNames[day.get('month')];
            dateObj['year'] = day.get('year');
            dateObj['is_disabled'] = day.isBefore(todayDateMoment, 'day');
            // console.log(day)
            this.ranges.push(dateObj)
            // adding next month dates
            // if (i == this.currMonthEndDate.getDate()) {
            //     let weekFirst = moment([this.currYear, this.currMonth, i]).startOf('week');
            //     for (let j = 1; j <= 7; j++) {
            //         if (weekFirst.get('month') > this.currMonth) {
            //             let dateObj = {};
            //             dateObj['daynumber'] = weekFirst.get('day');
            //             dateObj['day'] = this.dayNames[weekFirst.get('day')];
            //             dateObj['date'] = weekFirst.get('date');
            //             dateObj['monthnumber'] = weekFirst.get('month') + 1;
            //             dateObj['month'] = this.monthNames[weekFirst.get('month')];
            //             dateObj['year'] = weekFirst.get('year');
            //             this.ranges.push(dateObj)
            //         }
            //         weekFirst = weekFirst.add(1, 'days');
            //     }
            // }
        }
        // console.log(this.ranges);
        this.buildForm();
    }
    range = (value) => {
        // this function will return array of numbers passed through
        let a = [];
        for (let i = 0; i < value; ++i) {
            a.push(i + 1);
        } return a;
    }
    nextMonth = () => {
        this.currMonth++;
        if (this.currMonth >= 12) {
            this.currMonth = 0;
            this.nextYear();
        }
        this.getMonthDetails();
    }
    prevMonth = () => {
        this.currMonth--;
        if (this.currMonth <= -1) {
            this.currMonth = 11;
            this.prevYear();
        }
        this.getMonthDetails();
    }
    nextYear = () => {
        this.currYear = this.currYear + 1;
        this.getMonthDetails();
    }
    prevYear = () => {
        this.currYear = this.currYear - 1;
        this.getMonthDetails();
    }
    setInputVal(data) {
        this.settingprevVal = data.value;
    }
    checkOnlineQuota(data: FormGroup, ele) {
        let newDate = data.value;

        // console.log(data.value, ele)
    }
    saveDemo(data: FormGroup, self, ele) {
        // console.log(data)
        if (data.value.online_quota) {
            let tot_room: number = parseInt(data.value.total_room);
            let online_quota: number = parseInt(data.value.online_quota);
            let personal_quota: number = tot_room - online_quota;
            if (personal_quota < 0) {
                personal_quota = 0;

            }
            if (online_quota > tot_room) {
                self.value = tot_room;
                data.value.online_quota = tot_room;
            }
            if (data.value.online_quota) {
                if (this.settingprevVal.online_quota != data.value.online_quota) {
                    this.saveBookingSettings = this.BS.saveRoombookingSettings(data.value)
                        .subscribe((res) => {
                            if (res.status == "success") {
                                // console.log(res)
                                ele.value = personal_quota;
                            }
                        })
                }
            }
        }
    }
    isNumber(evt) {
        evt = (evt) ? evt : window.event;
        let charCode = (evt.which) ? evt.which : evt.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }
        return true;
    }
    ngOnDestroy(){
        if(this.sub){
            this.sub.unsubscribe();
        }
        if(this.saveBookingSettings){
            this.saveBookingSettings.unsubscribe();
        }

        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}