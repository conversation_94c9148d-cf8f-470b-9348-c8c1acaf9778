import { Component, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { apiUrl } from 'app/api-env';
import { ModalDirective } from 'ngx-bootstrap/modal';

interface ApiResponse {
  status: string;
  data?: any;
  message?: string;
}

@Component({
  selector: 'onlinepayment',
  templateUrl: './onlinepayment.component.html',
  styleUrls: ['./onlinepayment.style.scss']
})
export class OnlinePaymentComponent {
  paymentForm: FormGroup;
  qrCodeUrl: string = 'assets/qr-code.png';
  baseURL: string = apiUrl
  showEmptyTemplate: boolean = false;
  showTokenErrorMsg: string | null = null;
  authorizationToken: string = '';
  screenshotFile: File | null = null;
  tokenData: any;
  pancard_field: boolean = true;
  uploded: boolean = false
  screenshot: any;
  modalMessage: string = '';
  isError: boolean = false;
  isUpiCopied: boolean = false
  imagePreview: string | null = null;
  panCardPattern: any = '^[A-Z]{5}[0-9]{4}[A-Z]{1}$';
  allowedFileTypes: any = ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'];
  @ViewChild('showModal') public showModal: ModalDirective;
  @ViewChild('screenshotInput') screenshotInput: ElementRef;

  constructor(private fb: FormBuilder, private http: HttpClient, private route: ActivatedRoute) {
    this.paymentForm = this.fb.group({
      transactionId: ['', Validators.required],
      upi_id: ['', [Validators.required, Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+$')]],
      bank_name: ['', Validators.required],
      pancard_number: ['', [Validators.pattern(this.panCardPattern)]],
      screenshot: ['']
    });
  }

  verifyPaymentToken(){
    this.route.queryParams.subscribe(params => {
      this.authorizationToken = params['token'];
      this.showEmptyTemplate = true;
      if (!this.authorizationToken) {
        this.showEmptyTemplate = true;
        this.showTokenErrorMsg = "You are not authorized to access this page.";
        return;
      }

      let headers = new HttpHeaders({
        'Content-Type': 'application/json',
        Authorization: this.authorizationToken
      });

      this.http.get<ApiResponse>(`${this.baseURL}bookingApprover/verify`, { headers })
        .subscribe(
          (response) => {
            let res = response;
            if (res.status === 'success') {
              this.showEmptyTemplate = false;
              this.tokenData = res.data;
              this.pancard_field = (this.tokenData.total_amount <= 8000) ? false : true
            } else {
              this.showEmptyTemplate = true;
              this.showTokenErrorMsg = res.message;
            }
          },
          (error) => {
            this.showEmptyTemplate = true;
            this.showTokenErrorMsg = error.json().message || "Authorization failed.";
          }
        );
    });
  }
  ngOnInit() {
    this.verifyPaymentToken()
  }

  uploadIdProof() {
    const formData: FormData = new FormData();
    formData.append('file', this.screenshotInput.nativeElement.files[0]);
    return this.http.post(`${this.baseURL}guest/public/add/image`, formData)
  }
  //new functionalities added here
  ReturnDate(date: any) {
    const newDate = new Date(date);
    return newDate.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  }

  onFileChange(event: any) {
    let file = this.screenshotInput.nativeElement.files[0]

    if (!file) {
      return this.paymentForm.get('screenshot').setErrors({ required: `screenshot is required` });
    }
    if (file.size > 5 * 1024 * 1024) {
      return this.paymentForm.get('screenshot').setErrors({ invalidFileSize: `File size must be less than 5 MB` });

    } else if (!this.allowedFileTypes.includes(file.type)) {
      return this.paymentForm.get('screenshot').setErrors({ invalidFileType: `please upload in jpeg, png, jpg, jpeg* format` });
    }
  }


  submitPayment() {
    if (this.paymentForm.invalid) {
      this.paymentForm.get('screenshot').setErrors({ required: `screenshot is required` });
      return Object.keys(this.paymentForm.controls).forEach(field => {
        const control = this.paymentForm.get(field);
        control.markAsTouched();
      });
    }

    if(!this.screenshotInput.nativeElement.files[0]) {
      return this.paymentForm.get('screenshot').setErrors({ required: `screenshot is required` });
    }

    this.uploadIdProof().subscribe(
      (response: ApiResponse) => {
        const responseData = response;
        if (responseData.status === 'success') {
          
          let displayname = 'payment-ss-' + responseData.data.originalName;
          this.screenshot = { ...responseData.data, documentType: 4, displayname };
          const data = {
            transaction_id: this.paymentForm.value.transactionId,
            amount: this.tokenData.total_amount,
            bank_name: this.paymentForm.value.bank_name,
            upi_id: this.paymentForm.value.upi_id,
            online_booking_rooms_id: this.tokenData.online_booking_id,
            payment_verifiation_id: this.paymentForm.value.pancard_number,
            screenshot: this.screenshot
          };

          let headers = new HttpHeaders({
            'Content-Type': 'application/json',
            'Authorization': this.authorizationToken
          });

          let options = { headers };
          
          this.http.post<ApiResponse>(`${this.baseURL}onlinebooking/payment/add`, data, options)
            .subscribe(
              (response) => {
                this.showModalMessage("Payment submitted successfully!", false);
              },
              (error) => {
                this.showModalMessage("Error submitting payment. Please try again.", true);
              }
            );
        }
        else {
          this.showModalMessage("Error submitting payment. Please try again.", true);
        }
      },
      (error) => {
        console.log(error)
      }
    );
  }

  showModalMessage(message: string, isError: boolean) {
    this.modalMessage = message;
    this.isError = isError;
    this.showModal.show();
  }

  closeModal() {
        this.isError = false
    this.modalMessage = ''
    this.showModal.hide();
  }

  // copyUpi() {
  //   let upi_id = "muktajivanswamibapa@mahb";
  //   if (navigator.clipboard) {
  //     navigator.clipboard.writeText(upi_id).then(() => {
  //       this.isUpiCopied = true;
  //     }).catch(err => {
  //       console.error('Failed to copy text: ', err);
  //     });
  //     setTimeout(() => {
  //       this.isUpiCopied = false;
  //     }, 5000);
  //   } else {
  //     console.error('Clipboard API not supported');
  //   }
  // }

}
