/* Header Wrapper */
.c-main-booking-header-wrap {
  background: #ff9d00;
  background: linear-gradient(to bottom, #ff9d00 1%, #ed5904 100%);
  padding: 12px 0;
}

/* Header Section */
.c-main-booking-header-section {
  max-width: 1130px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Header Text */
.c-main-booking-header-text {
  color: #fff;
  font-size: 25px;
  font-weight: 700;
  margin: 0;
}

.c-main-booking-header-text small {
  font-size: 12px;
  color: #fff;
}

/* Mobile view adjustments */
@media (max-width: 767px) {
  .c-main-booking-header-section {
    display: block;
    text-align: center;
  }
  .c-main-booking-header-logo {
    margin: 0 auto;
  }
  .c-main-booking-header-text {
    font-size: 20px;
  }
}

/* Tablet view adjustments (768px to 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .c-main-booking-header-section {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .c-main-booking-header-logo {
    margin-right: 20px;
  }
  .c-main-booking-header-text {
    font-size: 22px;
  }
}

/* Desktop view adjustments (992px and above) */
@media (min-width: 992px) {
  .c-main-booking-header-section {
    display: flex;
    justify-content: space-between;
  }
  .c-main-booking-header-logo {
    margin-right: 20px;
  }
  .c-main-booking-header-text {
    font-size: 25px;
  }
}
.doc-upload-success {
  padding: 5px 10px;
  border: 1px solid green;
  background-color: #64bd6382;
  font-size: large;
  margin-right: 10px;
  border-radius: 15%;
  margin-left: 20px;
}

.doc-upload-cancel {
  padding: 5px 10px;
  border: 1px solid green;
  background-color: #e1531e8c;
  font-size: large;
  border-radius: 15%;
}

/* Style the input field */
.search-input {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/* Style the dropdown list */
.dropdown-list {
  max-height: 100px;
  overflow-y: auto;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-top: 5px;
  background-color: white;
  position: absolute;
  width: 100%;
  z-index: 99;
}

.dropdown-list ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.dropdown-list li {
  padding: 8px;
  cursor: pointer;
}

.dropdown-list li:hover {
  background-color: #f0f0f0;
}
.notes {
  background: beige;
  padding: 15px;
  border-radius: 20px;
  margin-bottom: 15px;
  color: red;
}

.c-main-booking-body-wrap {
  .c-main-booking-body-title {
    text-align: center;
    font-weight: 700;
    font-size: 22px;
    color: #656565;
    margin-bottom: 20px;
  }

  .c-main-booking-search-section-wrap {
    padding: 20px 20px 30px;
    background-color: #3e4095;
  }

  .c-main-booking-search-section-group {
    max-width: 768px;
    margin: auto;
    background: #fff;
    border-radius: 10px;
    box-shadow: 2px 2px 10px 1px rgba(0, 0, 0, 0.4);
    padding: 12px 12px 30px;
    position: relative;
  }

  .c-main-booking-search-control-group {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
    justify-content: space-around;
    margin: 2rem
  }

  .c-main-booking-search-control-box {
    flex: 1 1 100%;
    max-width: 100%;
    border: 1px solid #ddd;
    padding: 8px;
    margin-left: -1px;

    &:first-of-type {
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
    }

    &:last-of-type {
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
    }

    @media (min-width: 768px) {
      flex: 1 1 48%;
      max-width: 48%;
    }

    @media (min-width: 992px) {
      flex: 1 1 48%;
      max-width: 48%;
    }

    @media (min-width: 1200px) {
      flex: 1 1 33.3333%;
      max-width: 33.3333%;
    }
  }

  .c-main-booking-search-control-roomSelection {
    .wrapper {
      display: flex;

      input {
        text-align: center;
      }

      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
      }
    }
  }
.invalid-feedback{
  font-size: 12px;
}
  .c-main-booking-search-btn {
    text-align: center;
    position: absolute;
    bottom: -20px;
    left: 0;
    right: 0;
    border-radius: 25px;
    max-width: 200px;
    margin: auto;
    font-weight: 800;
    text-transform: uppercase;
    font-size: 20px;
    opacity: 1 !important;
  }
}

.c-main-booking-search-adult-wrap {
  display: flex;
  font-size: 20px;
  font-weight: 600;
  margin-top: 8px;
  color: #656565;

  &::after {
    content: "\f078";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    position: absolute;
    top: 50%;
    right: 20px;
    transform: rotate(0) translateY(-50%);
    transition: all 0.5s;
  }
}

.c-main-booking-search-control-roomSelection--groups {
  position: absolute;
  background: #f1f2f3;
  padding: 10px;
  border-radius: 10px;
  box-shadow: 1px 2px 18px 2px rgba(0, 0, 0, 0.4);
  display: flex;
}

.c-main-search-result-seaction-wrap {
  .c-main-result-box-title {
    margin-top: 20px;
  }

  .c-main-search-result-seaction-card {
    background-color: #fff;
    box-shadow: 2px 2px 14px 1px rgba(0, 0, 0, 0.2);

    .card-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 10px;
      text-transform: capitalize;
    }

    .card-footer {
      padding-top: 0;
    }

    .danger-color {
      color: #ed5904;
    }
  }
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  background-color: #f8f9fa;
  color: #333;
}

.error-content {
  max-width: 600px;
  padding: 20px;
}

.error-title {
  font-size: 2rem;
  font-weight: bold;
}

.error-message {
  font-size: 1.2rem;
  margin: 10px 0;
}