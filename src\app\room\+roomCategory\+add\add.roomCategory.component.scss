.amenities-area{
    height: 100px;
    background:#efefef;
    border: 2px dashed rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    padding: 5px;   
    box-shadow: 0 0 0px 5px #fff inset;
}
.amenities-selection{
    display: inline-block;
    border: 1px solid rgba(0,0,0,.15);
    background: #fff;
    border-radius: 100px;
    font-size: 11px;
    padding: 3px 3px 3px 8px;
    cursor: default;
    margin: 5px;
    font-weight: bold;
    transition: all ease-in-out 200px;
    box-shadow: 0 0 2px rgba(0,0,0,.0);
    & > *{
        display: inline-block;
        padding: 0px 0px;
        vertical-align: baseline;
        border: none;
    }
    input{
        -moz-appearance: none;
        -webkit-appearance: none;
        -ms-progress-appearance: none;
        outline: none;
        background: transparent;
        border: none;
    }
    .amenities-cost:not(:empty){
        position: relative;
        &::before{
            content: '-';
            margin-right: 10px;
        }
    }
    .amenities-action{
        background: #aaa;
        border-radius: 100%;
        height: 20px;
        width: 20px;
        color:#fff;
        font-weight: 100;
        font-size: 11px;
        text-align: center;
        line-height: 21px;
        cursor: pointer;
            margin-left: 5px;
    }
    &:hover{
        box-shadow: 0 0 2px rgba(0,0,0,.1)
    }
}
.well {
    padding: 15px;
    border: 2px dashed #d7d7d7;
    margin: 15px auto;
    background: #efefef;
    box-shadow: 0 0 0 5px #fff inset;
    border-radius: 5px;
}