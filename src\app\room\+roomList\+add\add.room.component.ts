import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RoomsService } from './../../../shared/services/room.service';
import { Select2OptionData } from 'ng2-select2';
// import * as data from '../../data';
declare var Messenger: any;
@Component({
    selector: 'add-room',
    templateUrl: '../rooms.actions.component.html'
})

export class AddRoomComponent implements OnInit {
    public pageName: string = "Add";
    select2Options: any = {
        width: '100%'
    };
    roomForm: FormGroup;
    //services
    private sub: any;
    private floor: any;
    private floorList: any;
    private getData: any;
    // Input and Outputs
    @Input() getHiddenAR;
    @Output() sendHiddenAR = new EventEmitter();

    public roomCat: any[];
    public wing: any[];
    private categoryListCache: any[] = null;
    private floorListCache: any[] = null;
    private wingListCache: any[] = null;
    selectedBuilding: any = null;
    selectedFloor: any = null;

    constructor(
        private _fb: FormBuilder,
        private RS: RoomsService
    ) {}
    ngOnInit() {
        this.buildForm();
        this.loadRoomCategories();
    }
    loadRoomCategories() {
        if (this.categoryListCache && this.wingListCache) {
            this.roomCat = this.categoryListCache;
            this.wing = this.wingListCache;
            // Set default building if available
            if (this.wing && this.wing.length > 0) {
                this.selectedBuilding = { id: this.wing[0].id, text: this.wing[0].name };
                this.roomForm.controls['building_id'].patchValue(this.wing[0].id);
                this.loadFloorsForBuilding(this.wing[0].id);
            }
            return;
        }

        this.getData = this.RS.getRoomCatandWingData()
            .subscribe((res) => {
                if (res.status == "success") {
                    if (res.data.roomcategories) {
                        this.roomCat = res.data.roomcategories;
                        this.categoryListCache = this.roomCat;
                    } else {
                        this.roomCat = [];
                        this.categoryListCache = [];
                        Messenger().post({  hideAfter: 5,
                            message: "No Room Categories found.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                    if (res.data.buildings) {
                        this.wing = res.data.buildings;
                        this.wingListCache = this.wing;
                        // Set default building if available
                        if (this.wing && this.wing.length > 0) {
                            this.selectedBuilding = { id: this.wing[0].id, text: this.wing[0].name };
                            this.roomForm.controls['building_id'].patchValue(this.wing[0].id);
                            this.loadFloorsForBuilding(this.wing[0].id);
                        }
                    } else {
                        this.wing = [];
                        this.wingListCache = [];
                        Messenger().post({  hideAfter: 5,
                            message: "No Buildings found.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                }
            });
    }

    buildForm() {
        this.roomForm = this._fb.group({
            title: ['', Validators.required],
            room_category_id: ['', [Validators.required, CustomValidators.digits]],
            door_id: ['', []],
            building_id: ['', [Validators.required, CustomValidators.digits]],
            floor_id: ['', [Validators.required, CustomValidators.digits]],
            default_bed: ['0', [Validators.required, CustomValidators.digits]],
            max_bed: ['0', [Validators.required, CustomValidators.digits]],
            status: [true, Validators.required]
        })
    }
    // dropdowns
    getCategorylist(): any[] {
        return jQuery.map(this.roomCat, function (obj) {
            return { id: obj.id, text: obj.name };
        });
    }
    getWingList(): any[] {
        if (this.wingListCache) {
            return jQuery.map(this.wingListCache, function (obj) {
                return { id: obj.id, text: obj.name };
            });
        }
        return [];
    }
    getFloorList(): any[] {
        if (this.floorListCache) {
            return jQuery.map(this.floorListCache, function (obj) {
                return { id: obj.id, text: obj.name };
            });
        }
        return [];
    }
    categoryChanged(event) {
        console.log("Category changed", event);
        this.roomForm.controls['room_category_id'].patchValue(event.id);
    }
    wingChanged(event) {
        console.log("Wing changed", event);
        if (event) {
            this.selectedBuilding = event;
            this.roomForm.controls['building_id'].patchValue(event.id);
            this.loadFloorsForBuilding(event.id);
            // Reset floor selection when building changes
            this.selectedFloor = null;
            this.roomForm.controls['floor_id'].patchValue(null);
        }
    }
    floorChanged(event) {
        console.log("Floor changed", event);
        if (event) {
            this.selectedFloor = event;
            this.roomForm.controls['floor_id'].patchValue(event.id);
        }
    }
    loadFloorsForBuilding(buildingId: number) {
        this.floor = this.RS.getBuildingFloors(buildingId).subscribe((res) => {
            if (res.status === "success") {
                this.floorList = res.data;
                this.floorListCache = res.data;
                // Set default floor if available
                if (this.floorList && this.floorList.length > 0) {
                    this.selectedFloor = { id: this.floorList[0].id, text: this.floorList[0].name };
                    this.roomForm.controls['floor_id'].patchValue(this.floorList[0].id);
                }
            }
        });
    }
    // save
    saveRoom() {
        console.log("Saving....");
        if (this.roomForm.valid) {
            if(this.roomForm.value && this.roomForm.value.door_id == ''){
                this.roomForm.value.door_id = null;
            }
            console.log("Conditions true",this.roomForm.value);
            this.sub = this.RS.saveRoomCatandWingData(this.roomForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        Messenger().post({  hideAfter: 5,
                            message: res.message,
                            type: res.status,
                            showCloseButton: true
                        });
                        this.toggleChild(res.data);
                    }
                    else {
                        console.log("Room not added");
                    }
                },(err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.roomForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        }else{
            console.log("Conditions false")            
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    // toggle to mains
    toggleChild(data) {
        this.getHiddenAR = !this.getHiddenAR;
        let result;
        if (data) {
            result = { 'getHiddenAR': this.getHiddenAR, 'data': data }
        } else {
            result = { 'getHiddenAR': this.getHiddenAR }
        }
        this.sendHiddenAR.emit(result);
    }
}