
<add-room-category *ngIf="!hiddenAddRC"
  [gethiddenAddRC]="hiddenAddRC" (sendhiddenAddRC)="handlehiddenAddRC($event)"
></add-room-category>


<edit-room-category *ngIf="!hiddenEditRC" [id]="selectedRC"
  [gethiddenEditRC]="hiddenEditRC" (sendhiddenEditRC)="handlehiddenEditRC($event)"
></edit-room-category>


<section class="widget" *ngIf="hiddenAddRC && hiddenEditRC">
  <header>
    <h4><span class="" style="color: red;">
      <i class="fa fa-sitemap"></i>&nbsp;&nbsp;
      {{'ROOM.ROOM_CAT.ROOM_CAT_MANAGE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right text-right col-sm-6">

    <button *ngIf="auth.roleAccessPermission('roomCategory','add')" (click)="showAddRC()" class="display-inline-block btn btn-sm btn-inverse" tooltip="{{'ROOM.ROOM_CAT.ADD_NEW_CAT' | translate:param}}" placement="top"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{'ROOM.ROOM_CAT.ADD' | translate:param}}</button>
    <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="searchEvent()" placeholder="{{'ROOM.ROOM_CAT.SEARCH' | translate:param}}">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body table-scroll">
    <div class="mt">
         <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
        <tr>
          <th>
            <mfDefaultSorter by="id">#</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="name">{{'ROOM.ROOM_CAT.NAME' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="">
            <mfDefaultSorter by="info">{{'ROOM.ROOM_CAT.MAX_PER' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="no-sort">
            <mfDefaultSorter by="status">{{'ROOM.ROOM_CAT.STATUS' | translate:param}}</mfDefaultSorter>
          </th>
          <th *ngIf="auth.roleAccessPermission('roomCategory','edit')" class="no-sort text-center">
            <mfDefaultSorter by="status">{{'ROOM.ROOM_CAT.ACTION' | translate:param}}</mfDefaultSorter>
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let room of mf.data">
          <td>{{room.id}}</td>
          <td><span class="fw-semi-bold uppercase">{{room.name}}</span></td>
          <td>{{room.max_occupancy}}</td>
          <td class=" ">
            <span class="text-success" *ngIf="room.status">{{'ROOM.ROOM_CAT.ACTIVE' | translate:param}}</span>
            <span class="text-danger" *ngIf="!room.status">{{'ROOM.ROOM_CAT.INACTIVE' | translate:param}}</span>
          </td>
          <td *ngIf="auth.roleAccessPermission('roomCategory','edit')" class="width-100 text-center">
            <button class="btn btn-xs btn-default" (click)="showEditRC(room.id)" tooltip="{{'ROOM.ROOM_CAT.EDIT_NEW_CAT' | translate:param}}" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{'ROOM.ROOM_CAT.EDIT' | translate:param}}</button>
          </td>
        </tr>
        <tr *ngIf="canViewRecords && mf.data.length === 0">
          <td colspan="100">
              {{'ROOM.ROOM_CAT.NO MATCHES' | translate:param}}
          </td>
        </tr>
        <tr *ngIf="!canViewRecords">
          <td class="text-danger" colspan="100">
            {{'ROOM.ROOM_CAT.PERMISSION_DENIED' | translate:param}}
          </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="12">
            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
          </td>
        </tr>
        </tfoot>
      </table>
    </div>
  </div>
</section>