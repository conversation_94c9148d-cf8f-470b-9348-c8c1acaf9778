import { Select2OptionData } from 'ng2-select2/ng2-select2';
export const inventoryList = [
    {
        'id': 1,
        'name': 'desk',
        'type': 'cosumable',
        'vandor': [
            { 'title': 'vandor1'},
            { 'title': 'vandor2'},
            { 'title': 'vandor3'}
        ],
        'status': 'Active'
    },
    {
        'id': 2,
        'name': 'Television',
        'type': 'Cosumable',
        'vandor': [
            { 'title': 'vandor1'},
            { 'title': 'vandor2'},
            { 'title': 'vandor3'}
        ],
        'status': 'Active'
    },
    {
        'id': 3,
        'name': 'bedsheet',
        'type': 'cosumable',
        'vandor': [
            { 'title': 'vandor1'},
            { 'title': 'vandor2'},
            { 'title': 'vandor3'}
        ],
        'status': 'Active'
    }
];

export const inventoryTypeData : Select2OptionData[] = [
    {
        'id':'cosumable',
        'text':'cosumable'
    },
    {
        'id':'parisable',
        'text':'parisable'
    },
    {
        'id':'capital',
        'text':'capital'
    }
]