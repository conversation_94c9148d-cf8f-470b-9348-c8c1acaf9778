<!--
  ********<add-customer-type></add-customer-type>
  ************************************************
  hiddenAddCT - will hide and show this component as well as list 
  [gethiddenAddCT] - will get hiddenAddCt value from parent to use in child ,
  (sendHiddenAddCT) - emits event and throws hiddenAddCT value changed from child and data if there ara to add to list

 -->

<add-customer-type *ngIf="!hiddenAddCT" 
  [gethiddenAddCT]="hiddenAddCT" (sendHiddenAddCT)="getHiddenAddCTfromChild($event)">
</add-customer-type>

<!--
  ********<edit-customer-type></edit-customer-type>
  ************************************************
  SAME AS ABOVE accept for edit customer type component
  [selectedCT] - will get selected customer type from parent to edit.
 -->

<edit-customer-type *ngIf="!hiddenEditCT" [selectedCT]="selectedCT"
  [gethiddenEditCT]="hiddenEditCT" (sendhiddenAddCT)="getHiddenEditCTfromChild($event)">
</edit-customer-type>



<section *ngIf="hiddenAddCT && hiddenEditCT" class="widget">
  <header>
    <h4><span class="" style="color: red;"><i class="fa fa-users"></i>&nbsp;&nbsp;{{ 'CUSTOMER_TYPE.CUST_TYPE_MANAGE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right text-right col-sm-6">

    <button *ngIf="auth.roleAccessPermission('customer','add')" (click)="showAddCT()" class="display-inline-block btn btn-sm btn-inverse" tooltip="{{ 'CUSTOMER_TYPE.ADD_NEW_CUST_TYPE' | translate:param}}" placement="top"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{ 'CUSTOMER_TYPE.ADD' | translate:param}}</button>
    <div class="form-group display-inline-block __search">
      <!---I think keyup would give a better result -->
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{ 'CUSTOMER_TYPE.SEARCH' | translate:param}}">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body table-scroll">
    <div class="mt">

         <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
        <tr>
          <th>
            <mfDefaultSorter by="id">#</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="name">{{ 'CUSTOMER_TYPE.CUST_TYPE' | translate:param}}</mfDefaultSorter>
          </th>
           <th class="no-sort">
            <mfDefaultSorter>{{ 'CUSTOMER_TYPE.DIS' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="no-sort">
            <mfDefaultSorter by="status">{{ 'CUSTOMER_TYPE.STATUS' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="no-sort text-center">
            <mfDefaultSorter by="status" tooltip="{{ 'CUSTOMER_TYPE.DEF_TOOLTIP' | translate:param}}" placement="bottom">{{ 'CUSTOMER_TYPE.DEFAULT' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="no-sort text-center">
            <mfDefaultSorter by="status" tooltip="{{ 'CUSTOMER_TYPE.BAKHI_TOOLTIP' | translate:param}}" placement="bottom">{{ 'CUSTOMER_TYPE.BAKHI_CHECK' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="no-sort text-center">
            <mfDefaultSorter by="status" tooltip="{{ 'CUSTOMER_TYPE.REF_REQ_TOOLTIP' | translate:param}}" placement="bottom">{{ 'CUSTOMER_TYPE.REF_REQ' | translate:param}}</mfDefaultSorter>
          </th>
          <th class="no-sort text-center">
            <mfDefaultSorter by="status" tooltip="{{ 'CUSTOMER_TYPE.COMME_REQ_TOOLTIP' | translate:param}}" placement="bottom">{{ 'CUSTOMER_TYPE.COMME_REQ' | translate:param}}</mfDefaultSorter>
          </th>
          <th *ngIf="auth.roleAccessPermission('customer','edit')" class="no-sort text-center">
            <mfDefaultSorter by="status">{{ 'CUSTOMER_TYPE.ACTION' | translate:param}}</mfDefaultSorter>
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let ds of mf.data; let i = index">
          <td>{{findIndex(ds.id,"id")}}</td>
          <td><span class="uppercase fw-semi-bold">{{ds.name}}</span></td>
          <td style="padding-left: 5px">
            <span *ngIf="ds.discount_type != 'none'">
              <span *ngIf="ds.discount_type == 'amount'">
               <strong> {{ds.discount_value}}</strong>&nbsp;&nbsp;<i class="fa fa-inr"></i>
              </span>
              <span *ngIf="ds.discount_type == 'percentage'">
               <strong> {{ds.discount_value}}</strong>&nbsp;&nbsp;<i class="fa fa-percent"></i>
              </span>
            </span>
            <span *ngIf="ds.discount_type == 'none' || ds.discount_type == null">
              None
            </span>
          </td>
          <td>
             <span class="text-success" *ngIf="ds.status">{{ 'CUSTOMER_TYPE.ACTIVE' | translate:param}}</span>
            <span class="text-danger" *ngIf="!ds.status">{{ 'CUSTOMER_TYPE.INACTIVE' | translate:param}}</span>
          </td>
          <td class="text-center">
            <div tooltip="{{ 'CUSTOMER_TYPE.DEF_TOOLTIP' | translate:param}}" placement="bottom" style="position: relative">
              <input type="radio" id="{{ds.id}}_is_default_radio" class="is_default_radio" name="is_default_radio" (change)="changeDefault(ds, 'is_default')" [checked]="ds.is_default">
              <label for="{{ds.id}}_is_default_radio"></label>
            </div>
          </td>
          <td class="text-center">
            <div tooltip="{{ 'CUSTOMER_TYPE.BAKHI_TOOLTIP' | translate:param}}" placement="bottom" style="position: relative">
              <input type="checkbox" id="{{ds.id}}_can_checkout_without_full_payment_radio" class="is_default_checkbox" name="can_checkout_without_full_payment_radio" (change)="updateBakhiStatus(ds)" [checked]="ds.checkout_without_full_payment">
              <label for="{{ds.id}}_can_checkout_without_full_payment_radio"></label>
            </div>
          </td>
          <td class="text-center">
            <div tooltip="{{ 'CUSTOMER_TYPE.REF_REQ_TOOLTIP' | translate:param}}" placement="bottom" style="position: relative">
              <input type="checkbox" id="{{ds.id}}_is_reference_necessary" class="is_default_checkbox" name="is_reference_necessary" (change)="updateCustomerReferenceStatus(ds)" [checked]="ds.is_reference_necessary">
              <label for="{{ds.id}}_is_reference_necessary"></label>
            </div>
          </td>
          <td class="text-center">
            <div tooltip="{{ 'CUSTOMER_TYPE.COMME_REQ_TOOLTIP' | translate:param}}" placement="bottom" style="position: relative">
              <input type="checkbox" id="{{ds.id}}_is_comment_necessary" class="is_default_checkbox" name="is_comment_necessary" (change)="updateCustomerCommentStatus(ds)" [checked]="ds.is_comment_necessary">
              <label for="{{ds.id}}_is_comment_necessary"></label>
            </div>
          </td>
          <td *ngIf="auth.roleAccessPermission('customer','edit')" class="width-100 text-center">
            <button (click)="showEditCT(ds)" class="btn btn-xs btn-default" tooltip="{{ 'CUSTOMER_TYPE.EDIT_CUST_TYPE' | translate:param}}" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{ 'CUSTOMER_TYPE.EDIT' | translate:param}}</button>
          </td>
        </tr>
        <tr *ngIf="canViewRecords && mf.data.length === 0">
          <td colspan="100">
              {{ 'CUSTOMER_TYPE.NO MATCHES' | translate:param}}
          </td>
        </tr>
        <tr *ngIf="!canViewRecords">
          <td class="text-danger" colspan="100">
              {{'CUSTOMER_TYPE.PERMISSION_DENIED' | translate:param}}
          </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="12">
            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
          </td>
        </tr>
        </tfoot>
      </table>


    </div>
  </div>
</section>