import { findIndex } from 'rxjs/operator/findIndex';
import { Component, OnInit, Input, Output, EventEmitter, OnDestroy, OnChanges } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { UserService } from './../../shared/services/user.service';
import { CustomValidators } from 'ng2-validation';
import { Select2OptionData } from "ng2-select2";
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;
@Component({
    selector: 'edit-reference-user',
    templateUrl: '../referenceUser.actions.html'
})

export class EditReferenceUserComponent implements OnInit {

    referenceUserForm: FormGroup;
    public pageType: string = "Edit";
    public customerTypes: any[] = [];
    public select2Option: any = {
        width: '100%',
        multiple: true,
    };
    @Input() data;
    @Input() getHiddeneditRU;
    @Input() customerTypeList: any[];
    @Output() sendHiddeneditRU = new EventEmitter();
    @Output() listeditedData = new EventEmitter();

    private sub: any;
    constructor(
        private _fb: FormBuilder,
        private US: UserService,
        public translate: TranslateService
    ) { 
        translate.get('REF_USER.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.buildForm();
        this.referenceUserForm.patchValue(this.data);
        
        // Ensure customer_type_ids is properly initialized
        if (this.data.customer_type_ids && this.data.customer_type_ids.length > 0) {
            // Convert string IDs to numbers if needed and ensure they are numbers
            const customerTypeIds = this.data.customer_type_ids.map(id => {
                const numId = typeof id === 'string' ? parseInt(id) : id;
                return numId;
            });
            this.referenceUserForm.controls['customer_type_ids'].patchValue(customerTypeIds);
        } else {
            this.referenceUserForm.controls['customer_type_ids'].patchValue([]);
        }
    }

    findIndex(searchTearm: any, property: any, targetArray: any[]) {
        for (let i = 0; i < targetArray.length; i++) {
            if (searchTearm === targetArray[i][property]) { return i };
        }
        return -1;
    }

    buildForm() {
        this.referenceUserForm = this._fb.group({
            name: ['', Validators.required],
            contact: ['', [Validators.required, CustomValidators.digits]],
            customer_type_ids: [[], Validators.required]
        })
        // this.referenceUserForm.patchValue(data);
    }
    toggleChild() {
        this.referenceUserForm.reset();
        this.getHiddeneditRU = !this.getHiddeneditRU;
        this.sendHiddeneditRU.emit(this.getHiddeneditRU);
    }
    saveReferenceUser() {
        if (this.referenceUserForm.valid) {
            // console.log("this.referenceUserForm.value : ", this.referenceUserForm.value);
            this.sub = this.US.updateReferenceUser(this.data.id, this.referenceUserForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        // --- Resetting the form is not required if the edit form it not Hidden after editing--- // 
                        this.referenceUserForm.reset();
                        let response = res.data;
                        this.listeditedData.emit(response);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.referenceUserForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({
                hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    customerTypeChanged(event: any) {
        let customerType = <FormControl>this.referenceUserForm.controls['customer_type_ids'];
        let selectedCustomerIds: any[] = customerType.value || [];
        if (event && event.id) {
            selectedCustomerIds.push(event.id);
            customerType.patchValue(selectedCustomerIds);
        }
    }

    removedCustomerType(event: any) {
        let customerType = <FormControl>this.referenceUserForm.controls['customer_type_ids'];
        let selectedCustomerIds: any[] = customerType.value || [];
        if (event && event.id) {
            const index = selectedCustomerIds.indexOf(event.id);
            if (index > -1) {
                selectedCustomerIds.splice(index, 1);
                customerType.patchValue(selectedCustomerIds);
            }
        }
    }

    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}