import { Component, OnInit } from '@angular/core';
import { RoomCategoryService } from './../../shared/services/roomCategory.service';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

@Component({
    selector: 'room-category',
    templateUrl: './roomCategory.component.html'
})
export class RoomCategoryComponent implements OnInit {
    config: any;// New Change ****
    data: any[];
    private sub: any;
    originalData: any[];
    searchQuery: string;
    // child component show/hide variable
    public canViewRecords: boolean;
    public hiddenAddRC: boolean = true;
    public hiddenEditRC: boolean = true;

    public selectedRC : any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****

    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private RCS : RoomCategoryService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() {
        this.canViewRecords = true;
        this.sub = this.RCS.getAllRoomCat()
        .subscribe((res) => {
            if(res.status == "success"){
                this.data = res.data;
                this.originalData = res.data;
                // console.log("this.data : ",this.data);
            }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }
    // show and handle add room category child component
    showAddRC(){
        this.hiddenAddRC = !this.hiddenAddRC;
    }
    handlehiddenAddRC(event){
        this.hiddenAddRC = event.gethiddenAddRC;
        if (this.canViewRecords && event.data) {
            // push to show on list
            this.originalData.push(event.data);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }

    // show and handle edit room category child component
    showEditRC(data){
        this.selectedRC = data;
        // console.log("Selected Room : ",this.selectedRC);
        this.hiddenEditRC = !this.hiddenEditRC;
    }
    handlehiddenEditRC(event){
        this.hiddenEditRC = event.gethiddenEditRC;
        if (this.canViewRecords && event.data) {
            this.data[this.findIndex(event.data.id, "id") - 1] = event.data;
            this.originalData[this.findIndex(event.data.id, "id", this.originalData) - 1] = event.data;
        }
    }
    searchEvent() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter( data => {
                return ((<string>data.name).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    initializeData() {
        this.data = this.originalData;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}