<add-amenities *ngIf="!hideAddAmenity"
 [gethideAddAminity]="hideAddAmenity" (sendhideAddAmenity)="handlehideAddAmenity($event)" >
</add-amenities>


<edit-amenities *ngIf="!hideEditAmenity" [selectedAmenity]="selectedAmenity"
 [gethideEditAminity]="hideEditAmenity" (sendhideEditAmenity)="handlehideEditAmenity($event)">
</edit-amenities>

<section *ngIf="hideAddAmenity && hideEditAmenity " class="widget">
  <header>
    <h4><span class="" style="color: red;"><i class="fa fa-star"></i>&nbsp;&nbsp;{{ 'AMENITIES.AMENITIES_MANAGEMENT' | translate:param }}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="float-sm-right text-right col-sm-6">

    <button *ngIf="auth.roleAccessPermission('amenity','add')" (click)="showAddAminity()" class="display-inline-block btn btn-sm btn-inverse" tooltip="{{ 'AMENITIES.ADD NEW AMENITY' | translate:param }}" placement="top"><i class="fa fa-plus"></i>&nbsp;&nbsp;{{ 'AMENITIES.ADD' | translate:param }}</button>
    <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? searchEvent() : null" placeholder="{{ 'AMENITIES.SEARCH' | translate:param }}">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body table-scroll">
    <div class="mt">

         <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
        <tr>
          <th>
            <mfDefaultSorter by="id">#</mfDefaultSorter>
          </th>
          <th>
            <mfDefaultSorter by="name">{{ 'AMENITIES.NAME' | translate:param }}</mfDefaultSorter>
          </th>
           <th>
            <mfDefaultSorter by="charge">{{ 'AMENITIES.CHARGE' | translate:param }}</mfDefaultSorter>
          </th>
          <th class="no-sort text-center">
            <mfDefaultSorter by="status">{{ 'AMENITIES.STATUS' | translate:param }}</mfDefaultSorter>
          </th>
          <th *ngIf="auth.roleAccessPermission('amenity','edit')"class="no-sort text-center">
            <mfDefaultSorter by="status">{{ 'AMENITIES.ACTION' | translate:param }}</mfDefaultSorter>
          </th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let ds of mf.data; let i =index">
          <td>{{findIndex(ds.id,"id")}}</td>
          <td><span class="uppercase fw-semi-bold">{{ds.name}}</span></td>
         <td><span class=""><i class="fa fa-inr"></i>&nbsp;{{ds.charge}}</span></td>
          <td class="text-center">
            <span class="text-success" *ngIf="ds.status">{{ 'AMENITIES.ACTIVE' | translate:param }}</span>
            <span class="text-danger" *ngIf="!ds.status">{{ 'AMENITIES.INACTIVE' | translate:param }}</span>
          </td>
          <td *ngIf="auth.roleAccessPermission('amenity','edit')" class="width-100 text-center">
            <button (click)="showEditAmenity(ds)" class="btn btn-xs btn-default" tooltip="{{ 'AMENITIES.EDIT_AMENITY_DETAILS' | translate:param }}" placement="top"><i class="fa fa-pencil"></i>&nbsp;&nbsp;{{ 'AMENITIES.EDIT' | translate:param }}</button>
          </td>
        </tr>
        <tr *ngIf="canViewRecords && mf.data.length === 0">
          <td colspan="100">
            {{ 'AMENITIES.NO MATCHES' | translate:param }}
          </td>
        </tr>
        <tr *ngIf="!canViewRecords">
          <td class="text-danger" colspan="100">
            You currently don't have the permission to view these records.
          </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <td colspan="12">
            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
          </td>
        </tr>
        </tfoot>
      </table>


    </div>
  </div>
</section>