import { UserService } from './../../shared/services/user.service';
import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
// import * as data  from './../data';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

@Component({
    selector: 'guest-list',
    templateUrl: './guest.list.component.html',
    styleUrls: ['./guest.list.component.scss']
})

export class GuestComponent implements OnInit {
    @ViewChild('staticModal') sm: ModalDirective;
    public isModalShown: boolean = false;
    config: any;// New Change ****
    selectedGuest: any;
    data: any[] = [];
    originalData: any[];
    searchQuery: string;
    // service
    private sub: any;
    private getGuest: any;
    public canViewRecords: boolean;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private US: UserService,
        private auth: AuthGuard,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    ngOnInit() {
        this.canViewRecords = true;
        this.sub = this.US.getAllGuestDetails()
            .subscribe((res) => {
                if (res.status == "success") {
                    this.data = res.data;
                    // console.log("this.data : ",this.data);
                    this.originalData = res.data;
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
    }
    findIndex = (searchTerm, property, array?) => {
        if (array) {
            for (var i = 0, len = array.length; i < len; i++) {
                if (array[i][property] === searchTerm) return (i + 1);
            }
            return -1;
        }else{
            for (var i = 0;  i < this.data.length; i++) {
                if (this.data[i][property] === searchTerm) return (i + 1);
            }
            return -1;
        }
    }
    showGuestDetails(guest) {
        this.getGuest = this.US.getGuestDetail(guest)
        .subscribe((res) => {
            if(res.status == "success"){
                // console.log(res.data)
                this.selectedGuest = res.data;
                this.sm.show();
            }
        })
    }
    search() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery != "") {
            this.data = this.data.filter( data => {
                return ((<string>data.name).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else {
            this.initializeData();
        }
    }
    clearSearch() {
        this.initializeData();
        this.searchQuery = undefined;
    }
    initializeData() {
        this.data = this.originalData;
    }
    ngOnDestroy(){
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if(this.sub){
            this.sub.unsubscribe();
        }
        if(this.getGuest){
            this.getGuest.unsubscribe();
        }
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}