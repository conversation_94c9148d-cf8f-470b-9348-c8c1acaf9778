<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-user-circle"></i>&nbsp;&nbsp;{{pageName}} {{ 'USER.ADD_PAGE.USER' | translate:param }}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item "><a (click)="toggleUserComponent()" href="javascript:void(0)">{{ 'USER.ADD_PAGE.USER_MANAGEMENT' | translate:param }}</a></li>
    <li class="breadcrumb-item active">{{pageName}} {{'USER.ADD_PAGE.USER' | translate:param }}</li>
  </ol>

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="userAdd" (ngSubmit)="saveUser()">
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'USER.ADD_PAGE.FIRST_NAME' | translate:param }}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="userAdd.controls.first_name.errors?.backend">{{userAdd.controls.first_name.errors?.backend}}</span>
              <input type="text"  formControlName="first_name" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="(!userAdd.controls.first_name.valid && !userAdd.controls.first_name.pristine) ">
                <span [hidden]="!userAdd.controls.first_name.errors?.required">{{ 'USER.ADD_PAGE.VALID_MSG.FNAME_REQ' | translate:param }}</span>
              </span>
                
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'USER.ADD_PAGE.LAST_NAME' | translate:param }}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="userAdd.controls.last_name.errors?.backend">{{userAdd.controls.last_name.errors?.backend}}</span>
              <input type="text"  formControlName="last_name" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!userAdd.controls.last_name.valid && !userAdd.controls.last_name.pristine">
                <span [hidden]="!userAdd.controls.last_name.errors?.required">{{ 'USER.ADD_PAGE.VALID_MSG.LNAME_REQ' | translate:param }}</span>
                <span [hidden]="!userAdd.controls.last_name.errors?.backend">{{userAdd.controls.last_name.errors?.backend}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{ 'USER.ADD_PAGE.USER_ROLE' | translate:param }}</label>
            <div class="col-md-8 capitalized">
              <ng-select
                [items]="roleList"
                bindLabel="text"
                bindValue="id"
                formControlName="role_id"
                (change)="userRoleChanged($event)"
                (selectionChanges)="userRoleChanged($event)"
                (ngModelChange)="userRoleChanged($event)"
                [clearable]="true"
                [searchable]="true"
                placeholder="Select role">
              </ng-select>
              <span class="errMsg __fromBackend" *ngIf="userAdd.controls.role_id.errors?.backend">{{userAdd.controls.role_id.errors?.backend}}</span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'USER.ADD_PAGE.EMAILID' | translate:param }}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="userAdd.controls.email.errors?.backend">{{userAdd.controls.email.errors?.backend}}</span>
              <input type="text"  class="form-control" formControlName="email" placeholder="">
              <span class="errMsg" *ngIf="!userAdd.controls.email.valid && !userAdd.controls.email.pristine">
                <span [hidden]="!userAdd.controls.email.errors?.required">{{ 'USER.ADD_PAGE.VALID_MSG.EMAIL_REQ' | translate:param }}</span>
                <span [hidden]="!userAdd.controls.email.errors?.email">{{ 'USER.ADD_PAGE.VALID_MSG.VALID_EMAIL' | translate:param }}</span>
                <!-- <span [hidden]="!userAdd.controls.email.errors?.backend">{{userAdd.controls.email.errors?.backend}}</span> -->
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'USER.ADD_PAGE.MOBILE_NO' | translate:param }}</label>
            <div class="col-md-8 ">
              <input type="text"  maxlength="10" minlength="10" class="form-control" placeholder="" formControlName="mobile_no" onkeypress='return event.charCode >= 48 && event.charCode <= 57'>
               <span class="errMsg" *ngIf="!userAdd.controls.mobile_no.valid && !userAdd.controls.mobile_no.pristine">
                <span [hidden]="!userAdd.controls.mobile_no.errors.digits">{{ 'USER.ADD_PAGE.VALID_MSG.VALID_MOBILE_NUM' | translate:param }}</span>
                <span [hidden]="!userAdd.controls.mobile_no.errors?.backend">{{userAdd.controls.mobile_no.errors?.backend}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'USER.ADD_PAGE.DOB' | translate:param }}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="userAdd.controls.dob.errors?.backend">{{userAdd.controls.dob.errors?.backend}}</span>
               <datetime formControlName="dob" [timepicker]="false"  [datepicker]="datepickerOpts"></datetime>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{ 'USER.ADD_PAGE.COUNTRY' | translate:param }}</label>
            <div class="col-md-8">
              <ng-select 
                [items]="countryList || []"
                bindLabel="text"
                bindValue="id"
                formControlName="country"
                (change)="countryChanged($event)"
                (selectionChanges)="countryChanged($event)"
                class="countrySelections"
                [clearable]="true"
                [searchable]="true"
                placeholder="Select country"
                notFoundText="No countries found">
              </ng-select>
              <span class="errMsg __fromBackend" *ngIf="userAdd.controls.country.errors?.backend">{{userAdd.controls.country.errors?.backend}}</span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{ 'USER.ADD_PAGE.CITY' | translate:param }}</label>
            <div class="col-md-8 ">
              <input type="text" formControlName="city" class="form-control">
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'USER.ADD_PAGE.ZIP_POSTAL' | translate:param }}</label>
            <div class="col-md-8 ">
              <input type="text"  maxlength="6" onkeypress='return (event.charCode >= 48 && event.charCode <= 57)'  class="form-control" formControlName="zip" placeholder="">
               <span class="errMsg" *ngIf="!userAdd.controls.zip.valid">
                 <span [hidden]="!userAdd.controls.zip.errors?.backend">{{userAdd.controls.dob.errors?.backend}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{ 'USER.ADD_PAGE.ADDRESS' | translate:param }}</label>
            <div class="col-md-8 ">
              <textarea rows="3" class="autogrow form-control transition-height" id="elastic-textarea" autosize placeholder="" formControlName="address"></textarea>
               <span class="errMsg" *ngIf="!userAdd.controls.address.valid ">
                 <span [hidden]="!userAdd.controls.zip.errors?.backend">{{userAdd.controls.dob.errors?.backend}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{ 'USER.ADD_PAGE.GENDER' | translate:param }}</label>
            <div class="col-md-8">
              <div class="radio-horizontal">
                <div class="abc-radio" *ngFor="let g of gender">
                  <input type="radio" formControlName="gender" [id]="g" [value]="g">
                  <label class="capitalize" [for]="g">
                        {{g}}
                      </label>
                </div>
              </div>
            </div>
            <!-- <div class="col-md-4">
                <div class="radio-horizontal">
                <div class="abc-checkbox abc-checkbox-primary">
                  <input type="checkbox" id="is_admin_check" value="true" formControlName="is_admin">
                  <label class="capitalize" for="is_admin_check" >
                        is Admin ?
                      </label>
                </div>
              </div>
            </div> -->
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{ 'USER.ADD_PAGE.STATUS' | translate:param }}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio-s1" [value]="true">
                  <label for="radio-s1">
                    {{ 'USER.ADD_PAGE.ACTIVE' | translate:param }}
                      </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio-s2" [value]="false">
                  <label for="radio-s2">
                    {{ 'USER.ADD_PAGE.INACTIVE' | translate:param }}
                      </label>
                </div>
              </div>
            </div>
          </div>
          <!--<pre>
            {{ userAdd.value | json}}
          </pre>-->
          <div class="form-group row">

            <div class="col-md-8 offset-md-3">
              <div class="">
                <button [disabled]="!userAdd.valid" type="submit" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{ 'USER.ADD_PAGE.SAVE' | translate:param }}</button>
                <button (click)="toggleUserComponent()" class="btn btn-sm btn-secondary">{{ 'USER.ADD_PAGE.CANCEL' | translate:param }}</button>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
