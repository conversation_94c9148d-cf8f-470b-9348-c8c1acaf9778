import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable()
export class GoogleMapService {

    constructor(private http: HttpClient) {
    }

    getAddrFromLtLng(lat: number = 19.0760098, lng: number = 72.8780646)
    {
        console.log("API Called :");
        let baseUrl = `https://maps.googleapis.com/maps/api/geocode/json?key=AIzaSyDe_oVpi9eRSN99G4o6TwVjJbFBNr58NxE&latlng=${lat},${lng}&sensor=true`;
        return this.http.get(baseUrl).map((res) => res);
    }
}