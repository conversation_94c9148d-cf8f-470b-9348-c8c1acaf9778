<div class="row">
        <div class="" [ngClass]="{'col-md-12': (hiddenAdd && hiddenEdit),'col-md-6': (!hiddenAdd || !hiddenEdit)}">
          <section class="widget">
            <header>
              <h4><span class="" style="color: red;"><i class="fa fa-calendar-times-o"></i>&nbsp;&nbsp;{{ 'EXPENSES.EXPENSES_MANAGEMENT' | translate:param }}</span></h4>
            </header>
            <hr class="large-hr">
            <div class="float-sm-right text-right col-sm-12">
              <button *ngIf="auth.roleAccessPermission('expense','add')" class="display-inline-block btn btn-sm btn-inverse"
                (click)="showAdd()" [disabled]="!hiddenAdd || !hiddenEdit" tooltip="Add new cancellation policy" placement="top">
                <i class="fa fa-plus"></i>&nbsp;&nbsp;{{ 'EXPENSES.ADD' | translate:param }}
              </button>
              <div class="form-group display-inline-block __search">
                <input type="text" class="form-control" placeholder="{{ 'EXPENSES.SEARCH' | translate:param }}" (keyup)="canViewRecords ? search() : null"
                  [(ngModel)]="searchQuery">
                <span class="form-group-addon"><i class="fa fa-search"></i></span>
                <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
              </div>
            </div>
            <div class="clearfix"></div>
            <div class="widget-body">
              <div class="mt">
      
                <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
                  <thead>
                    <tr>
                      <th>
                        <mfDefaultSorter by="id">#</mfDefaultSorter>
                      </th>
                      <th>
                        <mfDefaultSorter by="day_before">{{ 'EXPENSES.NOTE' | translate:param }}</mfDefaultSorter>
                      </th>
                      <th>
                        <mfDefaultSorter by="charge">{{ 'EXPENSES.DATE' | translate:param }}</mfDefaultSorter>
                      </th>
                      <th>
                        <mfDefaultSorter by="name">{{ 'EXPENSES.AMOUNT' | translate:param }}</mfDefaultSorter>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let ds of mf.data; let i =index" [ngClass]="{'__selected': (ds.id == selectedPolicy?.id)}">
                      <td>{{i + 1}}</td>
                      <td><span class="fw-semi-bold">{{ds.note}}</span></td>
                      <td><span class="">{{ds.expense_date | date}}</span></td>
                      <td><i *ngIf="ds.amount" class="fa fa-inr"></i>{{ds.amount}}</td>
                    </tr>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                      <td colspan="100">
                        {{ 'EXPENSES.NO MATCHES' | translate:param }}
                      </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                      <td class="text-danger" colspan="100">
                       {{ 'EXPENSES.PERMISSION_DENIED' | translate:param }}
                      </td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colspan="12">
                        <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </section>
        </div>
        <div class="col-md-6">
          <add-expenses *ngIf="!hiddenAdd" (sendAddedEdited)="addtoList($event) " (closeComp)="closeAdd($event)"></add-expenses>
          <!-- Edit is not available for expenses modules -->
          <!-- <edit-expenses *ngIf="!hiddenEdit" [selectedPolicy]="selectedPolicy" (sendEdited)="addupdatedtoList($event)"
            (closeComp)="closeEdit($event)"></edit-expenses> -->
        </div>
      </div>