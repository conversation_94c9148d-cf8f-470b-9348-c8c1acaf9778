import { Directive, ElementRef, Input, HostListener, AfterViewInit, EventEmitter, Output } from '@angular/core';

@Directive({
    selector: '[isDisabled]'
})

export class IsDisabled {

    element: any;
    @Input() percentageInput: any;
    @Input() amountInput: any;
    @Input() discountTypeInput: any;
    @Input() formGroupName : any; 
    @Output() disType = new EventEmitter();
    constructor(el: ElementRef) {
        this.element = el.nativeElement;
    }

    @HostListener('keyup', ['$event'])
    onkeyup($event) {
        let emiitedData;
        if ($event.target == this.percentageInput || $event.target == this.amountInput) {
            if ($event.target == this.percentageInput) {
                if ($event.target.value == "") {
                    this.amountInput.disabled = false;
                    emiitedData = {
                        'status': 'none',
                        'id': this.formGroupName,
                        'input': 'percentage'
                    }
                    this.disType.emit(emiitedData);
                    emiitedData = '';
                } else {
                    this.amountInput.disabled = true;
                      emiitedData = {
                        'status': 'percentage',
                        'id': this.formGroupName,
                        'input': 'amount'
                    }
                    this.disType.emit(emiitedData);
                    emiitedData = '';
                }
            } else {
                if ($event.target.value == "") {
                    this.percentageInput.disabled = false;
                    emiitedData = {
                        'status': 'none',
                        'id': this.formGroupName,
                        'input': 'amount'
                    }
                    this.disType.emit(emiitedData);
                    emiitedData = '';
                } else {
                    this.percentageInput.disabled = true;
                    emiitedData = {
                        'status': 'amount',
                        'id': this.formGroupName,
                        'input': 'percentage'
                    }
                    this.disType.emit(emiitedData);
                    emiitedData = '';
                }
            }
            
        }
    }

    ngAfterViewInit() {
        if (this.percentageInput.value == "" && this.amountInput.value != "") {
            this.percentageInput.disabled = true
        } else if (this.amountInput.value == "" && this.percentageInput.value != "") {
            this.amountInput.disabled = true
        } else if (this.percentageInput.value == "" && this.amountInput.value == "") {
            this.amountInput.disabled = false;
            this.percentageInput.disabled = false
        }
    }
}