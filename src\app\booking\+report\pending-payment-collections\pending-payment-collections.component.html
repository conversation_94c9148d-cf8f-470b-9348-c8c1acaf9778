<section class="widget">
    <header>
        <div class="row">
            <div class="col-sm-6">
                <h4>
                    <span class="red-header">
                        <i class="fa fa-clipboard"></i>&nbsp;&nbsp;
                        <span class="text-capitalize">
                            {{'PEN_PAYM_REPORT.PEN_PAY_COLL' | translate:param}}
                        </span>
                        {{'PEN_PAYM_REPORT.REP_MAN' | translate:param}}
                    </span>
                </h4>
            </div>
            <div class="__download">
                <button class="btn btn-sm btn-primary pull-right" *ngIf="data && data.length > 0" (click)="printRecords()">
                    {{'PEN_PAYM_REPORT.DOWN' | translate:param}} .csv
                    <i class="fa fa-download"></i>
                </button>
            </div>
            <div class="float-sm-right text-right col-sm-4">
                <div class="row">
                </div>
                <div class="form-group __search">
                    <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="search()" placeholder="{{'PEN_PAYM_REPORT.SEARCH' | translate:param}}">
                    <span class="form-group-addon">
                        <i class="fa fa-search"></i>
                    </span>
                    <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()">
                        <i class="fa fa-times"></i>
                    </span>
                </div>
            </div>
        </div>
    </header>
    <hr class="large-hr">
    <form [formGroup]="searchForm" (ngSubmit)="searchReports()">
        <!-- Filters -->
        <div class="row filter-row">
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'PEN_PAYM_REPORT.F_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime  [timepicker]="false" formControlName="fromDate"
                            [datepicker]="datepickerOpts" (ngModelChange)="toDataChange($event)"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'PEN_PAYM_REPORT.T_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime  [timepicker]="false" formControlName="toDate"
                            [datepicker]="datepickerOpts"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <button type="submit" class="btn btn-primary pull-right">Search</button>
            </div>
        </div>
        <!-- Amount Summary -->
        <div class="row" *ngIf="this.data && reportType != 'guest'" style="margin-top: 15px;">
            <div class="col-sm-12 col-md-8 col-lg-3">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'PEN_PAYM_REPORT.SUMM' | translate:param}}
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr>
                            <td>
                                {{'PEN_PAYM_REPORT.TOT_AMT' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{totalAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </form>
    <div class="clearfix"></div>
    <div class="widget-body table-scroll">
        <div class="mt">
            <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
                <thead>
                    <tr>
                        <th>
                            <mfDefaultSorter by="id">#</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="name">{{'PEN_PAYM_REPORT.NAME' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="unique_booking_id">{{'PEN_PAYM_REPORT.UNIQ_BOOK_ID' | translate:param}}</mfDefaultSorter>
                        </th>
                        <!-- class="no-sort text-center" -->
                        <th>
                            <mfDefaultSorter by="check_out">{{'PEN_PAYM_REPORT.CHECK_DATE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="payment_date">{{'PEN_PAYM_REPORT.PAY_DATE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter>{{'PEN_PAYM_REPORT.PAVATI_NO' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="amount">{{'PEN_PAYM_REPORT.AMOUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let ds of mf.data; let i = index">
                        <td>{{i + 1}}</td>
                        <td><span class="fw-semi-bold">{{ds.name}}</span></td>
                        <td><span class="">{{ds.unique_booking_id}}</span></td>
                        <td><span class="">{{ds.check_out ? (ds.check_out | date) : '-'}}</span></td>
                        <td><span class="">{{ds.payment_date ? (ds.payment_date | date) : '-'}}</span></td>
                        <td><span class="">{{ds.pavati_no | commaSeparated}}</span></td>
                        <td style="text-align: center;"><span  ><i class="fa fa-inr"></i> {{ds.amount}}</span></td>
                    </tr>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                        <td colspan="100">
                           {{'PEN_PAYM_REPORT.NO MATCHES' | translate:param}}
                        </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                        <td class="text-danger" colspan="100">
                            {{'PEN_PAYM_REPORT.PERMISSION_DENIED' | translate:param}}
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="12">
                            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</section>