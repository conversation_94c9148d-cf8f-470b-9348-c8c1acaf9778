import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { GuestComponent } from './+guestList/guest.list.component';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { NgSelectModule } from '@ng-select/ng-select';
import { ModalModule } from 'ngx-bootstrap/modal';
import { TextMaskModule } from 'angular2-text-mask';
import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';

import 'jasny-bootstrap/js/inputmask.js';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

export const route = [
    { path: '', component: GuestComponent, pathMatch: 'full'}
];

@NgModule({
    imports: [
        FormsModule,
        CommonModule,
        TooltipModule.forRoot(),
        NgSelectModule,
        TextMaskModule,
        DataTableModule,
        ModalModule.forRoot(),
        RouterModule.forChild(route),
        TranslateModule.forRoot({
        loader:{ 
            provide: TranslateLoader, 
            useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
            deps: [HttpClient] 
        }})
    ],
    exports: [],
    declarations: [
        GuestComponent
    ],
    providers: [],
})
export class GuestModule { }
