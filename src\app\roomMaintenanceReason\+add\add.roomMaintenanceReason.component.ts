import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Select2OptionData } from 'ng2-select2';
import { RoomMaintenanceReasonService } from 'app/shared/services/roomMaintenanceReason.service';
// import * as data from '../../data';
declare var Messenger: any;
@Component({
    selector: 'add-room-maintenance',
    templateUrl: '../roomMaintenanceReason.actions.component.html'
})

export class AddRoomMaintenanceReasonComponent implements OnInit {
    public pageName: string = "Add";
    select2Options: any = {
        width: '100%'
    };
    roomForm: FormGroup;
    //services
    private sub: any;
    private floor: any;
    private floorList: any;
    private getData: any;
    // Input and Outputs
    @Input() getHiddenAR;
    @Output() sendHiddenAR = new EventEmitter();

    public roomCat: any[];
    public wing: any[];

    constructor(
        private _fb: FormBuilder,
        private RS: RoomMaintenanceReasonService
    ) {}

    ngOnInit() {
        this.buildForm();
    }

    buildForm() {
        this.roomForm = this._fb.group({
            title: ['', Validators.required],
            description: ['', []],
            status: [true, Validators.required]
        })
    }

    // save
    saveRoom() {
        if (this.roomForm.valid) {
            this.sub = this.RS.saveRoomMaintenanceData(this.roomForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.toggleChild(res.data);
                    }
                },(err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.roomForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        }else{
            console.log("Conditions false")            
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    // toggle to mains
    toggleChild(data) {
        this.getHiddenAR = !this.getHiddenAR;
        let result;
        if (data) {
            result = { 'getHiddenAR': this.getHiddenAR, 'data': data }
        } else {
            result = { 'getHiddenAR': this.getHiddenAR }
        }
        this.sendHiddenAR.emit(result);
    }
}