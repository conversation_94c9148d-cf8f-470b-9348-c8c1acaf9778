import { CustomValidators } from 'ng2-validation';
import { UserRoleService } from './../../../shared/services/userrole.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { Select2OptionData } from 'ng2-select2';
import { TranslateService } from '@ngx-translate/core';

declare var jQuery: any;
declare var Messenger: any;

@Component({
    selector: 'edit-role',
    templateUrl: '../userRole.actions.html'
})
export class EditRoleComponent implements OnInit {
    pageType: string = "Edit";

    userRole: FormGroup;
    select2Options: any = {
        "width": "100%"
    }
    // Input/Outputs
    @Input() selecteUR;
    @Input() gethideEditUR;
    @Input() getGroupListData;
    @Output() sendhideEditUR = new EventEmitter();

    //services variables
    private addRoleService: any;

    dropdownSelect: any;

    constructor(
        private _fb: FormBuilder,
        private URS: UserRoleService,
        public translate: TranslateService
    ) {
        translate.get('USER_ROLE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
        this.buildFrom()

    }
    buildFrom() {
        this.userRole = this._fb.group({
            name: ['', Validators.required],
            group_id: ['',[ Validators.required, CustomValidators.digits]],
            status: ['', Validators.required],
            description: ['']
        })
    }
    ngOnInit() {
        console.log("Selected User : ",this.selecteUR);
        console.log("Selected User : ",this.getGroupListData);
        this.userRole.patchValue(this.selecteUR);
        this.dropdownSelect = this.userRole.controls['group_id'].value;

    }
    addUserRole() {
        if (this.userRole.value) {
            this.addRoleService = this.URS.UpdateUserRole(this.selecteUR.id, this.userRole.value)
                .subscribe((res) => {
                    if (res.status === "success") {
                        this.userRole.reset();
                        console.log("User role Updated response : ",res);
                        res.data['group.name'] = this.getGroupListData[this.findIndex(res.data.group_id) - 1].name;
                        this.toggleChild(res.data);
                    }
                },
                (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.userRole.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    findIndex(searchTerm) {
        for (var i = 0, len = this.getGroupListData.length; i < len; i++) {
            if (this.getGroupListData[i].id === searchTerm) return (i + 1);
        }
        return -1;
    }
    // dropdown on change update controler group value
    groupChanges(event) {
        console.log("changed group id : ",event);
        this.userRole.controls['group_id'].patchValue(event.value)
    }
    // method simply just returns data needed for to select dropdown to work
    getSelect2DefaultList(): Select2OptionData[] {
        return jQuery.map(this.getGroupListData, function (obj) {
            return { id: obj.id, text: obj.name };
        })
    }
    toggleChild(data) {
        let result;
        this.gethideEditUR = !this.gethideEditUR;
        if (data) {
            result = { gethideEditUR: this.gethideEditUR, data: data }
        } else {
            result = { gethideEditUR: this.gethideEditUR }
        }
        this.sendhideEditUR.emit(result);
    }

    ngOnDestroy() {
        if (this.addRoleService) {
            this.addRoleService.unsubscribe();
        }
    }
}