import { CustomValidators } from 'ng2-validation';
import { FormBuilder, Validators, FormGroup } from '@angular/forms';
// import { EventEmitter } from '@angular/common/src/facade/async';
import { Output, EventEmitter } from '@angular/core';
import { Component, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'edit-expenses',
    templateUrl: '../expenses.action.component.html'
})

export class EditExpenseComponent implements OnInit {
    pageType: string = 'Edit';
    @Input() selectedPolicy;
    @Output() sendEdited = new EventEmitter();
    @Output() closeComp = new EventEmitter();
    expensesForm: FormGroup;
    constructor(
        private fb: FormBuilder,
        public translate: TranslateService
    ) { 
        translate.get('EXPENSES.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.initForm();
    }

    initForm() {
        this.expensesForm = this.fb.group({
            note: ['', Validators.required],
            amount: ['', [Validators.required, CustomValidators.gte(0)]]
        });
    }

    closeThisComp() {
        this.closeComp.emit(true);
    }
}