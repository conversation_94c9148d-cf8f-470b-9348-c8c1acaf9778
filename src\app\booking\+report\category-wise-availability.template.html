<section class="widget">
    <header>
        <div class="row">
            <div class="col-sm-6">
                <h4>
                    <span class="" style="color: red;" *ngIf="reportType">
                        <i class="fa fa-clipboard"></i>&nbsp;&nbsp;
                        <span class="text-capitalize">
                            {{reportType}}
                        </span>
                        {{'CATEG_REPORT.REP_MAN'| translate:param}}</span>
                </h4>
            </div>
            <div class="__download float-sm-right col-sm-2">
                <button class="btn btn-sm btn-primary pull-right" *ngIf="data && data.length > 0"
                    (click)="printRecords()">
                    {{'CATEG_REPORT.DOWN'| translate:param}} .csv
                    <i class="fa fa-download"></i>
                </button>
            </div>
            <div class="float-sm-right text-right col-sm-4">
                <div class="row">
                </div>
                <div class="form-group __search">
                    <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="search()"
                        placeholder="{{'CATEG_REPORT.DOWN'| translate:param}}">
                    <span class="form-group-addon">
                        <i class="fa fa-search"></i>
                    </span>
                    <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()">
                        <i class="fa fa-times"></i>
                    </span>
                </div>
            </div>
        </div>
    </header>
    <hr class="large-hr">
    <form [formGroup]="searchForm" (ngSubmit)="searchReports()">
        <!-- Filters -->
        <div class="row filter-row" style="margin-bottom: 15px;" *ngIf="reportType != 'agents'">
            <!-- Room Type Filter -->
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'CATEG_REPORT.ROOM_TYP'| translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <ng-select  [items]="roomTypeList" bindLabel= "text"  bindValue="id" formControlName="room_type_id" [(ngModel)]="selectedFilterTypes.room_type_id" [searchable]="true" [clearable]="true" placeholder="Select Room Type" (change)="roomTypeChanged($event)"></ng-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row filter-row">
            <!-- From Date Filter -->
            <div class="col-sm-4">
                <div class="row" *ngIf="reportType != 'guest'">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'CATEG_REPORT.F_DATE'| translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime class="custom-width-datetime" [timepicker]="false" formControlName="fromDate"
                            [datepicker]="datepickerOpts" (ngModelChange)="toDataChange($event)"></datetime>
                    </div>
                </div>
            </div>
            <!-- To Date Filter -->
            <div class="col-sm-4">
                <div class="row" *ngIf="reportType != 'guest'">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px">
                            {{'CATEG_REPORT.T_DATE'| translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime class="custom-width-datetime" [timepicker]="false" formControlName="toDate"
                            [datepicker]="datepickerOpts"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <button type="submit" class="btn btn-primary pull-right">Search</button>
            </div>
        </div>
        <!--Availability Summary -->
        <div class="row" *ngIf="data" style="margin-top: 15px;">
            <div class="col-sm-3">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'CATEG_REPORT.SUMM'| translate:param}}
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr>
                            <td>
                                <strong>
                                    {{'CATEG_REPORT.TOTAL_ROOM'| translate:param}} :
                                </strong>
                            </td>
                            <td>
                                <strong>
                                    {{totalRooms}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-danger">
                                <strong>
                                    {{'CATEG_REPORT.OCCU'| translate:param}} :
                                </strong>
                            </td>
                            <td>
                                <strong>
                                    {{totalRoomsOccupied}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td class="text-success">
                                <strong>
                                    {{'CATEG_REPORT.AVAI'| translate:param}} :
                                </strong>
                            </td>
                            <td>
                                <strong>
                                    {{totalRoomsAvailable}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </form>
    <div class="clearfix"></div>
    <!-- Table for Category-wise-availability report -->
    <div class="widget-body table-scroll">
        <div class="mt">
            <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable"
                [mfRowsOnPage]="25">
                <thead>
                    <tr>
                        <th>
                            <mfDefaultSorter>#</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter>{{'CATEG_REPORT.ROOM_TYP'| translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter [by]="totalCount">{{'CATEG_REPORT.TOT'| translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter [by]="occupiedCount" class="text-danger">
                                {{'CATEG_REPORT.OCCU'| translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter [by]="freeCount" class="text-success">
                                {{'CATEG_REPORT.AVAI'| translate:param}}</mfDefaultSorter>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let ds of mf.data; let i = index;">
                        <td>{{ i + 1 }}</td>
                        <td>{{ ds.name }}</td>
                        <td>{{ getTotalRooms(ds) }}</td>
                        <td>{{ ds.occupied_count }}</td>
                        <td>{{ ds.free_count }}</td>
                    </tr>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                        <td colspan="100">
                            {{'CATEG_REPORT.NO MATCHES'| translate:param}}
                        </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                        <td class="text-danger" colspan="100">
                            {{'CATEG_REPORT.PERMISSION_DENIED'| translate:param}}
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="100">
                            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</section>