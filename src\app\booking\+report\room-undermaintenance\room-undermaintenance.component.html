<section class="widget revenue-report">
    <header>
        <div class="row">
            <div class="col-sm-6">
                <h4>
                    <span class="red-header">
                        <i class="fa fa-clipboard"></i>&nbsp;&nbsp;
                        <span class="text-capitalize">
                            {{'ROOM_MAINT_REASON_REPORT.ROOM_MAIN' | translate:param}}
                        </span>
                        {{'ROOM_MAINT_REASON_REPORT.REP_MANAGE' | translate:param}}
                    </span>
                </h4>
            </div>
            <div class="col-sm-2 __download">
                <button class="btn btn-sm btn-primary pull-right" *ngIf="data && data.length > 0" (click)="printRecords()">
                    {{'ROOM_MAINT_REASON_REPORT.PRINT' | translate:param}}
                    <i class="fa fa-print"></i>
                </button>
            </div>
            <div class="float-sm-right text-right col-sm-4">
                <div class="row">
                </div>
                <div class="form-group __search">
                    <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="search()" placeholder="{{'ROOM_MAINT_REASON_REPORT.SEARCH' | translate:param}}">
                    <span class="form-group-addon">
                        <i class="fa fa-search"></i>
                    </span>
                    <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()">
                        <i class="fa fa-times"></i>
                    </span>
                </div>
            </div>
        </div>
    </header>
   <hr class="large-hr">
    <div class="clearfix"></div>
    <div class="widget-body table-scroll">
        <div class="mt">
            <table class="table table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
                <thead>
                    <tr>
                        <th>
                            <mfDefaultSorter by="id">#</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="unique_booking_id">{{'ROOM_MAINT_REASON_REPORT.RES_ID' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="name">{{'ROOM_MAINT_REASON_REPORT.ROOM_TYP' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="title">{{'ROOM_MAINT_REASON_REPORT.ROOM_NO' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="maintence_reason_title">{{'ROOM_MAINT_REASON_REPORT.REASON' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="start">{{'ROOM_MAINT_REASON_REPORT.START_DATE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter by="end_date">{{'ROOM_MAINT_REASON_REPORT.END_DATE' | translate:param}}</mfDefaultSorter>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let ds of mf.data; let i = index">
                        <td>{{i + 1}}</td>
                        <td>{{ds.unique_booking_id}}</td>
                        <td><span class="">{{ds.name}}</span></td>
                        <td><span class="fw-semi-bold">{{ds.title}}</span></td>
                        <td><span class="">{{ds.maintence_reason_title ? ds.maintence_reason_title : '-'}}</span></td>
                        <td>
                            <span class="">
                                {{ds.start ? (ds.start | date:'d MMM yy') : '-'}}
                            </span>
                        </td>
                        <td>
                            <span class="">
                                {{ds.end_date ? (ds.end_date | date:'d MMM yy') : '-'}}
                            </span>
                        </td>
                    </tr>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                        <td colspan="100">
                            {{'ROOM_MAINT_REASON_REPORT.NO MATCHES' | translate:param}}
                        </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                        <td class="text-danger" colspan="100">
                            {{'ROOM_MAINT_REASON_REPORT.PERMISSION_DENIED' | translate:param}}
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="12">
                            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</section>