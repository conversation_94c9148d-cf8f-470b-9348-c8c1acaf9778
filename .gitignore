# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Users Environment Variables
.lock-wscript
# api-env.ts

# OS generated files #
.DS_Store
ehthumbs.db
./Icon?
Thumbs.db

# Node Files #
/node_modules
/bower_components
npm-debug.log

# Ignore vscode folder 
.vscode
vscode
# Typing #
/src/typings/tsd/
/typings/
/tsd_typings/

# Dist #
/dist
/ssl
/public/__build__/
/src/*/__build__/
/__build__/**
/public/dist/
/src/*/dist/
/dist/**
.webpack.json

# Doc #
/doc/

# IDE #
.idea/
*.swp
*.zip