<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-inr"></i>&nbsp;&nbsp;{{pageType}} {{'MANAGE_EXTRA_CHARGES.ADD_PAGE.EXTRA_CHARGES' | translate:param}}</span></h4>
  </header>
  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="extraChargesForm" (ngSubmit)="saveExtraCharges()">
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'MANAGE_EXTRA_CHARGES.ADD_PAGE.NAME' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="extraChargesForm.controls.name.errors?.backend">{{extraChargesForm.controls.name.errors?.backend}}</span>
              <input type="text" class="form-control" formControlName="name" placeholder="">
              <span class="errMsg" *ngIf="!extraChargesForm.controls.name.valid && !extraChargesForm.controls.name.pristine">
              <span [hidden]="!extraChargesForm.controls.name.errors.required">{{'MANAGE_EXTRA_CHARGES.ADD_PAGE.VALID_MSG.NAME_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'MANAGE_EXTRA_CHARGES.ADD_PAGE.CHARGE' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="extraChargesForm.controls.charge.errors?.backend">{{extraChargesForm.controls.charge.errors?.backend}}</span>
              <div class="input-group" [ngClass]="{'has-error': (!extraChargesForm.controls.charge.valid && !extraChargesForm.controls.charge.pristine) || (extraChargesForm.controls.charge.errors?.backend)}">
                <span class="input-group-addon"><i class="fa fa-inr"></i></span>
                <input type="text" class="form-control" formControlName="charge" placeholder="">
              </div>
              <span class="errMsg" *ngIf="!extraChargesForm.controls.charge.valid && !extraChargesForm.controls.charge.pristine">
              <span [hidden]="!extraChargesForm.controls.charge.errors.required">{{'MANAGE_EXTRA_CHARGES.ADD_PAGE.VALID_MSG.CHARGE_REQ' | translate:param}}</span>
              <span [hidden]="!extraChargesForm.controls.charge.errors.number">{{'MANAGE_EXTRA_CHARGES.ADD_PAGE.VALID_MSG.ONLY_DIGIT_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'MANAGE_EXTRA_CHARGES.ADD_PAGE.STATUS' | translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio-1" [value]="true">
                  <label for="radio-1">
                    {{'MANAGE_EXTRA_CHARGES.ADD_PAGE.ACTIVE' | translate:param}}
                      </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio-2" [value]="false">
                  <label for="radio-2">
                    {{'MANAGE_EXTRA_CHARGES.ADD_PAGE.INACTIVE' | translate:param}}
                      </label>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" [disabled]="!extraChargesForm.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'MANAGE_EXTRA_CHARGES.ADD_PAGE.SAVE' | translate:param}}</button>
                <button type="button" (click)="closeThisComp()" class="btn btn-sm btn-secondary">{{'MANAGE_EXTRA_CHARGES.ADD_PAGE.CLOSE' | translate:param}}</button>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
