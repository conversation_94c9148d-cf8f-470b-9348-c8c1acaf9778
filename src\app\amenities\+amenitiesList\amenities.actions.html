

<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-star"></i>&nbsp;&nbsp;{{pageType}} {{ 'AMENITIES.ADD_PAGE.AMENITIES' | translate:param }}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item "><a (click)="toggleChild()" href="javascript:void(0)">{{ 'AMENITIES.ADD_PAGE.BREAD_CRUMB_AMENITIES' | translate:param }}</a></li>
    <li class="breadcrumb-item active">{{pageType}} {{ 'AMENITIES.ADD_PAGE.AMENITIES' | translate:param }}</li>
  </ol>

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="amenitiesAdd" (ngSubmit)="saveAmenities()">
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'AMENITIES.ADD_PAGE.NAME' | translate:param }}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="amenitiesAdd.controls.name.errors?.backend">{{amenitiesAdd.controls.name.errors?.backend}}</span>
            <input type="text"  class="form-control" formControlName="name" name="name" placeholder="">
            <span class="errMsg" *ngIf="!amenitiesAdd.controls.name.valid && !amenitiesAdd.controls.name.untouched">
              <span [hidden]="!amenitiesAdd.controls.name.errors.required">{{ 'AMENITIES.ADD_PAGE.VALID_MSG.NAME_REQ' | translate:param }}</span>
            </span>
          </div>
        </div>

        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'AMENITIES.ADD_PAGE.CHARGE' | translate:param }}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="amenitiesAdd.controls.charge.errors?.backend">{{amenitiesAdd.controls.charge.errors?.backend}}</span>
            <div class="input-group" [ngClass]="{'has-error': (!amenitiesAdd.controls.charge.valid && !amenitiesAdd.controls.charge.untouched) || (amenitiesAdd.controls.charge.errors?.backend)}">
              <span class="input-group-addon"><i class="fa fa-inr"></i></span>
              <input type="text"  class="form-control" formControlName="charge"  placeholder="">
            </div>
            <span class="errMsg" *ngIf="!amenitiesAdd.controls.charge.valid && !amenitiesAdd.controls.charge.untouched">
              <span [hidden]="!amenitiesAdd.controls.charge.errors.required">{{ 'AMENITIES.ADD_PAGE.VALID_MSG.CHARGE_REQ' | translate:param }}</span>
              <span [hidden]="!amenitiesAdd.controls.charge.errors.number">{{ 'AMENITIES.ADD_PAGE.VALID_MSG.ONLY_DIGIT_REQ' | translate:param }}</span>
            </span>
          </div>
        </div>

        <div class="form-group row">
          <label class="col-md-3 col-form-label text-md-right" for="default-select">{{ 'AMENITIES.ADD_PAGE.STATUS' | translate:param }}</label>
          <div class="col-md-8 ">
            <div class="radio-horizontal">
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-1" [value]="true">
                <label for="radio-1">
                  {{ 'AMENITIES.ADD_PAGE.ACTIVE' | translate:param }}
                      </label>
              </div>
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-2" [value]="false">
                <label for="radio-2">
                  {{ 'AMENITIES.ADD_PAGE.INACTIVE' | translate:param }}
                      </label>
              </div>
            </div>
          </div>
        </div>

        <div class="form-group row">
          <div class="col-md-8 offset-md-3">
            <div class="">
              <button type="submit" [disabled]="!amenitiesAdd.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{ 'AMENITIES.ADD_PAGE.SAVE' | translate:param }}</button>
              <button (click)="toggleChild()" class="btn btn-sm btn-secondary">{{ 'AMENITIES.ADD_PAGE.CANCEL' | translate:param }}</button>
            </div>
          </div>
        </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
