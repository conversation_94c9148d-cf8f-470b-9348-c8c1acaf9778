import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { BookingService } from './../../shared/services/booking.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from "@angular/router";
import { ModalDirective } from 'ngx-bootstrap/modal';
import Moment from "moment";
import { extendMoment } from 'moment-range';

import * as _ from "lodash";
import { ElementRef } from '@angular/core';
import { Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
import { deepStrictEqual } from 'assert';
import { CustomValidators } from 'ng2-validation';

const moment = extendMoment(Moment);
@Component({
    selector: 'report',
    templateUrl: './report.component.html',
    styleUrls: ['./report.component.scss']
})

export class ReportComponent implements OnInit {
    @ViewChild('invoiceHtml') invoiceHtml: ElementRef;
    @ViewChild('imageModal') imageModalRef: ElementRef;
    @ViewChild('imageModal') imageModal: ModalDirective;
    @ViewChild('invoiceModal') invoiceModal: ModalDirective;
    @ViewChild('viewDetailsModal') viewDetailsModal: ModalDirective;
    @ViewChild('changeCustomerTypeModal') public changeCustomerTypeModal: ModalDirective;

    public totalAmaunt;
    data: any;
    config: any;// New Change ****
    image: any = {};
    reportType: string;// New Change ****
    reportName: string;
    currMoment: any = moment();
    freeRooms: number;
    printWindow: any;
    printWindow2: any;
    originalData: any;
    searchQuery: string;
    selectedReport: any;
    isTrue: boolean = true;
    invoiceDataForView: any;
    totalAmount: number = 0;
    totalAdvanceAmount: number = 0;
    netAmount: number = 0;
    cashAmount: number = 0;
    cardAmount: number = 0;
    chequeAmount: number = 0;
    bankAmount: number = 0;
    paidPandingAmount: number = 0;
    totalAmounts: number = 0;
    canShow: boolean = true;
    canViewRecords: boolean;
    expand: boolean = false;
    level1: boolean = false;
    level2: boolean = false;
    roomTypeList: any[] = [];
    isFalse: boolean = false;
    totalDiscount: number = 0;
    viewComp: boolean = false;
    customerTypeList: any[] = [];
    referenceTypeList: any[] = [];
    allCustomer: any[] = [];
    totalAmountRevenue: number = 0;
    totalRefundAmount: number = 0;
    totalPendingAmount: number = 0;
    totalCustomDiscount: number = 0;
    totalAmountReceived: number = 0;
    isPoliceInquiry: boolean = false;
    isPendingPayments: boolean = false;
    selectedFilterTypes: any = {
        location: "000000",
        guest_id: "000000",
        reference_id: "000000",
        room_type_id: "000000",
        booking_status: "000000",
    };

    selectedCustomer: any = {
        cust_id: ""
    }

    agentList: any[] = [];
    bookingStatusList: any[] = [];
    imageUrls: string[] = [];

    // ---PDF ----//
    public PDFData: any;
    public pdfURL: any;
    public documentBase64: any;

    // --- API --- //
    private sub: any;
    private csvSub: any;
    private getReports: any;
    private revenueReport: any;
    private guestDocument: any;
    private printBillService: any;
    private currentGuestReport: any;

    // --- Form --- //
    searchForm: FormGroup;
    customerTypeChangeForm: FormGroup;

    public bookingTypeOptions = {
        width: '100%',
    };
    public datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        todayHighlight: true,
        icon: 'fa fa-calendar',
        // endDate: new Date(),
        format: 'dd/mm/yyyy'
    }
    public bookingCheckoutDetails: any;
    public bookingCheckoutDetailsType: string;
    public selectedCustomerType: any;
    public customerTypeListMain: any[];
    public refereceUser: any[];
    public referenceUserCustomerTypeChange: any[];
    public isReferenceNeccessary: boolean;
    public isNoteNeccessary: boolean;
    public referenceUsersOriginal: any[];
    public referenceUsersMain: any[];
    public refereceUserSelected: any;
    public selectedReferenceUserCustomerTyepChange: any;
    public checkoutReferenceUserSelected: any;
    public selectCustomerValue: any;
    private customertypelistservice: any;
    modelLoading = false;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private router: Router,
        private _fb: FormBuilder,
        private BS: BookingService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        this.reportType = this.router.url.split('/')[this.router.url.split('/').length - 1];
        console.log(this.reportType , "this.reportType");

        if (this.reportType === 'booking') {
            this.datepickerOpts['startDate'] = new Date();
        }
        // New Change ****
        translate.get(`REPORT.${this.reportType}`).subscribe((res: string) => {
            if(res === "agents"){
                this.reportName = "Booking Center"
            }else if(res === "revenue"){
                this.reportName = "Collection & Discount"
            } else if(res === "REPORT.discount") {
                this.reportName = "Discount Approval"
            }
            else{
                this.reportName = res;
            }
        });
        this.initForm();
        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }
    getAmountReceived(item: any) {
        if (this.reportType === 'agents') {
            return item.agent_received;
        }
        else if (item.amount_paid) {
            return this.getTotalAmountPaid(item);
        }
        else {
            return '-';
        }
    }

    getUpdatedPayments(item: any) {
        return (this.getTotalAmountPaid(item)) - (item.agent_received ? item.agent_received : 0);
    }
    round(data: number) {
        if (!isNaN(Math.round(data))) {
            return Math.round(data);
        }
        else {
            return data;
        }
    }
    toDataChange(event) {
        this.searchForm.controls['toDate'].patchValue(event);
       }

    getGuestTypeList(item: any) {
        if (this.selectedCustomer.cust_id == '') {
            if (item.guestTypesList.length) {
                this.getLevel1Status();
            } else {
                let data = JSON.parse(JSON.stringify(this.searchForm.value));
                delete data['reference_id'];
                // console.log("FORM : ",this.searchForm.value);
                this.getRevenueReport(data, 'customer_id', item.reference_id).then((data: any) => {
                    if (data) {
                        item.guestTypesList = [...item.guestTypesList, ...data];
                        item.guestTypesList.forEach((guest) => {
                            guest['guestList'] = [];
                            guest['reference_id'] = item.reference_id;
                            guest['reference_name'] = item.reference_name;
                            let index = this.findIndex(guest.customer_id, "id", this.customerTypeList);
                            guest['c_name'] = index > -1 ? this.customerTypeList[index].text : null;
                        });
                        this.getLevel1Status();
                        // console.log("DATAAAAAAA : ", this.data, item, data);
                    }
                });
            }
        }
    }
    getGuestList(referenceId: number, item: any) {
        if (item.guestList.length) {
            this.getLevel2Status();
        } else {
            let index = this.findIndex(referenceId, "id", this.referenceTypeList);
            let referenceName = index > -1 ? this.referenceTypeList[index].text : null;
            let data = JSON.parse(JSON.stringify(this.searchForm.value));
            delete data['reference_id'];
            // console.log("FORM : ",this.searchForm.value);
            this.getRevenueReport(data, 'guest_id', item.reference_id, item.customer_id).then((data: any) => {
                if (data) {
                    data = (<any[]>data).map(dataItem => {
                        if (dataItem.hasOwnProperty('note')) {
                            dataItem.note = dataItem.note ? JSON.parse(dataItem.note) : [];
                        };
                        return dataItem;
                    })
                    item.guestList = [...item.guestList, ...data];
                    item.guestList.forEach((guest) => {
                        guest['reference_id'] = referenceId;
                        guest['reference_name'] = referenceName;
                        guest['c_name'] = item.c_name;
                    });
                    this.getLevel2Status();
                    // console.log("DATAAAAAAA : ", this.data, item, data);
                }
            });
        }
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    getLevel1Status() {
        if (_.findIndex(this.data, ['checked', true]) > -1) {
            this.level1 = true;
        }
        else {
            this.level1 = false;
        }
    }
    getLevel2Status() {
        let index = _.findIndex(this.data, (item) => {
            return (_.findIndex(item.guestTypesList, ['checked', true]) > -1);
        })
        if (index > -1) {
            this.level2 = true;
        }
        else {
            this.level2 = false;
        }
    }
    public reservationID = (a: any) => {
        if (a.reg_id) {
            return parseInt(a.reg_id);
        }
    }

    public agentReceiptNo = (a: any) => {
        if (a.agent_receipt_no) {
            return parseInt(a.agent_receipt_no);
        }
    }

    public bookingDate = (a: any) => {
        if (a.booking_date) {
            return a.booking_date;
        }
    }

    public referenceName = (a: any) => {
        if (a.reference_id) {
            return this.referenceTypeList[this.findIndex(a.reference_id, "id", this.referenceTypeList)].text;
        }
    }

    public pax = (a: any) => {
        if (a.child || a.adult) {
            return a.child ? a.adult ? parseInt(a.child) + parseInt(a.adult) : parseInt(a.child) : 0;
        }
    }

    public billNo = (a: any) => {
        if (a.bill_no) {
            let checkoutNo = (<string>a.bill_no).split('/');
            return parseInt(checkoutNo[checkoutNo.length - 1]);
        }
    }

    public guestName = (a: any) => {
        return (<string>a.guests.name).toString().toLowerCase();
    }

    parseInteger(text: any) {
        return parseInt(text);
    }

    getAgentLocation(agentId: number) {
        let index = this.findIndex(agentId, "id", this.agentList);
        if (agentId && index > -1) {
            return this.agentList[index].location_name;
        } else {
            return '-';
        }
    }

    ngOnInit() {
        this.canViewRecords = true;
        this.onService();
        this.inItCustomerTypeChangeForm();
        // this.searchForm.controls['fromDate'].setValue(moment(new Date()).format('dd/mm/yyyy'))

        this.allCustomer = [{ "id": "1", "text": "All-Customers" }];

    }

    onService() {
        this.sub = this.BS.getBookingReportFilters()
            .subscribe(res => {
                if (res.status == "success") {
                    if (this.reportType == 'agents') {
                        this.agentList = res.data.cityList.map((obj) => {
                            return { id: obj.id, text: obj.name }
                        });
                        this.bookingStatusList = [
                            { id: "reserved", text: "Reserved" },
                            { id: "checkin", text: "Check-in" },
                            { id: "checkout", text: "Check-out" },
                        ];
                        this.agentList = this.sortArray(this.agentList)
                        this.agentList.unshift({ "id": "000000", "text": "All-togather" });
                        this.bookingStatusList = this.sortArray(this.bookingStatusList)
                        this.bookingStatusList.unshift({ "id": "000000", "text": "All-togather" });
                    }
                    else {
                        this.agentList = res.data.agentsList;
                        this.customerTypeList = res.data.customerTypeList.map((obj) => {
                            return { id: obj.id, text: obj.name.toUpperCase() };
                        });
                        this.roomTypeList = res.data.roomTypeList.map((obj) => {
                            return { id: obj.id, text: obj.name };
                        });

                        this.customerTypeList.unshift({ "id": "000000", "text": "All-togather" });
                        this.roomTypeList.unshift({ "id": "000000", "text": "All-togather" });
                        this.customerTypeList = this.sortArray(this.customerTypeList)
                    }
                    this.referenceTypeList = res.data.referenceTypeList.map((obj) => {
                        return { id: obj.id, text: obj.name.toUpperCase() };
                    });
                    this.referenceTypeList.unshift({ "id": "000000", "text": "All-togather" });
                    this.referenceTypeList = this.sortArray(this.referenceTypeList)
                    if(this.reportType === 'discount') {
                        if (res.data.customerTypeList.length) {
                            this.customerTypeListMain = res.data.customerTypeList;
                            this.customerTypeListMain = jQuery.map(res.data.customerTypeList, function (obj) {
                              return { id: obj.id, text: obj.name.toUpperCase() , is_comment_necessary: obj.is_comment_necessary, is_reference_necessary: obj.is_reference_necessary };
                            });
                            this.customerTypeListMain = this.sortArray(this.customerTypeListMain)
                          }
                        let searchParams = {
                            fromDate: moment(this.searchForm.value.fromDate).format('YYYY-MM-DD'),
                            toDate: moment(this.searchForm.value.toDate).format('YYYY-MM-DD'),
                            guest_id: this.selectedFilterTypes.guest_id,
                            room_type_id: this.selectedFilterTypes.room_type_id,
                            reference_id: this.selectedFilterTypes.reference_id,
                        }
                        this.filterData(searchParams);
                    }
                }
            },
                error => {
                    this.canViewRecords = false;
                });
    }

    sortArray(array) {
        array.sort(function (x, y) {
            let a = x.text.toUpperCase(),
                b = y.text.toUpperCase();
            return a == b ? 0 : a > b ? 1 : -1;
        });
        return array
    }

    initForm() {
        if (this.reportType == 'agents') {
            this.searchForm = this._fb.group({
                fromDate: [null],
                toDate: [null],
                location: ['000000', [Validators.required]],
                booking_status: ['000000', [Validators.required]],
            });
        }
        else if (this.reportType == 'guest') {
            this.searchForm = this._fb.group({
                guest_id: ['000000', [Validators.required]],
                room_type_id: ['000000', [Validators.required]],
                reference_id: ['000000', [Validators.required]]
            });
        }
        else if (this.reportType == 'revenue') {
            this.searchForm = this._fb.group({
                fromDate: [null],
                toDate: [null],
                reference_id: ['000000', [Validators.required]]
            });
        }   else if (this.reportType == 'discount') {
            this.searchForm = this._fb.group({
                fromDate: [new Date()],
                toDate: [new Date()],
                guest_id: ['000000', [Validators.required]],
                room_type_id: ['000000', [Validators.required]],
                reference_id: ['000000', [Validators.required]]
            });
        }
        else {
            this.searchForm = this._fb.group({
                fromDate: [null],
                toDate: [null],
                guest_id: ['000000', [Validators.required]],
                room_type_id: ['000000', [Validators.required]],
                reference_id: ['000000', [Validators.required]]
            });
        }
    }

    getSearchParams() {
        if (this.searchForm.valid) {
            // console.log("1--------------")
            let searchParams = JSON.parse(JSON.stringify(this.searchForm.value));
            if (this.reportType != 'guest') {
                // console.log("1.5----------------------")
                searchParams.fromDate = moment(searchParams.fromDate).format('YYYY-MM-DD');
                searchParams.toDate = moment(searchParams.toDate).format('YYYY-MM-DD');
            }
            return searchParams;
        }
        else {
            // console.log("2--------------")
            for (let field in this.searchForm.controls) {
                this.searchForm.controls[field].markAsDirty();
                this.searchForm.controls[field].markAsTouched();
            }
        }
    }
    searchReports() {
        // console.log("FORM : ", this.searchForm.value);
        let searchParams = this.getSearchParams();
        let obj = searchParams;
        if (this.reportType == 'revenue' && this.selectedCustomer.cust_id != '') {
            obj['customers'] = this.selectedCustomer.cust_id;
        }
        // console.log("obj-------------------", obj)
        this.filterData(searchParams);
    }

    onReset() {
        // console.log("on reset--------------------------------------")
        if (this.reportType !== 'guest' && this.reportType !== 'discount' ) {
            this.searchForm.controls['fromDate'].setValue("");
            this.searchForm.controls['toDate'].setValue("");
        }
        if(this.reportType === 'discount'){
            this.searchForm.controls['fromDate'].setValue(new Date());
            this.searchForm.controls['toDate'].setValue(new Date());
        }
        this.data = [];
        this.originalData = [];
        this.selectedCustomer.cust_id = '000000';
        this.selectedFilterTypes.room_type_id = '000000';
        this.selectedFilterTypes.reference_id = '000000';
        this.selectedFilterTypes.guest_id = '000000'
        this.searchForm.value.guest_id = '000000';
        this.selectedCustomer.reference_id = '000000';
        this.selectedCustomer.room_type_id = '000000';
        let searchParams = this.getSearchParams();
        this.filterData(searchParams);
    }

    findIndex(value: any, property: string, targetArray: any[]) {
        for (let i = 0; i < targetArray.length; i++) {
            if (targetArray[i][property] == value) {
                return i;
            }
        }
        return -1;
    }

    /**
     * FIlters a given data with multiple filter types
     *
     * @param {any} dates : Date range for filter
    */
    referenceUserForDis;
    filterData(searchParams: any, isloading = false) {
        if (this.reportType !== 'guest' && this.reportType !== 'revenue' && this.reportType !== 'discount' && this.reportType !== 'advance-payment') {
            // console.log("A-------------------")
            this.getReports = this.BS.getBookingReport(this.reportType, searchParams)
                .subscribe(res => {
                    if (res.status == "success") {
                        let data = [];
                        if (this.reportType === 'booking') {
                            // console.log("B-------------------")
                            data = res.data.reports;
                            this.freeRooms = res.data.freeRooms;
                        }
                        else {
                            // console.log("C-------------------")
                            data = res.data;
                        }
                        if(this.reportType === 'checkout')
                        {
                          this.totalAdvanceAmount = res.totalAdvancement;
                          this.cashAmount = res.paymentBifurcation.cashAmount;
                          this.cardAmount = res.paymentBifurcation.cardAmount;
                          this.chequeAmount = res.paymentBifurcation.checkAmount;
                          this.bankAmount = res.paymentBifurcation.bankAmount;
                          this.paidPandingAmount = res.paymentBifurcation.paidPandingAmount;
                          this.totalAmounts = res.paymentBifurcation.totalAmount;
                        }
                        data = data.map(data => {
                            data.check_in = data.check_in ? moment(parseInt(data.check_in)).format() : data.check_in;
                            data.check_out = data.check_out ? moment(parseInt(data.check_out)).format() : data.check_out;
                            data.note = data.note ? JSON.parse(data.note) : null;
                            let isPavatiObject = false;
                            try {
                                if (data.pavati_no) {

                                    isPavatiObject = true;
                                    if (typeof JSON.parse(data.pavati_no) !== 'object') {
                                        isPavatiObject = false;
                                    }
                                }
                            } catch (err) {
                                isPavatiObject = false;
                            }
                            if (data.pavati_no && isPavatiObject) {
                                let pavatis = JSON.parse(data.pavati_no);
                                let pavatiString = '';
                                pavatis.forEach((pavati, index) => {
                                    // console.log("PAVATI STRING 111111111: ",pavatiString, pavati);
                                    if (index !== 0) {
                                        pavatiString += ',';
                                    }
                                    pavatiString += pavati;
                                    // console.log("PAVATI STRING 222222222: ",pavatiString, pavati);
                                });
                                data.pavati_no = pavatiString;
                                // console.log("PAVATIS  :",pavatis, pavatiString);
                            }
                            return data;
                        });
                        this.data = data;
                        this.originalData = data;
                        this.updateAmountSummary();
                    }
                });
        }
        else if (this.reportType === 'revenue') {
            // console.log("D-------------------")
            // console.log("Search Params : ", searchParams);
            this.getRevenueReport(searchParams, 'reference_id').then((res: any) => {
                let data = res;
                data.forEach((item) => {
                    item['guestTypesList'] = [];
                    if (this.findIndex(item.reference_id, "id", this.referenceTypeList) > -1) {
                        item['reference_name'] = this.referenceTypeList[this.findIndex(item.reference_id, "id", this.referenceTypeList)].text;
                    } else {
                        item['reference_name'] = null;
                    }
                });
                this.data = data;
                this.originalData = data;
                this.updateAmountSummary();
            });
        }
        else if (this.reportType === 'discount') {
            this.getReports = this.BS.geCustomDiscountBookingReport('checkin', searchParams)
            .subscribe(res => {
                if (res.status == "success") {
                    let data = [];
                    data = res.data.reports;
                    this.referenceUserForDis = res.data.referenceUsers;
                    if (res.data.discountReferenceUsers.length) {
                        this.referenceUsersMain = res.data.discountReferenceUsers;
                        let refereceUser: any[];
                        refereceUser = jQuery.map(res.data.discountReferenceUsers, function (obj) {
                          return { id: obj.id, text: obj.name.toUpperCase() };
                        });
                        this.refereceUser = refereceUser;
                        this.referenceUsersOriginal = refereceUser;
                        let tempRefereceUser = this.getFilteredReferences(this.selectCustomerValue);
                        if (tempRefereceUser.length > 0) {
                          this.refereceUser = tempRefereceUser;
                        } else {
                          this.refereceUser = refereceUser;
                        }
                    }
                    this.referenceTypeList = [];
                    this.referenceTypeList = res.data.discountReferenceUsers.map((obj) => {
                        return { id: obj.id, text: obj.name.toUpperCase() };
                    });
                    this.referenceTypeList.unshift({ "id": "000000", "text": "All-togather" });
                    this.referenceTypeList = this.sortArray(this.referenceTypeList);
                    data = data.map(data => {
                        data.check_in = data.check_in ? moment(parseInt(data.check_in)).format() : data.check_in;
                        data.check_out = data.check_out ? moment(parseInt(data.check_out)).format() : data.check_out;
                        data.note = data.note ? JSON.parse(data.note) : null;
                        data['message'] = '';
                        if(data.note != null){
                            data['message'] = data.note[data.note.length - 1].message
                        }
                        data.custom_discount_per_room = data.custom_discount_per_room ? data.custom_discount_per_room : 0.00;
                        data['totalDiscountCharges']  = 0;
                        data['totalChargesAfterDiscount']  = 0;
                        data.totalDiscountCharges = +(data.quantity * data.custom_discount_per_room).toFixed(0);
                        if(data.custom_discount_per_room != 0) {
                            data.totalChargesAfterDiscount = (data.net_amount - data.totalDiscountCharges).toFixed(0);
                        }
                        return data;
                    });
                    this.data = data;
                    this.originalData = data;
                    this.updateAmountSummary();
                    if(isloading){
                         this.modelLoading = false;
                         this.changeCustomerTypeModal.hide();
                    }
                }
            });
        }
        else if (this.reportType === 'advance-payment'){
          this.getReports = this.BS.getAdvancePaymentReport(searchParams)
                .subscribe(res => {
                    if (res.status == "success") {
                        let data = [];
                        if (this.reportType === 'booking') {
                            // console.log("B-------------------")
                            data = res.data.reports;
                            this.freeRooms = res.data.freeRooms;
                        }
                        else {
                            // console.log("C-------------------")
                            data = res.data;
                        }
                        data = data.map(data => {
                            data.check_in = data.check_in ? moment(parseInt(data.check_in)).format() : data.check_in;
                            data.check_out = data.check_out ? moment(parseInt(data.check_out)).format() : data.check_out;
                            data.note = data.note ? JSON.parse(data.note) : null;
                            let isPavatiObject = false;
                            try {
                                if (data.pavati_no) {
                                    isPavatiObject = true;
                                    if (typeof JSON.parse(data.pavati_no) !== 'object') {
                                        isPavatiObject = false;
                                    }
                                }
                            } catch (err) {
                                isPavatiObject = false;
                            }
                            if (data.pavati_no && isPavatiObject) {
                                let pavatis = JSON.parse(data.pavati_no);
                                let pavatiString = '';
                                pavatis.forEach((pavati, index) => {
                                    // console.log("PAVATI STRING 111111111: ",pavatiString, pavati);
                                    if (index !== 0) {
                                        pavatiString += ',';
                                    }
                                    pavatiString += pavati;
                                    // console.log("PAVATI STRING 222222222: ",pavatiString, pavati);
                                });
                                data.pavati_no = pavatiString;
                                // console.log("PAVATIS  :",pavatis, pavatiString);
                            }
                            return data;
                        });
                        this.data = data;
                        this.originalData = data;
                        this.updateAmountSummary();
                    }
                });
        }
        else {
            // console.log("E-------------------")
            this.currentGuestReport = this.BS.getCurrentGuestReport(this.reportType, searchParams)
                .subscribe((res) => {
                    if (res.status == "success") {
                        let data = res.data;
                        this.data = data;
                        this.originalData = data;
                        // console.log("DATA : ", this.data);
                    }
                });
        }
    }

    getRevenueReport(searchParams: any, type: string, reference?: number, customer?: number) {
        return new Promise((resolve, reject) => {
            return this.revenueReport = this.BS.getRevenueReport(searchParams, type, reference, customer).subscribe((res) => {
                if (res.status == "success") {
                    resolve(res.data);
                }
            }, (err) => reject(err));
        });
    }

    getNotes(notes) {
        if (notes.trim() === '') return null;
        return notes;
    }
    /**
     * Returns a summary of filter report records
     */
    updateAmountSummary() {
        this.initializeSummary();
        this.data.forEach(data => {
            this.totalAmount += this.getTotalAmount(data);
            this.totalDiscount += data.discount ? data.discount : 0;
            this.totalCustomDiscount += data.custom_discount ? data.custom_discount : 0;
            this.totalRefundAmount += data.refundable_amount ? data.refundable_amount : 0;
            this.totalAmountReceived += this.getTotalAmountPaid(data);
            this.netAmount += data.net_amount ? data.net_amount : 0;
            this.totalAmountRevenue += data.total_amount ? data.total_amount : 0;
        });
        this.totalPendingAmount = (this.totalAmount - this.totalDiscount - this.totalCustomDiscount - this.totalAmountReceived - this.totalRefundAmount);
    }
    /**
     * Returns the sum of 'Payments done' and 'Extra amount received'(return amount)
     * @param {any} data : Booking data
     */
    getTotalAmountPaid(data: any) {
        return data.amount_paid ? (data.amount_paid - (data.return_amount ? parseInt(data.return_amount) : 0)) : 0;
    }
    /**
     * Returns the sum of 'Total booking amount', 'Fund amount', 'Early checkin charges' and 'Card swipe charges'
     * @param {any} data : Booking data
     */
    getTotalAmount(data: any) {
        let fundAmount = data.fund_amount ? data.fund_amount : 0;
        let total_amount = data.total_amount ? data.total_amount : 0;
        let cardSwipeCharges = data.cardswipecharges ? data.cardswipecharges : 0;
        let earlycheckincharge = data.earlycheckincharge ? data.earlycheckincharge : 0;
        let extra_pax_charges = data.extra_pax_charges ? data.extra_pax_charges : 0;
        return (total_amount + fundAmount + cardSwipeCharges + earlycheckincharge + extra_pax_charges);
    }
    /**
     * Returns the pending booking amount
     * @param {any} item : Booking data
     */
    getBalanceAmount(item: any) {
        let bAmount = 0;
        bAmount = this.getTotalAmount(item) - this.getTotalDiscount(item) - this.getTotalAmountPaid(item);
        bAmount = bAmount < 0 ? 0 : bAmount;
        return bAmount;
    }
    /**
     * Return the sum of 'Normal discount' and 'Custom discount'
     * @param {any} item : Booking data
     */
    getTotalDiscount(item: any) {
        let discount = item.discount ? item.discount : 0;
        let custom_discount = item.custom_discount ? item.custom_discount : 0;
        return discount + custom_discount;
    }
    guestTypeChanged(event: any) {
        let guestType = <FormControl>this.searchForm.controls['guest_id'];
        this.selectedFilterTypes.guest_id = event.id;
        guestType.patchValue(event.id);
    }

    roomTypeChanged(event: any) {
        console.log("event----", event)
        let roomType = <FormControl>this.searchForm.controls['room_type_id'];
        this.selectedFilterTypes.room_type_id = event.id;
        roomType.patchValue(event.id);
    }

    referenceTypeChanged(event: any) {
        console.log(event)
        let referenceType = <FormControl>this.searchForm.controls['reference_id'];
        this.selectedFilterTypes.reference_id = event?.id;
        referenceType.patchValue(event?.id)
    }

    customerTypeChanged(event: any) {
        this.selectedCustomer.cust_id = event?.id;
    }

    getSearchedReference() {
        let index = this.findIndex(this.selectedFilterTypes.reference_id, "id", this.referenceTypeList);
        if (this.selectedFilterTypes.reference_id != '000000' && index > -1) {
            return this.referenceTypeList[index].text;
        }
        else {
            return 'N/A';
        }
    }

    getSearchedFromDate() {
        let fromDate = <FormControl>this.searchForm.get('fromDate');
        if (fromDate.value) {
            return fromDate.value;
        }
        else {
            return 'N/A';
        }
    }

    getSearchedToDate() {
        let toDate = <FormControl>this.searchForm.get('toDate');
        if (toDate.value) {
            return toDate.value;
        }
        else {
            return 'N/A';
        }
    }

    locationChanged(event: any) {
        let location = <FormControl>this.searchForm.controls['location'];
        this.selectedFilterTypes.location = event?.id;
        location.patchValue(event?.id);
    }

    bookingStatusChanged(event: any) {
        let booking_status = <FormControl>this.searchForm.controls['booking_status'];
        this.selectedFilterTypes.booking_status = event?.id;
        booking_status.patchValue(event?.id);
    }
    updatePendingPayments(item: any) {
        this.isPendingPayments = true;
        this.selectedReport = {
            id: item.id,
            end: item.end,
            start: item.start,
            username: item.name,
            customer_id: item.customer_id
        };
    }
    cancelPayment(data?: any) {
        this.isPendingPayments = false;
        if (data) {
            let index = this.findIndex(data.id, "id", this.originalData);
            if (data.payment_completed_status) {
                if (index > -1) {
                    (<any[]>this.originalData).splice(index, 1);
                    this.updateAmountSummary();
                }
            }
            else {
                if (index > -1) {
                    for (let key in data) {
                        if (this.originalData[index].hasOwnProperty(key)) {
                            this.originalData[index][key] = data[key];
                        }
                    }
                    this.updateAmountSummary();
                }
            }
        }
    }
    policeInquiry(item: any) {
        this.isPoliceInquiry = true;
        this.viewComp = true;
        this.selectedReport = item;
    }

    viewDetails(item: any) {
        this.isPoliceInquiry = false;
        this.showReportDetails(item);
    }

    showReportDetails(item: any) {
        this.viewComp = true;
        this.selectedReport = item;
        this.viewDetailsModal.show();
    }

    viewDocumentProof(item: any) {
        this.canShow = false;
        this.imageUrls = [];
        for (const imgUrl of item.guest_documents) { 
            let mimetype: any[] = [];
            if (item.mimetype) {
                mimetype = item.mimetype.split('/');
            }
            this.image['isImage'] = mimetype.indexOf('application') >= 0 ? false : true;
            if (this.image['isImage'] == false) {
                this.guestDocument = this.BS.getFileBase64({ document_url: this.image['guest_document'] })
                    .subscribe(res => {
                        if (res) {
                            this.documentBase64 = null;
                            let file: any = new Blob([res], { type: 'application/pdf' });
                            const reader = new FileReader();
    
                            reader.readAsDataURL(file);
                            reader.addEventListener('loadend', (e: any) => {
                                let documentBase64 = reader.result;
                                var winparams = `dependent=yes,locationbar=no,scrollbars=yes,menubar=yes,resizable,screenX=50,screenY=50,width=850,height=1050`;
                                var htmlPop = `<embed width=100% height=100% type="application/pdf" src="${documentBase64}"></embed>`;
                                this.printWindow2 = window.open("", "PDF", winparams).document.write(htmlPop);
                            });
                        }
                    })
            }
            else {
                this.imageUrls.push(imgUrl)
            }
        }
        this.imageModal.show();
    }

    viewInvoice(item: any) {
        this.printBillService = this.BS.getBookingBillDetails(item.id).subscribe(res => {
            let data = res.data;
            let adult = 0; let child = 0;
            let hasEarlyCheckInCharge = 0;
            for (let i = 0; i < data.bookingRoom.length; i++) {
                adult += data.bookingRoom[i].adult;
                child += data.bookingRoom[i].child;
                for (let j = 0; j < data.bookingRoomCharge.length; j++) {
                    if (data.bookingRoom[i].id == data.bookingRoomCharge[j].booking_room_id) {
                        if (data.bookingRoom[i].early_checkin_charge) {
                            hasEarlyCheckInCharge += data.bookingRoom[i].early_checkin_charge;
                        }
                        data.bookingRoom[i]['bookingCharges'] = data.bookingRoomCharge[j];
                        break;
                    }
                }
            }
            data['booking_details'] = data;
            data['early_check_in_charges'] = hasEarlyCheckInCharge;
            data['adult'] = adult;
            data['child'] = child;
            data['action_type'] = "invoice";
            for (let i = 0; i < data.fundsList.length; i++) {
                data.fundsList[i]['amount'] = 0;
                if (data.bookingFund.length > 0) {
                    for (let j = 0; j < data.bookingFund.length; j++) {
                        if (data.fundsList[i].id == data.bookingFund[j]['fund_id']) {
                            data.fundsList[i]['amount'] += data.bookingFund[j]['amount'];
                        }
                    }
                    if (data.fundsList[i]['id'] == data.defaultFund.fund_id) {
                        data.fundsList[i]['amount'] += (data.paidAmount - data.fundAmount);
                    }
                }
                else if (data.fundsList[i].id == data.defaultFund.fund_id) {
                    data.fundsList[i]['amount'] = data.paidAmount;
                }
            }
            this.invoiceDataForView = data;
            this.invoiceModal.show();
            this.totalAmaunt = this.invoiceDataForView.fundsList[0].amount
        })

    }
    getHtml(bookingId: number) {
        let data = {
            data: this.invoiceHtml.nativeElement.innerHTML
        }
        this.BS.saveHtmltoPDF(bookingId, data).subscribe((res) => {
            if (res) {
                //old
                // this.PDFData = null;
                // let file: any = new Blob([res], { type: 'application/pdf' });
                // const reader = new FileReader();

                // reader.readAsDataURL(file);
                // reader.addEventListener('loadend', (e: any) => {
                //     let PDFData = reader.result;
                //     var winparams = `dependent=yes,locationbar=no,scrollbars=yes,menubar=yes,resizable,screenX=50,screenY=50,width=850,height=1050`;
                //     var htmlPop = `<embed width=100% height=100% type="application/pdf" src="${PDFData}"></embed>`;
                //     this.printWindow = window.open("", "PDF", winparams).document.write(htmlPop);
                // });

                //new change by kajal
                var blob = new Blob([res], {type: 'application/pdf'});
                const blobUrl = URL.createObjectURL(blob);
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = blobUrl;
                document.body.appendChild(iframe);
                iframe.contentWindow.print();
            }

        })
    }
    PrintDocument() {
        window.print();
    }

    closeModal() {
        this.canShow = true;
        this.imageModal.hide()
    }

    printRecords() {
        if (this.reportType === 'revenue') {
            window.print();
        }
        else if (this.data && this.data.length > 0) {
            let searchParams = this.getSearchParams();
            // let data = JSON.stringify(this.data);
            // let dataArray = (<any[]>JSON.parse(data));
            // dataArray.forEach(item => {
            //     item.address = item.address + ',' + item.city + ',' + item.zip;
            //     item['reference_name'] = item.reference_id ? this.referenceTypeList[this.findIndex(item['reference_id'], "id", this.referenceTypeList)].text : '';
            //     item.start = moment(item.start).format('YYYY-MM-DD');
            //     item.end = moment(item.end).format('YYYY-MM-DD');
            //     let notes = '';
            //     if (item.note && item.note.length) {
            //         (<any[]>item.note).forEach((note, index) => {
            //             notes += (index > 0 ? ', ' : '') + note.message;
            //         });
            //     }
            //     item.note = notes;
            // });
            // this.csvSub = this.BS.getCsvReport(dataArray, this.reportType)
            this.csvSub = this.BS.getCsvReport(searchParams, this.reportType)
                .subscribe(res => {
                    let parsedResponse = res;
                    let blob = new Blob([parsedResponse], { type: 'text/csv' });
                    let url = window.URL.createObjectURL(blob);
                    if (navigator.msSaveOrOpenBlob) {
                        navigator.msSaveBlob(blob, this.reportType + '.csv');
                    } else {
                        let a = document.createElement('a');
                        a.href = url;
                        a.download = this.reportType + '.csv';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    }
                    window.URL.revokeObjectURL(url);
                },
                    err => console.log("error : ", err)
                );
        }

    }

    search() {
        if ((this.searchForm && this.data) || this.reportType == 'guest') {
            this.initializeData();
            if (this.data && this.searchQuery && this.searchQuery.trim() != '') {
                this.data = this.data.filter(data => {
                    let searchTarget = '';
                    Object.keys(data).forEach(key => {
                        searchTarget += data[key];
                    })
                    return (searchTarget.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
                });
                if (this.reportType !== 'guest') {
                    this.updateAmountSummary();
                }
            }
            else {
                this.initializeData();
            }
        }
    }

    clearSearch() {
        this.initializeData();
        this.searchQuery = undefined;
    }

    initializeData() {
        this.data = this.originalData;
        if (this.reportType !== 'guest') {
            this.updateAmountSummary();
        }
    }

    initializeSummary() {
        this.netAmount =
            this.totalAmount =
            this.totalDiscount =
            this.totalPendingAmount =
            this.totalAmountReceived =
            this.totalCustomDiscount =
            this.totalAmountRevenue =
            this.totalRefundAmount = 0;
    }

    truncateText(text: string, characters: number) {
        return (text.substr(0, characters) + '...');
    }

    closeComp(event) {
        this.isPoliceInquiry = false;
        this.viewComp = false;
        this.viewDetailsModal.hide();
        if (event) {
            setTimeout(() => {
                this.viewDocumentProof(event);
            }, 1000);
        }
    }
    cancelCheckout() {
        this.changeCustomerTypeModal.hide();
        this.selectedReferenceUserCustomerTyepChange = ''
      }

    guessName = '';
    requestBookingGuestTypeChange(item: any) {
        this.initializeCheckoutDetails();
        this.bookingCheckoutDetails = item;
        this.bookingCheckoutDetailsType = 'customer-type-change';
        this.selectedCustomerType = item.customer_id;
        this.customerTypeChangeForm.controls['customer_id'].patchValue(item.customer_id);
        this.isReferenceNeccessary = this.checkIfReferenceIsNeccessary();
        this.isNoteNeccessary = this.checkIfNoteIsNeccessary();
        this.guessName = item.name;
        // Pop-up for changing customer type before checkout
        this.changeCustomerTypeModal.show();
        this.commanCustomerChange(item.guest_id);
    }

    initializeCheckoutDetails() {
        this.selectedCustomerType = undefined;
        this.bookingCheckoutDetails = undefined;
        this.bookingCheckoutDetailsType = undefined;
        this.customerTypeChangeForm.removeControl('note');
        this.customerTypeChangeForm.removeControl('reference_id');
    }

    checkoutAfterCustomerChange() {
     if (this.customerTypeChangeForm.valid) {
          this.changeBookingGuestType();
        } else {
            for (let key in this.customerTypeChangeForm.controls) {
                this.customerTypeChangeForm.controls[key].markAsDirty();
                this.customerTypeChangeForm.controls[key].markAsTouched();
              }
            }
    }

    checkoutCustomerTypeChanged(event: any) {
        console.log("form details", this.customerTypeChangeForm.controls);
        
         if (this.bookingCheckoutDetails) {
            this.selectedCustomerType = event.id;
            this.commanCustomerChange(event.id);
        }
    }

    private commanCustomerChange(cust_id: any) {
        this.referenceUserCustomerTypeChange = this.getFilteredReferences(cust_id);
        this.referenceUserCustomerTypeChange = this.sortArray(this.referenceUserCustomerTypeChange);
        this.referenceUserCustomerTypeChange.unshift({ id: "000000", text: 'Please select a reference type' });
        this.isReferenceNeccessary = this.checkIfReferenceIsNeccessary();
        this.isNoteNeccessary = this.checkIfNoteIsNeccessary();
        if (this.bookingCheckoutDetails.reference_id == null) {
            this.selectedReferenceUserCustomerTyepChange = this.referenceUserCustomerTypeChange[0].id;
        }
        else {
            if (this.customerTypeChangeForm.get('reference_id')) {
                this.customerTypeChangeForm.controls['reference_id'].patchValue(this.bookingCheckoutDetails.reference_id);
            }
            this.selectedReferenceUserCustomerTyepChange = this.bookingCheckoutDetails.reference_id;
        }
        if (this.isNoteNeccessary) {
            if (this.bookingCheckoutDetails.message != '') {
                this.customerTypeChangeForm.controls['note'].patchValue(this.bookingCheckoutDetails.message);
            }
        }
    }

    preCheckoutReferenceTypeChanged(event: any) {
        if (this.customerTypeChangeForm && this.customerTypeChangeForm.get('reference_id')) {
          this.customerTypeChangeForm.controls['reference_id'].patchValue(event.id);
        }
    }

    checkIfNoteIsNeccessary() {
        this.customerTypeChangeForm.removeControl('note');
        if (!this.customerTypeListMain) {
          return false;
        }
        let index = this.findIndex(parseInt(this.selectedCustomerType), "id", this.customerTypeListMain);
        if (index > -1) {
          let isNoteNeccessary = this.customerTypeListMain[index].is_comment_necessary;
          if (isNoteNeccessary && !this.customerTypeChangeForm.get('note')) {
             if(this.customerTypeListMain[index].id == 3)  {
                this.customerTypeChangeForm.addControl('note', new FormControl('', []));
             } else {
                 this.customerTypeChangeForm.addControl('note', new FormControl('', [Validators.required]));
             }
          } else if (!isNoteNeccessary && this.customerTypeChangeForm.get('note')) {
            this.customerTypeChangeForm.removeControl('note');
          }
          return isNoteNeccessary;
        }
        return false;
    }

    checkIfReferenceIsNeccessary() {
        if (!this.customerTypeListMain) {
          return false;
        }
        let index = this.findIndex(parseInt(this.selectedCustomerType), "id", this.customerTypeListMain);
        if (index > -1) {
          let isReferenceNeccessary = this.customerTypeListMain[index].is_reference_necessary;
          if (isReferenceNeccessary && !this.customerTypeChangeForm.get('reference_id')) {
            this.customerTypeChangeForm.addControl('reference_id', new FormControl('', [Validators.required, CustomValidators.notEqual('000000')]));

        } else if (!isReferenceNeccessary) {
            if(this.customerTypeListMain[index].id == 3)  {
                this.customerTypeChangeForm.removeControl('reference_id');
                this.customerTypeChangeForm.addControl('reference_id', new FormControl('', []));
             } else {
               this.customerTypeChangeForm.removeControl('reference_id');
             }
          }
          return isReferenceNeccessary;
        }

        return false;
    }

    // checkIfReferenceIsNeccessary() {
    //     if (!this.customerTypeListMain) {
    //       return false;
    //     }
    //     let index = this.findIndex(parseInt(this.selectedCustomerType), "id", this.customerTypeListMain);
    //     if (index > -1) {
    //       let isReferenceNeccessary = this.customerTypeListMain[index].is_reference_necessary;
    //       if (isReferenceNeccessary && !this.customerTypeChangeForm.get('reference_id')) {
    //         this.customerTypeChangeForm.addControl('reference_id', new FormControl('', [Validators.required, CustomValidators.notEqual('000000')]));
    //       } else if (!isReferenceNeccessary && this.customerTypeChangeForm.get('reference_id')) {
    //         this.customerTypeChangeForm.removeControl('reference_id');
    //       }
    //       return isReferenceNeccessary;
    //     }

    //     return false;
    // }

    inItCustomerTypeChangeForm() {
        this.customerTypeChangeForm = this._fb.group({
          customer_id: ['', Validators.required],
          reference_id: ['']
        });
    }

    getFilteredReferences(selectedValue: any) {
        let referencesAllowed = [];
        this.referenceUsersMain.forEach(reference => {
          if (reference.customer_type_ids) {
            reference.customer_type_ids.forEach((customerIds: any[]) => {
              if ((customerIds.indexOf(selectedValue) >= 0) && (referencesAllowed.indexOf(reference.id) < 0)) {
                referencesAllowed.push(reference.id);
              }
            });
          }
        });
        let refereceUser = [];
        referencesAllowed.forEach(id => {
          refereceUser.push(this.referenceUsersOriginal[this.findIndex(id, "id", this.referenceUsersOriginal)]);
        });


        return refereceUser;
    }

    changeBookingGuestType() {
        if (this.bookingCheckoutDetails) {
          let paymentDate = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';
          this.customerTypeChangeForm.get('customer_id').patchValue(this.selectedCustomerType);
          this.customerTypeChangeForm.addControl('is_discount_approval', new FormControl(true));
          this.modelLoading = true;
          if( this.customerTypeChangeForm.value.reference_id === '000000') {
            this.customerTypeChangeForm.get('reference_id').patchValue('');
          }
          this.sub = this.BS.changeBookingGuestType(this.bookingCheckoutDetails.id, this.customerTypeChangeForm.value, paymentDate)
            .subscribe((res) => {
              if (res.status === 'success') {
                let param = this.getSearchParams()
                this.filterData(param, true)
              }
            });
        }
    }

    addCustomeDiscount(data){
        let obj = {
            custom_discount_per_room: data.custom_discount_per_room
        }
        this.BS.customDiscount(data.id , obj).subscribe((res) => {
            data.totalDiscountCharges = +(data.quantity * data.custom_discount_per_room).toFixed(0) ;
            data.totalChargesAfterDiscount = (data.net_amount - data.totalDiscountCharges).toFixed(0);
        })
    }


    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();

        if (this.customertypelistservice) {
             this.customertypelistservice.unsubscribe();
        }
      }
}

