/***********************/
/*        Layout       */
/***********************/
app, .app {
  height: 100%;
  display: block;
}

.chat-sidebar-container {
  color: $text-color;
}
.uppercase {
  text-transform: uppercase;
}
.sidebar{
  position: fixed;
  z-index: 0;
  left: 0;
  top: 0;
  bottom: 0;
  width: $sidebar-width;
  background-color: $sidebar-bg-color;
  color: $sidebar-color;

  .slimScrollBar{
    @include border-radius(0 !important);
    background-color: $sidebar-color !important;
  }
}

.page-controls, .content-wrap, .loader-wrap{
  left: $sidebar-width;
  @include transition(left $sidebar-transition-time ease-in-out);

  @include media-breakpoint-up(md) {
    margin-left: $sidebar-icon-state-width;
    left: $sidebar-width - $sidebar-icon-state-width;
  }

  .nav-collapsed &{
    left: 0
  }

  .nav-static &{
    @include media-breakpoint-up(lg) {
      @include transition(none);
      left: 0;
      margin-left: $sidebar-width;
    }
  }
}

.page-controls {
  .dropdown-toggle-notifications {
    .rounded-circle {
      margin-top: -1px;
    }

    .circle {
      width: #{$line-height-base}rem;
      height: #{$line-height-base}rem;
      line-height: #{$line-height-base}rem;
    }
  }
}

.page-controls{
  @include transition(
          left $sidebar-transition-time ease-in-out,
          right $sidebar-transition-time ease-in-out
  );
  right: -$sidebar-width;

  .nav-collapsed &{
    right: 0
  }

  .nav-static & {
    @include media-breakpoint-up(lg) {
      right: 0;
    }
  }
}

.page-controls {
  position: absolute;
  z-index: 1;
  height: $navbar-height;

  @include media-breakpoint-down(sm) {
    font-size: 16px;

    .navbar-brand{
      position: absolute;
      left: 0;
      right: 0;
      font-weight: $font-weight-bold;
      text-align: center;

      > .fa-circle{
        font-size: 10px;
      }
    }

    .navbar-nav{
      position: relative;
      z-index: 1;
    }
  }

  .avatar{
    width: 30px;
    margin-top: -5px;
    &.float-xs-left,
    &.pull-left{
      margin-right: 5px;
    }
    &.float-xs-right,
    &.pull-right{
      margin-left: 5px;
    }
  }

  .navbar-nav > li > a {
    .rounded.rounded-lg,
    .circle.circle.lg{
      margin: -7px -7px -8px;
    }
  }

  .navbar-form.navbar-left{
    @include media-breakpoint-up(md) {
      padding-right: 0;
    }
  }

  .navbar-form.navbar-left{
    @include media-breakpoint-down(md) {
      padding-right: 0;
    }
  }
  .navbar-nav .fa-times{
    font-size: 20px;
    vertical-align: -2px;
  }

  .navbar-form {

    .input-group-addon {
      line-height: 1.5;

      .fa {
        vertical-align: -1px;
      }
    }

    .form-control {
      line-height: 1.7;
    }
  }
}

/* ff icons valign fix */
@-moz-document url-prefix() {
  .page-controls .navbar-nav > li > a > .rounded-lg > i{
    vertical-align: -8%;
  }
}

.content-wrap{
  position: relative;
  z-index: 0;
  height: 100%;
}

.content{
  min-height: 100%;
  padding: $content-padding-top $content-padding-horizontal $content-padding-vertical;
  background-color: $body-bg;

  @include media-breakpoint-down(sm) {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.logo{
  margin: 11px 0 19px;
  height: $navbar-height;
  width: 100%;
  line-height: $navbar-height;
  text-align: center;
  vertical-align: middle;
  font-weight: $font-weight-bold;
  font-size: 18px;

  @include transition(width .3s ease-in-out);
  a{
    color: $white;
    text-decoration: none;
  }

  .nav-collapsed &{
    width: $sidebar-icon-state-width;
  }

  .nav-static &{
    @include media-breakpoint-up(lg) {
      width: 100%;
      @include transition(none);
    }
  }
}

.sidebar-nav{
  padding: 10px 0;
  font-size: 14px;

  @media (min-width: map_get($grid-breakpoints, lg)) and (min-height: $screen-lg-height), (max-width: map_get($grid-breakpoints, md) - 1px){
    font-size: 16px;
  }

  li a{
    display: block;
    color: $sidebar-color;
    text-decoration: none;

    .toggle{
      float: right;
      line-height: 18px;
      margin-right: $sidebar-padding-horizontal + $sidebar-slim-scroll-width;
      transition: transform .35s ease;
    }

    &.collapsed .toggle{
      transform: rotate(90deg);
    }

    .tag{
      float: right;
      line-height: 8px;
      margin-top: 7px;
      margin-right: $sidebar-padding-horizontal + $sidebar-slim-scroll-width;
      padding: 7px;
      border-radius: $border-radius-sm;

      @media (min-width: map_get($grid-breakpoints, lg)) and (min-height: $screen-lg-height), (max-width: map_get($grid-breakpoints, md) - 1px){
        margin-top: 11px;
      }
    }
  }

  > li > a{
    position: relative;
    border-top: 1px solid $sidebar-item-border-color;
    padding-left: 50px;
    line-height: 35px;

    &:hover{
      background-color: $sidebar-item-hover-bg-color;
    }

    @media (min-width: map_get($grid-breakpoints, lg)) and (min-height: $screen-lg-height), (max-width: map_get($grid-breakpoints, md) - 1px){
      line-height: 44px;
    }

    .icon{
      @media (min-width: map_get($grid-breakpoints, lg)) and (min-height: $screen-lg-height), (max-width: map_get($grid-breakpoints, md) - 1px){
        top: 8px;
      }
      
      display: block;
      position: absolute;
      top: 3px;
      left: $sidebar-padding-horizontal;
      width: 28px;
      height: 28px;
      line-height: 28px;
      text-align: center;

      .fa, .glyphicon{
        font-size: 16px;
        color: rgba($sidebar-color, .7);
      }

      .fa {
        position: relative;
        top: 1px;
      }

      .glyphicon{
        top: 6px;
      }

      .glyphicon-th{
        left: 0;
      }

      .glyphicon-tree-conifer{
        left: 0;
      }

      .glyphicon-map-marker{
        top: 6px;
      }

      .fa-envelope{
        top: 0;
      }

      @media (min-width: map_get($grid-breakpoints, lg)) and (min-height: $screen-lg-height), (max-width: map_get($grid-breakpoints, md) - 1px){
        .glyphicon {
          top: 6px;
        }

        .glyphicon-map-marker {
          top: 7px;
        }

        .fa {
          top: 0;
        }
      }

    }

    .toggle{
      line-height: 35px;

      @media (min-width: map_get($grid-breakpoints, lg)) and (min-height: $screen-lg-height), (max-width: map_get($grid-breakpoints, md) - 1px){
        line-height: 44px;
      }
    }

  }

  > .open > a{
    background-color: lighten($sidebar-item-hover-bg-color, 2%);
  }

  > li:last-child > a{
    border-bottom: 1px solid $sidebar-item-border-color;
  }

  > .active > a{
    color: $sidebar-item-active-color;
    background-color: $sidebar-bg-color;

    .icon{
      // border-radius: 50%;
      // background-color: $sidebar-item-active-color;
      
      .fa, .glyphicon{
        // color: $sidebar-bg-color;
        color: $sidebar-item-active-color
      }
    }
  }

  /*
   * Sub menus
   */

  > li ul{
    padding: 0;
    font-size: 13px;
    background-color: lighten($sidebar-bg-color, 5%);
    list-style: none;

    @media (min-width: map_get($grid-breakpoints, lg)) and (min-height: $screen-lg-height), (max-width: map_get($grid-breakpoints, md) - 1px){
      font-size: 14px;
    }

    > li{
      > a{
        padding: 10px 0 10px 40px;
        .icon{
          margin-left: -5px;
          margin-right: 5px;
          opacity: 0;
        }
        &:hover{
          background-color: lighten($sidebar-item-hover-bg-color, 5%);
          .icon{
            opacity: 1
          }
        }
      }

      &.active{
        > a{
          font-weight: normal;
          color: $white;
          .icon{
            opacity: 1;
          }
        }
      }

      /* third submenu */
      ul > li > a{
        padding-left: 55px;
      }
    }
  }

  /*
  First sub-menu
   */
  > li > ul{
    /* simulate padding */
    &:before{
      content: "";
      display: block;
      // padding-top: $line-height-computed/2;
    }

    &:after{
      content: "";
      display: block;
      // padding-bottom: $line-height-computed/2;
    }
  }
}

.sidebar-nav-title{
  margin: 35px 0 5px $sidebar-padding-horizontal;
  font-size: 14px;
  text-transform: uppercase;

  opacity: 1;
  @include transition(opacity $sidebar-transition-time ease-in-out);

  .action-link{
    color: $sidebar-color;
    float: right;
    margin-right: $sidebar-padding-horizontal + $sidebar-slim-scroll-width;
    margin-top: -1px;

    .fa, .glyphicon{
      font-size: 9px;
    }

    .glyphicon {
      top: 5px;
    }
  }

  .nav-collapsed &{
    opacity: 0;
  }

  .nav-static &{
    @include media-breakpoint-up(lg){
      opacity: 1;
      @include transition(none);
    }
  }
}

.sidebar-status{
  position: relative;
  margin: 10px 0 20px;
  padding: 0 ($sidebar-padding-horizontal + $sidebar-slim-scroll-width) 0 $sidebar-padding-horizontal;

  > a{
    display: block;
    line-height: 40px;
    color: $gray-lighter;
    text-decoration: none;
  }

  .dropdown-menu{
    position: fixed;
    top: 57px;
    left: 0;
    margin: 0 20px;
    width: 320px;
    color: $text-color;
    @media (max-width: 380px) {
      margin: 0;
      width: 100%;
    }
  }

  /* fix notifications dropdown when there is no enough vertical space*/

  @media (max-height: 500px) {
    .dropdown-menu{
      bottom: 20px;
    }

    .notifications{
      .card-header{
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
      }

      .list-group{
        position: absolute;
        top: 85px;
        left: 0;
        right: 0;
        bottom: 43px;
        height: auto;
      }

      .card-footer{
        position: absolute;
        bottom: -5px;
        left: 0;
        right: 0;
      }
    }
  }
}

.sidebar-labels{
  list-style: none;
  padding: $sidebar-padding-horizontal;
  padding-right: $sidebar-padding-horizontal + $sidebar-slim-scroll-width;
  font-size: $font-size-mini;
  > li{
    > a{
      color: $gray-lighter;
      text-decoration: none;
      font-weight: normal;

      .circle,
      .circle-o{
        position: relative;
        top: -2px;
        font-size: 9px;
        height: 12px;
        width: 12px;
        line-height: 12px;
        @include transition(margin-left $sidebar-transition-time ease-in-out);
      }

      .fa-circle{
        font-size: 11px;
        vertical-align: 1px;
        @include transition(margin-left $sidebar-transition-time ease-in-out);
      }

      .circle-o{
        top: 1px;
        left: 1px
      }

      .label-name{
        opacity: 1;
        @include transition(opacity $sidebar-transition-time ease-in-out);
      }

      .nav-collapsed & {
        .circle,
        .circle-o,
        .fa-circle{
          margin-left: 8px;
        }

        .label-name{
          opacity: 0;
        }
      }

      .nav-static & {
        @include media-breakpoint-up(lg){
          .circle,
          .circle-o,
          .fa-circle{
            @include transition(none);
            margin-left: 0;
          }
          .label-name {
            @include transition(none);
            opacity: 1;
          }
        }
      }
    }

    + li {
      margin-top: $line-height-computed/2;
    }
  }
}

.sidebar-alerts{
  font-size: $font-size-mini;
  @include transition(opacity $sidebar-transition-time ease-in-out);
  opacity: 1;

  .nav-collapsed &{
    opacity: 0;
  }

  .nav-static &{
    @include media-breakpoint-up(lg){
      opacity: 1;
      @include transition(none);
    }
  }
  .alert{
    margin-bottom: 0;
    padding: $line-height-computed/2 $sidebar-padding-horizontal;
    padding-right: $sidebar-padding-horizontal + $sidebar-slim-scroll-width;

    .close{
      font-size: 16px;
      text-shadow: none;
      opacity: 1;
      color: $sidebar-color;
    }
  }

  .progress{
    background-color: lighten($sidebar-bg-color, 10%);
  }
}

.chat-sidebar{
  position: fixed;
  top:0;
  bottom: 0;
  right: -$chat-sidebar-width;
  width: $chat-sidebar-width;
  background-color: $sidebar-bg-color;
  @include transition(right $sidebar-transition-time ease-in-out);
}

/* body tag is given this class */
.chat-sidebar-container{
  position: relative;
  &, .sidebar{
    left: 0;
    @include transition(left $sidebar-transition-time ease-in-out);
  }
  &.chat-sidebar-opened{
    &, .sidebar{
      left: -$chat-sidebar-width;
    }

    .chat-sidebar{
      right: 0;
    }
  }
}

.chat-sidebar-header{
  width: 100%;
  position: absolute;
  top: 0;
  padding: 10px;
  z-index: 3;
  background-color: $sidebar-bg-color;

  .form-control {
    padding: 0.6rem 0.85rem;
  }
}

.chat-sidebar-content{
  color: $sidebar-color;
}


.chat-sidebar-contacts{
  padding: 20px 0;
  left: -100%;
  @include transition(left .4s ease-in-out);
  &.open{
    left: 0;
  }
  .sidebar-nav-title{
    margin-left: 20px;
    margin-right: 10px;

    &:first-child{
      margin-top: 0;
    }

    .nav-collapsed &{
      opacity: 1;
    }
  }
}

.chat-sidebar-chat{
  right: -100%;
  padding-bottom: 54px; /*footer height*/
  background-color: $sidebar-bg-color;
  @include transition(right .2s ease-in-out);
  &.open{
    right: 0;
  }

  .title{
    margin-bottom: 0;
    > a{
      display: block;
      padding: $padding-large-vertical $padding-large-horizontal;
      background-color: rgba(255,255,255, .1);
      color: $gray-lighter;
      text-decoration: none;
    }

    &:hover {
      cursor: pointer;
    }
  }

  .message-list{
    list-style: none;
    padding-left: 0;
    padding-top: $line-height-computed/2;
  }

  .message{
    padding: 10px 20px;
    @include clearfix();
    > .thumb-sm{
      float: left;
    }

    &.from-me > .thumb-sm{
      float: right;
    }
  }

}

.message-body{
  .chat-sidebar-chat & {
    position: relative;
    margin-left: 50px;
    padding: 10px;
    font-size: $font-size-mini;
    font-weight: $font-weight-normal;
    background-color: $gray-lighter;
    color: $text-color;
    border-radius: $border-radius;

    &:before{
      right: 100%;
      top: 8px;
      content: " ";
      height: 0;
      width: 0;
      position: absolute;
      border: 10px solid rgba(0,0,0,0);
      border-right-color: $gray-lighter;
    }
  }

  .chat-sidebar-chat .message.from-me &{
    margin-left: 0;
    margin-right: 50px;
    background-color: $brand-warning;
    color: $gray-dark;
    &:before{
      right: auto;
      left: 100%;
      border-right-color: rgba(0,0,0,0);
      border-left-color: $brand-warning;
    }
  }
}

.chat-sidebar-panel{
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  padding-top: 100px;
}

.chat-sidebar-title{
  margin: 10px;
  text-transform: uppercase;
  font-size: 15px;
  font-weight: $font-weight-normal;
}

.chat-sidebar-user-group{
  margin-top: 10px;
  .list-group-item{
    background-color: transparent;
    margin-bottom: 0;
    padding: 10px 20px;
    border: 0;
    color: $gray-lighter;
    border-radius: 0;

    &:hover {
      cursor: pointer;
    }

    &.active{
      &,
      &:focus,
      &:hover{
        background-color: rgba(255,255,255, .1);
        color: $brand-warning;
      }

      .badge{
        background-color: $brand-danger;
        color: $white;
      }

      .message-sender{
        font-weight: $font-weight-semi-bold;
      }
    }

    .circle-o{
      margin-top: 13px;
      font-size: 8px;
    }

    .fa-circle{
      font-size: 11px;
      line-height: 37px;
    }

    .badge{
      margin-top: 9px;
      margin-right: 6px;
      padding: 3px 5px;
    }
  }

  .thumb .status,
  .thumb-sm .status{
    border-color: $sidebar-bg-color;
  }

  a.list-group-item:hover{
    color: $gray-lighter;
    background-color: rgba(0,0,0,.15);
  }

  .message-preview{
    margin: 0;
    width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: $font-size-smaller;
    color: $text-muted;
  }

  .message-sender{
    margin: 0 0 5px;
  }
}

.chat-sidebar-footer{
  position: absolute;
  bottom: 0;
  right: -100%;
  width: 100%;
  margin-bottom: 0;
  padding: 10px;
  background-color: #3a3a3a;

  @include transition(right .2s ease-in-out);
  &.open{
    right: 0;
  }
}

.chat-notification{
  position: absolute;
  right: 12px;
  top: 35px;
  z-index: 20;
  margin-top: 3px;
  padding: 5px 0;
  cursor: pointer;

  &:before{
    content: ' ';
    position: absolute;
    top: 0;
    right: 18px;
    width: 0;
    height: 0;
    border-left: 5px solid rgba(0, 0, 0, 0);
    border-right: 5px solid rgba(0, 0, 0, 0);
    border-bottom: 5px solid $gray-dark;
  }
}

.chat-notification-inner{
  min-width: 120px;
  padding: 8px;
  font-size: 12px;
  border-radius: $border-radius;
  text-decoration: none;
  background-color: $gray-dark;
  color: $white;

  .title{
    margin: 0;
    font-weight: $font-weight-semi-bold;
    line-height: 28px;
    font-size: $font-size-sm;

    .thumb-xs{
      vertical-align: -9px;
      margin-right: 5px;
    }
  }

  .text{
    margin-top: 5px;
    margin-bottom: 0;
    color: $gray-light;
  }
}

.chat-notification-sing{
  position: absolute;
  top: 16px;
  left: 10px;
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: $brand-danger;
}

.page-title{
  margin: 0 0 calc(#{$line-height-computed} + 6px);
}

.notifications{
  @include media-breakpoint-up(md){
    width: 333px;
  }

  .card-header {
    background-color: #fff;

    .btn-group-justified {
      display: flex;

      label {
        flex: 1;
        padding-top: 5px;
        padding-bottom: 5px;
      }
    }

  }

  height: 100%;

  .list-group{
    height: 320px;
    overflow-y: scroll;
  }
  .list-group-item{
    @include transition(background-color .15s ease-in-out);
    &:hover{
      background-color: $list-group-hover-bg;
      .progress-bar{
        background-color: $white !important;
      }
    }
    .progress{
      @include transition(background-color .15s ease-in-out);
    }
  }

  a.list-group-item {
    text-decoration: none;
    color: $gray;
  }

  .btn-notifications-reload{
    color: $navbar-dashboard-link-color;
  }
}

/***********************/
/*        Loader       */
/***********************/

.loader-wrap{
  position: fixed;
  z-index: 0;
  right: 0;
  top: 0;
  bottom: 0;
  text-align: center;
  background-color: $body-bg;

  opacity: 1;
  transition: transform $sidebar-transition-time ease-in-out, left $sidebar-transition-time ease-in-out, opacity .2s ease-out, background-color .2s ease-out;
  &.hiding{
    opacity: 0;
    background-color: rgba(0,0,0,0);
  }

  .fa{
    position: absolute;
    top: 50%;
    left: 50%;
    line-height: 30px;
    margin-top: -15px;
    margin-left: -10px;
    font-size: 20px;
    vertical-align: middle;
  }

  .nav-static & {
    @include media-breakpoint-up(lg){
      @include transition(opacity .2s ease-out, background-color .2s ease-out);
      transform: translate(0, 0);
      margin-left: $sidebar-width;
    }
  }
}

/***********************/
/*       Widget        */
/***********************/

.widget{
  position: relative;
  margin-bottom: $grid-gutter-width;
  padding: $widget-padding-vertical $widget-padding-horizontal;
  background: $widget-bg-color;
  border-radius: $border-radius-sm;
  > header{
    margin: (-$widget-padding-vertical) (-$widget-padding-horizontal);
    padding: $widget-padding-vertical $widget-padding-horizontal;
    h1,h2,h3,h4,h5,h6{
      margin: 0;
    }

    + .widget-body,
    + .widget-body.no-padding{
      margin-top: $widget-padding-vertical;
    }
  }

  > .widget-body.no-padding{
    margin: (-$widget-padding-vertical) (-$widget-padding-horizontal);

    + footer{
      margin-top: $widget-padding-vertical;
    }
  }

  > footer{
    margin: 0 (-$widget-padding-horizontal) (-$widget-padding-vertical);
    padding: $widget-padding-vertical $widget-padding-horizontal;
  }

  &:hover .widget-controls-hover{
    opacity: 1;
  }

  .loader{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;

    .spinner{
      position: absolute;
      top: 50%;
      width: 100%; //ie fix
      margin-top: -10px;
      font-size: 20px;
      text-align: center;
    }
  }

  .widget-top-overflow,
  .widget-middle-overflow{
    position: relative;
    margin: 0 (-$widget-padding-horizontal);

    > img{
      max-width: 100%;
    }
  }

  .widget-top-overflow{
    margin-top: (-$widget-padding-vertical);
    border-top-left-radius: $border-radius;
    border-top-right-radius: $border-radius;
    overflow: hidden;

    > img{
      border-top-left-radius: $border-radius;
      border-top-right-radius: $border-radius;
    }

    > .btn-toolbar {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      margin-right: $widget-padding-horizontal;

      @include media-breakpoint-up(md){
        top: auto;
        bottom: 0;
      }
    }
  }

  .widget-table-overflow{
    margin: 0 (-$widget-padding-horizontal) (-$widget-padding-vertical);

    th:first-child,
    td:first-child {
      padding-left: $widget-padding-horizontal;
    }

    th:last-child,
    td:last-child {
      padding-right: $widget-padding-horizontal;
    }
  }
}

.widget-controls{
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  padding: 14px;
  font-size: $font-size-sm;

  &.left{
    left: 0;
    right: auto;
  }

  &.widget-controls-hover{
    opacity: 0;
    @include transition(opacity .15s);
  }



  > a{
    padding: 1px 4px;
    border-radius: 4px;
    color: rgba($black, .4);
    @include transition(color .15s ease-in-out);

    &:hover{
      color: rgba($black, .1);
      text-decoration: none;
    }

    > .glyphicon{
      vertical-align: 0;
      font-size: 12px;
    }

    > .fa{
      vertical-align: 1px;
    }
  }

  > .btn-group{
    vertical-align: baseline;
  }
}

.widget-card { min-height: 135px;}

.widget-icon{
  opacity: .5;
  font-size: 40px;
  height: 70px;

  .glyphicon {
    top: 5px;
  }
}

.widget-image{
  position: relative;
  overflow: hidden;
  margin: (-$widget-padding-vertical) (-$widget-padding-horizontal);
  border-radius: $border-radius;

  > img{
    max-width: 100%;
    border-radius: $border-radius $border-radius 0 0;
    transition: transform .15s ease;
  }

  &:hover > img{
    transform: scale(1.1, 1.1);
  }

  .title{
    position: absolute;
    top: 0;
    left: 0;
    margin: 20px;
  }

  .info{
    position: absolute;
    top: 0;
    right: 0;
    margin: 20px;
  }
}

.widget-footer-bottom{
  position: absolute;
  bottom: 0;
  width: 100%;
}

.widget-sm{
  height: 230px;
}

.widget-md{
  height: 373px;
}

.windget-padding-md {
  padding: $widget-padding-vertical $widget-padding-horizontal;
}

.windget-padding-lg {
  padding: $widget-padding-vertical*2 $widget-padding-horizontal*2;
}

.widget-body-container{
  position: relative;
  height: 100%;
}

.widget-top-overflow,
.widget-middle-overflow{
  position: relative;
  margin: 0 (-$widget-padding-horizontal);

  > img{
    max-width: 100%;
  }
}

.widget-top-overflow{
  margin-top: (-$widget-padding-vertical);
  border-top-left-radius: $border-radius;
  border-top-right-radius: $border-radius;
  overflow: hidden;

  > img{
    border-top-left-radius: $border-radius;
    border-top-right-radius: $border-radius;
  }

  > .btn-toolbar {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    margin-right: $widget-padding-horizontal;

    @include media-breakpoint-up(md){
      top: auto;
      bottom: 0;
    }
  }
}

/***********************************/
/**         Widgets Grid          **/
/***********************************/

.widget-container{
  min-height: 30px;

  .widget:not(.fullscreened) > header{
    cursor: move;
  }
}

.widget-placeholder{
  background:  rgba($white, .4);
  border: 1px dashed $gray-light;
  margin: -1px -1px calc(#{$grid-gutter-width} - 1px) -1px;
}

/***********************/
/*       Buttons       */
/***********************/

.btn-inverse {
  @include button-variant($white, $gray, darken($gray, 10%));
}

.btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

/***********************/
/*     Content Map     */
/***********************/

.content-map{
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  + .page-title{
    position: relative;
    z-index: 2;
  }
}

/***********************/
/*    Part:Stats Row   */
/***********************/

.stats-row{
  margin-bottom: 15px;
}

.stat-item{
  display: inline-block;
  padding-right: 15px;

  & + .stat-item{
    padding-left: 15px;
    border-left: 1px solid $hr-border-color;
  }
  .name{
    margin-bottom: 2px;
  }
  .value{
    font-weight: bold;
  }

  &.stat-item-mini-chart{
    position: relative;
    top: -12px;
    padding-left: 0;
    border-left: none;
  }
}

.capitalized{
  text-transform: capitalize
}
.__selection_style{
  border-color: #888
}
.table-no-mar{
  margin: 0;
  tr:first-child{
    td{
      border-top: transparent
    }
  }
}
.bootstrap-timepicker-widget{
  max-width: 230px;
  padding: 10px;
  margin-top: 5px;
  &.timepicker-orient-top::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    margin-top: -10px;
    margin-left: 10px;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    border-bottom: 10px solid white;
    filter: drop-shadow(0 -1px 0px rgba(0,0,0,.2))
  }
  &.timepicker-orient-bottom::before{
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    margin-bottom: -10px;
    margin-left: 10px;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    border-top: 10px solid white;
    filter: drop-shadow(0 1px 0px rgba(0,0,0,.2))
  }
  tr{
    td{
      text-align: center;
      a{
        color: #000;
        &:hover{
          color: #F2BE35
        }
      }
    }
  }
}
.bootstrap-timepicker-widget.open{
  display: block
}
.bootstrap-timepicker-hour,.bootstrap-timepicker-minute,.bootstrap-timepicker-meridian{
    display: inline-block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    text-align: center;
    line-height: 1.5;
    color: #555555;
    background-color: #fff;
    background-image: none;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.25rem;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.input-group{
  .no-padd{
    padding: 1px !important;
    transition: all ease 250ms;
  }
  .small-height{
    height: 34px;
     border-right: none !important;
     &:focus ~ .no-padd{
       border-color: #4D90FE;
       border-left-color: transparent;
     }
  }
}
mfBootstrapPaginator {
  .pagination:not(.pull-right) {
    float: right !important;
  }
  .pagination.pull-right {
    float: left !important;
  }
}
.loader-reservation {
  position: absolute;
  right: 0;
  // bottom: 0;
  left: 0;
  top: 0;
  width: 100%;
  height: 75vh;
  margin: auto;
  z-index: 1000;
  text-align: center;
  .loader-base {
    left: 50%;
    top: 50%;
    opacity: 0.5;
    width: 100px;
    z-index: 2000;
    position: absolute;
    background-color: #000000;
    transform: translate(-50%, -50%);
    height: 100px;
    border-radius: 5px;
    .fa-spinner {
      left: 30%;
      top: 30%;
      color: #FFFFFF;
      position: absolute;
      transform: translate(-50%,-50%);
    }
  }
  &.with-white-background {
    .center-align {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%,-50%);
    }
    background-color: #FFF;
  }
}
.early-check-in-limit {
  table {
    width: auto;
    .form-control, button.btn {
      padding: 6px 12px;
    }
    a.btn.btn-link {
      padding: 0.375rem 1rem;
    }
  }
}
tr.common-room:hover {
  background: #F0F4C3;
}
tr.common-room.is-selected {
  background: #F0F4C3 !important;
}
fieldset {
  padding: 10px 20px 15px !important;
  margin-top: 5px;
  legend {
    padding: 5px 10px 0px !important;
  }
}
@media (min-width: 992px) {
  .pdfViewer.removePageBorders .page {
    width: 100% !important;
    height: 100% !important;
    .textLayer {
      display: none !important;
    }
    .canvasWrapper {
        width: 100% !important;
        height: 100% !important;
        canvas {
        width: 600px !important; 
        height: 100% !important;
        margin: auto !important;       
        }
    }
  }
}

@media (max-width: 991px) {
  .pdfViewer.removePageBorders .page {
    width: 100% !important;
    height: 100% !important;
    .textLayer {
      display: none !important;
    }
    .canvasWrapper {
        width: 100% !important;
        height: 100% !important;
        canvas {
          width: 550px !important; 
          height: 100% !important;
          margin: auto !important;       
        }
    }
  }
}
datetime.custom-width-datetime {
  .form-inline {
    width: -webkit-fill-available !important;
      div {
          width: -webkit-fill-available !important;
      }
  }
}

.table-scroll {
  overflow: auto;
}
table.no-bottom-border:last-child {
  border-bottom: none !important;
}

// ---- CSS RESTRUCTURED ---- //
.invoice-view .invoice-header-image {
  min-height: 100px;
  background-size: 115%;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(assets/img/unnamed-new.png);
}
.invoice-view {
font-family: "Open Sans", sans-serif;
  font-size: 14px;
  line-height: 1.5;
  padding: 15px;
}
.invoice-view * {
  box-sizing: border-box;
}
.mt {
  margin-top: 1rem;
}
.widget-body.invoice-view .mt {
  margin-top: 0px;
}

.invoice-view div.mt.billing-guest{
  color: #000000 !important;
  min-height: 38vh;
  background-size: 50%;
}
.row.invoice-view-bootstrap-convertion.background-logo-image {
background-position: center !important;
background-repeat: no-repeat !important;
background-size: 150px !important;
background: url(data:image/png;base64,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);
}
.row.invoice-view-bootstrap-convertion {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 992px) {
  .row.invoice-view-bootstrap-convertion {
    margin-right: -15px !important;
    margin-left: -15px !important;
  }
}
@media (min-width: 768px) {
  .row.invoice-view-bootstrap-convertion {
    margin-right: -15px !important;
    margin-left: -15px !important;
  }
}
@media (min-width: 576px) {
  .row.invoice-view-bootstrap-convertion {
    margin-right: -15px !important;
    margin-left: -15px !important;
  }
}
.col-sm-12.invoice-view-bootstrap-convertion {
  position: relative;
  min-height: 1px;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-top: 1rem;
}
@media (min-width: 576px) {
  .col-sm-12.invoice-view-bootstrap-convertion {
    flex: 0 0 100%;
    max-width: 100%;
    margin-top: 1rem;  
  }
}
@media (min-width: 992px) {
  .col-sm-12.invoice-view-bootstrap-convertion {
    padding-right: 15px !important;
    padding-left: 15px !important;
    margin-top: 1rem;    
  }
}
@media (min-width: 768px) {
  .col-sm-12.invoice-view-bootstrap-convertion {
    padding-right: 15px !important;
    padding-left: 15px !important;
    margin-top: 1rem;    
  }
}
@media (min-width: 576px) {
  .col-sm-12.invoice-view-bootstrap-convertion {
    padding-right: 15px !important;
    padding-left: 15px !important;
    margin-top: 1rem;    
  }
}
.col-sm-6.invoice-view-bootstrap-convertion {
  position: relative;
  min-height: 1px;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  display: inline-block !important;
  margin-top: 1rem;  
}
@media (min-width: 576px) {
  .col-sm-6.invoice-view-bootstrap-convertion {
    flex: 0 0 50%;
    max-width: 50%;
    margin-top: 1rem;    
  }
}
@media (min-width: 992px) {
  .col-sm-6.invoice-view-bootstrap-convertion {
    padding-right: 15px !important;
    padding-left: 15px !important;
    margin-top: 1rem;
  }
}
@media (min-width: 768px) {
  .col-sm-6.invoice-view-bootstrap-convertion {
    padding-right: 15px !important;
    padding-left: 15px !important;
    margin-top: 1rem;  
  }
}
@media (min-width: 576px) {
  .col-sm-6.invoice-view-bootstrap-convertion {
    padding-right: 15px !important;
    padding-left: 15px !important;
    margin-top: 1rem;
  }
}
  div.image.rupee-icon-for-amount {
    margin-top: 0px;
    display: inline-block;
    min-height: 11px;
    width: 11px;
    background-size: 100% !important;
    max-width: 256px;
    max-height: 256px;
    background-position: center;
    background-repeat: no-repeat !important;
    background: url(data:image/svg+xml;utf8;base64,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)
  }
p.bill-no {
  margin-bottom: 0px !important;
}
p.receipt-footer-text{
  text-align: center;
   font-weight: bold;
   font-size: 18px;
}
div.customer-name {
  span:nth-child(1) {
    color: #000000;font-weight: 500;
  }
  span:nth-child(2) {
    margin-bottom: 0px;
    font-weight: 500;
    font-size: 1.6rem;
  }
}
div.mt.billing-guest .bill-date {
  float:right;
}
.mt.invoice-body {
  margin-top: 1rem !important;
}
.mt.invoice-body .table {
  border: 1px solid #000000;
  border-collapse: collapse;
  background-color: transparent;
  width: 100%;
  max-width: 100%;
}
.mt.invoice-body .table.no-m-b {
  margin-bottom: 0px;
}
.mt.invoice-body .table th, .mt.invoice-body .table td {
  border: 1px solid #000000 !important;
  padding: 0.5rem;
  font-size: 14px;
}
.mt.invoice-body .table-condence thead th {
  border: 1px solid #000000 !important;
  font-weight: 500;
}
.mt.invoice-body .table th:first-child, .mt.invoice-body .table td:first-child {
  width: 10%;
  text-align: center !important;
}
.mt.invoice-body .table th:nth-child(2), .mt.invoice-body .table td:nth-child(2) {
  width: 30%;
}
.mt.invoice-body .table th:last-child, .mt.invoice-body .table td:last-child {
  text-align: center !important;
  width: 20%;
}
tr.invoice-total-amount {
  text-align: center !important;
  font-size: 1.6rem;
  font-weight: 800 !important;
}
tr.invoice-total-amount td:first-child {
  border-right: none !important;
}
tr.invoice-total-amount td:nth-child(2) {
  border-left: none !important;
}
.text-capitalize {
  text-transform: capitalize !important;
}
.dharamshala-guide-lines {
  overflow-wrap: break-word;
  font-family: "Open Sans", sans-serif;
  font-size: 11px;
  line-height: 1.5;
  font-weight: 300;
}
div.customer-name span:nth-child(2) {
  font-size: 1.2rem;
}
// ---- CSS RESTRUCTURED ---- //
.room-categorytype-selection {
  .select2-selection__rendered {
    min-width: 250px
  }
}
@media print {
  bs-tooltip-container {
    display: none !important;
  }
  section.widget {
    display: none !important;
    .hide-in-print,.__search,.__download {
      display: none!important;
    }
  }
  .widget.revenue-report {
    display: block !important;
  }
  .filter-row {
    display: none !important;
  }
  .with-labels {
    .guest-type-level {
        color: #8e7452 !important;
        font-weight: 100;
    }
    .guest-level {
        color: #795548 !important;
        font-weight: 100;
    }
    td, th {
        font-size: 11px !important;
    }
  }
  td, th {
    font-size: 11px !important;
  }
}
.booking-cancellation {
  p {
    margin-bottom: 5px !important;
  }
}
.padding-left-30 {
  padding-left: 30px;
}
.padding-right-30 {
  padding-right: 30px;
}
.padding-left-right-30 {
  padding-left: 30px;
  padding-right: 30px;
}