import { AuthGuard } from './../guards/auth-guard.service';
import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class UserRoleService{

    constructor(private chttp: CommonHttpService,
                private authGuard: AuthGuard) {
     }

     getGroupList(){
         return this.chttp.get('menu/list');
     }
     getUserRoles(){
         return this.chttp.get('role/list');
     }
     getOneUserRole(id){
         return this.chttp.get(`role/${id}`);
     }
     
     saveUserRole(data){
        return this.chttp.post('role/add',data, true);
     }
     UpdateUserRole(id,data){
        return this.chttp.post(`role/edit/${id}`,data, true);
     }

     // role management
     
     getuserRoleModal(){
         return this.chttp.get('role/list/model');
     }
     getAssignedRoles(id,name){
        return this.chttp.get(`role/list/permission/${id}/${name}`);
     }
     setActionStatus(data){
         return this.chttp.post('rolepermission/edit', data, false);
     }

}