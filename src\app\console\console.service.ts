import { Injectable, Ng<PERSON>one } from '@angular/core';
import { HttpClient, HttpResponse, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import {apiUrl} from "../api-env"
@Injectable()
export class ConsoleService {
  private logs: string[] = [];
  private serverLogUrl = apiUrl+'api/log';
  // Use a single endpoint for different log types


  constructor(private zone: NgZone, private http: HttpClient) {
    this.overrideConsole();
  }

  private overrideConsole() {
    const originalConsoleLog = console.log;
    const originalConsoleWarn = console.warn;

    console.log = (...args: any[]) => {
      this.zone.run(() => {
        const logMessage = args.join(' ');
        this.logs.push(logMessage);
        this.sendLogToServer({ type: 'log', message: logMessage });
        this.printLogLocally(logMessage);
      });
      originalConsoleLog.apply(console, args);
    };

    console.warn = (...args: any[]) => {
      this.zone.run(() => {
        const logMessage = `Warning: ${args.join(' ')}`;
        this.logs.push(logMessage);
        this.sendLogToServer({ type: 'warning', message: logMessage });
        this.printLogLocally(logMessage);
      });
      originalConsoleWarn.apply(console, args);
    };
  }

  private sendLogToServer(logData: { type: string; message: any }): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });

    return this.http.post(this.serverLogUrl, logData, { headers });
  }

  private printLogLocally(logMessage: string): void {
    console.log('Locally logged:', logMessage);
  }

}
