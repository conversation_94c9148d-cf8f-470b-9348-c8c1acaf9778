<style>
  .form {
    padding: 25px;
    background: #fff;
    border-radius: 10px;
  }

</style>
<div class="container">
  <div class="jumbotron">
    <div class="form col-sm-6 offset-sm-3 col-md-4 offset-md-4">
      <form [formGroup]="forgetPasswordForm" (ngSubmit)="sendLink()">
        <div class="row">
          <div class="col-sm-12">
            <input type="text" placeholder="{{'FORGOT_PASS.EMAIL_ADD' | translate:param}}" formControlName="email" class="form-control">
            <span class="errMsg" *ngIf="!forgetPasswordForm.controls.email.valid && forgetPasswordForm.controls.email.touched">
                <span [hidden]="!forgetPasswordForm.controls.email.errors?.required">{{'FORGOT_PASS.EMAIL_REQ' | translate:param}}</span>
                <span [hidden]="!forgetPasswordForm.controls.email.errors?.email">{{'FORGOT_PASS.EMAIL_VALID' | translate:param}}</span>
              </span>
          </div>
        </div>
     
        <!--<div class="row">
          <label for="" class="col-sm-4 text-right">Confirm Password</label>
          <div class="col-sm-8">
            <input type="password" formControlName="cfm_password" class="form-control">
            <span class="errMsg" *ngIf="!forgetPasswordForm.controls.cfm_password.valid && forgetPasswordForm.controls.cfm_password.touched">
                <span [hidden]="!forgetPasswordForm.controls.cfm_password.errors?.required">Confirm your Password.</span>
                <span [hidden]="!forgetPasswordForm.controls.cfm_password.errors?.MatchPassword">Password does not match </span>
              </span>
          </div>
        </div>-->
        <br>
        <div class="row">
          <div class="col-sm-12">
              <input type="submit" [disabled]="!forgetPasswordForm.valid" value="{{'FORGOT_PASS.SEND_LINK' | translate:param}}" class="btn btn-sm btn-block btn-inverse">
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
