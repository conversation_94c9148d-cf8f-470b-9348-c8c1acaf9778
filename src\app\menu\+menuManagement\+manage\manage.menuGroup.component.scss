body {
  overflow-x: visible;
}

@import "../../../scss/variables";
@import "../../../../../node_modules/bootstrap/scss/_variables";
@import "../../../../../node_modules/bootstrap/scss/mixins";

/**
 * Nestable
 */

.dd {
  position: relative;
  display: block;
  margin: 0;
  padding: 0;
  max-width: 600px;
  list-style: none;
  font-size: 13px;
  line-height: 20px;
}

.dd-list {
  display: block;
  position: relative;
  margin: 0;
  padding: 0;
  list-style: none;
}

.dd-list .dd-list {
  padding-left: 30px;
}

.dd-collapsed .dd-list {
  display: none;
}

.dd-item,
.dd-empty,
.dd-placeholder {
  display: block;
  position: relative;
  margin: 0;
  padding: 0;
  min-height: 20px;
  font-size: 13px;
  line-height: 20px;
}

.dd-handle {
  display: block;
  height: 30px;
  margin: 5px 0;
  padding: 5px 10px;
  color: $text-color;
  text-decoration: none;
  background: $white;
  border: 1px solid $list-group-border-color;
  border-radius: $border-radius;
  box-sizing: border-box;
}

.dd-item>button {
  display: block;
  position: relative;
  cursor: pointer;
  float: left;
  width: 25px;
  height: 20px;
  margin: 5px 0;
  padding: 0;
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
  border: 0;
  background: transparent;
  font-size: 12px;
  line-height: 1;
  text-align: center;
  font-weight: bold;
  color: $text-color;
  &:focus,
  &:active {
    outline: 0;
  }
}

.dd-item>button:before {
  content: '+';
  display: block;
  position: absolute;
  width: 100%;
  text-align: center;
  text-indent: 0;
}

.dd-item>button[data-action="collapse"]:before {
  content: '-';
}

.dd-placeholder,
.dd-empty {
  margin: 5px 0;
  padding: 0;
  min-height: 30px;
  background: $gray-semi-lighter;
  border: 1px dashed $gray-light;
  box-sizing: border-box;
  border-radius: $border-radius;
}

.dd-empty {
  border: 1px dashed $gray-light;
  min-height: 100px;
  background-size: 60px 60px;
  background-position: 0 0, 30px 30px;
  background-image: -moz-linear-gradient(45deg, #fff 25%, transparent 25%, transparent 75%, #fff 75%, #fff), -moz-linear-gradient(45deg, #fff 25%, transparent 25%, transparent 75%, #fff 75%, #fff);
  background-image: linear-gradient(45deg, #FFF 25%, rgba(0, 0, 0, 0) 25%, rgba(0, 0, 0, 0) 75%, #FFF 75%, #FFF), linear-gradient(45deg, #FFF 25%, rgba(0, 0, 0, 0) 25%, rgba(0, 0, 0, 0) 75%, #FFF 75%, #FFF);
}

.dd-dragel {
  position: absolute;
  pointer-events: none;
  z-index: 9999;
  background: #fff;
  border: 1px solid #ddd;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, .15));
  span {
    vertical-align: bottom;
    margin-top: 5px;
    display: inline-block;
  }
  .dd-handle {
    margin: 0 5px 0 0;
    border: none;
    box-shadow: none !important;
    filter: drop-shadow(0 0 0px rgba(0, 0, 0, .0));
  }
  .managemenu-list-actions {
    display: none
  }
}

.dd-dragel>.dd-item .dd-handle {
  margin-top: 0;
}

.dd-item button[data-action="collapse"] {
  display: none
}

.dd-dragel .dd-handle {
  -webkit-box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, .1);
  box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, .1);
}


/**
 * Nestable Extras
 */

.nestable-lists {
  display: block;
  clear: both;
  padding: 30px 0;
  width: 100%;
  border: 0;
  border-top: 2px solid #ddd;
  border-bottom: 2px solid #ddd;
}

@media only screen and (min-width: 700px) {
  .dd+.dd {
    margin-left: 2%;
  }
}

.dd-hover>.dd-handle {
  background: #2ea8e5 !important;
}


/**
 * Nestable Draggable Handles
 */

.dd3-content {
  display: block;
  height: 30px;
  margin: 5px 0;
  padding: 5px 10px 5px 40px;
  color: #333;
  text-decoration: none;
  font-weight: bold;
  border: 1px solid #ccc;
  background: #fafafa;
  background: linear-gradient(top, #fafafa 0%, #eee 100%);
  border-radius: 3px;
  box-sizing: border-box;
}

.dd3-content:hover {
  color: #2ea8e5;
  background: #fff;
}

.dd-dragel>.dd3-item>.dd3-content {
  margin: 0;
}

.dd3-item>button {
  margin-left: 30px;
}

.dd3-handle {
  position: absolute;
  margin: 0;
  left: 0;
  top: 0;
  cursor: pointer;
  width: 30px;
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
  border: 1px solid #aaa;
  background: #ddd;
  background: -webkit-linear-gradient(top, #ddd 0%, #bbb 100%);
  background: -moz-linear-gradient(top, #ddd 0%, #bbb 100%);
  background: linear-gradient(top, #ddd 0%, #bbb 100%);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.dd3-handle:before {
  content: '≡';
  display: block;
  position: absolute;
  left: 0;
  top: 3px;
  width: 100%;
  text-align: center;
  text-indent: 0;
  color: #fff;
  font-size: 20px;
  font-weight: normal;
}

.dd3-handle:hover {
  background: #ddd;
}


/***********************************/


/**      LIST GROUP SORTABLE      **/


/***********************************/

.list-group-sortable {
  >.list-group-item {
    margin-bottom: 0;
    border-radius: $border-radius;
    +.list-group-item {
      margin-top: $line-height-computed/2;
    }
  }
  >.list-group-item-placeholder {
    border: 1px dashed $gray-light;
    background-color: $gray-semi-lighter;
  }
  &:last-of-type>.list-group-item:last-child {
    border-bottom: 1px solid $list-group-border-color;
  }
}

.managemenu-list {
  padding: 10px;
  border-radius: 5px;
  margin: 0;
  border: 2px dashed #ccc;
  li {
    width: 100%;
    list-style-type: none;
    padding: 5px 10px;
    margin: 4px 0;
    user-select: none;
    cursor: default;
    border: 1px solid #ddd;
    div.dd-handle {
      border: none;
      padding: 0px 10px;
      color: #555555;
      text-decoration: none;
      border-radius: 0.25rem;
      box-sizing: border-box;
      display: inline-block;
      cursor: pointer;
      margin: 0 0 0 -10px;
      background: transparent;
      height: 20px;
      .menu-icon {
        margin-right: 5px;
        color: #888;
        font-size: 11px;
        vertical-align: baseline;
      }
      &:hover {
        .menu-icon {
          color: #218BC3;
        }
      }
    }
    .managemenu-list-actions {
      position: absolute;
      top: 0;
      right: 0;
      display: inline-block;
      margin: 5px;
      z-index: 999999;
      a {
        opacity: 0;
        transform: translateX(10px);
        display: inline-block;
        transition: all ease-in-out 250ms;
        &+a {
          margin-left: 6px;
        }
        &:hover {
          i {
            color: #218BC3
          }
        }
      }
      i {
        color: #777
      }
    }
    &:hover>.managemenu-list-actions {
      a {
        opacity: 1;
        transform: translateX(0px)
      }
    }
  }
  .children-menuitem {
    position: relative;
    &::before {
      content: '\f148';
      font-family: fontAwesome;
      transform: rotate(90deg);
      display: inline-block;
      position: absolute;
      height: 35px;
      font-size: 17px;
      color: #ccc;
      left: 0;
      margin-left: -25px;
    }
  }
}

.popover-content {
  padding: 5px;
  max-height: 250px;
  overflow-y: auto;
  width: 245px;
  border: 1px solid #ddd;
  box-shadow: none;
  &::before {
    content: '';
    background: #ebeff1;
    height: 5px;
    width: 30px;
    position: absolute;
    top: 0;
    margin-left: 5px;
    left: 0;
  }
}

.arrow {
  content: '';
  border-bottom: 15px solid #ebeff1;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  height: 0;
  width: 0;
  position: absolute;
  top: 0;
  margin-top: -13px;
  margin-left: 5px;
  left: 0;
  z-index: -2;
  filter: drop-shadow(0px 0 1px rgba(0, 0, 0, .32));
}

.iconpicker-items {
  a {
    margin: 3px;
    height: 30px;
    width: 30px;
    text-align: center;
    display: inline-block;
    font-size: 20px;
    color: #333;
    i {
      line-height: 10px;
    }
    &.iconpicker-selected {
      color: #fff !important;
      border-radius: 5px;
    }
  }
}
.icp-dd ~ .dropdown-menu{
  border: none;
  box-shadow: none;
  background: transparent;
}
.flexed{
  display: flex;
  & > *{
    flex : 1
  }
}
.alert-danger{
  margin-bottom: 0px;
  padding: 2px 25px 2px 7px;
  .close{
    margin-right: -15px;
  }
}