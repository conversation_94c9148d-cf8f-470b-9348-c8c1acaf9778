import { Component, EventEmitter, Output, ViewEncapsulation, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { BookingService } from 'app/shared/services/booking.service';
import { PaymentRequestService } from 'app/shared/services/payment.service';
import { forkJoin } from 'rxjs/observable/forkJoin';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/observable/of';
import 'rxjs/add/operator/catch';

@Component({
  selector: 'app-online-booking-popup',
  templateUrl: '../onlineBookingPopup.component.html',
  encapsulation: ViewEncapsulation.None
})
export class OnlineBookingPopupComponent {
  @Output() closed: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('onlineBookingModal') public onlineBookingModal: ModalDirective;
  private isInitialized = false;
  total_booking_request: number = 0;
  total_payment_request: number = 0;

  constructor(
    private bookingService: BookingService,
    private paymentService: PaymentRequestService,
  ) {}

  open(): void {
    if (this.onlineBookingModal) {
      this.onlineBookingModal.show();
    }
  }

  close(): void {
    if (this.onlineBookingModal) {
      this.onlineBookingModal.hide();
    }
  }

  ngAfterViewInit(): void {
    this.isInitialized = true;
    if (this.onlineBookingModal) {
      this.onlineBookingModal.onHidden.subscribe(() => {
        this.closed.emit();
        // Notify anyone waiting that modal is hidden
        document.dispatchEvent(new CustomEvent('online-booking-popup-hidden', { bubbles: true }));
      });
    }

    // If login flow set a flag to check after navigation, do it now
    const shouldCheck = localStorage.getItem('show_online_booking_popup') === '1';
    if (shouldCheck) {
      localStorage.removeItem('show_online_booking_popup');
      this.checkCountsAndMaybeOpen();
    }

    // Also support an explicit trigger to check via custom event
    document.addEventListener('trigger-check-online-booking-popup', this.handleTriggerCheck, true);
  }

  ngOnDestroy(): void {
    document.removeEventListener('trigger-check-online-booking-popup', this.handleTriggerCheck, true);
  }

  private handleTriggerCheck = () => {
    this.checkCountsAndMaybeOpen();
  }

  private checkCountsAndMaybeOpen(): void {
    if (!this.isInitialized) {
      return;
    }
    const booking$ = this.bookingService.getOnlineBookingList()
      .catch(() => Observable.of({ data: [] }));
    const payment$ = this.paymentService.getOnlinePaymentList()
      .catch(() => Observable.of({ data: [] }));

    forkJoin([booking$, payment$]).subscribe(([bookingRes, paymentRes]: any) => {
      this.total_booking_request = bookingRes.data.length ? bookingRes.data.length : 0;
      this.total_payment_request = paymentRes.data.length ? paymentRes.data.length : 0;
      const total = this.total_booking_request + this.total_payment_request;
      console.log(total, 'total');
      if (total > 0) {
        this.open();
      }
    });
  }
}


