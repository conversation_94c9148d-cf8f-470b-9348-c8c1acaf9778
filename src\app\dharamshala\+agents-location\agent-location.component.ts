import { DharamshalaService } from './../../shared/services/dharamshala.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****

@Component({
    selector: 'agents',
    templateUrl: './agent-location.component.html'
})

export class AgentLocationComponent implements OnInit {
    config: any;// New Change **** 
    // --- API --- //
    private sub: any;
    private cities: any;

    public data: any;
    public stateList: any[];
    public selectedAgent: any;
    public searchQuery: string;
    public originalData: any[];
    public canViewRecords: boolean;
    public hideAdd: boolean = true;
    public hideEdit: boolean = true;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private DS: DharamshalaService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) { 
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() {
    // console.log("stateList : ",this.stateList);
    this.canViewRecords = true;
    this.sub = this.DS.getAgentLocationList()
    .subscribe((res) => {
        if (res.status == "success") {
            this.stateList = res.data.stateList.map((data) => {
                return { id: data.state, text: data.state };
            });
            console.log("State List  : ",this.stateList);
            this.stateList.unshift({ id: "000000", text: "---Please select one---"});
            this.data = res.data.agentLocationList;
            this.originalData = res.data.agentLocationList;
            }
        }, error => {
            if (error.status == 403) {
                this.canViewRecords = false;
            }
        });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    showAdd() {
        this.hideAdd = false;
        this.hideEdit = true;
    }
    handleAddHide() {
        this.hideAdd = true;
    }
    addToListProcess(event) {
        if(this.canViewRecords) {
            this.originalData.push(event);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }

    showEdit(agentLocation) {
        this.hideAdd = true;
        this.hideEdit = false;
        this.selectedAgent = agentLocation;
    }
    handleEditHide() {
        this.hideEdit = true;
    }
    updateToListProcess(event) {
        if(this.canViewRecords) {
            this.data[this.findIndex(event.id, "id")] = event;
            this.originalData[this.findIndex(event.id, "id", this.originalData)] = event;        
        }
    }
    findIndex(searchTerm, property, searchArray?: any[]) {
        searchArray = searchArray ? searchArray :  this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i);
        }
        return -1;
    }

    parseInteger(data: string) {
        return parseInt(data);
    }

    searchEvent() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery != '') {
            this.data = this.data.filter( data => {
                let searchTargetString = (<string>data.name).concat(data.state);
                return (searchTargetString.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            });
        }
        else
            this.initializeData();
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    initializeData() {
        this.data = this.originalData;
    }
    ngOnDestroy(){
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if(this.canViewRecords && this.sub){
            this.sub.unsubscribe();
        }
        if(this.canViewRecords && this.cities) {
            this.cities.unsubscribe();
        }

        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}