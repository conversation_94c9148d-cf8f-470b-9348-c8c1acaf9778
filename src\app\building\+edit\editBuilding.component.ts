import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Select2OptionData } from 'ng2-select2/ng2-select2';
import { BuildingService } from './../../shared/services/building.service';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'edit-building',
    templateUrl: '../building.actions.component.html'
})

export class EditBuildingComponent implements OnInit {
    pageType: string = "Edit";

    @Input() dharamshalaList: any[];
    @Input() building: any;
    @Input() gethiddenEditBuilding: boolean;
    @Output() sendBuildingEvent = new EventEmitter();
    select2Options: any = {
        "width": "100%"
    }
    dropdownSelect: number;
    buildingForm: FormGroup;
    private sub: any;
    constructor(
        private _fb: FormBuilder,
        private BS: BuildingService,
        public translate: TranslateService,
    ) { 
        translate.get('BUILDING.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.buildForm();
       
    }
    buildForm() {
        this.buildingForm = this._fb.group({
            dharamshala_id: ['', Validators.required],
            name: ['', Validators.required],
            status: ['', Validators.required],
            floors: this._fb.array([])
        });
        this.buildingForm.patchValue(this.building);
        this.dropdownSelect = this.building['dharamshala_id'];
        this.initFloors();
        console.log("Form Data : ",this.buildingForm);
    }
    getDharamshalaList(): Select2OptionData[] {
        if (this.dharamshalaList.length > 0) {
            return jQuery.map(this.dharamshalaList, function (obj) {
                return { id: obj.id, text: obj.name };
            })
        }
    }
    dharamshalaChanges(event) {
        this.buildingForm.controls['dharamshala_id'].patchValue(event.value);
    }
    initFloors() {
        if (this.building) {
            if (this.building.floors.length > 0) {
                this.building.floors.forEach(element => {
                    this.addFloors(element);
                });
            } else {
                this.addFloors();
            }
        }
    }
    addFloors(ele?) {
        let control = <FormArray>this.buildingForm.controls['floors'];
        control.push(this._fb.group({
            id: [(ele) ? ele.id : ''],
            name: [{
                value: (ele) ? ele.name : '',
                disabled: (this.buildingForm.controls['status'].value) ? false : 'disabled'
            }, Validators.required],
            status: [{
                value: (ele) ? ele.status : true,
                disabled: (this.buildingForm.controls['status'].value) ? false : 'disabled'
            }, Validators.required]
        }));
    }
    deleteFloor(index) {
        let control = <FormArray>this.buildingForm.controls['floors'];
        control.removeAt(index)
    }
    toggleChild(data) {
        let result;
        this.gethiddenEditBuilding = !this.gethiddenEditBuilding;
        if (data) {
            result = { gethiddenEditBuilding: this.gethiddenEditBuilding, data: data }
        } else {
            result = { gethiddenEditBuilding: this.gethiddenEditBuilding }
        }
        this.sendBuildingEvent.emit(result);
    }
    saveBuilding() {
        if (this.buildingForm.valid) {
            this.sub = this.BS.updateBuilding(this.building.id, this.buildingForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.buildingForm.reset();
                        this.toggleChild(res.data);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.buildingForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    manageFloorStatus() {
        if (!this.buildingForm.controls['status'].value) {
            let floors = <FormArray>this.buildingForm.controls['floors'];
            floors.controls.forEach((element: FormGroup, index: number) => {
                element.controls['status'].patchValue(false);
                element.controls['status'].disable();
                element.controls['name'].disable();
                if (element.controls['id'].value == "") {
                    floors.removeAt(index);
                }
            })
        } else {
            let floors = <FormArray>this.buildingForm.controls['floors'];
            floors.controls.forEach((element: FormGroup) => {
                element.controls['status'].enable();
                element.controls['name'].enable();
            })
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}