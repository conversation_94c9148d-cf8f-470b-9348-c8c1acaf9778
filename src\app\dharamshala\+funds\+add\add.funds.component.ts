import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, Output, EventEmitter, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'add-funds',
    templateUrl: '../funds.action.html'
})

export class AddFundsComponent implements OnInit {
    public fundsTypeForm: FormGroup;
    public pageType: string = "Add";
    private sub: any;

    @Output() hideAddEvent = new EventEmitter();
    @Output() addToList = new EventEmitter();
    constructor(
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        public translate: TranslateService
    ) { 
        translate.get('FUNDS.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.initForm();
    }
    initForm() {
        this.fundsTypeForm = this._fb.group({
            name: ['', Validators.required],
            status: [true, Validators.required],
            is_default: [false]
        })
    }
    hideComponent() {
        this.hideAddEvent.emit();
        this.fundsTypeForm.reset();
    }
    toggleChild(data: any = true) {
        this.addToList.emit(data);
    }
    saveFundType() {
        if (this.fundsTypeForm.valid) {
            this.sub = this.DS.saveFunds(this.fundsTypeForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.toggleChild(res.data);
                        this.fundsTypeForm.reset();
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.fundsTypeForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}