import { CustomValidators } from 'ng2-validation';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, Output, EventEmitter, OnDestroy, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'add-stay-type',
    templateUrl: '../stay-type.action.html',
    styleUrls: ['../stay-type-style.scss'],
    encapsulation: ViewEncapsulation.None
})

export class AddStayTypeComponent implements OnInit, OnDestroy {
    public menuIcon: string;
    public StayTypeForm: FormGroup;
    public pageType: string = "Add";
    private sub: any;

    @Output() hideAddEvent = new EventEmitter();
    @Output() addToList = new EventEmitter();
    constructor(
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        public translate: TranslateService
    ) { 
        translate.get('STAY_TYPE.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            this.pageType = res;
        });
    }

    ngOnInit() {
        this.initForm();
    }
    onIconSelect(icon: string): void {
        console.log('Icon selected:', icon);
        this.menuIcon = icon.replace('fa fa-', '');
        this.StayTypeForm.patchValue({
            stay_type_icon: this.menuIcon
        });
    }
    initForm() {
        this.StayTypeForm = this._fb.group({
            is_default: [false],
            name: ['', Validators.required],
            status: [true, Validators.required],
            stay_type_icon: [''],
            duration: ['', [Validators.required, CustomValidators.digits, CustomValidators.lte(24)]],
            charge: ['', [Validators.required, CustomValidators.number, CustomValidators.gte(0), CustomValidators.lte(100)]],
        })
    }
    hideComponent() {
        this.hideAddEvent.emit();
        this.StayTypeForm.reset();
        this.initializeStayTypeIcon();
    }
    initializeStayTypeIcon() {
        this.menuIcon = undefined;
    }
    toggleChild(data: any = true) {
        this.addToList.emit(data);
    }
    saveStayType() {
        if (this.StayTypeForm.valid) {
            this.sub = this.DS.saveStayType(this.StayTypeForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.toggleChild(res.data);
                        this.StayTypeForm.reset();
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.StayTypeForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}