import { CustomValidators } from 'ng2-validation';
import { Location } from '@angular/common';
import { Component, OnInit, Output, EventEmitter, Input, OnChanges, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { FormControl } from '@angular/forms/src/model';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'edit-agent',
    templateUrl: '../agent.action.html'
})

export class EditAgentComponent implements OnInit {
    public selectedCity: any;
    public pageType: string = "Edit";
    public agentTypeForm: FormGroup;
    private sub: any;
    @Input() cityList;
    @Input() selectedAgent;
    @Output() updateToList = new EventEmitter();
    @Output() hideEditEvent = new EventEmitter();
    select2Options: any = {
        width: '100%'
    };
    constructor(
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        public translate: TranslateService;

    ) { 
        translate.get('AGENT.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.selectedCity = this.selectedAgent.location;
        this.initForm();
    }
    ngOnChanges(change: any) {
        if (this.agentTypeForm) {
            this.selectedCity = change.selectedAgent.currentValue.location;
            this.agentTypeForm.patchValue(change.selectedAgent.currentValue);
        }
    }
    initForm() {
        this.agentTypeForm = this._fb.group({
            name: ['', Validators.required],
            location: ['', [Validators.required, CustomValidators.notEqual("000000")]],
            location_name: [''],
            status: [true, Validators.required]
        })
        this.agentTypeForm.patchValue(this.selectedAgent);
        console.log("Form : ",this.agentTypeForm.value);
    }
    cityChanged(event: any) {
        console.log("data got in the event", event);
        console.log("city list", this.cityList);
        
        
        let cityName = this.cityList[event?.id]?.text;
        console.log("name of the city", cityName);
        
        this.agentTypeForm.get('location').patchValue(event?.id);
        this.agentTypeForm.get('location_name').patchValue(cityName);
        this.selectedCity = cityName
        console.log("Location : ",this.agentTypeForm.value);
    }
    saveAgentType() {
        if (this.agentTypeForm.valid) {
            this.sub = this.DS.updateAgent(this.selectedAgent.id,this.agentTypeForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.updateToList.emit(res.data);
                        this.agentTypeForm.reset();
                        this.hideComponent();
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.agentTypeForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            for(let field in this.agentTypeForm.controls) {
                this.agentTypeForm.controls[field].markAsDirty();
                this.agentTypeForm.controls[field].markAsTouched();
            }
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    findIndex(searchTerm: any, property: any, targetArray: any[]) {
        for(let i = 0; i < targetArray.length; i++) {
            if(targetArray[i][property] === searchTerm) { return i}
        }
        return -1;
    }
    hideComponent() {
        this.hideEditEvent.emit();
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}