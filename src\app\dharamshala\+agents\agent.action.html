<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-user-secret"></i>&nbsp;&nbsp;{{pageType}} {{'AGENT.ADD_PAGE.AGEN_TYPE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="agentTypeForm" (ngSubmit)="saveAgentType()">
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'AGENT.ADD_PAGE.NAME' | translate:param}}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="agentTypeForm.controls.name.errors?.backend">{{agentTypeForm.controls.name.errors?.backend}}</span>
            <input type="text"  class="form-control" formControlName="name" name="name" placeholder="">
            <span class="errMsg" *ngIf="!agentTypeForm.controls.name.valid && !agentTypeForm.controls.name.untouched">
              <span [hidden]="!agentTypeForm.controls.name.errors.required">{{'AGENT.ADD_PAGE.VALID_MSG.NAME_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'AGENT.ADD_PAGE.LOCATION' | translate:param}}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="agentTypeForm.controls.location.errors?.backend">{{agentTypeForm.controls.location.errors?.backend}}</span>
            <!-- <input type="text"  class="form-control" formControlName="location" name="location" placeholder=""> -->
             <ng-select [items]="cityList" bindLabel="text" bindValue="id" formControlName="location" (change)="cityChanged($event)" ></ng-select>
             <span class="errMsg" *ngIf="!agentTypeForm.controls.location.valid && !agentTypeForm.controls.location.untouched">
              <span [hidden]="!agentTypeForm.controls.location.errors.required">{{'AGENT.ADD_PAGE.VALID_MSG.LOCATION_REQ' | translate:param}}</span>
              <span [hidden]="!agentTypeForm.controls.location.errors?.notEqual">{{'AGENT.ADD_PAGE.VALID_MSG.LOCATION_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'AGENT.ADD_PAGE.STATUS' | translate:param}}</label>
          <div class="col-md-8 ">
            <div class="radio-horizontal">
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-1" [value]="true">
                <label for="radio-1">
                  {{'AGENT.ADD_PAGE.ACTIVE' | translate:param}}
                      </label>
              </div>
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-2" [value]="false">
                <label for="radio-2">
                  {{'AGENT.ADD_PAGE.INACTIVE' | translate:param}}
                      </label>
              </div>
            </div>
          </div>
        </div>

        <div class="form-group row">
          <div class="col-md-8 offset-md-3">
            <div class="">
              <button type="submit" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'AGENT.ADD_PAGE.SAVE' | translate:param}}</button>
              <button type="button" (click)="hideComponent()" class="btn btn-sm btn-secondary">{{'AGENT.ADD_PAGE.CANCEL' | translate:param}}</button>
            </div>
          </div>
        </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
