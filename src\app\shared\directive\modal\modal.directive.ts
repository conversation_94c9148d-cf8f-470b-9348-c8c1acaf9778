import { HostListener, Directive, Input } from '@angular/core';
import { ModalService } from './modal.service';

@Directive({
  selector: '[openModal]'
})

export class ModalDirective {

  @Input('modal') identifier: string;

  constructor(private modalService: ModalService) { }

  @HostListener('click', ['$event'])
  clickEvent(event) {
    event.preventDefault();
    event.stopPropagation();
    this.modalService.open(this.identifier);
  }

}