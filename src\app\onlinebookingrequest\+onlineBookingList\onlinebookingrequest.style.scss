.table-scroll {
    overflow: auto;
}

table.no-bottom-border:last-child {
    border-bottom: none !important;
}

.red-header {
    color: red
}

.bgcolor {
    background: #c5bdbd;
    padding: 3px;
}

.label {
    font-size: 12px;
    font-weight: bold;
    padding: 3px 7px;
    min-width: 100px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    text-transform: uppercase;
    filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, .352));
    -webkit-filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, .352));

    &.label-success {
        background: #4CAF50;
    }

    &.label-danger {
        background: #F44336;
    }

    &.label-warning {
        background: #CDDC39;
        color: #333 !important
    }

    &.label-primary {
        background: #2196F3;
    }

    &.label-light {
        background: rgb(138, 138, 138);
    }
}

.uppercase {
    text-transform: uppercase;
}

.reference-level {
    cursor: pointer;
}

.guest-type-level {
    cursor: pointer;
    background-color: #faebd7;
    color: #795548;
    font-weight: bold;

    td {
        padding-left: 15px;
    }
}

.guest-level {
    color: #FFFFFF;
    font-weight: bold;
    background-color: #8e7452;

    td {
        padding-left: 30px;

        span.revenue-report-note {
            white-space: normal;
        }
    }

    td:last-child {
        padding-left: 15px;
    }
}

.width-150 {
    width: 150px;
}

.change-customer-type {
    .row.extra-padding {
        padding: 15px 0px 5px 15px;
    }
}

.btn-margin {
    margin-top: 0px !important;
}

.loader-parent-style {
    display: flex;
    justify-content: center;
    // padding: 2rem 0;
    font-size: 11px;
}

@media (max-width: 592px) { 
    .btn-margin {
        margin-top: 10px !important;
    }
}

@media (min-width: 992px) {
    .modal-lg {
        img {
            max-width: 500px;
            max-height: 500px;
            border: 0.1px solid gainsboro;
            margin: 20px 0px;
        }

        max-width: 1024px;
    }
}

@media (max-width: 991px) {
    .modal-lg {
        img {
            max-width: 500px;
        }

        max-width: 500px;
    }
}

@media print {

    // .widget,.widget-body {
    //     display: none !important;
    // }
    // .with-labels {
    // .guest-type-level {
    //     color: #8e7452 !important;
    // }
    // .guest-level {
    //     color: #795548 !important;
    // }
    // td, th {
    //     font-size: 10px !important;
    // }
    // }

    .widget.enable-print,
    .widget-body.enable-print {
        display: block !important;
    }

    header {
        .view-details-buttons {
            display: none;
        }
    }

    .large-hr.enable-print {
        display: none;
    }

    .modal-dialog.modal-lg {

        // width: 100% !important;
        .textLayer {
            display: none !important;
        }

        .modal-content {
            border: none;

            img {
                max-width: 600px;
                max-height: 900px;
                object-fit: contain;
            }

            .ng2-pdf-viewer-container {
                display: block !important;

                .pdfViewer.removePageBorders .page {
                    width: auto !important;

                    .canvasWrapper {
                        width: auto !important;

                        canvas {
                            width: 100% !important;
                            height: auto !important;
                            margin: auto;
                        }
                    }
                }
            }

            .modal-footer {
                display: none;
            }
        }
    }
}

.panel-heading h4 {
    margin: 0;
}

.panel-default {
    border: 1px solid #ddd;
    border-radius: 3px;
}

div.amount-summaries {
    margin-top: 15px;
    margin-bottom: 15px;

    .row.filter-row {
        margin-top: 5px;
        //   color: #5d8fc2;
    }
}

.pi-photo-id {
    max-width: 50%;
    height: auto;
    margin: 0 auto;
    display: table;
}

.text-align {
    text-align: right !important;
}