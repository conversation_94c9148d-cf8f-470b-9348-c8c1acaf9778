import { Directive, ElementRef, OnInit } from '@angular/core';

@Directive({
    selector: '[autoWidth]'
})

export class AutoWidthDirective implements OnInit {
    $el: any;
    element: any;
    elementProperty: any;
    elementLength: number;
    eleFont : any;
    eleFontSize : any;
    eleFontWeight: any;

    constructor(el: ElementRef) {
        this.element = el.nativeElement;
        console.log("this.element : ",this.element);
        // el.nativeElement.style.width = "";
    }

    ngOnInit() {
        // get font family
        this.eleFont = this.css(this.element,'font-family');
        // get font size
        this.eleFontSize = this.css(this.element, 'font-size');
        // get font weight
        this.eleFontWeight = this.css(this.element, 'font-weight');
        // set up variable containing all above properties
        this.elementProperty = this.eleFontWeight+" "+this.eleFontSize+" "+this.eleFont;
        // calculate element length based on text
        console.log("this.element.value : ",this.element.value);
        console.log("this.elementProperty : ",this.elementProperty);
        this.elementLength = this.getTextWidth(this.element.value,this.elementProperty)
        // setting up width
        this.element.style.width = ((this.elementLength) + 5) + "px";
    }
    css(element, property) {
        return window.getComputedStyle(element, null).getPropertyValue(property);
    }
    getTextWidth(text, font) {
        let canvas = document.createElement("canvas");
        let context = canvas.getContext("2d");
        context.font = font;
        let metrics = context.measureText(text);
        canvas = null;
        return metrics.width;
    }
}
