import { SelectFirstInputDirective } from './directive/focus.directive';
import { AutoWidthDirective } from './directive/autoWidth.directive';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { DropzoneDirective } from './directive/dropzone.directive';
import { TranslateModule, TranslateLoader, TranslateService } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClient } from '@angular/common/http';
import { TwoDigitDecimaNumberDirective } from './directive/decimalNumber.directive';
import { DatepickerValueAccessor } from './directive/datepicker-value-accessor.directive';
import { ModalModule } from 'ngx-bootstrap/modal';
import { OnlineBookingPopupComponent } from 'app/login/OnlineBookingPopup/onlineBookingPopup.component';
export function createTranslateLoader(http: HttpClient) {
    return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}
@NgModule({
    imports: [CommonModule, TranslateModule, ModalModule, RouterModule],
    exports: [CommonModule, DropzoneDirective, AutoWidthDirective, TranslateModule, RouterModule, DatepickerValueAccessor, OnlineBookingPopupComponent ],
    declarations: [DropzoneDirective, AutoWidthDirective, TwoDigitDecimaNumberDirective, DatepickerValueAccessor, OnlineBookingPopupComponent],
    providers: [TranslateService]
})
export class SharedModule { }
