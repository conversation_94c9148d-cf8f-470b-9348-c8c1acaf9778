/* Header Wrapper */
.c-main-booking-header-wrap {
  background: #ff9d00;
  background: linear-gradient(to bottom, #ff9d00 1%, #ed5904 100%);
  padding: 12px 0;
}

/* Header Section */
.c-main-booking-header-section {
  max-width: 1130px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Header Text */
.c-main-booking-header-text {
  color: #fff;
  font-size: 25px;
  font-weight: 700;
  margin: 0;
}

.c-main-booking-header-text small {
  font-size: 12px;
  color: #fff;
}

/* Mobile view adjustments */
@media (max-width: 767px) {
  .c-main-booking-header-section {
    display: block;
    text-align: center;
  }
  .c-main-booking-header-logo {
    margin: 0 auto;
  }
  .c-main-booking-header-text {
    font-size: 20px;
  }
}

/* Tablet view adjustments (768px to 991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .c-main-booking-header-section {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .c-main-booking-header-logo {
    margin-right: 20px;
  }
  .c-main-booking-header-text {
    font-size: 22px;
  }
}

/* Desktop view adjustments (992px and above) */
@media (min-width: 992px) {
  .c-main-booking-header-section {
    display: flex;
    justify-content: space-between;
  }
  .c-main-booking-header-logo {
    margin-right: 20px;
  }
  .c-main-booking-header-text {
    font-size: 25px;
  }
}

.payment-container {
  max-width: 500px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.qr-code-container {
  margin-bottom: 20px;
}

.qr-code {
  width: 200px;
  height: auto;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  background-color: #f8f9fa;
  color: #333;
}

.error-content {
  max-width: 600px;
  padding: 20px;
}

.error-title {
  font-size: 2rem;
  font-weight: bold;
}

.error-message {
  font-size: 1.2rem;
  margin: 10px 0;
}

.doc-upload-success {
  padding: 5px 10px;
  border: 1px solid green;
  background-color: #64bd6382;
  font-size: large;
  margin-right: 10px;
  border-radius: 15%;
  margin-left: 20px;
}

.doc-upload-cancel {
  padding: 5px 10px;
  border: 1px solid green;
  background-color: #e1531e8c;
  font-size: large;
  border-radius: 15%;
}

.copy:hover {
  cursor: pointer;
}

.notes {
  background: beige;
  padding: 15px;
  border-radius: 20px;
  margin-bottom: 15px;
  color: red;
}