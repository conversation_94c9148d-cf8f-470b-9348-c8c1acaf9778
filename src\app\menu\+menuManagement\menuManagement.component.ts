import { MenuGroupService } from './../../shared/services/menuGroup.service';
import { Component, OnDestroy, ViewEncapsulation } from '@angular/core';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
@Component({
    selector: 'menu-management',
    templateUrl: './menuManagement.component.html',
    providers: [MenuGroupService]
})
export class MenuManagementComponent{
    config: any;// New Change ****
    data: any[];
    selectedMG: any;
    originalData: any[];
    searchQuery: string;
    //service variable
    private sub: any;
    // hide/show child component
    hideAddMG : boolean = true;
    hideEditMG : boolean = true;
    public canViewRecords: boolean;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****

    constructor(
        public translate: TranslateService,// New Change ****
        private auth: AuthGuard,
        private MS: MenuGroupService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        this.canViewRecords = true;
        this.sub = this.MS.getAllMenuGroup().subscribe((res) => {
            this.data = res.data;
            this.originalData = res.data;
            // console.log("this.data : ",this.data);
        },error => {
            if (error.status == 403) {
                this.canViewRecords =false;
            }
        });
        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }
    findIndex(searchTerm, searchArray?: any[]) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i].id === searchTerm) return (i + 1);
        }
        return -1;
    }
    // add menu group component 
    showAddMG(){
        this.hideAddMG = !this.hideAddMG;
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    handlehideAddMG(event){
        this.hideAddMG = event.gethideAddMG;
        if(this.canViewRecords && event.data){
            this.originalData.push(event.data);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }
    //edit menu group component
    showEdit(ele){
        this.hideEditMG = !this.hideEditMG;
        this.selectedMG = ele;
    }
    handlehideEditMG(event){
        this.hideEditMG = event.gethideEditMG;
        if(this.canViewRecords && event.data){
            this.data[this.findIndex(event.data.id) - 1] = event.data;
            this.originalData[this.findIndex(event.data.id,this.originalData) - 1] = event.data;
        }
    }
    searchEvent() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery != '') {
            this.data = this.data.filter( data => {
                return ((<string>data.name).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            })
        }
        else
        this.initializeData();
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    initializeData() {
        this.data = this.originalData;
    }
    ngOnDestroy(){
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if(this.sub) {
            this.sub.unsubscribe();
        }
        // New Change ****
        if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
    }
}