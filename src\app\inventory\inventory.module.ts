import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTableModule } from 'angular2-datatable';

import { InventoryListComponent } from './+inventoryList/inventoryList.component';
import { AddInventoryComponent } from './+inventoryList/+add/add.inventory.component';
import { EditInventoryComponent } from './+inventoryList/+edit/edit.inventory.component';
import { NgSelectModule } from '@ng-select/ng-select';



const route = [
    { path: '', component: InventoryListComponent, pathMatch: 'full'},
    { path: 'add', component: AddInventoryComponent },
    { path: 'edit/:id', component: EditInventoryComponent}
];

@NgModule({
    imports: [
        RouterModule.forChild(route),
        DataTableModule,
        FormsModule,
        ReactiveFormsModule,
        CommonModule,
        NgSelectModule
    ],
    exports: [],
    declarations: [
        InventoryListComponent,
        AddInventoryComponent,
        EditInventoryComponent
    ],
    providers: [],
})
export class InventoryModule { }
