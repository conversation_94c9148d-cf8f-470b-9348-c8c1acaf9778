import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { DharamshalaService } from './../../../shared/services/dharamshala.service';
import { TranslateService } from '@ngx-translate/core';

declare var Messenger: any;
@Component({
    selector: 'add-agent-location',
    templateUrl: '../agent-location.action.html'
})

export class AddAgentLocationComponent implements OnInit {
    public pageType: string = "Add";
    public agentLocationForm: FormGroup;
    private sub: any;
    @Input() stateList;
    @Output() addToList = new EventEmitter();
    @Output() hideAddEvent = new EventEmitter();
    public select2Options: any = {
        width: '100%'
    };
    constructor(
        private _fb: FormBuilder,
        private DS: DharamshalaService,
        public translate: TranslateService;
    ) { 
        translate.get('AGENT_LOCATION.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.initForm();
    }
    initForm() {
        this.agentLocationForm = this._fb.group({
            name: ['', Validators.required],
            state: ['', [Validators.required, CustomValidators.notEqual("000000")]],
            status: [true, Validators.required]
        })
    }
    saveAgentType() {
        if (this.agentLocationForm.valid) {
            this.sub = this.DS.saveAgentLocation(this.agentLocationForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        this.addToList.emit(res.data);
                        this.agentLocationForm.reset();
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.agentLocationForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            for(let field in this.agentLocationForm.controls) {
                this.agentLocationForm.controls[field].markAsDirty();
                this.agentLocationForm.controls[field].markAsTouched();
            }
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    stateChanged(event: any) {
        let stateName = this.stateList[this.findIndex(event?.id,"id",this.stateList)]?.text;
        this.agentLocationForm.get('state').patchValue(stateName);
        console.log("State : ",this.agentLocationForm.value, stateName);
    }
    findIndex(searchTerm: any, property: any, targetArray: any[]) {
        for(let i = 0; i < targetArray.length; i++) {
            if(targetArray[i][property] === searchTerm) { return i}
        }
        return -1;
    }
    hideComponent() {
        this.hideAddEvent.emit();
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}