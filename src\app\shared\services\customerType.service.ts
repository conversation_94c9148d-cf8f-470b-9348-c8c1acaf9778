import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()
export class CustomerTypeService {

    constructor(private chttp: CommonHttpService) {  }
    
    getAllCustomerType(){
        return this.chttp.get('customer/list');
    }
    getOneCustomerType(id){
        return this.chttp.get(`customer/${id}`)
    }
    saveCustomerType(data){
        return this.chttp.post('customer/add', data, true);
    }
    UpdateCustomerType(id,data){
        return this.chttp.post(`customer/edit/${id}`, data, true);
    }
    // --- API to be implemented --- //
    updateDefaultCustomer(id, data, fieldType: string) {
        return this.chttp.post(`customer/edit/default/${id}?field_type=${fieldType}`,data,true);
    }

    updateBakhiStatus(id, data: any) {
        return this.chttp.post(`customer/edit/bakhistatus/${id}`, data, true);
    }

    updateCustomerReferenceStatus(id, data: any) {
        return this.chttp.post(`customer/edit/referencestatus/${id}`, data, true);
    }

    updateCustomerCommentStatus(id, data: any) {
        return this.chttp.post(`customer/edit/commentstatus/${id}`, data, true);        
    }
}