import { menuList } from './sidebar.data';
import { _secretKey } from './../../shared/globals/config';
import { Component, OnInit, ElementRef, HostListener } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { Location } from '@angular/common';
import { AppConfig } from '../../app.config';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
declare var jQuery: any;
import * as CryptoJS from 'crypto-js';
import * as myGlobals from './globals';
import { BookingService } from 'app/shared/services/booking.service';
import { PaymentRequestService } from 'app/shared/services/payment.service';


@Component({
  selector: '[sidebar]',
  templateUrl: './sidebar.template.html',
  styleUrls: ['./sidebar.component.scss']
})

export class Sidebar implements OnInit {

  menuListJson: any;
  sideMenuHtmlCode: string;
  $el: any;
  menus: any;
  config: any;
  router: Router;
  location: Location;
  defaultMenu: any;
  isSuperAdmin: boolean = false;
  total_request: number = 0;
  total_booking_request: number = 0;
  total_payment_request: number = 0;

  private _secretKey: String = _secretKey;
  public $destroy = new Subject(); // New Change ****
  private langChangeSub: Subscription; // New Change ****
  private closeTimeout: any; // Add timeout reference

  constructor(
    config: AppConfig, 
    el: ElementRef, 
    router: Router, 
    location: Location,
    public booking: BookingService,
    public payment: PaymentRequestService,
    public translate: TranslateService,// New Change ****
    private TS: TranslateEventService, // New Change ****
    ) {
      this.config = config.getConfig();// New Change ****
      let currentLang = localStorage.getItem('currentLang'); // New Change ****
      translate.setDefaultLang(currentLang);// New Change ****

    this.$el = jQuery(el.nativeElement);
    // console.log("This.$el : ",this.$el);
    this.config = config.getConfig();
    this.router = router;
    this.location = location;
    // get default menu
    let defaultMenu = localStorage.getItem('dm');
    let DMdata = CryptoJS.AES.decrypt(defaultMenu, this._secretKey);
    let DMJson = JSON.parse(DMdata.toString(CryptoJS.enc.Utf8));
    this.defaultMenu = DMJson;
    // console.log("Default Menu  : ",this.defaultMenu);
    // get user given menu
    let userMenu = localStorage.getItem('m');
    let UMdata = CryptoJS.AES.decrypt(userMenu, this._secretKey);
    let UMJson = JSON.parse(UMdata.toString(CryptoJS.enc.Utf8));
    this.menus = UMJson
    // fill menu items
    if (this.menus.length > 0) {
      this.menuListJson = this.inputRouterLink(this.menus);
      // Initialize collapse state for each menu item
      this.menuListJson.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.isCollapsed = true;
        }
      });
    } else {
      this.isSuperAdmin = true;
      this.menuListJson = menuList // this means user is super admin
      // Initialize collapse state for admin menu items
      if (this.menuListJson) {
        this.menuListJson.forEach(item => {
          if (item.children && item.children.length > 0) {
            item.isCollapsed = true;
          }
        });
      }
    }
    // New Change ****
    this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
      this.changeLang(res);
    });
  }

  // Add method to close all opened submenus
  closeAllSubmenus(): void {
    if (this.menuListJson) {
      this.menuListJson.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.isCollapsed = true;
        }
      });
    }
    
    // Also reset all DOM elements to ensure proper CSS state
    if (this.$el) {
      // Find all collapse elements and reset them
      const $collapseElements = this.$el.find('.collapse');
      $collapseElements.each((index, element) => {
        const $element = jQuery(element);
        // Remove 'in' class and reset height
        $element.removeClass('in');
        $element.css('height', '0px');
        $element.css('display', 'none');
      });
      
      // Reset all parent menu items
      const $menuItems = this.$el.find('.sidebar-nav li');
      $menuItems.each((index, element) => {
        const $element = jQuery(element);
        $element.removeClass('open');
        
        // Reset toggle icons
        const $toggleIcon = $element.find('.toggle.fa');
        if ($toggleIcon.length) {
          $toggleIcon.removeClass('fa-angle-up').addClass('fa-angle-down');
        }
      });
    }
  }

  // Public method that can be called from layout component
  public closeAllMenus(): void {
    this.closeAllSubmenus();
  }

  // Method to handle menu item clicks properly
  public toggleMenuItem(item: any): void {
    if (item.children && item.children.length > 0) {
      // Toggle the collapse state
      item.isCollapsed = !item.isCollapsed;
      
      // Ensure proper DOM state
      if (this.$el) {
        const $menuItem = this.$el.find(`[data-menu-id="${item.id}"]`).closest('li');
        const $collapse = $menuItem.find('.collapse');
        const $toggleIcon = $menuItem.find('.toggle.fa');
        
        if (item.isCollapsed) {
          // Close the menu
          $collapse.removeClass('in');
          $collapse.css('height', '0px');
          $collapse.css('display', 'none');
          $menuItem.removeClass('open');
          if ($toggleIcon.length) {
            $toggleIcon.removeClass('fa-angle-up').addClass('fa-angle-down');
          }
        } else {
          // Open the menu
          $collapse.addClass('in');
          $collapse.css('height', 'auto');
          $collapse.css('display', 'block');
          $menuItem.addClass('open');
          if ($toggleIcon.length) {
            $toggleIcon.removeClass('fa-angle-down').addClass('fa-angle-up');
          }
          
          // In static mode, close other open menus to maintain clean state
          if (this.isStaticMode()) {
            this.closeOtherMenus(item.id);
          }
        }
      }
    }
  }

  // Method to close other menus when one is opened (for static mode)
  private closeOtherMenus(currentMenuId: string): void {
    if (this.menuListJson) {
      this.menuListJson.forEach(item => {
        if (item.children && item.children.length > 0 && item.id !== currentMenuId) {
          if (!item.isCollapsed) {
            item.isCollapsed = true;
            
            // Update DOM state for this menu
            if (this.$el) {
              const $menuItem = this.$el.find(`[data-menu-id="${item.id}"]`).closest('li');
              const $collapse = $menuItem.find('.collapse');
              const $toggleIcon = $menuItem.find('.toggle.fa');
              
              $collapse.removeClass('in');
              $collapse.css('height', '0px');
              $collapse.css('display', 'none');
              $menuItem.removeClass('open');
              if ($toggleIcon.length) {
                $toggleIcon.removeClass('fa-angle-up').addClass('fa-angle-down');
              }
            }
          }
        }
      });
    }
  }

  // Add HostListener for mouse leave events
  @HostListener('mouseleave')
  onMouseLeave(): void {
    // Only close submenus when mouse leaves if NOT in static mode
    if (!this.isStaticMode()) {
      // Close all submenus when mouse leaves the sidebar with a small delay
      this.closeTimeout = setTimeout(() => {
        this.closeAllSubmenus();
      }, 150); // 150ms delay to allow users to move to submenu items
    }
  }

  // Add HostListener for mouse enter events
  @HostListener('mouseenter')
  onMouseEnter(): void {
    // Cancel the close timeout if user re-enters the sidebar
    if (this.closeTimeout) {
      clearTimeout(this.closeTimeout);
      this.closeTimeout = null;
    }
    
    // Ensure proper DOM state when re-entering (only for non-static mode)
    if (!this.isStaticMode() && this.$el) {
      // Reset any corrupted menu states
      const $menuItems = this.$el.find('.sidebar-nav li');
      $menuItems.each((index, element) => {
        const $element = jQuery(element);
        const $collapse = $element.find('.collapse');
        const $toggleIcon = $element.find('.toggle.fa');
        
        // Ensure collapsed menus are properly hidden
        if ($collapse.length && !$collapse.hasClass('in')) {
          $collapse.css('height', '0px');
          $collapse.css('display', 'none');
          $element.removeClass('open');
          if ($toggleIcon.length) {
            $toggleIcon.removeClass('fa-angle-up').addClass('fa-angle-down');
          }
        }
      });
    }
  }

  // Method to check if sidebar is in static mode
  private isStaticMode(): boolean {
    return jQuery('layout').hasClass('nav-static');
  }

  // Method to handle navigation state changes
  public onNavigationStateChange(): void {
    // Small delay to ensure DOM is updated
    setTimeout(() => {
      if (this.isStaticMode()) {
        // In static mode, ensure all menus are collapsed initially
        this.closeAllSubmenus();
      } else {
        // In non-static mode, ensure proper state
        this.initializeMenuStates();
      }
    }, 100);
  }

  changeLang(lang: string) {
    // New Change ****
    this.translate.use(lang);
  }

  initSidebarScroll(): void {
    let $sidebarContent = this.$el.find('.js-sidebar-content');
    if (this.$el.find('.slimScrollDiv').length !== 0) {
      $sidebarContent.slimscroll({
        destroy: true
      });
    }
    $sidebarContent.slimscroll({
      height: window.innerHeight,
      size: '4px'
    });
  }
  public get _isSuperAdmin() {
    return this.isSuperAdmin;
  }
  changeActiveNavigationItem(location): void {
    if (!this.$el) return;
    this.$el.find('.sidebar-nav .active').removeClass('active');
    let $newActiveLink = this.$el.find('a[href="#' + location.path().split('?')[0] + '"]');

    // Instead of using Bootstrap's collapse, we'll use class toggling
    if (!$newActiveLink.is('.active > .collapse > li > a')) {
      let $activeCollapse = this.$el.find('.active .active').closest('.collapse');
      if ($activeCollapse.length) {
        $activeCollapse.removeClass('in');
        $activeCollapse.css('height', '0px');
      }
    }

    this.$el.find('.sidebar-nav .active').removeClass('active');

    if ($newActiveLink.length) {
      $newActiveLink.closest('li').addClass('active')
        .parents('li').addClass('active');

      // Show parent collapse
      let $collapse = $newActiveLink.closest('.collapse');
      if ($collapse.length) {
        $collapse.addClass('in');
        $collapse.css('height', 'auto');
        $collapse.siblings('a[data-toggle=collapse]').removeClass('collapsed');
      }
    }
  }

  ngAfterViewInit(): void {
    this.changeActiveNavigationItem(this.location);
    // Initialize menu states properly
    this.initializeMenuStates();
  }

  // Method to initialize menu states properly
  private initializeMenuStates(): void {
    // Small delay to ensure DOM is ready
    setTimeout(() => {
      if (this.$el && this.menuListJson) {
        this.menuListJson.forEach(item => {
          if (item.children && item.children.length > 0) {
            // Ensure all menus start collapsed
            item.isCollapsed = true;
            
            // Find corresponding DOM element and ensure proper state
            const $menuItem = this.$el.find(`[data-menu-id="${item.id}"]`).closest('li');
            if ($menuItem.length) {
              const $collapse = $menuItem.find('.collapse');
              const $toggleIcon = $menuItem.find('.toggle.fa');
              
              // Ensure collapsed state
              $collapse.removeClass('in');
              $collapse.css('height', '0px');
              $collapse.css('display', 'none');
              $menuItem.removeClass('open');
              if ($toggleIcon.length) {
                $toggleIcon.removeClass('fa-angle-up').addClass('fa-angle-down');
              }
            }
          }
        });
      }
    }, 200);
  }

  ngOnInit(): void {

    jQuery(window).on('sn:resize', this.initSidebarScroll.bind(this));
    this.initSidebarScroll();

    this.payment.getOnlinePaymentList().subscribe((res: any) => {
      console.log(res, 'payment api call')
      this.total_payment_request = res.data.length;
    }, (err: any)=>{
      console.log(err, 'errrrrr getOnlinePaymentList')
    });

    this.booking.getOnlineBookingList().subscribe((res: any) => {
      console.log(res, 'Booking api call')
      this.total_booking_request = res.data.length;
    }, (err: any)=>{
      console.log(err, 'errrrrr getOnlineBookingList')
    });

    this.total_request = this.total_payment_request + this.total_booking_request

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeActiveNavigationItem(this.location);
        // Reset menu states on navigation
        this.resetMenuStatesOnNavigation();
      }
    });
  }

  // Method to reset menu states when navigation occurs
  private resetMenuStatesOnNavigation(): void {
    // Small delay to ensure DOM is updated
    setTimeout(() => {
      if (this.menuListJson) {
        this.menuListJson.forEach(item => {
          if (item.children && item.children.length > 0) {
            // Only reset if not the active menu
            const isActive = this.isMenuActive(item);
            if (!isActive) {
              item.isCollapsed = true;
            }
          }
        });
      }
    }, 100);
  }

  // Helper method to check if a menu is active
  private isMenuActive(item: any): boolean {
    const currentUrl = this.router.url;
    if (item.routerLink && currentUrl.includes(item.routerLink)) {
      return true;
    }
    if (item.children) {
      return item.children.some((child: any) => {
        return child.routerLink && currentUrl.includes(child.routerLink);
      });
    }
    return false;
  }
  openWindow(url: string) {
    window.open(url,"_blank");
  }

  /**
   * Inserts the indentifier 'routerLink' for corresponding router-id
   * 
   * @param data : Data including Target & Source arrays for extracting/inserting 'routerLink'
   */
  inputRouterLink(menu: any): Promise<any> {
      menu.forEach(element => {
        let ele = element;
        this.defaultMenu.forEach(element => {
          let defaultEle = element;
          if (defaultEle.id == ele.router_link) {
            ele['routerLink'] = element.url;
          }
          if (ele.children) {
            ele.children.forEach(element => {
              let child = element;
              this.defaultMenu.forEach(element => {
                if (element.id == child.router_link) {
                  child['routerLink'] = element.url;
                }
              });
            });
          }
        });
      });
      return menu;
  }
  ngOnDestroy() {
    // Clear any pending timeout
    if (this.closeTimeout) {
      clearTimeout(this.closeTimeout);
    }
    
    this.$destroy.next(); // New Change ****
    this.$destroy.complete(); // New Change ****
    
    // New Change ****
    if (this.langChangeSub)
        this.langChangeSub.unsubscribe();
  }
}
