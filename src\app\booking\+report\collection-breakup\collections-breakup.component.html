<section class="widget  revenue-report">
    <header>
        <div class="row">
            <div class="col-sm-6">
                <h4>
                    <span class="red-header">
                        <i class="fa fa-clipboard"></i>&nbsp;&nbsp;
                        <span class="text-capitalize">
                            {{'COLL_BREAKUP_REPORT.COLL_BREAK_UP' | translate:param}}
                        </span>
                        {{'COLL_BREAKUP_REPORT.REP_MAN' | translate:param}}
                    </span>
                </h4>
            </div>
            <div class="col-sm-2 __download">
                <button class="btn btn-sm btn-primary pull-right" *ngIf="data && data.length > 0" (click)="printRecords()">
                    {{'COLL_BREAKUP_REPORT.PRINT' | translate:param}}
                    <i class="fa fa-print"></i>
                </button>
            </div>
            <!-- <div class="float-sm-right text-right col-sm-4">
                <div class="row">
                </div>
                <div class="form-group __search">
                    <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="search()" placeholder="{{'COLL_BREAKUP_REPORT.SEARCH_TXT' | translate:param}}">
                    <span class="form-group-addon">
                        <i class="fa fa-search"></i>
                    </span>
                    <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()">
                        <i class="fa fa-times"></i>
                    </span>
                </div>
            </div> -->
        </div>
    </header>
    <hr class="large-hr">
    <form [formGroup]="searchForm" (ngSubmit)="searchReports()">
        <!-- Filters -->
        <div class="row filter-row" >
            <div class="col-sm-4" *ngIf="isAdmin">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px;">
                         {{'COLL_BREAKUP_REPORT.USER_NAME' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                       <ng-select [items]="usersList"
                            [ngModelOptions]="{standalone: true}"
                            [(ngModel)]="selectedFilterTypes.user_id" 
                            (change)="userChanged($event)" 
                            bindValue="id" 
                            bindLabel="text"></ng-select>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-5" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px;">
                         {{'COLL_BREAKUP_REPORT.PAY_MODE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-7" style="padding-left: 0px;">
                        <ng-select [items]="paymentType" 
                                 bindValue="id" 
                                 bindLabel="text" 
                                 [(ngModel)]="selectedFilterTypes.payment_mode"
                                 [ngModelOptions]="{standalone: true}"
                                 (change)="paymentModeChanged($event)"></ng-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row filter-row" style="margin-top: 15px;">
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px;">
                            {{'COLL_BREAKUP_REPORT.F_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime [timepicker]="false" formControlName="fromDate"
                            [datepicker]="datepickerOpts" (ngModelChange)="toDataChange($event)"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-4" style="padding-right: 0px;">
                        <label for="" class="absolute " style="font-weight: 600;padding-top: 5px;">
                            {{'COLL_BREAKUP_REPORT.T_DATE' | translate:param}} :
                        </label>
                    </div>
                    <div class="col-sm-8" style="padding-left: 0px;">
                        <datetime  [timepicker]="false" formControlName="toDate"
                            [datepicker]="datepickerOpts"></datetime>
                    </div>
                </div>
            </div>
            <div class="col-sm-4 btn-margin">
                <button type="button" (click)="onReset()" style="margin-left: 5px;"
                    class="btn btn-danger pull-right">{{'COLL_BREAKUP_REPORT.RESET' | translate:param}}</button>
                <button type="submit" class="btn btn-primary pull-right">{{'COLL_BREAKUP_REPORT.SEARCH' | translate:param}}</button>
            </div>
        </div>
        <!-- Amount Summary -->
        <div class="row" *ngIf="data" style="margin-top: 17px;">
            <div class="col-sm-12 col-md-8 col-lg-5">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'COLL_BREAKUP_REPORT.SUMM' | translate:param}}
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr>
                            <td>
                                1) &nbsp; {{'COLL_BREAKUP_REPORT.AGENT_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <span style="color: green;">(+) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{agentAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                2) &nbsp; {{'COLL_BREAKUP_REPORT.ADV_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <span style="color: green;">(+) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{advanceAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                              3) &nbsp; {{'COLL_BREAKUP_REPORT.CHECKOUt_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <span style="color: green;">(+) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{checkinAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                              4) &nbsp;  {{'COLL_BREAKUP_REPORT.PEN_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <span style="color: green;">(+) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{pendingAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                             5) &nbsp; {{'COLL_BREAKUP_REPORT.REF_AMT' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                <span style="color: red;">(-) </span> &nbsp; <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{returnAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                               &nbsp;  <strong>  {{'COLL_BREAKUP_REPORT.TOT_AMT' | translate:param}} </strong>
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong> 
                                    {{totalAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="col-sm-12 col-md-8 col-lg-5">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>
                            {{'COLL_BREAKUP_REPORT.SUMM_MODE' | translate:param}}
                        </strong>
                    </div>
                    <table class="table table-no-mar">
                        <tr>
                            <td>
                                1) &nbsp; {{'COLL_BREAKUP_REPORT.AGENT_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{agentAmtPay | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                2) &nbsp; {{'COLL_BREAKUP_REPORT.CARD_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{cardAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                              3) &nbsp; {{'COLL_BREAKUP_REPORT.CHQ_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{chequeAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                              4) &nbsp;  {{'COLL_BREAKUP_REPORT.BANK_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;">
                               <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{bankAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                             5) &nbsp; {{'COLL_BREAKUP_REPORT.CASH_PAY' | translate:param}}
                            </td>
                            <td style="text-align: right;color: red;">
                               <i class="fa fa-inr"></i>&nbsp;
                                <strong>
                                    {{cashAmt | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                               &nbsp;  <strong>  {{'COLL_BREAKUP_REPORT.TOT_AMT' | translate:param}} </strong>
                            </td>
                            <td style="text-align: right;">
                                 <i class="fa fa-inr"></i>&nbsp;
                                <strong> 
                                    {{totalPaymentAmount | number: '1.2-2'}}
                                </strong>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </form>
    <div class="clearfix"></div>
    <div class="widget-body table-scroll">
        <div class="mt">
            <table class="table with-labels table-condence no-m-b" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
                <thead>
                    <tr>
                        <th>
                            <mfDefaultSorter by="id">#</mfDefaultSorter>
                        </th>
                        <th style="width: 200px;">
                            <!-- <mfDefaultSorter by="name">{{'COLL_BREAKUP_REPORT.NAME' | translate:param}}</mfDefaultSorter> -->
                        </th>
                        <th>
                            {{'COLL_BREAKUP_REPORT.DATE' | translate:param}}
                        </th>
                        <th>
                            <mfDefaultSorter >{{'COLL_BREAKUP_REPORT.GUEST_NAME' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter>{{'COLL_BREAKUP_REPORT.GUEST_TYPE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter>{{'COLL_BREAKUP_REPORT.PAY_MODE' | translate:param}}</mfDefaultSorter>
                        </th>
                        <th>
                            <mfDefaultSorter >{{'COLL_BREAKUP_REPORT.AMOUNT' | translate:param}}</mfDefaultSorter>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <ng-template ngFor let-ds [ngForOf]="mf.data" let-i="index">
                        <tr (click)="ds.checked = !ds.checked" class="reference-level">
                            <td class="fw-semi-bold">{{ i + 1 }}</td>
                            <td class="uppercase" colspan="8">
                                <span class="fw-semi-bold">{{ds.name}} Payment</span>
                            </td>
                        </tr>
                        <ng-template [ngIf]="ds.checked">
                         <ng-template ngFor let-gts [ngForOf]="ds.data" let-j="index" > 
                            <tr  class="guest-type-level">
                                <td>{{j + 1}}</td>
                                <td>-</td>
                                <td>{{gts.payment_date | date}}</td>
                                <td>{{gts.name | uppercase}}</td>
                                <td>{{gts.guest_type}}</td>
                                <td style="text-align: left;">
                                    {{gts.payment_mode_name}}
                                </td>
                                <td style="text-align: right;padding-right: 20px;">
                                    <span *ngIf></span>
                                    {{(gts.card_charge_amount > 0 ?  gts.amount + gts.card_charge_amount : gts.amount) | number:'1.2-2' }}
                                </td>
                            </tr>
                         </ng-template>
                         </ng-template>
                    </ng-template>
                    <tr *ngIf="canViewRecords && mf.data.length === 0">
                        <td colspan="100">
                           {{'COLL_BREAKUP_REPORT.NO MATCHES' | translate:param}}
                        </td>
                    </tr>
                    <tr *ngIf="!canViewRecords">
                        <td class="text-danger" colspan="100">
                            {{'COLL_BREAKUP_REPORT.PERMISSION_DENIED' | translate:param}}
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="12">
                            <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</section>