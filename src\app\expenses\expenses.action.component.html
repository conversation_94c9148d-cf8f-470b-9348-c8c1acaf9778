<section class="widget">
    <header>
        <h4><span class="capitalized"><i class="fa fa-calendar-times-o"></i>&nbsp;&nbsp;{{pageType}} {{ 'EXPENSES.ADD_PAGE.EXPENSES' | translate:param }}</span></h4>
    </header>
    <div class="clearfix"></div>
    <hr>
    <div class="widget-body">
        <div class="mt">
            <fieldset>
                <form [formGroup]="expensesForm" (ngSubmit)="addExpense()">

                    <div class="form-group row">
                        <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'EXPENSES.ADD_PAGE.AMOUNT' | translate:param }}</label>
                        <div class="col-md-8 ">
                            <span class="errMsg __fromBackend" *ngIf="expensesForm.controls.amount.errors?.backend">{{expensesForm.controls.amount.errors?.backend}}</span>
                            <input type="text" class="form-control" formControlName="amount" placeholder="">
                            <span class="errMsg" *ngIf="!expensesForm.controls.amount.valid && !expensesForm.controls.amount.pristine">
                                <span [hidden]="!expensesForm.controls.amount.errors.required">{{ 'EXPENSES.ADD_PAGE.VALID_MSG.AMT_REQ' | translate:param }}</span>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{ 'EXPENSES.ADD_PAGE.NOTE' | translate:param }}</label>
                        <div class="col-md-8 ">
                            <span class="errMsg __fromBackend" *ngIf="expensesForm.controls.note.errors?.backend">{{expensesForm.controls.note.errors?.backend}}</span>
                            <textarea type="text" class="form-control" formControlName="note" placeholder=""></textarea>
                            <span class="errMsg" *ngIf="!expensesForm.controls.note.valid && !expensesForm.controls.note.pristine">
                                <span [hidden]="!expensesForm.controls.note.errors.required">{{ 'EXPENSES.ADD_PAGE.VALID_MSG.NOT_REQ' | translate:param }}</span>
                            </span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-md-8 offset-md-3">
                            <div class="">
                                <button type="submit" [disabled]="!expensesForm.valid" class="btn btn-sm btn-inverse capitalized"><i
                                        class="fa fa-check"></i>{{ 'EXPENSES.ADD_PAGE.SAVE' | translate:param }}</button>
                                <button type="button" (click)="closeThisComp()" class="btn btn-sm btn-secondary">{{ 'EXPENSES.ADD_PAGE.CLOSE' | translate:param }}</button>
                            </div>
                        </div>
                    </div>
                </form>
            </fieldset>
        </div>
    </div>
</section>