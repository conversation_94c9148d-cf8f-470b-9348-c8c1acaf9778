<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-user"></i>&nbsp;&nbsp;{{pageType}} {{'DIS_REF_USER.ADD_PAGE.REF_USER' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="clearfix"></div>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="referenceUserForm" (ngSubmit)="saveReferenceUser()">
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DIS_REF_USER.ADD_PAGE.CUST_TYPE' | translate:param}}</label>
          <div class="col-md-8" *ngIf="pageType !== 'Edit'">
            <span class="errMsg __fromBackend" *ngIf="referenceUserForm.controls.user_id.errors?.backend">{{referenceUserForm.controls.user_id.errors?.backend}}</span>
            <ng-select [items]="userList"
                      [(ngModel)]="user"
                      [formControlName]="'user_id'"
                      [bindLabel]="'text'"
                      [bindValue]="'id'"
                      [clearable]="false"
                      [disabled]="pageType === 'Edit'"
                      (change)="changeUser($event)">
            </ng-select>
            <span class="errMsg" *ngIf="!referenceUserForm.controls.user_id.valid && !referenceUserForm.controls.user_id.pristine">
              <span [hidden]="!referenceUserForm.controls.user_id.errors.required">{{'DIS_REF_USER.ADD_PAGE.VALID_MSG.PER_REQ' | translate:param}}</span>
            </span>
          </div>
          <div class="col-md-8" *ngIf="pageType == 'Edit'">
            <input type="text" class="form-control" [(ngModel)]="user" [ngModelOptions]="{standalone: true}" [disabled]="true" placeholder="">
          </div>
        </div>
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'DIS_REF_USER.ADD_PAGE.REF_USER' | translate:param}}</label>
          <div class="col-md-8">
            <span class="errMsg __fromBackend" *ngIf="referenceUserForm.controls.reference_user_ids.errors?.backend">{{referenceUserForm.controls.reference_user_ids.errors?.backend}}</span>
            <ng-select [items]="referenceList"
                      bindLabel="text"
                      bindValue="id"
                      [formControlName]="reference_user_ids"
                      (change)="customerTypeChanged($event)"
                      [clearable]="true">
            </ng-select>
            <span class="errMsg" *ngIf="!referenceUserForm.controls.reference_user_ids.valid && !referenceUserForm.controls.reference_user_ids.pristine">
              <span [hidden]="!referenceUserForm.controls.reference_user_ids.errors.required">{{'DIS_REF_USER.ADD_PAGE.VALID_MSG.REF_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label for="guestPer"  class="col-md-3  col-form-label text-md-right">{{'DIS_REF_USER.ADD_PAGE.GUEST_TYPE_CHANGE' | translate:param}}</label>
          <div class="col-md-8">
          <input type="checkbox" #guestPer id="guestPer" formControlName="allow_to_update_customer_type" >
         </div>
        </div>

        <div class="form-group row">
          <label for="refPer" class="col-md-3  col-form-label text-md-right">{{'DIS_REF_USER.ADD_PAGE.REF_CHANGE' | translate:param}}</label>
          <div class="col-md-8">
          <input type="checkbox" #refPer id="refPer" formControlName="allow_to_update_reference_user" >
         </div>
        </div>

        <div class="form-group row">
          <div class="col-md-8  offset-md-3">
            <div class="">
              <button type="submit" [disabled]="!referenceUserForm.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'DIS_REF_USER.ADD_PAGE.SAVE' | translate:param}}</button>
              <button type="button" (click)="toggleChild()" class="btn btn-sm btn-secondary">{{'DIS_REF_USER.ADD_PAGE.CLOSE' | translate:param}}</button>
            </div>
          </div>
        </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
