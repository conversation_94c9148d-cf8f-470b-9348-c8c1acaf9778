import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { FormControl, FormGroup, FormBuilder, FormArray, Validators } from '@angular/forms';

import { FileUploader, FileUploaderOptions } from 'ng2-file-upload';

import { _secret<PERSON><PERSON>, _secret<PERSON>ey_auth } from './../../../shared/globals/config';
import { RoomCategoryService } from './../../../shared/services/roomCategory.service';
import { AuthGuard } from './../../../shared/guards/auth-guard.service';
import { apiUrl } from './../../../api-env';

import * as CryptoJS from 'crypto-js';
import { TranslateService } from '@ngx-translate/core';
declare var jQuery: any;
declare var Messenger: any;
const URL = apiUrl + 'roomcategory/add/image';

@Component({
    selector: 'add-room-category',
    templateUrl: '../roomCategory.actions.component.html',
    styleUrls: ['./add.roomCategory.component.scss']
})
export class AddRoomCategoryComponent implements OnInit {
    public uploader: FileUploader = new FileUploader({ url: URL });
    public uploadedImages: any[] = [];
    select2Options: any = {
        width: '100%'
    };
    pageName: string = "Add";
    // service variable
    private sub: any;
    private saveRC: any;
    // input / outputs
    @Input() gethiddenAddRC;
    @Output() sendhiddenAddRC = new EventEmitter();

    public eminityChargevalue: number;
    public amenities: any;
    public customerTypeData: any;
    public roomCategory: FormGroup;
    public aminityValue: any;
    public animityList: any[] = [];
    public dharmashalaList: any[] = [];
    public discountTypeInput: any;
    public selectedDharamshala: any;
    public commonRoomCategory: any;
    constructor(
        private RCS: RoomCategoryService,
        private _fb: FormBuilder,
        private authGuard: AuthGuard,
        public translate: TranslateService
    ) {
        // get all dropdown lists and othre usefull data before form init.
        this.sub = this.RCS.getInitForAdd()
            .subscribe((res) => {
                if (res.status = "success") {
                    this.amenities = res.data.amenities;
                    this.customerTypeData = res.data.customers;
                    this.dharmashalaList = res.data.dharamshalas;
                    this.selectedDharamshala = this.dharmashalaList[0].id;
                    // setting customre type (tariff) in form array
                    this.setCustomerType();
                }
            })
            this.buildForm();
            translate.get('ROOM.ROOM_CAT.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
                console.log(res);
                this.pageName = res;
                //=> 'hello world'
            });
    }

    /**
     * setting tariff (customer type) values to form
     * pushing form group as per recieved array
     */
    setCustomerType() {
        let control = <FormArray>this.roomCategory.controls['tariff'];
        this.customerTypeData.forEach(element => {
            control.push(this._fb.group({
                customer_id: [(element.id), Validators.required],
                discount_type: [element.discount_type, [Validators.required]],
                customer_name: [(element.name), Validators.required],
                percentage: [((element.discount_type == "percentage") ? element.discount_value : ''), CustomValidators.number],
                amount: [((element.discount_type == "amount") ? element.discount_value : '0'), CustomValidators.number],
                extra_bed_charge: ['0', [Validators.required, CustomValidators.number]],
                extra_adult_charge: ['0', [Validators.required, CustomValidators.number]],
                extra_child_charge: ['0', [Validators.required, CustomValidators.number]]
            }))
        });
    }
    /**
     * build form for add room category
     * 
     */
    buildForm() {
        this.roomCategory = this._fb.group({
            // dharamshala_id: ['', Validators.required],
            name: ['', Validators.required],
            total_room: ['0', [Validators.required, CustomValidators.digits]],
            charges: ['0', [Validators.required, CustomValidators.number]],
            default_online_quota: ['0', [Validators.required, CustomValidators.digits]],
            std_occupancy: ['0', [Validators.required, CustomValidators.digits]],
            // std_child_occupancy: ['', [Validators.required, CustomValidators.digits]],
            extra_adult_charges: ['0', [Validators.required, CustomValidators.digits]],
            extra_child_charges: ['0', [Validators.required, CustomValidators.digits]],
            max_occupancy: ['0', [Validators.required, CustomValidators.digits]],
            description: [''],
            amenities: this._fb.array([]),
            uploadedImages: this._fb.array([]),
            tariff: this._fb.array([]),
            release_quota_before: ['', [Validators.required, CustomValidators.digits]],
            status: [true, Validators.required],
            is_common_room_category: [false]
        });

    }
    /**
     * this will add selected eminity and charges to aminity list
     * @param {any} eminityCharge : aminity charge (input element from local variable)
     */
    addAminity(eminityCharge) {
        if (!isNaN(eminityCharge.value)) {
            this.addAminitControl(this.aminityValue, this.amenities[this.findIndex('id', this.aminityValue)].name, eminityCharge.value);
        }
    }
    /**
     * this will remove aminity from aminity list at given index
     * @param {any} index : number
     */
    removeAminity(index) {
        let control = <FormArray>this.roomCategory.controls['amenities'];
        control.removeAt(index);
    }
    /**
     * Aminity control to add form control to aminity list (When form submitting)
     * @param {any} id : number - id of aminity (hidden)
     * @param {any} name : string - name of the aminity
     * @param {any} charge : number - charge of aminity
     */
    addAminitControl(id, name, charge) {
        let letmeaddyou: boolean = true;
        let control = <FormArray>this.roomCategory.controls['amenities'];
        let group = this._fb.group({
            amenity_id: [id],
            title: [name],
            charge: [charge]
        }) // create form group
        control.controls.forEach((element: FormGroup) => {
            if (element.controls['amenity_id'].value == id) {
                letmeaddyou = false;
            }
        });
        // if there is not element with the same name.
        if (letmeaddyou) {
            control.push(group);
        }
    }
    /**
     * when image has been uploaded but not saved, images will be list out to form control
     * @param {any} res : response - response from image upload api
     */
    addUploadedImages(res) {
        let control = <FormArray>this.roomCategory.controls['uploadedImages'];
        control.push(
            this._fb.group({
                mimetype: [res.data.mimetype],
                originalName: [res.data.originalName],
                size: [res.data.size],
                uploaded: [res.data.uploaded],
                is_deleted: [res.data.is_deleted]
            })
        );
    }
    ngOnInit() {
        
        this.uploader.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
            //things to do on completion
            if (response) {
                let res = JSON.parse(response);
                this.addUploadedImages(res);
            }
        };
        this.uploader.onBeforeUploadItem = (item: any) => {
            // image uploaded - add token of auth
            let token = this.authGuard.ud.session_id;
            let timestamp = (+ new Date()).toString();
            let generatedToken = CryptoJS.AES.encrypt(token,
                _secretKey_auth);
            this.uploader.authToken = generatedToken;
        }
    }
    /**
     * this will change form control value of discount_type but emitted value of directive used 
     * when enable/disable input keypress
     * @param {any} event : value emmited from directive
     */
    handleDisType(event) {
        let arr = this.roomCategory.get('tariff') as FormArray;
        arr.controls[event.id].get('discount_type').patchValue(event.status);
        arr.controls[event.id].get(event.input).patchValue('');
    }
    setRoomCategoryCommon() {
        let category = <FormControl>this.roomCategory.controls['is_common_room_category'];
        let value = category.value;
        category.patchValue(!value);
        this.commonRoomCategory = !value;
        if(!value) {
            Messenger().post({
                message: "Common room category selected!",
                status: "success",
                showCloseButton: true
            })
        }
    }
    // aminity dropdown initialization
    getSelect2DefaultList() {
        return jQuery.map(this.amenities, function (obj) {
            return { id: obj.id, text: obj.name };
        })
    }
    // dharamshala dropdown initialization if there is any
    getDharamshalaList() {
        if(this.dharmashalaList.length > 0){
            return jQuery.map(this.dharmashalaList, function (obj) {
                return { id: obj.id, text: obj.name };
            })
        }
        return [];
    }
    // dharmashala list dropdown change event
    // dharamshalaListchange(event) {
    //     setTimeout(()=>{
    //         this.roomCategory.controls['dharamshala_id'].patchValue(event.value);
    //     });
    // }
    // aminity dropdown list change event
    select2Changed(event) {
        if (event) {
            this.aminityValue = event.id;
            this.eminityChargevalue = this.amenities[this.findIndex('id', event.id)].charge;
        }
    }
    findIndex(params, searchTerm) {
        for (var i = 0, len = this.amenities.length; i < len; i++) {
            if (this.amenities[i][params] === parseInt(searchTerm)) return i;
        }
        return -1;
    }
    // toggle between parent and child
    toggleChild(data) {
        let result;
        this.gethiddenAddRC = !this.gethiddenAddRC;
        if (data) {
            // send data for to add to list
            result = { gethiddenAddRC: this.gethiddenAddRC, data: data }
        } else {
            result = { gethiddenAddRC: this.gethiddenAddRC }
        }
        this.sendhiddenAddRC.emit(result);
    }
    //save
    saveRoomCategory() {
        if (this.roomCategory.valid) {
            this.saveRC = this.RCS.saveRoomCategory(this.roomCategory.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        if (res.data) {
                            this.roomCategory.reset();
                            Messenger().post({  hideAfter: 5,
                                message: res.message,
                                type: res.status,
                                showCloseButton: true
                            });
                            this.toggleChild(res.data);// send added result
                        }
                    } 
                }, (err) => {
                   let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.roomCategory.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        }else{
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
        if (this.saveRC) {
            this.saveRC.unsubscribe();
        }
    }
}