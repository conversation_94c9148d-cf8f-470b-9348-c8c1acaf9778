import { <PERSON><PERSON><PERSON><PERSON>, Valida<PERSON>, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
import * as moment from "moment";
import { BookingService } from "../../../shared/services/booking.service";

@Component({
    selector: 'view-booking',
    templateUrl: './view.bookingList.component.html',
    styleUrls: ['../booking.list.component.scss']
})

export class ViewBookingListComponent implements OnInit {
    config: any;// New Change ****
    private sub: any;
    private saveAlternate_BG: any;

    public canAdd: boolean;
    public showForm: boolean;
    private alternateBG_Form: FormGroup;
    id: number;

    @Input() data: any;
    @Output() goBack = new EventEmitter();
    public bookingType: any = [
        {
            id: '0',
            text: 'Single Booking'
        },
        {
            id: '1',
            text: 'Group Booking'
        }
    ]
    public sourceType: any = [
        {
            id: '0',
            text: 'On the spot'
        },
        {
            id: '1',
            text: 'Telephonic'
        }
    ]
    public paymentType: any = [
        {
            id: '0',
            text: 'Cash Payment'
        },
        {
            id: '1',
            text: 'Card Payment'
        }
    ]
    public proofTypeList: any = [
        {
            id: '0',
            text: 'Driving Licence'
        },
        {
            id: '1',
            text: 'Adhar Card'
        },
        {
            id: '2',
            text: 'Pan Card'
        },
        {
            id: '3',
            text: 'Election Card'
        }
    ]
    public selectedBooking: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private _fb: FormBuilder,
        private BS: BookingService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    ngOnInit() {
        this.selectedBooking = this.data;
        this.updateCanAdd();
        console.log("this.selectedBooking : ",this.selectedBooking);
        if ((this.selectedBooking.detail[0].check_in && (<string>this.selectedBooking.detail[0].check_in).search(':') < 0) ||
            (this.selectedBooking.detail[0].check_out && (<string>this.selectedBooking.detail[0].check_out).search(':') < 0)) {
            for(let i = 0; i < this.selectedBooking.detail.length; i++) {
                this.selectedBooking.detail[i].check_in, this.selectedBooking.detail[i].check = this.formatDates(this.selectedBooking.detail[i]);
            }
        }
    }

    initBG_Form(selectedBooking?: any) {
        this.alternateBG_Form = this._fb.group({
            name: [(selectedBooking ? selectedBooking.alternate_billing_guest : ''), Validators.required],
            status: [(selectedBooking ? selectedBooking.is_alternate_bg_active : true), Validators.required]
        });
    }

    updateCanAdd() {
        this.canAdd = this.selectedBooking.alternate_billing_guest ? false : true;
    }

    addAlternateBG() {
        this.initBG_Form();
        this.showForm = true;
    }

    editAlternateBG() {
        this.initBG_Form(this.selectedBooking);
        this.showForm = true;
    }

    saveAlternateBG() {
        if(this.alternateBG_Form.valid) {
            console.log("",this.alternateBG_Form.value);
            this.saveAlternate_BG = this.BS.addAlternateBillingGuest(this.selectedBooking.id,this.alternateBG_Form.value)
            .subscribe( res => {
                if(res.status == "success") {
                    this.selectedBooking.is_alternate_bg_active = this.alternateBG_Form.value.status;
                    this.selectedBooking.alternate_billing_guest = this.alternateBG_Form.value.name;
                    this.alternateBG_Form.reset();
                    this.updateCanAdd();
                    this.showForm = false;
                }
            });
        }
        else {
            for(let field in this.alternateBG_Form.controls) {
                this.alternateBG_Form.controls[field].markAsTouched();
                this.alternateBG_Form.controls[field].markAsDirty();
            }
        }
    }

    hideAlternateBG() {
        this.alternateBG_Form.reset();
        this.showForm = false;
    }

    formatCheckoutDate(checkout) {
        return moment(checkout).add(1,'days');
    }
    formatDates(selectedBooking: any) {
        let In = selectedBooking.check_in ? new Date(+selectedBooking.check_in) : null;
        let Out = selectedBooking.check_out ? new Date(+selectedBooking.check_out) : null;
        selectedBooking.check_in = In ? (In.getHours() + ':' + In.getMinutes()) : null;
        selectedBooking.check_out = Out ? (Out.getHours() + ':' + Out.getMinutes()) : null;
        return selectedBooking.check_in,selectedBooking.check_out;
    }
    gobacksimon(){
        this.goBack.emit(true);
        this.selectedBooking = "";
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
        
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}