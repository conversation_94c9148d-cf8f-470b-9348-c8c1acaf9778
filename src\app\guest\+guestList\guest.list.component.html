<section class="widget">
  <header>
    <h4><span class=""><i class="fa fa-hotel"></i>&nbsp;&nbsp;{{'GUEST_LIST.CUST_LIST_MANG' | translate:param }}</span></h4>
  </header>
  <hr class="large-hr">
  <!--<ol class="breadcrumb float-sm-left">
    <li class="breadcrumb-item">Role Management</li>
    <li class="breadcrumb-item active">Role Permission</li>
  </ol>-->
  <div class="float-sm-right">
<!--
    <a [routerLink]="['add']" class="display-inline-block btn btn-sm btn-inverse"><i class="fa fa-plus"></i>&nbsp;&nbsp;Add</a>-->
    <div class="form-group display-inline-block __search">
      <input type="text" class="form-control" [(ngModel)]="searchQuery" (keyup)="canViewRecords ? search() : null" placeholder="{{'GUEST_LIST.SEARCH' | translate:param }}">
      <span class="form-group-addon"><i class="fa fa-search"></i></span>
      <span *ngIf="searchQuery" class="form-group-addon right" (click)="clearSearch()"><i class="fa fa-times"></i></span>
    </div>
  </div>
  <div class="clearfix"></div>
  <div class="widget-body table-scroll">
    <div class="mt">

      <table class="table table-condence no-m-b small-footprint" [mfData]="data" #mf="mfDataTable" [mfRowsOnPage]="10">
        <thead>
          <tr>
            <th>
              <mfDefaultSorter by="id">#</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="name">{{'GUEST_LIST.FULL_NAME' | translate:param }}</mfDefaultSorter>
            </th>
           
            <th class=" ">
              <mfDefaultSorter by="">{{'GUEST_LIST.CONT' | translate:param }}</mfDefaultSorter>
            </th>
            <th>
              <mfDefaultSorter by="email">{{'GUEST_LIST.EMAILID' | translate:param }}</mfDefaultSorter>
            </th>
            <th *ngIf="auth.roleAccessPermission('guest','view')" class="no-sort" style="width: 150px">
              <mfDefaultSorter by="action">{{'GUEST_LIST.ACTION' | translate:param }}</mfDefaultSorter>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let guest of mf.data">
            <td>{{findIndex(guest.id,"id")}}</td>
            <td><span class="uppercase fw-semi-bold">{{guest.name}}</span></td>
            
            <td>{{guest.contact}}</td>
           <td>{{guest.email}}</td>
            <td *ngIf="auth.roleAccessPermission('guest','view')">
              <div class="btn-toolbar">
                <a (click)="showGuestDetails(guest.id)" class="btn btn-sm btn-default" tooltip="{{'GUEST_LIST.VIEW_CUST_DETAI' | translate:param }}" placement="top"><i class="fa fa-question"></i>&nbsp;&nbsp;{{'GUEST_LIST.VIEW_CUST_DETAI' | translate:param }}</a>
                <!-- <a href="javascript:void(0)">Booking</a> -->
              </div>
            </td>
          </tr>
          <tr *ngIf="canViewRecords && mf.data.length === 0">
            <td colspan="100">
              {{'GUEST_LIST.NO MATCHES' | translate:param }}
            </td>
          </tr>
          <tr *ngIf="!canViewRecords">
            <td class="text-danger" colspan="100">
              {{'GUEST_LIST.PERMISSION_DENIED' | translate:param }}
            </td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="12">
              <mfBootstrapPaginator [rowsOnPageSet]="[10, 25, 50, 100]"></mfBootstrapPaginator>
            </td>
          </tr>
        </tfoot>
      </table>


    </div>
  </div>
</section>
<div class="modal fade" bsModal #staticModal="bs-modal" [config]="{backdrop: 'static'}"
     tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="pull-left">{{'GUEST_LIST.CUST_DETAILS' | translate:param }}</h4>
        <button type="button" class="close pull-right" aria-label="Close" (click)="staticModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
        <div class="clearfix"></div>
      </div>
      <div class="modal-body">
        <table class="table table-condence table-strong-right table-no-borders">
          <tr>
            <td><strong>{{'GUEST_LIST.NAME' | translate:param }} :</strong></td>
            <td class="capitalized">{{selectedGuest?.name}}</td>
          </tr>
          <tr>
            <td><strong>{{'GUEST_LIST.GENDER' | translate:param }} :</strong></td>
            <td>
              <span class="capitalize" *ngIf="selectedGuest?.gender">{{selectedGuest?.gender}}</span>
              <!-- <span *ngIf="selectedGuest?.gender == 'female'">Female</span> -->
            </td>
          </tr>
         
          <tr>
            <td><strong>{{'GUEST_LIST.EMAILID' | translate:param }} :</strong></td>
            <td>{{selectedGuest?.email}}</td>
          </tr>
          <tr>
            <td><strong>{{'GUEST_LIST.CONC_NO' | translate:param }} :</strong></td>
            <td>{{selectedGuest?.contact}}</td>
          </tr>
          <tr>
            <td><strong>{{'GUEST_LIST.ADDRESS' | translate:param }} :</strong></td>
            <td>{{selectedGuest?.address}}</td>
          </tr>
          <tr>
            <td><strong>{{'GUEST_LIST.ZIP_POSTAL' | translate:param }} :</strong></td>
            <td>{{selectedGuest?.zip}}</td>
          </tr>
          <tr>
            <td><strong>{{'GUEST_LIST.CITY' | translate:param }} :</strong></td>
            <td>{{selectedGuest?.city}}</td>
          </tr>
          
        </table>
        </div>
    </div>
  </div>
</div>
