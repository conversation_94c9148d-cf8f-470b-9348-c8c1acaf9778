import { Component, On<PERSON>estroy, ViewEncapsulation, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MenuGroupService } from './../../../shared/services/menuGroup.service';
import { Menu, menuList } from './../../../layout/sidebar/sidebar.data';
import { TranslateService } from '@ngx-translate/core';
declare var Messenger: any;
@Component({
    selector: 'add-menu-group',
    templateUrl: '../menuManagement.action.html',
    encapsulation: ViewEncapsulation.None
})
export class AddMenuGroupComponent {
    pageType: string = "Add";
    menuAdd: FormGroup;
    // Inputs/Outputs
    @Input() gethideAddMG;
    @Output() sendhideAddMG = new EventEmitter();

    // service variables
    private sub: any;

    name: any;
    status: any;

    constructor(
        private _fb: FormBuilder,
        private MG: MenuGroupService,
        public translate: TranslateService
    ) {
        this.buidForm();
        // console.log("menu list : ",menuList);
        translate.get('MENU_MANAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageType = res;
            //=> 'hello world'
        });
    }
    buidForm() {
        this.menuAdd = this._fb.group({
            name: ['', Validators.required],
            status: [true, Validators.required]
        });
        this.name = this.menuAdd.controls['name'];
        this.status = this.menuAdd.controls['status'];
    }
    addMenu() {
        // console.log(this.menuAdd.value);
        if (this.menuAdd.valid) {
            this.menuAdd.value['menu'] = menuList;
            // console.log("menu list : ",JSON.stringify(menuList));
            this.sub = this.MG.addMenuGroup(this.menuAdd.value).subscribe((res) => {
                if (res.status === 'success') {
                    this.menuAdd.reset();
                    this.toggleChild(res.data)
                } 
            },
                (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.menuAdd.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        }else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    toggleChild(data) {
        let result;
        this.gethideAddMG = !this.gethideAddMG;
        if (data) {
            result = { gethideAddMG: this.gethideAddMG, data: data }
        } else {
            result = { gethideAddMG: this.gethideAddMG }
        }
        this.sendhideAddMG.emit(result);
    }
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}