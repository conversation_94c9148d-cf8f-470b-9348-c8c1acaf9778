<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-star"></i>&nbsp;&nbsp;{{pageType}} {{'CUSTOMER_TYPE.ADD_PAGE.CUST_TYPE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item "><a (click)="toggleChild()" href="javascript:void(0)">{{'CUSTOMER_TYPE.ADD_PAGE.CUST_TYPE' | translate:param}}</a></li>
    <li class="breadcrumb-item active">{{pageType}} {{'CUSTOMER_TYPE.ADD_PAGE.CUST_TYPE' | translate:param}}</li>
  </ol>

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="customerTypeAdd" (ngSubmit)="saveCustomerType()">

          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'CUSTOMER_TYPE.ADD_PAGE.TYPE_NAME' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="customerTypeAdd.controls.name.errors?.backend">{{customerTypeAdd.controls.name.errors?.backend}}</span>
              <input type="text" id="normal-field" class="form-control" formControlName="name" placeholder="">
              <span class="errMsg" *ngIf="!customerTypeAdd.controls.name.valid && !customerTypeAdd.controls.name.pristine">
                <span [hidden]="!customerTypeAdd.controls.name.errors.required">{{'CUSTOMER_TYPE.ADD_PAGE.VALID_MSG.CUST_NAME_REQ' | translate:param}}</span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'CUSTOMER_TYPE.ADD_PAGE.DISC_TYPE' | translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio" *ngFor="let d of discountTypeRadio; let i = index">
                  <input type="radio" formControlName="discount_type" (change)="resrValue()" id="radio-a{{i}}" [value]="d">
                  <label for="radio-a{{i}}" class="capitalize">
                        {{d}}
                      </label>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row" *ngIf="customerTypeAdd.controls.discount_type.value != 'none'">
            <label class="col-md-3  col-form-label text-md-right">{{'CUSTOMER_TYPE.ADD_PAGE.DISCOUNT' | translate:param}}</label>
            <div class="col-md-8 ">
               <span class="errMsg __fromBackend" *ngIf="customerTypeAdd.controls.discount_type.errors?.backend">{{customerTypeAdd.controls.discount_type.errors?.backend}}</span>
              <div class="input-group" [ngClass]="{'has-error': (!customerTypeAdd.controls.discount_value.valid && !customerTypeAdd.controls.discount_value.pristine)}">
                <span class="input-group-addon"><i class="fa fa-inr" [ngClass]="{'fa-inr': (customerTypeAdd.controls.discount_type.value == 'amount'), 'fa-percent' : (customerTypeAdd.controls.discount_type.value == 'percentage')}"></i></span>
              <input type="text" class="form-control" formControlName="discount_value" (change)="changeDiscountValue()" placeholder="">
              </div>
               <span class="errMsg" *ngIf="!customerTypeAdd.controls.discount_value.valid && !customerTypeAdd.controls.discount_value.pristine">
                <span [hidden]="!customerTypeAdd.controls.discount_value.errors.number">{{'CUSTOMER_TYPE.ADD_PAGE.VALID_MSG.ONLY_DIGIT_REQUIRED' | translate:param}}</span>
                <span [hidden]="isPercentageValid">{{'CUSTOMER_TYPE.ADD_PAGE.VALID_MSG.INVALID_VAL' | translate:param}}</span>
                  </span>
            </div>
          </div>
          <div class="form-group row">
            <label for="" class="col-md-3 col-form-label text-md-right">{{'CUSTOMER_TYPE.ADD_PAGE.EXT_BAD_CHAR' | translate:param}}</label>
            <div class="col-md-8">
               <span class="errMsg __fromBackend" *ngIf="customerTypeAdd.controls.extra_bed_charge.errors?.backend">{{customerTypeAdd.controls.extra_bed_charge.errors?.backend}}</span>
                  <div class="input-group" [ngClass]="{'has-error': (!customerTypeAdd.controls.extra_bed_charge.valid && !customerTypeAdd.controls.extra_bed_charge.pristine) || customerTypeAdd.controls.extra_bed_charge.errors?.backend }">
                <span class="input-group-addon"><i class="fa fa-inr"></i></span>
                  <input type="text" class="form-control" formControlName="extra_bed_charge" placeholder="">
                  </div>
                  <span class="errMsg" *ngIf="!customerTypeAdd.controls.extra_bed_charge.valid && !customerTypeAdd.controls.extra_bed_charge.pristine">
                <span [hidden]="!customerTypeAdd.controls.extra_bed_charge.errors.required">{{'CUSTOMER_TYPE.ADD_PAGE.VALID_MSG.EXT_BAD_CHAR_REQ' | translate:param}}</span>
                <span [hidden]="!customerTypeAdd.controls.extra_bed_charge.errors.number">{{'CUSTOMER_TYPE.ADD_PAGE.VALID_MSG.CURR_REQ' | translate:param}}</span>
                  </span>
                
            </div>
          </div>
          <div class="form-group row">
            <label class="col-md-3 form-label text-md-right" for="default-select">{{'CUSTOMER_TYPE.ADD_PAGE.STATUS' | translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio-1" [value]="true">
                  <label for="radio-1">
                      {{'CUSTOMER_TYPE.ADD_PAGE.ACTIVE' | translate:param}}
                      </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio-2" [value]="false">
                  <label for="radio-2">
                      {{'CUSTOMER_TYPE.ADD_PAGE.INACTIVE' | translate:param}}
                      </label>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" [disabled]="!customerTypeAdd.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'CUSTOMER_TYPE.ADD_PAGE.SAVE' | translate:param}}</button>
                <button (click)="toggleChild()" class="btn btn-sm btn-secondary">{{'CUSTOMER_TYPE.ADD_PAGE.CANCEL' | translate:param}}</button>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
      <!--<pre>
        {{ customerTypeAdd.value | json}}
      </pre>-->
    </div>
  </div>
</section>
