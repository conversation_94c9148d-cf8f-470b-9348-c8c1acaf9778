import { BookingService } from './../../shared/services/booking.service';
import { Component, OnInit } from '@angular/core';
import { AuthGuard } from "../../shared/guards/auth-guard.service";
import { TranslateService } from '@ngx-translate/core';
import { AppConfig } from 'app/app.config';
import { TranslateEventService } from 'app/shared/services/translation.service';
import { Subject, Subscription } from 'rxjs';

@Component({
    selector: 'manage-extra-charges',
    templateUrl: './extraCharges.component.html'
})

export class ExtraChargesComponent implements OnInit {
    data: any[] = [];
    originalData: any[] = [];
    config: any;
    private sub: any;
    searchQuery: string;
    selectedElement : any;
    hiddenAdd: boolean = true;
    hiddenEdit: boolean = true;
    public canViewRecords: boolean;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        private auth: AuthGuard,
        private BS: BookingService,
        public translate: TranslateService,
        private TS: TranslateEventService, // New Change ****
        config: AppConfig
    ) { 
        this.config = config.getConfig();
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);
        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() {
        this.canViewRecords = true;
        this.sub = this.BS.getAllExtraChanges()
            .subscribe((res) => {
                if (res.status == "success") {
                    this.data = res.data;
                    this.originalData = res.data;
                }
            }, error => {
                if (error.status == 403) {
                    this.canViewRecords = false;
                }
            });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }

    findIndex(searchTerm, property, searchArray?) {
        searchArray = searchArray ? searchArray : this.data;
        for (var i = 0, len = searchArray.length; i < len; i++) {
            if (searchArray[i][property] === searchTerm) return (i + 1);
        }
        return -1;
    }
    showAdd() {
        this.hiddenAdd = false;
        this.hiddenEdit = true;
    }
    addToData(event) {
        if (this.canViewRecords && event) {
            // this.data.push(event);
            this.originalData.push(event);
            this.searchQuery = undefined;
            this.initializeData();
        }
    }

    showEdit(ele){
        this.selectedElement = ele;
        this.hiddenEdit = false;
        this.hiddenAdd = true;
    }
    updateToList(event){
        if(this.canViewRecords && event){
            this.data[this.findIndex(event.id,"id",this.data) - 1] = event;
            this.originalData[this.findIndex(event.id,"id",this.originalData) - 1] = event;
            this.selectedElement = ""
        }
    }
    search() {
        this.initializeData();
        if(this.searchQuery && this.searchQuery.trim() != '') {
            this.data = this.data.filter( data => {
                return ((<string>data.name).toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
            })
        }
        else {
            this.initializeData();
        }
    }
    initializeData() {
        this.data = this.originalData;
    }
    clearSearch() {
        this.searchQuery = undefined;
        this.initializeData();
    }
    closeThisComp(event) {
        if (event == "hiddenAdd") {
            this.hiddenAdd = true;
        } else {
            this.hiddenEdit = true;
            this.selectedElement = "";
        }
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if(this.sub) {
            this.sub.unsubscribe();
        }
         // New Change ****
         if (this.langChangeSub)
         this.langChangeSub.unsubscribe();
    }
}