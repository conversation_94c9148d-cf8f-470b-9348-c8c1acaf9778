<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-star"></i>&nbsp;&nbsp;{{pageType}} {{'MENU_MANAGE.MENU_GRP' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <ol class="breadcrumb float-sm-left capitalized">
    <li class="breadcrumb-item "><a (click)="toggleChild()" href="javascript:void(0)">{{'MENU_MANAGE.MENU_MANAGE' | translate:param}}</a></li>
    <li class="breadcrumb-item active">{{pageType}} {{'MENU_MANAGE.MENU_GRP' | translate:param}}</li>
  </ol>

  <div class="clearfix"></div>
  <hr>
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="menuAdd" (ngSubmit)="addMenu()">
          <div class="form-group row">
            <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'MENU_MANAGE.NAME' | translate:param}}</label>
            <div class="col-md-8 ">
              <span class="errMsg __fromBackend" *ngIf="menuAdd.controls.name.errors?.backend">{{menuAdd.controls.name.errors?.backend}}</span>
              <input type="text" id="normal-field" formControlName="name" class="form-control" placeholder="">
              <span class="errMsg" *ngIf="!name.valid && !name.untouched">
                <span [hidden]="!name.errors?.required">
                  {{'MENU_MANAGE.GROP_NAME' | translate:param}}
                </span>
              </span>
            </div>
          </div>

          <div class="form-group row">
            <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'MENU_MANAGE.STATUS' | translate:param}}</label>
            <div class="col-md-8 ">
              <div class="radio-horizontal">
                <div class="abc-radio">

                  <input type="radio" formControlName="status" id="radio-0" [value]="true">
                  <label for="radio-0">
                    {{'MENU_MANAGE.ACTIVE' | translate:param}}
                      </label>
                </div>
                <div class="abc-radio">
                  <input type="radio" formControlName="status" id="radio-1" [value]="false">
                  <label for="radio-1">
                    {{'MENU_MANAGE.INACTIVE' | translate:param}}
                      </label>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group row">
            <div class="col-md-8 offset-md-3">
              <div class="">
                <button type="submit" class="btn btn-sm btn-inverse capitalized" [disabled]="!menuAdd.valid"><i class="fa fa-check"></i>{{'MENU_MANAGE.SAVE' | translate:param}}</button>
                <button (click)="toggleChild()" class="btn btn-sm btn-secondary">{{'MENU_MANAGE.CANCEL' | translate:param}}</button>
              </div>
            </div>
          </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
