import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';

import { CustomValidators } from 'ng2-validation';
import { UserService } from './../../../shared/services/user.service';
import { TranslateService } from '@ngx-translate/core';

declare var jQuery: any;
declare var Messenger: any;

@Component({
    selector: 'add-user',
    templateUrl: '../userManagement.actions.html'
})
export class AddUserComponent implements OnInit {

    userAdd: any;
    selectedCountry: any = '103';
    private sub: any;
    pageName: string = "Add";
    select2Options: any = {
        width: '100%'
    };
    public bookingTypeOptions: any = {
        width: '100%',
        minimumResultsForSearch: Infinity
    };
    dateMask = {
        mask: [/\d/, /\d/,
            '-', /\d/, /\d/,
            '-', /[1-9]/, /\d/, /\d/, /\d/]
    };
    // redio button settings
    gender: string[] = ['male', 'female'];
    datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        todayHighlight: true,
        icon: 'fa fa-calendar',
        endDate: new Date(),
        format: 'dd/mm/yyyy'
    }
    @Input() gethiddenAdduser: boolean;
    @Input() data: any;
    @Input() countryList: any[];
    @Output() toggleHiddenAddUser = new EventEmitter();
    roles: any[];
    roleList: any[] = []; // New property to store role list
    constructor(
        private _fb: FormBuilder,
        private US: UserService,
        public translate: TranslateService
    ) { 
        translate.get('USER.ADD_PAGE.PAGETYPE').subscribe((res: string) => {
            console.log(res);
            this.pageName = res;
            //=> 'hello world'
        });
    }

    ngOnInit() {
        this.roles = jQuery.map(this.data, function (obj) {
            return { id: obj.id, text: obj.name };
        }) // create role list according to select2 element { id, text} format;
        
        // Initialize roleList property to prevent infinite loop
        this.initializeRoleList();
        
        this.buildForm();
        if (this.data.length > 0) {
            this.userAdd.controls['role_id'].patchValue(this.data[0].id);
        }
        this.userAdd.controls['country'].patchValue('103');
    }
    
    // New method to initialize role list once
    initializeRoleList() {
        console.log("initializeRoleList called, data:", this.data);
        if (this.data && this.data.length > 0) {
            this.roleList = jQuery.map(this.data, function (obj) {
                return { id: obj.id, text: obj.name };
            });
        } else {
            this.roleList = [];
        }
        console.log("roleList initialized:", this.roleList);
    }
    
    demo(event) {
        event.preventDefault();

    }
    saveUser() {
        if (this.userAdd.valid) {
            this.sub = this.US.saveUserData(this.userAdd.value)
                .subscribe((res) => {
                    if (res.status === "success") {
                        this.userAdd.reset();
                        this.toggleUserComponent(res.data);
                    }
                },
                (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control: FormControl = this.userAdd.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }

    getRolelist() {
        // Return the cached role list instead of computing it every time
        return this.roleList;
    }
    
    // dropdown change methods
    userRoleChanged(event: any) {
        console.log("userRoleChanged triggered with event:", event);
        if (event && event.id) {
            this.userAdd.controls['role_id'].patchValue(event.id);
            console.log("Updated role_id to:", event.id);
        }
    }
    
    countryChanged(event: any) {
        console.log("countryChanged event:", event);
        if (event && event.id) {
            this.userAdd.controls['country'].patchValue(event.id);
        }
    }
    
    // helper functions 
    buildForm() {
         this.userAdd = this._fb.group({
            first_name: ['', Validators.required],
            last_name: ['', Validators.required],
            role_id: [(this.roles.length > 0) ? this.roles[0].id : '', Validators.required],
            email: ['', [Validators.required, CustomValidators.email]],
            mobile_no: ['', CustomValidators.digits],
            dob: [''],
            country: ['India', Validators.required],
            city: [''],
            zip: [''],
            address: [''],
            gender: ['male', Validators.required],
            status: [true, Validators.required],
            is_admin: [false] // to do : remove this field later.
        })

    }
    findIndex(value, arrayName, searchParams) {
        let searchTerm = value,
            index = -1;
        for (let i = 0, len = arrayName.length; i < len; i++) {

            if (arrayName[i][searchParams] === searchTerm) {
                index = i;
                break;
            }
        }
        return index;
    }
    // helper
    toggleUserComponent(data) {
        this.gethiddenAdduser = !this.gethiddenAdduser;
        let sendData;
        if (data) {
            // this will recieved data if user is added
            sendData = { gethiddenAdduser: this.gethiddenAdduser, data: data };
        } else {
            sendData = { gethiddenAdduser: this.gethiddenAdduser };
        }
        this.toggleHiddenAddUser.emit(sendData);
    }
    ngOnDestroy() {
        this.userAdd.reset();
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }
}

// https://www.npmjs.com/package/ng2-validation