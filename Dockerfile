# FROM nginx:1.17.1-alpine
# COPY nginx.conf /etc/nginx/nginx.conf
# COPY dist/ /usr/share/nginx/html
# COPY ./ssl/bookings.sgadi.com.p7b /etc/nginx/bookings.sgadi.com.p7b
# COPY ./ssl/bookings.sgadi.com_key.key /etc/nginx/bookings.sgadi.com_key.key
# RUN chmod 600 /etc/nginx/bookings.sgadi.com_key.key
# RUN chmod 600 /etc/nginx/bookings.sgadi.com.p7b

# EXPOSE 80
# EXPOSE 443

FROM nginx:1.17.1-alpine
COPY nginx.conf /etc/nginx/nginx.conf
COPY ./ciSsl/bookings.swaminarayangadi.com.crt /etc/nginx/bookings.swaminarayangadi.com.crt
COPY ./ciSsl/bookings.swaminarayangadi.com_key.txt /etc/nginx/bookings.swaminarayangadi.com_key.txt
RUN chmod 600 /etc/nginx/bookings.swaminarayangadi.com_key.txt
RUN chmod 600 /etc/nginx/bookings.swaminarayangadi.com.crt 
COPY dist/ /usr/share/nginx/html
EXPOSE 80
EXPOSE 8080
EXPOSE 443
