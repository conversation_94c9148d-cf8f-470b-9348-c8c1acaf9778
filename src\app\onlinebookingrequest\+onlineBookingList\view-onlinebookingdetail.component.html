<section class="widget" [ngClass]="{'enable-print': isPoliceInquiry}">
    <header>
        <div class="row">
            <div class="col-sm-8">
                <h4>
                    <span class="capitalized">
                        <i class="fa fa-clipboard"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.VIEW_BOOK' |
                        translate:param}}</span>
                </h4>
            </div>
            <div class="col-sm-4 view-details-buttons">
                <button type="button" class="btn btn-sm btn-danger float-sm-right" (click)="goback()">
                    <i class="fa fa-angle-left"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.BACK' | translate:param}}</button>
                <button type="button" *ngIf="isPoliceInquiry" class="btn btn-sm btn-danger float-sm-right"
                    style="margin-right: 10px;" (click)="printDocument()">
                    <i class="fa fa-angle-left"></i>&nbsp;&nbsp;{{'VIEW_BOOKI_DETAI.PRINT' | translate:param}}</button>
            </div>
        </div>
    </header>
    <hr class="large-hr" [ngClass]="{'enable-print': isPoliceInquiry}">
    <div class="clearfix"></div>
    <div class="widget-body" *ngIf="selectedData && data" [ngClass]="{'enable-print': isPoliceInquiry}">
        <!-- Summary Panels -->
        <div class="mt">
            <div class="row">
                <div class="col-md-6" *ngIf="!isPoliceInquiry">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4>{{'VIEW_BOOKI_DETAI.BOOK_DE' | translate:param}}</h4>
                        </div>
                        <div class="panel-body">
                            <table class="table table-no-mar">
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.RES_ID' | translate:param}}:</td>
                                    <td>
                                        <strong>{{data[0].booking_id ? data[0].booking_id : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.STAY_TYPE' | translate:param}}:</td>
                                    <td>
                                        <strong>{{data[0].stay_type_id == "1" ? 'Full Day' : data[0].stay_type_id == "2"
                                            ? 'Half Day' :
                                            data[0].stay_type_id == "3" ? 'Shower' : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.BOOK_TYPE' | translate:param}}:</td>
                                    <td>
                                        <strong>{{data[0].booking_type == '0' ? 'Single Booking' : data[0].booking_type
                                            == '1' ? 'Group
                                            Booking' : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.BOOK_STAT' | translate:param}}:</td>
                                    <td>
                                        <strong [ngStyle]="{'color': 
                          data[0].current_status == 'checkin' ? '#64bd63' :
                          data[0].current_status == 'reserved' ? '#5d8fc2' :
                          data[0].current_status == 'checkout' ? '#d80000' : 'inherit'}"
                                            class="capitalize">{{data[0].current_status
                                            ? data[0].current_status : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.BOOK_DATE' | translate:param}}:</td>
                                    <td>
                                        <strong>{{(data[0].booking_date ? (data[0].booking_date | date) :
                                            '-')}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.RATE' | translate:param}}:</td>
                                    <td>
                                        <strong>
                                            <span *ngIf="selectedReport.total_amount">
                                                +
                                                <!-- <i class="fa fa-inr"></i> -->
                                                &nbsp;{{selectedReport.total_amount}}
                                            </span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr *ngIf="selectedReport.extra_pax_charges">
                                    <td>{{'VIEW_BOOKI_DETAI.EX_AD_GU_CHA' | translate:param}}:</td>
                                    <td>
                                        <strong>
                                            <span>
                                                +
                                                <!-- <i class="fa fa-inr"></i> -->
                                                &nbsp;{{selectedReport.extra_pax_charges ?
                                                selectedReport.extra_pax_charges
                                                : '-'}}
                                            </span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr *ngIf="selectedReport.cardswipecharges">
                                    <td>{{'VIEW_BOOKI_DETAI.CAR_SWIP_CHAR' | translate:param}}:</td>
                                    <td>
                                        <strong>
                                            <span>
                                                +
                                                <!-- <i class="fa fa-inr"></i> -->
                                                &nbsp;{{selectedReport.cardswipecharges}}
                                            </span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr *ngIf="selectedReport.earlycheckincharge">
                                    <td>{{'VIEW_BOOKI_DETAI.EA_CHECK_CHARG' | translate:param}}:</td>
                                    <td>
                                        <strong>
                                            <span>
                                                +
                                                <!-- <i class="fa fa-inr"></i> -->
                                                &nbsp;{{selectedReport.earlycheckincharge}}
                                            </span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr *ngIf="selectedReport.fund_amount">
                                    <td>{{'VIEW_BOOKI_DETAI.FUND_AMT' | translate:param}}:</td>
                                    <td>
                                        <strong>
                                            <span>
                                                +
                                                <!-- <i class="fa fa-inr"></i> -->
                                                &nbsp;{{selectedReport.fund_amount}}
                                            </span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.DISCOUNT' | translate:param}}:</td>
                                    <td>
                                        <strong>
                                            <span>
                                                <!-- <i class="fa fa-inr"></i> -->
                                                &nbsp;{{getTotalDiscount()}}
                                            </span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.PAID' | translate:param}}:</td>
                                    <td>
                                        <strong>
                                            <span>
                                                -
                                                <!-- <i class="fa fa-inr"></i> -->
                                                &nbsp;{{getAmountPaid()}}
                                            </span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.BALAN_AMT' | translate:param}}:</td>
                                    <td>
                                        <strong>
                                            <span *ngIf="selectedReport.total_amount">
                                                <!-- <i class="fa fa-inr"></i> -->
                                                &nbsp;{{getBalanceAmount()}}
                                            </span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.REF_AMT' | translate:param}} :</td>
                                    <td>
                                        <strong>
                                            <span *ngIf="selectedReport.return_amount">
                                                &nbsp;{{selectedReport.return_amount}}
                                            </span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_IN' | translate:param}}:</td>
                                    <td>
                                        <strong class="text-success">{{(selectedReport.expected_check_in ?
                                            (selectedReport.expected_check_in
                                            | date:'h:mm a') : '-' )}}&nbsp;Hrs.</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.EXP_CHECK_OUT' | translate:param}}:</td>
                                    <td>
                                        <strong class="text-success">{{(selectedReport.expected_check_out ?
                                            (formatCheckoutDate(selectedReport.expected_check_out)
                                            | date:'h:mm a') : '-')}}&nbsp;Hrs.</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.ACTU_CHECK_IN' | translate:param}}:</td>
                                    <td>
                                        <strong class="text-success">
                                            {{selectedReport.check_in ? (selectedReport.check_in | date:'h:mm a, MMM d,
                                            y') : '-'}}
                                            <span *ngIf="selectedReport.checkInBy !== null">by {{ data[0].checkInBy
                                                }}</span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.ACTU_CHECK_OUT' | translate:param}}:</td>
                                    <td>
                                        <strong class="text-danger">
                                            {{selectedReport.check_out ? (selectedReport.check_out | date:'h:mm a, MMM
                                            d, y') : '-'}}
                                            <span *ngIf="selectedReport.checkOutBy !== null">by {{ data[0].checkOutBy
                                                }}</span>
                                        </strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>PAN Card:</td>
                                    <td>
                                        <strong class="text-danger">
                                            {{data[0].guests.pancard_number ? data[0].guests.pancard_number : ''}}
                                        </strong>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" *ngIf="!isPoliceInquiry">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4>{{'VIEW_BOOKI_DETAI.BILL_GUE_DETAI' | translate:param}}</h4>
                        </div>
                        <div class="panel-body">
                            <table class="table table-no-mar">
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.GUEST_NAME' | translate:param}}:</td>
                                    <td style="text-transform:uppercase;">
                                        <strong>{{selectedReport.name ? selectedReport.name : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.GUE_TYPE' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{selectedReport.guest_type ? selectedReport.guest_type : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.CONT' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{data[0].guests.contact ? data[0].guests.contact : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.EMAIL' | translate:param}}:</td>
                                    <td>
                                        <strong>{{data[0].guests.email ? data[0].guests.email : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.ADDRESS' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{data[0].guests.address ? data[0].guests.address : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.CITY' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{data[0].guests.city ? data[0].guests.city : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.ZIP' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{data[0].guests.zip ? data[0].guests.zip : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.ROOM_NO' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{selectedReport.room_no ? selectedReport.room_no : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.ROOM_TYP' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{selectedReport.room_type ? selectedReport.room_type : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.TOT_ADU' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{selectedReport.adult ? selectedReport.adult : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.TOT_CHIL' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{selectedReport.child ? selectedReport.child : '-'}}</strong>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-8 col-md-6 col-lg-4" *ngIf="isPoliceInquiry">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4>{{'VIEW_BOOKI_DETAI.DETAILS' | translate:param}}</h4>
                        </div>
                        <div class="panel-body">
                            <table class="table table-no-mar">
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.GUEST_NAME' | translate:param}}:</td>
                                    <td style="text-transform:uppercase;">
                                        <strong>{{selectedReport.name ? selectedReport.name : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.CONT' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{data[0].guests.contact ? data[0].guests.contact : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.ADDRESS' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{data[0].guests.address ? data[0].guests.address : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.CITY' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{data[0].guests.city ? data[0].guests.city : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.ZIP' | translate:param}}:</td>
                                    <td class="capitalize">
                                        <strong>{{data[0].guests.zip ? data[0].guests.zip : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.CHECK_IN' | translate:param}}:</td>
                                    <td>
                                        <strong class="text-success">{{selectedReport.check_in ?
                                            (selectedReport.check_in | date:'h:mm a,
                                            MMM
                                            d, y') : '-'}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{{'VIEW_BOOKI_DETAI.CHECK_OUT' | translate:param}}:</td>
                                    <td>
                                        <strong class="text-danger">{{selectedReport.check_out ?
                                            (selectedReport.check_out | date:'h:mm a,
                                            MMM
                                            d, y') : '-'}}</strong>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" *ngIf="!isPoliceInquiry && selectedReport && selectedReport.note">
                <div class="col-md-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4>{{'VIEW_BOOKI_DETAI.NOTES' | translate:param}}</h4>
                        </div>
                        <div class="panel-body">
                            <accordion [closeOthers]="true" class="mb-lg show" id="accordion">
                                <accordion-group *ngFor="let note of selectedReport.note;">
                                    <div accordion-heading>
                                        {{getNoteTitle(note)}}
                                        <i class="fa fa-angle-down pull-xs-right"></i>
                                    </div>
                                    <div>
                                        {{note.message}}
                                    </div>
                                </accordion-group>
                            </accordion>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</section>