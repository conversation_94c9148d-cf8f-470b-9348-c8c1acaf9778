import { CommonHttpService } from './common-http/common-http.service';
import { Injectable } from '@angular/core';

@Injectable()
export class PaymentRequestService {
    constructor(private chttp: CommonHttpService) { }

    //list online bookings
    getOnlinePaymentList() {
        return this.chttp.get(`onlinepayment/list`);
    }
    //save particular online booking data
    saveOnlinePaymentData(id: any, data: any) {
        return this.chttp.post(`onlinebooking/save/${id}`, data, true);
    }
    viewOnlinePayment(id: any) {
        return this.chttp.get(`onlinebooking/view/${id}`);
    }
    //aprove onlinebooking data
    approveOnlinePayment(data: any) {
        return this.chttp.post(`onlinepayment/approve/${data.id}`, { data: data });
    }
    getonlinepayment(id: any){
        return this.chttp.get(`getonlinepayment/${id}`);
    }
    cancelOnlinePayment(id: any, reason: string) {
        return this.chttp.post(`onlinepayment/cancel/${id}`, { reason: reason });
    }

    createBooking(data: any) {
        return this.chttp.post('onlineBooking/add', data)
    }
}
