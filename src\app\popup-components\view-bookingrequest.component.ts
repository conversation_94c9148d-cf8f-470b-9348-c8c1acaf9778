import { Select2OptionData } from 'ng2-select2';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { TranslateService } from '@ngx-translate/core';
import { AppConfig } from 'app/app.config';
import { TranslateEventService } from 'app/shared/services/translation.service';
import { Subject, Subscription } from 'rxjs';

@Component({
    selector: 'view-requestBooking-details',
    templateUrl: './view-bookingrequest.component.html',
    styleUrls: ['./view-bookingrequest.style.scss']
})

export class ViewrequestBookingDetailsComponent implements OnInit, OnDestroy {
    @ViewChild('imageModal') imageModal: ModalDirective;
    config: any;
    id: number;
    imageUrls: string[] = [];
    @Input() data: any;
    @Input() isPoliceInquiry: boolean;
    @Output() goBack = new EventEmitter();
    @Input() selectedBooking: any;
    @Input() cancelId: any;
    @Output() canclemodal = new EventEmitter<number>();
    public image: any = {};
    public printWindow2: any;
    public guestDocument: any;
    public selectedReport: any;
    public documentBase64: any;
    public canShow: boolean = true;
    public bookingType: any = [
        {
            id: '0',
            text: 'Single Booking'
        },
        {
            id: '1',
            text: 'Group Booking'
        }
    ];
    public paymentType: Array<Select2OptionData> = [
        {
            id: '0',
            text: 'Cash Payment'
        },
        {
            id: '1',
            text: 'Card Payment'
        },
        {
            id: '2',
            text: 'Cheque Payment'
        }
    ];
    note = []
    public bookingPayments: any[];
    public $destroy = new Subject();
    private langChangeSub: Subscription;
    constructor(
        // public translate: TranslateService,
        // config: AppConfig,
        // private TS: TranslateEventService,
    ) {
        // this.config = config.getConfig();
        // let currentLang = localStorage.getItem('currentLang');
        // translate.setDefaultLang(currentLang);


        // this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
        //     this.changeLang(res);
        // });
    }

    // public guestName = (a: any) => {
    //     return (<string>a.guests.name).toString().toLowerCase();
    // }
    // changeLang(lang: string) {

    //     this.translate.use(lang);
    // }

    ngOnInit() {
        console.log('Received Booking Data:', this.selectedBooking);

    }
    viewDocumentProof(item: any) {
        this.canShow = false;
        this.imageUrls = [];
        for (const imgUrl of item.guest_Doc) {
            let mimetype: any[] = [];
            if (item.mimetype) {
                mimetype = item.mimetype.split('/');
            }
            this.image['isImage'] = mimetype.indexOf('application') >= 0 ? false : true;
            if (this.image['isImage'] == false) {
                // this.guestDocument = this.BS.getFileBase64({ document_url: this.image['guest_document'] })
                //     .subscribe(res => {
                //         if (res) {
                //             this.documentBase64 = null;
                //             let file: any = new Blob([res], { type: 'application/pdf' });
                //             const reader = new FileReader();

                //             reader.readAsDataURL(file);
                //             reader.addEventListener('loadend', (e: any) => {
                //                 let documentBase64 = reader.result;
                //                 var winparams = `dependent=yes,locationbar=no,scrollbars=yes,menubar=yes,resizable,screenX=50,screenY=50,width=850,height=1050`;
                //                 var htmlPop = `<embed width=100% height=100% type="application/pdf" src="${documentBase64}"></embed>`;
                //                 this.printWindow2 = window.open("", "PDF", winparams).document.write(htmlPop);
                //             });
                //         }
                //     })
            }
            else {
                this.imageUrls.push(imgUrl)
            }
        }
        this.imageModal.show();
    }
    gobacksimon() {
        this.goBack.emit();
        this.selectedBooking = null;
    }
    cancelRequest() {
        this.goBack.emit();
        this.cancelId = this.selectedBooking.id;
        console.log('Cancel Request id:', this.cancelId);
        setTimeout(() => {
            this.canclemodal.emit(this.cancelId);            
        }, 1000);
    }

    ngOnDestroy(): void {

    }
}