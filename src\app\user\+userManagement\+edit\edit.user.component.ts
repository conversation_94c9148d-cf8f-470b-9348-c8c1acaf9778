import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';

import { CustomValidators } from 'ng2-validation';
import { UserService } from './../../../shared/services/user.service';
import { TranslateService } from '@ngx-translate/core';


declare var jQuery: any;
declare var Messenger: any;
@Component({
    selector: 'edit-user',
    templateUrl: '../userManagement.actions.html'
})
export class EditUserComponent implements OnInit {
    userAdd: any;
    selectedCountry: any;
    pageName: string = "Edit";
    initRolelist: any;
    private sub: any;
    roles: any;
    roleList: any[] = []; // New property to store role list
    select2Options: any = {
        width: '100%'
    };
    public bookingTypeOptions: any = {
        width: '100%',
        minimumResultsForSearch: Infinity
    };
    dateMask = {
        mask: [/\d/, /\d/,
            '-', /\d/, /\d/,
            '-', /[1-9]/, /\d/, /\d/, /\d/]
    };
    // redio button settings
    gender: string[] = ['male', 'female'];
    datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        todayHighlight: true,
        assumeNearbyYear: true,
        format: 'dd / mm / yyyy',
        icon: 'fa fa-calendar'
    }
    // input / output controls for to communication with parent
    @Input() data: any;
    @Input() rolesList: any;
    @Input() countryList: any[];
    @Input() gethiddenEditUser: any;
    @Output() toggleHiddenEditUser = new EventEmitter();

    constructor(
        private _fb: FormBuilder,
        private US: UserService,
        public translate: TranslateService
    ) {
        translate.get('USER.ADD_PAGE.EDIT_PAGE_TYPE').subscribe((res: string) => {
            console.log(res);
            this.pageName = res;
            //=> 'hello world'
        }); 
    }

    ngOnInit() {
        this.roles = jQuery.map(this.rolesList, function (obj) {
            return { id: obj.id, text: obj.name };
        }) // create role list according to select2 element { id, text} format;
        // Initialize roleList property to prevent infinite loop
        this.initializeRoleList();
        this.buildForm();
        let date : Date = new Date(this.data.dob.toString())
        let year = this.data.dob
        this.userAdd.patchValue(this.data); // patching values
        this.userAdd.controls['dob'].patchValue(date)
        this.initRolelist = this.data.role_id; // initrolelist for dropdown init value
        this.selectedCountry = this.data.country;
    }

    // New method to initialize role list once
    initializeRoleList() {
        if (this.rolesList && this.rolesList.length > 0) {
            this.roleList = jQuery.map(this.rolesList, function (obj) {
                return { id: obj.id, text: obj.name };
            });
        } else {
            this.roleList = [];
        }
    }

    saveUser() {
        if (this.userAdd.valid) {
            this.sub = this.US.updateUserData(this.data.id, this.userAdd.value)
                .subscribe((res) => {
                    if (res.status === "success") {
                        this.userAdd.reset();
                        this.toggleUserComponent(res.data);
                    }
                },
                (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control: FormControl = this.userAdd.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } 
        // else {
        //     Messenger().post({  hideAfter: 5,
        //         message: "Form can not be submitted",
        //         type: "error",
        //         showCloseButton: true
        //     });
        // }
    }
    
    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
    }

    getRolelist() {
        // Return the cached role list instead of computing it every time
        return this.roleList;
    }
    
    // dropdown change methods
    userRoleChanged(event: any) {
        if (event && event.id) {
            this.userAdd.controls['role_id'].patchValue(event.id);
        }
    }
    
    countryChanged(event: any) {
        if (event && event.id) {
            this.userAdd.controls['country'].patchValue(event.id);
            this.selectedCountry = event.id;
        }
    }
    // helpre functions 
    buildForm() {
        this.userAdd = this._fb.group({
            first_name: ['', Validators.required],
            last_name: ['', Validators.required],
            role_id: ['', Validators.required],
            email: ['', [Validators.required, CustomValidators.email]],
            mobile_no: ['', CustomValidators.digits],
            dob: [''],
            country: ['', Validators.required],
            city: [''],
            zip: [''],
            address: [''],
            gender: ['', Validators.required],
            status: ['', Validators.required],
            is_admin: [''] // to do : remove this field later.      
        })
    }
    toggleUserComponent(data) {
        this.gethiddenEditUser = !this.gethiddenEditUser;
        let sendData;
        if (data) {
            // this will recieved data if user is edite
            sendData = { gethiddenEditUser: this.gethiddenEditUser, data: data };
        } else {
            sendData = { gethiddenEditUser: this.gethiddenEditUser };
        }
        this.toggleHiddenEditUser.emit(sendData);
    }
    findIndex(searchTerm) {

        for (var i = 0, len = this.roles.length; i < len; i++) {
            if (this.roles[i].id === searchTerm) return (i);
        }
        return -1;
    }
}