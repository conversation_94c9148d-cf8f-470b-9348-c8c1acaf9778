import { NgSelectModule } from '@ng-select/ng-select';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

import { DataTableModule } from 'angular2-datatable';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { DiscountReferenceUserComponent } from './discountReferenceUser.component';
import { AddDiscountReferenceUserComponent } from './+add/add.discountReferenceUser.component';
import { EditDiscountReferenceUserComponent } from './+edit/edit.discountReferenceUser.component';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
// Create translate loader
export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

// all routes related to this Role module.
export const routes = [
  { path: '', component: DiscountReferenceUserComponent, pathMatch: 'full' }
]

@NgModule({
  imports: [
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    NgSelectModule,
    DataTableModule,
    TooltipModule.forRoot(),
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    RouterModule.forChild(routes)
  ],
  exports: [RouterModule],
  declarations: [
    DiscountReferenceUserComponent,
    AddDiscountReferenceUserComponent,
    EditDiscountReferenceUserComponent
  ],
  providers: [],
})
export class DiscountReferenceUserModule {
  static routes = routes;
}
