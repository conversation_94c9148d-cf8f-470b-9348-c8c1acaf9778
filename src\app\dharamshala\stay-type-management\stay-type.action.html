<section class="widget">
  <header>
    <h4><span class="capitalized"><i class="fa fa-inr"></i>&nbsp;&nbsp;{{pageType}} {{'STAY_TYPE.ADD_PAGE.STAY_TYPE' | translate:param}}</span></h4>
  </header>
  <hr class="large-hr">
  <div class="widget-body">
    <div class="mt">
      <fieldset>
        <form [formGroup]="StayTypeForm" (ngSubmit)="saveStayType()">
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'STAY_TYPE.ADD_PAGE.NAME' | translate:param}}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="StayTypeForm.controls.name.errors?.backend">{{StayTypeForm.controls.name.errors?.backend}}</span>
            <input type="text"  class="form-control" formControlName="name" name="name" placeholder="">
            <span class="errMsg" *ngIf="!StayTypeForm.controls.name.valid && !StayTypeForm.controls.name.untouched">
              <span [hidden]="!StayTypeForm.controls.name.errors.required">{{'STAY_TYPE.ADD_PAGE.VALID_MSG.NAME_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'STAY_TYPE.ADD_PAGE.DURATION' | translate:param}}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="StayTypeForm.controls.duration.errors?.backend">{{StayTypeForm.controls.duration.errors?.backend}}</span>
            <div class="input-group" [ngClass]="{'has-error': (!StayTypeForm.controls.duration.valid && !StayTypeForm.controls.duration.untouched)}">
              <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
            <input type="text" class="form-control" formControlName="duration" name="duration" placeholder="{{'STAY_TYPE.ADD_PAGE.IN_HOURS' | translate:param}}">
            </div>
            <span class="errMsg" *ngIf="!StayTypeForm.controls.duration.valid && !StayTypeForm.controls.duration.untouched">
              <span [hidden]="!StayTypeForm.controls.duration.errors.required">{{'STAY_TYPE.ADD_PAGE.VALID_MSG.DURA_REQ' | translate:param}}</span>
              <span [hidden]="!StayTypeForm.controls.duration.errors.digits">{{'STAY_TYPE.ADD_PAGE.VALID_MSG.ONLY_DIGIT_REQ' | translate:param}}</span>
              <span [hidden]="StayTypeForm.controls.duration.errors.digits || !StayTypeForm.controls.duration.errors.lte">{{'STAY_TYPE.ADD_PAGE.VALID_MSG.POSITIVE_NUM_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>
        <div class="form-group row">
          <label for="normal-field" class="col-md-3  col-form-label text-md-right">{{'STAY_TYPE.ADD_PAGE.CHARGE' | translate:param}}</label>
          <div class="col-md-8 ">
            <span class="errMsg __fromBackend" *ngIf="StayTypeForm.controls.charge.errors?.backend">{{StayTypeForm.controls.charge.errors?.backend}}</span>
            <div class="input-group" [ngClass]="{'has-error': (!StayTypeForm.controls.charge.valid && !StayTypeForm.controls.charge.untouched)}">
              <span class="input-group-addon"><i class="fa fa-percent"></i></span>
            <input type="text" class="form-control" formControlName="charge" name="charge" placeholder="{{'STAY_TYPE.ADD_PAGE.IN_PERCE' | translate:param}}">
            </div>
            <span class="errMsg" *ngIf="!StayTypeForm.controls.charge.valid && !StayTypeForm.controls.charge.untouched">
              <span [hidden]="!StayTypeForm.controls.charge.errors.required">{{'STAY_TYPE.ADD_PAGE.VALID_MSG.CHARGE_REQ' | translate:param}}</span>
              <span [hidden]="!StayTypeForm.controls.charge.errors.number">{{'STAY_TYPE.ADD_PAGE.VALID_MSG.ONLY_DIGIT_REQUIRED' | translate:param}}</span>
              <span [hidden]="StayTypeForm.controls.charge.errors.number || (!StayTypeForm.controls.charge.errors.lte && !StayTypeForm.controls.charge.errors.gte)">{{'STAY_TYPE.ADD_PAGE.VALID_MSG.POSITIVE_REQ' | translate:param}}</span>
            </span>
          </div>
        </div>

        <div class="form-group row">
          <label class="col-md-3 col-form-label text-md-right" for="default-select">{{'STAY_TYPE.ADD_PAGE.STATUS' | translate:param}}</label>
          <div class="col-md-8 ">
            <div class="radio-horizontal">
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-1" [value]="true">
                <label for="radio-1">
                  {{'STAY_TYPE.ADD_PAGE.ACTIVE' | translate:param}}
                      </label>
              </div>
              <div class="abc-radio">
                <input type="radio" formControlName="status" id="radio-2" [value]="false">
                <label for="radio-2">
                  {{'STAY_TYPE.ADD_PAGE.INACTIVE' | translate:param}}
                      </label>
              </div>
            </div>
          </div>
        </div>
        <div class="form-group row">
          <label class="col-md-3 col-form-label text-md-right" for="menu-icon">{{'STAY_TYPE.ADD_PAGE.ICON' | translate:param}}</label>                
          <div class="col-md-8">
            <input id="menu-icon" type="hidden" formControlName="stay_type_icon">
            <button
              class="btn btn-default"
              type="button"
              [iconPicker]="menuIcon"
              [ipIconPack]="['fa']"
              (iconPickerSelect)="onIconSelect($event)"
            >
              <i *ngIf="menuIcon" [class]="'fa fa-' + menuIcon"></i>
              <span *ngIf="!menuIcon">Select Icon</span>
            </button>
          </div>
        </div>

        <div class="form-group row">
          <div class="col-md-8 offset-md-3">
            <div class="">
              <button type="submit" [disabled]="!StayTypeForm.valid" class="btn btn-sm btn-inverse capitalized"><i class="fa fa-check"></i>{{'STAY_TYPE.ADD_PAGE.SAVE' | translate:param}}</button>
              <button type="button" (click)="hideComponent()" class="btn btn-sm btn-secondary">{{'STAY_TYPE.ADD_PAGE.CANCEL' | translate:param}}</button>
            </div>
          </div>
        </div>
        </form>
      </fieldset>
    </div>
  </div>
</section>
