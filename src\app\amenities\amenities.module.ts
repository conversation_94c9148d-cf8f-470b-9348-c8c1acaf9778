import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { DataTableModule } from 'angular2-datatable';
import { AmenitiesComponent } from './+amenitiesList/amenities.component';
import { AddAmenitiesComponent } from './+amenitiesList/+add/add.amenitiesList.component';
import { EditAmenitiesComponent } from './+amenitiesList/+edit/edit.amenitiesList.component';
import { AmenitiesService } from './../shared/services/amenities.service';
import { TooltipModule } from "ngx-bootstrap/tooltip";
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { SharedModule } from 'app/shared/shared.module';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
// all routes related to this Role module.
export const routes = [
  { path: '', component: AmenitiesComponent, pathMatch: 'full' }
//   { path: 'add', component: AddAmenitiesComponent },
//   { path: 'edit/:id', component: EditAmenitiesComponent }
];

@NgModule({
    imports: [
        FormsModule,
        CommonModule,
        TooltipModule,
        DataTableModule,
        RouterModule.forChild(routes),
        ReactiveFormsModule,
        TranslateModule.forRoot({
          loader:{ 
              provide: TranslateLoader, 
              useFactory: (http: HttpClient) => new TranslateHttpLoader(http, './assets/i18n/', '.json'),
            deps: [HttpClient] 
          }}),
          SharedModule
    ],
    exports: [],
    declarations: [
        AmenitiesComponent,
        AddAmenitiesComponent,
        EditAmenitiesComponent
    ],
    providers: [AmenitiesService],
})
export class AmenitiesModule { }
