{"compilerOptions": {"target": "es5", "module": "ESNext", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "importHelpers": true, "sourceMap": true, "noEmit": true, "noEmitHelpers": true, "strictNullChecks": false, "skipLibCheck": true, "baseUrl": "./src", "paths": {}, "lib": ["ESNext", "dom", "es2015", "es2016", "es2017", "es2018"], "types": ["<PERSON><PERSON><PERSON>", "node"]}, "exclude": ["node_modules", "dist", "src/**/*.spec.ts", "src/**/*.e2e.ts"], "awesomeTypescriptLoaderOptions": {"forkChecker": true, "useWebpackText": true}, "angularCompilerOptions": {"genDir": "./compiled", "skipMetadataEmit": true}, "compileOnSave": false, "buildOnSave": false, "atom": {"rewriteTsconfig": false}}