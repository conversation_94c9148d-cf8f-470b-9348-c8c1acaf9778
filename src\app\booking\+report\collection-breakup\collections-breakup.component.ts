import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { Component, OnInit, OnDestroy } from '@angular/core';
import * as moment from "moment";
import { BookingService } from 'app/shared/services/booking.service';
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subscription, Subject } from 'rxjs';
import { AuthGuard } from 'app/shared/guards/auth-guard.service';
import { paymentType } from "../../data";
import { SSL_OP_SSLEAY_080_CLIENT_DH_BUG } from 'constants';
declare var Messenger: any;

@Component({
    selector: 'collections-breakup',
    templateUrl: 'collections-breakup.component.html',
    styleUrls: ['collections-breakup.component.scss']
})

export class CollectionsBreakUpComponent implements OnInit, On<PERSON><PERSON>roy {
    config: any;// New Change ****
    data: any[] = [];
    searchQuery: String;
    searchForm: FormGroup;
    canViewRecords: boolean;
    totalAmount: number = 0;
    totalPaymentAmount : number = 0;
    originalData: any[] = [];
    public datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        format: 'dd/mm/yyyy',
        todayHighlight: true,
        icon: 'fa fa-calendar',
    }
    isAdmin;
    public paymentType = paymentType;
    public usersList = [];
    public bookingTypeOptions = {
        width: '100%',
    };
    selectedFilterTypes = {
        payment_mode: '000000',
        user_id: '000000'
    }
    advanceAmt = 0;
    pendingAmt = 0;
    checkinAmt = 0;
    returnAmt = 0;
    agentAmt = 0;
    chequeAmt = 0;
    cashAmt = 0;
    bankAmt = 0;
    cardAmt = 0;
    agentAmtPay = 0;
    private csvSub: Subscription;
    private getReportDataSub: Subscription;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private BS: BookingService,
        private fb: FormBuilder,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
        private authGuard: AuthGuard,
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****

        // New Change ****
        this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
        this.isAdmin = this.authGuard.isAdmin();
     }

    ngOnInit() {
        this.initForm()
        this.canViewRecords = true;
        this.paymentType = this.paymentType.map((obj) => {
            return { id: obj.id, text: obj.text.toUpperCase()}
        });
        this.paymentType.unshift({ "id": "000000", "text": "All-togather" });
        this.selectedFilterTypes = {
            payment_mode: '000000',
            user_id: '000000'
        };

        this.searchReports()
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    toDataChange(event) {
     this.searchForm.controls['toDate'].patchValue(event);
    }
    initForm() {
        this.searchForm = this.fb.group({
            toDate: [new Date()],
            fromDate: [new Date()]
        });
    }
    getSearchParams() {
        if (this.searchForm.valid) {
            let searchParams = JSON.parse(JSON.stringify(this.searchForm.value));
            searchParams.fromDate = moment(searchParams.fromDate).format('YYYY-MM-DD');
            searchParams.toDate = moment(searchParams.toDate).format('YYYY-MM-DD');
            return searchParams;
        }
        else {
            for (let field in this.searchForm.controls) {
                this.searchForm.controls[field].markAsDirty();
                this.searchForm.controls[field].markAsTouched();
            }
        }

    }
    searchReports() {
        let searchParams = this.getSearchParams();
        searchParams['payment_mode'] = this.selectedFilterTypes.payment_mode;
        searchParams['user_id'] = this.selectedFilterTypes.user_id;
        this.getReportData(searchParams);
    }
    paymentModeChanged(event) {
     this.selectedFilterTypes.payment_mode = event.id;
     this.searchReports();
    }
    userChanged(event){
        this.selectedFilterTypes.user_id = event?.id
    }


    getReportData(searchParams) {
        this.originalData = []
        this.getReportDataSub = this.BS.getCollectionBreakupReport(searchParams)
            .subscribe((res) => {
                if (res.status === 'success') {
                    // this.originalData = res.data.paymentRecords;
                    let a = res.data.paymentRecords;
                    if (a.hasOwnProperty('agent')) {
                        let obj = {
                            name: 'agent',
                            checked: true,
                            data: []
                        }
                        obj.data = a.agent
                        this.originalData.push(obj)
                    }
                    if (a.hasOwnProperty('advance')) {
                        let obj = {
                            name: 'advance',
                            checked: true,
                            data: []
                        }
                        obj.data = a.advance
                        this.originalData.push(obj)
                    }
                    if (a.hasOwnProperty('checkin')) {
                        let obj = {
                            name: 'checkin/checkout',
                            checked: true,
                            data: []
                        }
                        obj.data = a.checkin
                        this.originalData.push(obj)
                    }
                    if (a.hasOwnProperty('pending')) {
                        let obj = {
                            name: 'pending',
                            checked: true,
                            data: []
                        }
                        obj.data = a.pending
                        this.originalData.push(obj)
                    }
                    if (a.hasOwnProperty('return')) {
                        let obj = {
                            name: 'refund',
                            checked: true,
                            data: []
                        }
                        obj.data = a.return
                        this.originalData.push(obj)
                    }
                    this.usersList = res.data.users.map((obj) => {
                        return { id: obj.user_id, text: obj.first_name.toUpperCase() + ' ' + obj.last_name.toUpperCase()}
                    });
                    this.usersList = this.sortArray(this.usersList)
                    this.usersList.unshift({ "id": "000000", "text": "All-togather" });
                    this.initializeData();
                    this.updateAmountSummary();
                }
            });
    }
    search() {
        if (this.searchForm && this.data) {
            this.initializeData();
            if (this.data && this.searchQuery && this.searchQuery.trim() != '') {
                this.data = this.data.filter(data => {
                    let searchTarget = '';
                    Object.keys(data).forEach(key => {
                        searchTarget += data[key];
                    })
                    console.log('serach ', this.data )
                    return (searchTarget.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
                });
            }
            else {
                this.initializeData();
            }
        }
    }
    clearSearch() {
        this.initializeData();
        this.searchQuery = undefined;
    }
    onReset() {
        // console.log("on reset--------------------------------------")
        this.searchForm.controls['fromDate'].setValue(new Date());
        this.searchForm.controls['toDate'].setValue(new Date());
        this.selectedFilterTypes.payment_mode = '000000';
        this.selectedFilterTypes.user_id = '000000';
        this.data = [];
        this.originalData = [];
        this.initializeSummary()
        let searchParams = this.getSearchParams();
        this.getReportData(searchParams);
    }
    initializeData() {
        this.data = this.originalData;
    }

    initializeSummary() {
        this.advanceAmt = 0;
        this.pendingAmt = 0;
        this.checkinAmt = 0;
        this.returnAmt = 0;
        this.agentAmt = 0;
        this.totalAmount = 0;
        this.chequeAmt = 0;
        this.cashAmt = 0;
        this.bankAmt = 0;
        this.cardAmt = 0;
        this.agentAmtPay = 0;
        this.totalPaymentAmount = 0;
    }

    updateAmountSummary() {
        this.initializeSummary();
        this.data.map(resData => {
            resData.data.map(resData1 => {
                if(resData1.payment_mode === '0') {
                    resData1['payment_mode_name'] = 'CASH';
                    if(resData.name === 'agent') {
                        this.agentAmtPay =  this.agentAmtPay + resData1.amount;
                    } else {
                        this.cashAmt = resData.name === 'refund' ? this.cashAmt - resData1.amount : this.cashAmt + resData1.amount;
                    }
                } else if(resData1.payment_mode === '1') {
                    resData1['payment_mode_name'] = 'CARD';
                    if(resData.name === 'agent') {
                        this.agentAmtPay =  this.agentAmtPay + resData1.amount + resData1.card_charge_amount;
                    } else {
                        this.cardAmt = resData.name === 'refund' ? this.cardAmt - resData1.amount - resData1.card_charge_amount : this.cardAmt + resData1.amount + resData1.card_charge_amount;
                    }
                } else if(resData1.payment_mode === '2') {
                    resData1['payment_mode_name'] = 'CHEQUE';
                    if(resData.name === 'agent') {
                        this.agentAmtPay =  this.agentAmtPay + resData1.amount;
                    } else {
                        this.chequeAmt = resData.name === 'refund' ? this.chequeAmt - resData1.amount : this.chequeAmt + resData1.amount;
                    }
                } else if(resData1.payment_mode === '3') {
                    resData1['payment_mode_name'] = 'BANK';
                    if(resData.name === 'agent') {
                        this.agentAmtPay =  this.agentAmtPay + resData1.amount;
                    } else {
                       this.bankAmt = resData.name === 'refund' ? this.bankAmt - resData1.amount : this.bankAmt + resData1.amount;
                    }
                }
                this.totalPaymentAmount = this.cashAmt +  this.cardAmt + this.chequeAmt + this.bankAmt + this.agentAmtPay;

                if(resData.name === 'pending'){
                    this.pendingAmt = this.pendingAmt +  resData1.amount + resData1.card_charge_amount;
                } else if (resData.name === 'advance'){
                    this.advanceAmt = this.advanceAmt +  resData1.amount + resData1.card_charge_amount;
                } else if (resData.name === 'checkin/checkout'){
                    this.checkinAmt = this.checkinAmt +  resData1.amount + resData1.card_charge_amount;
                } else if (resData.name === 'refund'){
                    this.returnAmt = this.returnAmt +  resData1.amount + resData1.card_charge_amount;
                } else if (resData.name === 'agent'){
                    this.agentAmt = this.agentAmt +  resData1.amount + resData1.card_charge_amount;
                }
                this.totalAmount =  this.pendingAmt +  this.advanceAmt + this.checkinAmt + this.agentAmt -  this.returnAmt ;

            })
        })
    }

    printRecords() {
        if (this.data && this.data.length > 0) {
            window.print();
        } else {
            this.showNotification('No data available!', 'error', true);
        }

    }
    showNotification(message: string, type: string, showCloseButton: boolean = true, hideAfter: number = 3) {
        Messenger().post({
          type: type,
          message: message,
          hideAfter: hideAfter,
          showCloseButton: showCloseButton,
        });
      }
    sortArray(array) {
        array.sort(function (x, y) {
            let a = x.text.toUpperCase(),
                b = y.text.toUpperCase();
            return a == b ? 0 : a > b ? 1 : -1;
        });
        return array
    }
    ngOnDestroy(): void {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****

        if (this.getReportDataSub)
            this.getReportDataSub.unsubscribe();
        if (this.csvSub)
            this.csvSub.unsubscribe();
        // New Change ****
        if (this.langChangeSub)
            this.langChangeSub.unsubscribe();
    }
}
