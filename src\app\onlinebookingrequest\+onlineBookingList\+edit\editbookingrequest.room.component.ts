import { CustomValidators } from 'ng2-validation';
import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, Validators, FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Select2OptionData } from 'ng2-select2';
import { RoomsService } from '../../../shared/services/room.service';
// import * as data from '../../data';
declare var Messenger: any;
@Component({
    selector: 'edit-room',
    templateUrl: '../onlineRequestRooms.actions.component.html'
})

export class EditOnlineBookingRequest implements OnInit {
    public pageName: string = "Edit";
    select2Options: any = {
        width: '100%'
    };
    roomForm: FormGroup;
    //services
    private sub: any;
    private floor: any;
    private getData: any;
    private floorList: any;
    // Input and outputs
    @Input() getHiddenER;
    @Input() selectedRoom;
    @Output() sendHiddenER = new EventEmitter();
    public roomCat: any[];
    public wing: any[];
    public selectedFloorId: any;
    public selectedBuildingId: any;
    public selectedRoomCategoryId: any;
    constructor(
        private _fb: FormBuilder,
        private RS: RoomsService
    ) {
        this.getData = this.RS.getRoomCatandWingData()
            .subscribe((res) => {
                if (res.status == "success") {
                    if (res.data.roomcategories) {
                        this.roomCat = res.data.roomcategories;
                    } else {
                        this.roomCat = [];
                        Messenger().post({  hideAfter: 5,
                            message: "No Room Categories found.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                    if (res.data.buildings) {
                        this.wing = res.data.buildings;
                    } else {
                        this.wing = [];
                        Messenger().post({  hideAfter: 5,
                            message: "No Buildings found.",
                            type: "info",
                            showCloseButton: true
                        });
                    }
                }
            })
        }
        
    ngOnInit() {
        this.buildForm();
        console.log("this.selectedRoom : ",this.selectedRoom);
        this.setBuilding().then( res => {
            setTimeout(() => {
                this.selectedFloorId = this.selectedRoom['floor_id'];
            }, 200);
        })
        this.roomForm.patchValue(this.selectedRoom);
    }
    setBuilding() {
        return new Promise<any>((resolve,reject) => {
            setTimeout(() => {
                this.selectedBuildingId = this.selectedRoom['building_id'];
                this.selectedRoomCategoryId = this.selectedRoom['room_category_id'];
                resolve(true);
            }, 200);
        })
    }
    buildForm() {
        this.roomForm = this._fb.group({
            title: ['', Validators.required],
            room_category_id: ['', [Validators.required, CustomValidators.digits]],
            door_id: ['', []],
            building_id: ['', [Validators.required, CustomValidators.digits]],
            floor_id: ['',[Validators.required, CustomValidators.digits]],
            default_bed: ['0', [Validators.required, CustomValidators.digits]],
            max_bed: ['0', [Validators.required, CustomValidators.digits]],
            status: ['', Validators.required]
        })
    }
    ngOnDestroy() {

    }
    getCategorylist(): Select2OptionData[] {
        return jQuery.map(this.roomCat, function (obj) {
            return { id: obj.id, text: obj.name };
        })
    }
    getWingList(): Select2OptionData[] {
        return jQuery.map(this.wing, function (obj) {
            return { id: obj.id, text: obj.name };
        })
    }
    getFloorList(): Select2OptionData[] {
        if (this.floorList) {
            return jQuery.map(this.floorList, function (obj) {
                return { id: obj.id, text: obj.name };
            })
        }
    }
    categoryChanged(event) {
        this.roomForm.controls['room_category_id'].patchValue(event.value);
    }
    wingChanged(event) {
        console.log("Wing  changed", event);        
        this.roomForm.controls['building_id'].patchValue(event.value);
        this.floor = this.RS.getBuildingFloors(event.value).subscribe((res) => {
            this.floorList = res.data;
        });

    }
    floorChanged(event) {
        console.log("Floor list changed", event.value);
        this.roomForm.controls['floor_id'].patchValue(event.value);
    }
    // save
    saveRoom() {
        if (this.roomForm.valid) {
            if(this.roomForm.value && this.roomForm.value.door_id == ''){
                this.roomForm.value.door_id = null;
            }
            console.log("Form : ",this.roomForm.value);
            this.sub = this.RS.updateRoomCatandWingData(this.selectedRoom.id, this.roomForm.value)
                .subscribe((res) => {
                    if (res.status == "success") {
                        Messenger().post({  hideAfter: 5,
                            message: res.message,
                            type: res.status,
                            showCloseButton: true
                        });
                        this.toggleChild(res.data);
                    }
                }, (err) => {
                    let errBody = JSON.parse(err._body);
                    let errors = errBody.data;
                    if (errors.length > 0) {
                        errors.forEach(element => {
                            let control = this.roomForm.controls[element.fieldname];
                            control.setErrors({
                                backend: element.error
                            });
                        });
                    }
                })
        } else {
            Messenger().post({  hideAfter: 5,
                message: "Form can not be submitted",
                type: "error",
                showCloseButton: true
            });
        }
    }
    // toggle to mains
    toggleChild(data) {
        this.getHiddenER = !this.getHiddenER;
        let result;
        if (data) {
            result = { 'getHiddenER': this.getHiddenER, 'data': data }
        } else {
            result = { 'getHiddenER': this.getHiddenER }
        }
        this.sendHiddenER.emit(result);
    }
}