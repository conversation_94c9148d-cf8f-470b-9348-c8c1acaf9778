import { Router } from '@angular/router';
import { BookingService } from './../../shared/services/booking.service';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { Component, OnInit, OnDestroy } from "@angular/core";
import { TranslateService } from '@ngx-translate/core';// New Change ****
import { AppConfig } from 'app/app.config';// New Change ****
import { TranslateEventService } from 'app/shared/services/translation.service';// New Change ****
import { Subject, Subscription } from 'rxjs';// New Change ****
import * as moment from 'moment';

@Component({
    selector: 'category-wise-availability',
    templateUrl: './category-wise-availability.template.html',
    styleUrls: ['./report.component.scss']
})
export class CategoryWiseAvailabilityComponent implements OnInit, OnDestroy {
    data: any[];
    csvSub: any;
    reportType: any;
    config: any;// New Change ****
    searchFilters: any;
    searchQuery: string;
    originalData: any[];
    searchForm: FormGroup;
    canViewRecords: boolean;
    roomTypeList: any[] = [];

    totalRooms: number;
    totalRoomsOccupied: number;
    totalRoomsAvailable: number;

    selectedFilterTypes: any = {
        room_type_id: "000000",
    };
    bookingTypeOptions: Select2.Options = {
        width: '100%',
    };
    public datepickerOpts = {
        autoclose: true,
        todayBtn: 'linked',
        format: 'dd/mm/yyyy',
        todayHighlight: true,
        icon: 'fa fa-calendar'
    }

    getReports: any;
    public $destroy = new Subject(); // New Change ****
    private langChangeSub: Subscription; // New Change ****
    constructor(
        public translate: TranslateService,// New Change ****
        private router: Router,
        private _fb: FormBuilder,
        private BS: BookingService,
        config: AppConfig,// New Change ****
        private TS: TranslateEventService, // New Change ****
    ) {
        this.config = config.getConfig();// New Change ****
        let currentLang = localStorage.getItem('currentLang'); // New Change ****
        translate.setDefaultLang(currentLang);// New Change ****
        this.reportType = this.router.url.split('/')[this.router.url.split('/').length - 1];
        this.inItForm();
         // New Change ****
         this.langChangeSub = this.TS.$langChangeSubject.takeUntil(this.$destroy).subscribe((res: any) => {
            this.changeLang(res);
        });
    }

    ngOnInit() {
        this.canViewRecords = true;
        this.searchFilters = this.BS.getBookingReportFilters()
            .subscribe(res => {
                if (res.status == "success") {
                    this.roomTypeList = res.data.roomTypeList.map((obj) => {
                        return { id: obj.id, text: obj.name };
                    });
                    this.roomTypeList.unshift({ "id": "000000", "text": "All-togather" });
                }
            },
                error => {
                    this.canViewRecords = false;
                });
    }
    changeLang(lang: string) {
        // New Change ****
        this.translate.use(lang);
    }
    inItForm() {
        this.searchForm = this._fb.group({
            toDate: [null],
            fromDate: [null],
            room_type_id: [null, [Validators.required]]
        });
    }
    toDataChange(event) {
        this.searchForm.controls['toDate'].patchValue(event);
       }
    searchReports() {
        if (this.searchForm.valid) {
            let searchParams = JSON.parse(JSON.stringify(this.searchForm.value));
            searchParams.fromDate = moment(searchParams.fromDate).format('YYYY-MM-DD');
            searchParams.toDate = moment(searchParams.toDate).format('YYYY-MM-DD');
            this.filterData(searchParams);
        }
        else {
            for (let field in this.searchForm.controls) {
                this.searchForm.controls[field].markAsDirty();
                this.searchForm.controls[field].markAsTouched();
            }
        }
    }
    getTotalRooms(item: any) {
        let freeCount = item.free_count ? parseInt(item.free_count) : 0;
        let occupiedCount = item.occupied_count ? parseInt(item.occupied_count) : 0;
        return freeCount + occupiedCount;
    }
    public totalCount = (a: any) => {
        return this.getTotalRooms(a);
    }
    public freeCount = (a: any) => {
        return (a.free_count ? parseInt(a.free_count) : 0);

    }
    public occupiedCount = (a: any) => {
        return (a.occupied_count ? parseInt(a.occupied_count) : 0);
    }
    /**
    * FIlters a given data with multiple filter types
    * 
    * @param {any} dates : Date range for filter
   */
    filterData(searchParams: any) {
        this.getReports = this.BS.getCategoryWiseAvailabilityReport(searchParams)
            .subscribe(res => {
                if (res.status == "success") {
                    let data = res.data;
                    this.data = data;
                    this.originalData = data;
                    this.updateAmountSummary();
                }
            });
    }
    search() {
        if (this.searchForm && this.data) {
            this.initializeData();
            if (this.data && this.searchQuery && this.searchQuery.trim() != '') {
                this.data = this.data.filter(data => {
                    let searchTarget = '';
                    Object.keys(data).forEach(key => {
                        searchTarget += data[key];
                    })
                    return (searchTarget.toLowerCase().indexOf(this.searchQuery.toLowerCase()) > -1);
                });
                this.updateAmountSummary();
            }
            else {
                this.initializeData();
            }
        }
    }
    initializeData() {
        this.data = this.originalData;
        this.updateAmountSummary();
    }
    clearSearch() {
        this.initializeData();
        this.searchQuery = undefined;
    }
    printRecords() {
        if (this.data && this.data.length > 0) {
            let data = JSON.parse(JSON.stringify(this.data));
            data.forEach(item => {
                delete item.roomcatid;
                let freeCount = item.free_count ? parseInt(item.free_count) : 0;
                let occupiedCount = item.occupied_count ? parseInt(item.occupied_count) : 0;
                item['total'] = freeCount + occupiedCount;
            });
            this.csvSub = this.BS.getCsvReport(data, 'room-availability')
                .subscribe(res => {
                    // Create blob from the text response
                    let blob = new Blob([res], { type: 'text/csv;charset=utf-8' });
                    let url = window.URL.createObjectURL(blob);
                    
                    // Create download link
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = this.reportType + '.csv';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                },
                    err => console.log("error : ", err)
                );
        }

    }
    /**
     * Returns a summary of filter report records
     */
    updateAmountSummary() {
        this.initializeSummary();
        if(this.data) {
            this.data.forEach(data => {
                this.totalRoomsAvailable += parseInt(data.free_count);
                this.totalRoomsOccupied += parseInt(data.occupied_count);
                this.totalRooms += parseInt(data.free_count) + parseInt(data.occupied_count);
            });
        }
    }
    initializeSummary() {
        this.totalRoomsAvailable = this.totalRoomsOccupied = this.totalRooms = 0;
    }
    roomTypeChanged(event: any) {
        let roomType = <FormControl>this.searchForm.controls['room_type_id'];
        this.selectedFilterTypes.room_type_id = event?.id;
        roomType.patchValue(event?.id);
    }
    ngOnDestroy() {
        this.$destroy.next(); // New Change ****
        this.$destroy.complete(); // New Change ****
         // New Change ****
         if (this.langChangeSub)
         this.langChangeSub.unsubscribe();
    }
}